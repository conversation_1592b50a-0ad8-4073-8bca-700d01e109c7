#!/bin/bash
set -e

# Omnispace Deployment Script
# Deploys Omnispace platform with proper initialization

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [start|stop|restart|status|logs|init] [options]"
    echo ""
    echo "Commands:"
    echo "  init        - Initialize Omnispace (first-time setup)"
    echo "  start       - Start Omnispace platform"
    echo "  stop        - Stop Omnispace platform"
    echo "  restart     - Restart Omnispace platform"
    echo "  status      - Show service status"
    echo "  logs        - Show service logs"
    echo ""
    echo "Options:"
    echo "  --build     - Build images before starting"
    echo "  --pull      - Pull latest images before starting"
    echo "  --follow    - Follow logs (for logs command)"
    echo "  --service   - Specify service for logs (omnispace|guacamole|guacd|guacamole-db)"
    echo ""
    echo "Examples:"
    echo "  $0 init                           # First-time setup"
    echo "  $0 start --build                  # Build and start"
    echo "  $0 logs --follow --service omnispace  # Follow Omnispace logs"
}

# Parse arguments
COMMAND=""
BUILD_IMAGES=false
PULL_IMAGES=false
FOLLOW_LOGS=false
SERVICE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        init|start|stop|restart|status|logs)
            COMMAND="$1"
            shift
            ;;
        --build)
            BUILD_IMAGES=true
            shift
            ;;
        --pull)
            PULL_IMAGES=true
            shift
            ;;
        --follow)
            FOLLOW_LOGS=true
            shift
            ;;
        --service)
            SERVICE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

if [ -z "$COMMAND" ]; then
    print_error "No command specified"
    show_usage
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running"
        exit 1
    fi
    
    # Check Docker Compose
    if ! docker compose version >/dev/null 2>&1; then
        print_error "Docker Compose V2 is not available"
        exit 1
    fi
    
    # Check environment file
    if [ ! -f ".env" ]; then
        if [ -f ".env.docker" ]; then
            print_warning ".env file not found, copying from .env.docker template"
            cp .env.docker .env
        else
            print_error ".env file not found. Please create one from .env.docker template"
            exit 1
        fi
    fi
}

# Initialize Omnispace (first-time setup)
init_omnispace() {
    print_status "Initializing Omnispace platform..."
    
    # Create necessary directories
    mkdir -p workspace-images/guacamole/init
    mkdir -p backups
    
    # Generate Guacamole database initialization script
    print_status "Generating Guacamole database initialization script..."
    if docker run --rm guacamole/guacamole:1.5.5 /opt/guacamole/bin/initdb.sh --postgresql > workspace-images/guacamole/init/initdb.sql; then
        print_success "Database initialization script created"
    else
        print_error "Failed to create database initialization script"
        exit 1
    fi
    
    # Create workspace network
    print_status "Creating workspace network..."
    if docker network create omnispace-workspace-network 2>/dev/null; then
        print_success "Workspace network created"
    else
        print_warning "Workspace network already exists"
    fi
    
    # Generate secure passwords if not set
    if ! grep -q "GUACAMOLE_DB_PASSWORD.*change_me" .env; then
        print_status "Generating secure database password..."
        SECURE_PASSWORD=$(openssl rand -base64 32)
        sed -i "s/guacamole_secure_pass_2025_change_me/$SECURE_PASSWORD/" .env
    fi
    
    if ! grep -q "JWT_SECRET.*change_me" .env; then
        print_status "Generating secure JWT secret..."
        JWT_SECRET=$(openssl rand -base64 64)
        sed -i "s/your_jwt_secret_key_change_me_in_production/$JWT_SECRET/" .env
    fi
    
    print_success "Omnispace initialization completed!"
    print_status "Next steps:"
    echo "1. Review and update .env file with your API keys"
    echo "2. Run: $0 start --build"
    echo "3. Access Omnispace at http://localhost:3000"
    echo "4. Access Guacamole at http://localhost:8080 (guacadmin/guacadmin)"
}

# Start Omnispace platform
start_omnispace() {
    print_status "Starting Omnispace platform..."
    
    # Build images if requested
    if [ "$BUILD_IMAGES" = true ]; then
        print_status "Building images..."
        ./scripts/build.sh all
    fi
    
    # Pull images if requested
    if [ "$PULL_IMAGES" = true ]; then
        print_status "Pulling latest images..."
        docker compose pull
    fi
    
    # Start services
    docker compose up -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    if docker compose ps | grep -q "unhealthy"; then
        print_warning "Some services may not be healthy. Check logs with: $0 logs"
    fi
    
    print_success "Omnispace platform started!"
    print_status "Access points:"
    echo "- Omnispace Web UI: http://localhost:$(grep OMNISPACE_PORT .env | cut -d'=' -f2 || echo 3000)"
    echo "- Guacamole Interface: http://localhost:$(grep GUACAMOLE_PORT .env | cut -d'=' -f2 || echo 8080)"
}

# Stop Omnispace platform
stop_omnispace() {
    print_status "Stopping Omnispace platform..."
    docker compose down
    print_success "Omnispace platform stopped"
}

# Restart Omnispace platform
restart_omnispace() {
    print_status "Restarting Omnispace platform..."
    stop_omnispace
    sleep 5
    start_omnispace
}

# Show service status
show_status() {
    print_status "Omnispace platform status:"
    docker compose ps
    
    echo ""
    print_status "Docker networks:"
    docker network ls | grep omnispace
    
    echo ""
    print_status "Docker volumes:"
    docker volume ls | grep omnispace
}

# Show service logs
show_logs() {
    if [ -n "$SERVICE" ]; then
        if [ "$FOLLOW_LOGS" = true ]; then
            docker compose logs -f "$SERVICE"
        else
            docker compose logs "$SERVICE"
        fi
    else
        if [ "$FOLLOW_LOGS" = true ]; then
            docker compose logs -f
        else
            docker compose logs
        fi
    fi
}

# Execute command
check_prerequisites

case $COMMAND in
    init)
        init_omnispace
        ;;
    start)
        start_omnispace
        ;;
    stop)
        stop_omnispace
        ;;
    restart)
        restart_omnispace
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
esac
