#!/bin/bash
set -e

# Omnispace Build Script
# Builds the main application and workspace images

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [app|workspaces|all] [--no-cache] [--push]"
    echo ""
    echo "Build targets:"
    echo "  app         - Build Omnispace application only"
    echo "  workspaces  - Build workspace images only"
    echo "  all         - Build everything (default)"
    echo ""
    echo "Options:"
    echo "  --no-cache  - Build without using Docker cache"
    echo "  --push      - Push images to registry after building"
    echo ""
    echo "Examples:"
    echo "  $0 app                    # Build Omnispace application"
    echo "  $0 workspaces --no-cache  # Build workspace images without cache"
    echo "  $0 all --push             # Build everything and push to registry"
}

# Parse arguments
BUILD_TARGET="all"
NO_CACHE=""
PUSH_IMAGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        app|workspaces|all)
            BUILD_TARGET="$1"
            shift
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Get version tag
VERSION=$(date +%Y%m%d)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

print_status "Building Omnispace - Version: $VERSION, Commit: $GIT_COMMIT"

# Build Omnispace application
build_app() {
    print_status "Building Omnispace application..."
    
    # Check if Next.js config supports standalone output
    if ! grep -q "output.*standalone" next.config.ts 2>/dev/null; then
        print_warning "Adding standalone output to next.config.ts for Docker optimization"
        # This would need to be done manually or with a more sophisticated script
    fi
    
    if docker build $NO_CACHE -t omnispace/app:latest -t omnispace/app:$VERSION .; then
        print_success "Omnispace application built successfully"
        
        # Show image size
        IMAGE_SIZE=$(docker images omnispace/app:latest --format "{{.Size}}")
        print_status "Image size: $IMAGE_SIZE"
    else
        print_error "Failed to build Omnispace application"
        return 1
    fi
}

# Build workspace images
build_workspaces() {
    print_status "Building workspace images..."
    
    if [ ! -d "workspace-images" ]; then
        print_error "workspace-images directory not found"
        return 1
    fi
    
    cd workspace-images
    
    if [ -f "build-all.sh" ]; then
        if NO_CACHE_FLAG="$NO_CACHE" ./build-all.sh; then
            print_success "Workspace images built successfully"
        else
            print_error "Failed to build workspace images"
            cd ..
            return 1
        fi
    else
        print_error "build-all.sh script not found in workspace-images directory"
        cd ..
        return 1
    fi
    
    cd ..
}

# Push images to registry
push_images() {
    if [ "$PUSH_IMAGES" = true ]; then
        print_status "Pushing images to registry..."
        
        # Check if registry is configured
        REGISTRY=${DOCKER_REGISTRY:-}
        if [ -z "$REGISTRY" ]; then
            print_warning "DOCKER_REGISTRY not set, using Docker Hub"
        fi
        
        # Push application image
        if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
            docker push omnispace/app:latest
            docker push omnispace/app:$VERSION
        fi
        
        # Push workspace images
        if [ "$BUILD_TARGET" = "workspaces" ] || [ "$BUILD_TARGET" = "all" ]; then
            docker push omnispace/ubuntu-desktop:latest
            docker push omnispace/ubuntu-desktop:$VERSION
            docker push omnispace/development-env:latest
            docker push omnispace/development-env:$VERSION
            docker push omnispace/minimal-desktop:latest
            docker push omnispace/minimal-desktop:$VERSION
        fi
        
        print_success "Images pushed to registry"
    fi
}

# Execute build based on target
case $BUILD_TARGET in
    app)
        build_app
        ;;
    workspaces)
        build_workspaces
        ;;
    all)
        build_app
        build_workspaces
        ;;
esac

# Push images if requested
push_images

# Display summary
print_status "Build Summary:"
echo "Target: $BUILD_TARGET"
echo "Version: $VERSION"
echo "Git Commit: $GIT_COMMIT"
echo "Cache: $([ -n "$NO_CACHE" ] && echo "disabled" || echo "enabled")"
echo "Push: $([ "$PUSH_IMAGES" = true ] && echo "enabled" || echo "disabled")"

print_status "Built images:"
docker images | grep omnispace

print_success "Build completed successfully!"
