const fs = require('fs');
const path = require('path');

// Function to recursively get all .ts and .tsx files in a directory
function getAllTsFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  list.forEach(file => {
    file = path.resolve(dir, file);
    const stat = fs.statSync(file);
    if (stat && stat.isDirectory()) {
      // Skip node_modules and other directories we don't want to scan
      if (file.includes('node_modules') || file.includes('.next') || file.includes('.git')) {
        return;
      }
      results = [...results, ...getAllTsFiles(file)];
    } else if (path.extname(file) === '.ts' || path.extname(file) === '.tsx') {
      results.push(file);
    }
  });
  return results;
}

// Function to update imports in a file
function updateFileImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    
    // Check if the file imports from services/docker
    if (content.includes("import") && content.includes("@/services/docker")) {
      // Replace imports of WorkspaceInfo from services/docker to types/docker
      const workspaceInfoRegex = /import\s*{([^}]*WorkspaceInfo[^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(workspaceInfoRegex, (match, imports) => {
        updated = true;
        return `import {${imports}} from '@/types/docker'`;
      });
      
      // Replace imports of ContainerInfo from services/docker to types/docker
      const containerInfoRegex = /import\s*{([^}]*ContainerInfo[^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(containerInfoRegex, (match, imports) => {
        updated = true;
        return `import {${imports}} from '@/types/docker'`;
      });
      
      // Replace imports of CreateContainerOptions from services/docker to types/docker
      const createContainerOptionsRegex = /import\s*{([^}]*CreateContainerOptions[^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(createContainerOptionsRegex, (match, imports) => {
        updated = true;
        return `import {${imports}} from '@/types/docker'`;
      });
      
      // Replace imports of CreateWorkspaceOptions from services/docker to types/docker
      const createWorkspaceOptionsRegex = /import\s*{([^}]*CreateWorkspaceOptions[^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(createWorkspaceOptionsRegex, (match, imports) => {
        updated = true;
        return `import {${imports}} from '@/types/docker'`;
      });
      
      // Handle cases where all types are imported together
      const combinedRegex = /import\s*{([^}]*WorkspaceInfo[^}]*ContainerInfo[^}]*CreateContainerOptions[^}]*CreateWorkspaceOptions[^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(combinedRegex, (match, imports) => {
        updated = true;
        return `import {${imports}} from '@/types/docker'`;
      });
      
      // Handle multiple separate imports from the same file
      const multipleImportsRegex = /import\s*{([^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(multipleImportsRegex, (match, imports) => {
        // Only update if imports contain our target types
        if (imports.includes('WorkspaceInfo') || imports.includes('ContainerInfo') || 
            imports.includes('CreateContainerOptions') || imports.includes('CreateWorkspaceOptions')) {
          updated = true;
          return `import {${imports}} from '@/types/docker'`;
        }
        return match;
      });
      
      // Handle re-exports
      const reExportRegex = /export type\s*{([^}]*)}\s*from\s*['"]@\/services\/docker['"]/g;
      content = content.replace(reExportRegex, (match, exports) => {
        // Only update if exports contain our target types
        if (exports.includes('WorkspaceInfo') || exports.includes('ContainerInfo') || 
            exports.includes('CreateContainerOptions') || exports.includes('CreateWorkspaceOptions')) {
          updated = true;
          return `export type {${exports}} from '@/types/docker'`;
        }
        return match;
      });
    }
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error updating file ${filePath}:`, error);
    return false;
  }
}

// Main execution
function main() {
  const srcDir = path.resolve(__dirname, '../src');
  const tsFiles = getAllTsFiles(srcDir);
  
  console.log(`Found ${tsFiles.length} TS/TSX files to check`);
  
  let updatedFiles = 0;
  tsFiles.forEach(file => {
    if (updateFileImports(file)) {
      updatedFiles++;
    }
  });
  
  console.log(`\nUpdated ${updatedFiles} files with new Docker type imports`);
}

main();