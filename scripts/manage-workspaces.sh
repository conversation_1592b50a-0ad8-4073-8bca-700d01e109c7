#!/bin/bash
set -e

# Omnispace Workspace Management Script
# Manages workspace containers and Guacamole connections

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [create|list|stop|remove|logs] [options]"
    echo ""
    echo "Commands:"
    echo "  create      - Create a new workspace container"
    echo "  list        - List all workspace containers"
    echo "  stop        - Stop workspace container(s)"
    echo "  remove      - Remove workspace container(s)"
    echo "  logs        - Show workspace container logs"
    echo "  cleanup     - Remove stopped workspace containers"
    echo ""
    echo "Create options:"
    echo "  --type      - Workspace type (ubuntu|development|minimal)"
    echo "  --name      - Container name (default: auto-generated)"
    echo "  --user      - User ID for the workspace"
    echo "  --password  - VNC password (default: auto-generated)"
    echo "  --width     - Display width (default: 1920)"
    echo "  --height    - Display height (default: 1080)"
    echo "  --cpu       - CPU limit (default: 2)"
    echo "  --memory    - Memory limit in MB (default: 2048)"
    echo ""
    echo "Other options:"
    echo "  --container - Container name/ID for stop/remove/logs commands"
    echo "  --all       - Apply to all workspace containers"
    echo ""
    echo "Examples:"
    echo "  $0 create --type ubuntu --user john --cpu 4 --memory 4096"
    echo "  $0 list"
    echo "  $0 stop --container workspace-john-ubuntu"
    echo "  $0 remove --all"
    echo "  $0 logs --container workspace-john-ubuntu"
}

# Default values
WORKSPACE_TYPE=""
CONTAINER_NAME=""
USER_ID=""
VNC_PASSWORD=""
DISPLAY_WIDTH=1920
DISPLAY_HEIGHT=1080
CPU_LIMIT=2
MEMORY_LIMIT=2048
TARGET_CONTAINER=""
APPLY_ALL=false

# Parse arguments
COMMAND=""
while [[ $# -gt 0 ]]; do
    case $1 in
        create|list|stop|remove|logs|cleanup)
            COMMAND="$1"
            shift
            ;;
        --type)
            WORKSPACE_TYPE="$2"
            shift 2
            ;;
        --name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --user)
            USER_ID="$2"
            shift 2
            ;;
        --password)
            VNC_PASSWORD="$2"
            shift 2
            ;;
        --width)
            DISPLAY_WIDTH="$2"
            shift 2
            ;;
        --height)
            DISPLAY_HEIGHT="$2"
            shift 2
            ;;
        --cpu)
            CPU_LIMIT="$2"
            shift 2
            ;;
        --memory)
            MEMORY_LIMIT="$2"
            shift 2
            ;;
        --container)
            TARGET_CONTAINER="$2"
            shift 2
            ;;
        --all)
            APPLY_ALL=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

if [ -z "$COMMAND" ]; then
    print_error "No command specified"
    show_usage
    exit 1
fi

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running"
    exit 1
fi

# Get next available VNC port
get_next_vnc_port() {
    local base_port=5901
    local port=$base_port
    
    while docker ps --format "{{.Ports}}" | grep -q ":$port->"; do
        ((port++))
    done
    
    echo $port
}

# Generate container name
generate_container_name() {
    local user=${USER_ID:-"user"}
    local type=${WORKSPACE_TYPE:-"ubuntu"}
    local timestamp=$(date +%s)
    echo "workspace-${user}-${type}-${timestamp}"
}

# Create workspace container
create_workspace() {
    # Validate workspace type
    case $WORKSPACE_TYPE in
        ubuntu|development|minimal)
            ;;
        "")
            print_error "Workspace type is required (--type ubuntu|development|minimal)"
            exit 1
            ;;
        *)
            print_error "Invalid workspace type: $WORKSPACE_TYPE"
            exit 1
            ;;
    esac
    
    # Generate container name if not provided
    if [ -z "$CONTAINER_NAME" ]; then
        CONTAINER_NAME=$(generate_container_name)
    fi
    
    # Generate VNC password if not provided
    if [ -z "$VNC_PASSWORD" ]; then
        VNC_PASSWORD=$(openssl rand -base64 12)
    fi
    
    # Get next available VNC port
    VNC_PORT=$(get_next_vnc_port)
    
    # Set image name based on type
    case $WORKSPACE_TYPE in
        ubuntu)
            IMAGE_NAME="omnispace/ubuntu-desktop:latest"
            ;;
        development)
            IMAGE_NAME="omnispace/development-env:latest"
            ;;
        minimal)
            IMAGE_NAME="omnispace/minimal-desktop:latest"
            ;;
    esac
    
    print_status "Creating workspace container..."
    print_status "Type: $WORKSPACE_TYPE"
    print_status "Name: $CONTAINER_NAME"
    print_status "User: ${USER_ID:-workspace}"
    print_status "VNC Port: $VNC_PORT"
    print_status "Resolution: ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}"
    print_status "Resources: ${CPU_LIMIT} CPU, ${MEMORY_LIMIT}MB RAM"
    
    # Create container
    docker run -d \
        --name "$CONTAINER_NAME" \
        --network omnispace-workspace-network \
        --label "omnispace.workspace=true" \
        --label "omnispace.workspace.type=$WORKSPACE_TYPE" \
        --label "omnispace.workspace.user=${USER_ID:-workspace}" \
        --cpus="$CPU_LIMIT" \
        --memory="${MEMORY_LIMIT}m" \
        -p "$VNC_PORT:5901" \
        -e VNC_PASSWORD="$VNC_PASSWORD" \
        -e DISPLAY_WIDTH="$DISPLAY_WIDTH" \
        -e DISPLAY_HEIGHT="$DISPLAY_HEIGHT" \
        -e USER_NAME="${USER_ID:-workspace}" \
        $([ "$WORKSPACE_TYPE" = "development" ] && echo "-v /var/run/docker.sock:/var/run/docker.sock") \
        "$IMAGE_NAME"
    
    if [ $? -eq 0 ]; then
        print_success "Workspace container created successfully!"
        echo ""
        print_status "Connection details:"
        echo "Container Name: $CONTAINER_NAME"
        echo "VNC Port: $VNC_PORT"
        echo "VNC Password: $VNC_PASSWORD"
        echo "VNC URL: vnc://localhost:$VNC_PORT"
        echo ""
        print_status "The workspace will be automatically registered with Guacamole"
    else
        print_error "Failed to create workspace container"
        exit 1
    fi
}

# List workspace containers
list_workspaces() {
    print_status "Workspace containers:"
    echo ""
    
    # Get workspace containers
    CONTAINERS=$(docker ps -a --filter "label=omnispace.workspace=true" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Label \"omnispace.workspace.type\"}}\t{{.Label \"omnispace.workspace.user\"}}")
    
    if [ -z "$CONTAINERS" ] || [ "$CONTAINERS" = "NAMES	STATUS	PORTS	omnispace.workspace.type	omnispace.workspace.user" ]; then
        print_warning "No workspace containers found"
    else
        echo "$CONTAINERS" | head -1  # Header
        echo "$CONTAINERS" | tail -n +2 | sort
    fi
}

# Stop workspace containers
stop_workspaces() {
    if [ "$APPLY_ALL" = true ]; then
        print_status "Stopping all workspace containers..."
        CONTAINERS=$(docker ps --filter "label=omnispace.workspace=true" --format "{{.Names}}")
        if [ -z "$CONTAINERS" ]; then
            print_warning "No running workspace containers found"
            return
        fi
        echo "$CONTAINERS" | xargs -r docker stop
        print_success "All workspace containers stopped"
    elif [ -n "$TARGET_CONTAINER" ]; then
        print_status "Stopping container: $TARGET_CONTAINER"
        docker stop "$TARGET_CONTAINER"
        print_success "Container stopped: $TARGET_CONTAINER"
    else
        print_error "Specify --container <name> or --all"
        exit 1
    fi
}

# Remove workspace containers
remove_workspaces() {
    if [ "$APPLY_ALL" = true ]; then
        print_status "Removing all workspace containers..."
        CONTAINERS=$(docker ps -a --filter "label=omnispace.workspace=true" --format "{{.Names}}")
        if [ -z "$CONTAINERS" ]; then
            print_warning "No workspace containers found"
            return
        fi
        echo "$CONTAINERS" | xargs -r docker rm -f
        print_success "All workspace containers removed"
    elif [ -n "$TARGET_CONTAINER" ]; then
        print_status "Removing container: $TARGET_CONTAINER"
        docker rm -f "$TARGET_CONTAINER"
        print_success "Container removed: $TARGET_CONTAINER"
    else
        print_error "Specify --container <name> or --all"
        exit 1
    fi
}

# Show workspace container logs
show_workspace_logs() {
    if [ -z "$TARGET_CONTAINER" ]; then
        print_error "Specify --container <name>"
        exit 1
    fi
    
    print_status "Showing logs for: $TARGET_CONTAINER"
    docker logs -f "$TARGET_CONTAINER"
}

# Cleanup stopped workspace containers
cleanup_workspaces() {
    print_status "Cleaning up stopped workspace containers..."
    STOPPED_CONTAINERS=$(docker ps -a --filter "label=omnispace.workspace=true" --filter "status=exited" --format "{{.Names}}")
    
    if [ -z "$STOPPED_CONTAINERS" ]; then
        print_warning "No stopped workspace containers found"
    else
        echo "$STOPPED_CONTAINERS" | xargs -r docker rm
        print_success "Stopped workspace containers cleaned up"
    fi
}

# Execute command
case $COMMAND in
    create)
        create_workspace
        ;;
    list)
        list_workspaces
        ;;
    stop)
        stop_workspaces
        ;;
    remove)
        remove_workspaces
        ;;
    logs)
        show_workspace_logs
        ;;
    cleanup)
        cleanup_workspaces
        ;;
esac
