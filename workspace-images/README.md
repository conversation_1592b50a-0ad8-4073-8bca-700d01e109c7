# Omnispace Workspace Images

This directory contains Docker images for the ephemeral workspace containers that Omnispace manages. These images are designed to work with Apache Guacamole for clientless remote desktop access.

## Architecture

The workspace images are designed to integrate with Apache Guacamole, providing:
- **Clientless access**: No VNC client needed - works directly in web browsers
- **Multiple protocols**: Support for VNC, RDP, SSH through Guacamole
- **Better security**: Built-in authentication and session management
- **Optimized performance**: Web-optimized with compression and caching

## Available Images

### 1. Ubuntu Desktop (`ubuntu-desktop/`)
- **Base**: Ubuntu 22.04 LTS
- **Desktop**: GNOME with minimal configuration
- **VNC Server**: TigerVNC for Guacamole integration
- **Default Resources**: 2 CPU, 2GB RAM, 20GB disk
- **Features**:
  - Full Ubuntu desktop environment
  - Firefox web browser
  - LibreOffice suite
  - Basic development tools (vim, nano, curl, wget)
  - File manager and terminal

### 2. Development Environment (`development-env/`)
- **Base**: Ubuntu 22.04 LTS
- **Desktop**: GNOME with development-focused setup
- **VNC Server**: TigerVNC for Guacamole integration
- **Default Resources**: 4 CPU, 4GB RAM, 40GB disk
- **Features**:
  - All Ubuntu Desktop features
  - Visual Studio Code
  - Docker CE
  - Git with common configurations
  - Node.js (LTS) and npm/pnpm
  - Python 3 with pip
  - Java OpenJDK
  - Development databases (PostgreSQL, Redis)
  - Common development tools and utilities

### 3. Minimal Desktop (`minimal-desktop/`)
- **Base**: Alpine Linux
- **Desktop**: XFCE4 lightweight desktop
- **VNC Server**: x11vnc for Guacamole integration
- **Default Resources**: 1 CPU, 1GB RAM, 10GB disk
- **Features**:
  - Lightweight XFCE desktop
  - Basic applications (terminal, file manager, text editor)
  - Minimal resource footprint
  - Fast startup time

## Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Apache Guacamole running (see `guacamole/` directory)

### Build All Images
```bash
# Build all workspace images
./build-all.sh

# Or build individual images
cd ubuntu-desktop && docker build -t omnispace/ubuntu-desktop .
cd development-env && docker build -t omnispace/development-env .
cd minimal-desktop && docker build -t omnispace/minimal-desktop .
```

### Run a Workspace
```bash
# Start Ubuntu Desktop workspace
docker run -d \
  --name workspace-ubuntu-1 \
  -p 5901:5901 \
  -e VNC_PASSWORD=secure123 \
  -e DISPLAY_WIDTH=1920 \
  -e DISPLAY_HEIGHT=1080 \
  omnispace/ubuntu-desktop

# Start Development Environment
docker run -d \
  --name workspace-dev-1 \
  -p 5902:5901 \
  -e VNC_PASSWORD=secure123 \
  -e DISPLAY_WIDTH=1920 \
  -e DISPLAY_HEIGHT=1080 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  omnispace/development-env
```

## Integration with Omnispace

These images are designed to be managed by the Omnispace platform:

1. **Container Creation**: Omnispace creates containers from these images
2. **VNC Configuration**: Automatic VNC server setup with dynamic ports
3. **Guacamole Integration**: Containers are registered with Guacamole
4. **User Access**: Users connect through Guacamole web interface
5. **Resource Management**: CPU, memory, and storage limits enforced
6. **Lifecycle Management**: Automatic cleanup of ephemeral workspaces

## Environment Variables

All workspace images support these environment variables:

- `VNC_PASSWORD`: Password for VNC access (default: randomly generated)
- `DISPLAY_WIDTH`: Screen width in pixels (default: 1920)
- `DISPLAY_HEIGHT`: Screen height in pixels (default: 1080)
- `VNC_DEPTH`: Color depth (default: 24)
- `USER_NAME`: Username for the workspace user (default: workspace)
- `USER_PASSWORD`: Password for the workspace user (default: randomly generated)

## Security Considerations

- VNC passwords are randomly generated if not specified
- User accounts have limited privileges
- Network access can be restricted through Docker networking
- File system access is containerized
- Regular security updates applied to base images

## Building and Deployment

See individual image directories for specific build instructions and customization options.

## Troubleshooting

### Common Issues
1. **VNC Connection Failed**: Check if VNC server is running and port is accessible
2. **Display Issues**: Verify DISPLAY_WIDTH and DISPLAY_HEIGHT settings
3. **Performance**: Adjust CPU and memory limits based on workload

### Logs
```bash
# Check container logs
docker logs workspace-ubuntu-1

# Check VNC server logs
docker exec workspace-ubuntu-1 cat /var/log/vnc.log
```
