#!/bin/bash
set -e

# Generate random passwords if not provided
if [ -z "$VNC_PASSWORD" ]; then
    VNC_PASSWORD=$(openssl rand -base64 12)
    echo "Generated VNC password: $VNC_PASSWORD"
fi

if [ -z "$USER_PASSWORD" ]; then
    USER_PASSWORD=$(openssl rand -base64 12)
    echo "Generated user password: $USER_PASSWORD"
fi

# Set user password
echo "$USER_NAME:$USER_PASSWORD" | sudo chpasswd

# Set VNC password
mkdir -p ~/.vnc
echo "$VNC_PASSWORD" | vncpasswd -f > ~/.vnc/passwd
chmod 600 ~/.vnc/passwd

# Create VNC configuration file
cat > ~/.vnc/config << EOF
geometry=${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}
depth=${VNC_DEPTH}
dpi=96
EOF

# Set up environment variables for desktop session
cat > ~/.vnc/environment << EOF
XKL_XMODMAP_DISABLE=1
XDG_CURRENT_DESKTOP=GNOME
XDG_SESSION_DESKTOP=gnome
XDG_SESSION_TYPE=x11
GNOME_SHELL_SESSION_MODE=ubuntu
EOF

# Create log directory
mkdir -p ~/.vnc/logs

# Start dbus session
if [ -z "$DBUS_SESSION_BUS_ADDRESS" ]; then
    eval $(dbus-launch --sh-syntax)
    export DBUS_SESSION_BUS_ADDRESS
fi

# Initialize GNOME settings
gsettings set org.gnome.desktop.screensaver lock-enabled false 2>/dev/null || true
gsettings set org.gnome.desktop.session idle-delay 0 2>/dev/null || true
gsettings set org.gnome.settings-daemon.plugins.power sleep-inactive-ac-type 'nothing' 2>/dev/null || true

# Start Docker daemon if socket is mounted
if [ -S /var/run/docker.sock ]; then
    echo "Docker socket detected, Docker commands will be available"
fi

# Execute the command
exec "$@"
