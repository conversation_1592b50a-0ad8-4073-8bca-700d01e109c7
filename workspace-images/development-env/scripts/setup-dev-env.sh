#!/bin/bash
set -e

echo "Setting up development environment..."

# Create development directories
mkdir -p /home/<USER>/Projects
mkdir -p /home/<USER>/Scripts
mkdir -p /home/<USER>/.local/bin

# Set up Git global configuration
cat > /home/<USER>/.gitconfig << EOF
[user]
    name = Workspace User
    email = <EMAIL>
[init]
    defaultBranch = main
[core]
    editor = code --wait
[pull]
    rebase = false
EOF

# Create VS Code settings
mkdir -p /home/<USER>/.config/Code/User
cat > /home/<USER>/.config/Code/User/settings.json << EOF
{
    "workbench.colorTheme": "Default Dark+",
    "editor.fontFamily": "'Fira Code', 'Courier New', monospace",
    "editor.fontLigatures": true,
    "editor.fontSize": 14,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.wordWrap": "on",
    "editor.minimap.enabled": true,
    "terminal.integrated.fontSize": 14,
    "files.autoSave": "afterDelay",
    "extensions.autoUpdate": false,
    "telemetry.telemetryLevel": "off"
}
EOF

# Create desktop shortcuts for development tools
mkdir -p /home/<USER>/Desktop

# VS Code shortcut
cat > /home/<USER>/Desktop/VSCode.desktop << EOF
[Desktop Entry]
Type=Application
Name=Visual Studio Code
Exec=code
Icon=code
Terminal=false
Categories=Development;
EOF

# Terminal shortcut
cat > /home/<USER>/Desktop/Terminal.desktop << EOF
[Desktop Entry]
Type=Application
Name=Terminal
Exec=gnome-terminal
Icon=utilities-terminal
Terminal=false
Categories=System;
EOF

# Firefox shortcut
cat > /home/<USER>/Desktop/Firefox.desktop << EOF
[Desktop Entry]
Type=Application
Name=Firefox
Exec=firefox
Icon=firefox
Terminal=false
Categories=Network;
EOF

# Make desktop shortcuts executable
chmod +x /home/<USER>/Desktop/*.desktop

# Create useful development scripts
cat > /home/<USER>/Scripts/start-postgres.sh << 'EOF'
#!/bin/bash
# Start PostgreSQL in Docker
docker run -d \
  --name postgres-dev \
  -e POSTGRES_PASSWORD=devpass \
  -e POSTGRES_DB=devdb \
  -p 5432:5432 \
  postgres:15
EOF

cat > /home/<USER>/Scripts/start-redis.sh << 'EOF'
#!/bin/bash
# Start Redis in Docker
docker run -d \
  --name redis-dev \
  -p 6379:6379 \
  redis:7-alpine
EOF

cat > /home/<USER>/Scripts/cleanup-containers.sh << 'EOF'
#!/bin/bash
# Clean up development containers
docker stop postgres-dev redis-dev 2>/dev/null || true
docker rm postgres-dev redis-dev 2>/dev/null || true
EOF

chmod +x /home/<USER>/Scripts/*.sh

# Set up Python virtual environment
python3 -m venv /home/<USER>/.venv
echo 'source ~/.venv/bin/activate' >> /home/<USER>/.bashrc

# Install common Python packages
/home/<USER>/.venv/bin/pip install --upgrade pip
/home/<USER>/.venv/bin/pip install \
    requests \
    flask \
    fastapi \
    django \
    pytest \
    black \
    flake8 \
    jupyter

# Set ownership of all files
chown -R ${USER_NAME}:${USER_NAME} /home/<USER>

echo "Development environment setup complete!"
