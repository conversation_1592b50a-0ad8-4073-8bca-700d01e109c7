# Development Environment Workspace for Omnispace
# Base: Ubuntu 22.04 LTS with development tools and GNOME desktop
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set default environment variables
ENV VNC_PASSWORD=""
ENV DISPLAY_WIDTH=1920
ENV DISPLAY_HEIGHT=1080
ENV VNC_DEPTH=24
ENV USER_NAME=workspace
ENV USER_PASSWORD=""
ENV DISPLAY=:1

# Install system packages and desktop environment
RUN apt-get update && apt-get install -y \
    # Base system
    sudo curl wget git vim nano htop tree \
    software-properties-common apt-transport-https ca-certificates \
    gnupg lsb-release \
    # Desktop environment
    ubuntu-desktop-minimal \
    gnome-terminal gnome-system-monitor \
    nautilus gedit firefox \
    # VNC server and X11
    tigervnc-standalone-server tigervnc-common \
    xvfb x11vnc dbus-x11 \
    # Development tools
    build-essential cmake make \
    python3 python3-pip python3-venv \
    openjdk-17-jdk maven gradle \
    # Database clients
    postgresql-client redis-tools \
    # Network and system tools
    net-tools iputils-ping telnet netcat \
    # Fonts and themes
    fonts-liberation fonts-dejavu-core fonts-firacode \
    # Audio support
    pulseaudio pulseaudio-utils \
    # Additional utilities
    libreoffice-writer libreoffice-calc \
    file-roller unzip zip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# Install Node.js (LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - \
    && apt-get install -y nodejs

# Install pnpm
RUN npm install -g pnpm

# Install Docker CE
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Visual Studio Code
RUN wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg \
    && install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/ \
    && echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list \
    && apt-get update \
    && apt-get install -y code \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create workspace user with sudo privileges
RUN useradd -m -s /bin/bash -G sudo,docker ${USER_NAME} \
    && echo "${USER_NAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set up VNC directories and permissions
RUN mkdir -p /home/<USER>/.vnc \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>

# Copy startup scripts
COPY scripts/start-vnc.sh /usr/local/bin/start-vnc.sh
COPY scripts/start-desktop.sh /usr/local/bin/start-desktop.sh
COPY scripts/entrypoint.sh /usr/local/bin/entrypoint.sh
COPY scripts/setup-dev-env.sh /usr/local/bin/setup-dev-env.sh

# Make scripts executable
RUN chmod +x /usr/local/bin/*.sh

# Create VNC configuration
RUN mkdir -p /home/<USER>/.vnc \
    && echo "#!/bin/bash" > /home/<USER>/.vnc/xstartup \
    && echo "unset SESSION_MANAGER" >> /home/<USER>/.vnc/xstartup \
    && echo "unset DBUS_SESSION_BUS_ADDRESS" >> /home/<USER>/.vnc/xstartup \
    && echo "export XKL_XMODMAP_DISABLE=1" >> /home/<USER>/.vnc/xstartup \
    && echo "export XDG_CURRENT_DESKTOP=GNOME" >> /home/<USER>/.vnc/xstartup \
    && echo "export XDG_SESSION_DESKTOP=gnome" >> /home/<USER>/.vnc/xstartup \
    && echo "export XDG_SESSION_TYPE=x11" >> /home/<USER>/.vnc/xstartup \
    && echo "dbus-launch --exit-with-session gnome-session &" >> /home/<USER>/.vnc/xstartup \
    && chmod +x /home/<USER>/.vnc/xstartup \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.vnc

# Configure GNOME for VNC
RUN mkdir -p /home/<USER>/.config/gnome-initial-setup \
    && echo "yes" > /home/<USER>/.config/gnome-initial-setup/done \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config

# Set up development environment
RUN /usr/local/bin/setup-dev-env.sh

# Expose VNC port
EXPOSE 5901

# Set working directory
WORKDIR /home/<USER>

# Switch to workspace user
USER ${USER_NAME}

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["start-vnc"]
