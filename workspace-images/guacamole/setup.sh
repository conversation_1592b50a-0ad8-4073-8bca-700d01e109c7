#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Setting up Apache Guacamole for Omnispace..."

# Create necessary directories
mkdir -p init nginx/ssl

# Generate database initialization script
print_status "Generating database initialization script..."
docker run --rm guacamole/guacamole:1.5.5 /opt/guacamole/bin/initdb.sh --postgresql > init/initdb.sql

if [ -f init/initdb.sql ]; then
    print_success "Database initialization script created"
else
    print_error "Failed to create database initialization script"
    exit 1
fi

# Generate self-signed SSL certificate for nginx (if using nginx profile)
if [ ! -f nginx/ssl/guacamole.crt ]; then
    print_status "Generating self-signed SSL certificate..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/guacamole.key \
        -out nginx/ssl/guacamole.crt \
        -subj "/C=US/ST=State/L=City/O=Omnispace/CN=guacamole.local"
    
    if [ -f nginx/ssl/guacamole.crt ]; then
        print_success "SSL certificate generated"
    else
        print_error "Failed to generate SSL certificate"
        exit 1
    fi
fi

# Create nginx configuration
print_status "Creating nginx configuration..."
cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream guacamole {
        server guacamole:8080;
    }

    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name _;

        ssl_certificate /etc/nginx/ssl/guacamole.crt;
        ssl_certificate_key /etc/nginx/ssl/guacamole.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        client_max_body_size 100M;

        location / {
            proxy_pass http://guacamole;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
EOF

print_success "Nginx configuration created"

# Create environment file template
print_status "Creating environment file template..."
cat > .env.example << 'EOF'
# Guacamole Database Configuration
GUACAMOLE_DB_PASSWORD=guacamole_secure_pass_2025

# Guacamole Web Application Configuration
GUACAMOLE_PORT=8080
GUACAMOLE_HTTPS_PORT=8443
GUACAMOLE_HTTP_PORT=8080
WEBAPP_CONTEXT=ROOT

# Security Features
TOTP_ENABLED=true

# Nginx Configuration (if using nginx profile)
# Uncomment to use nginx reverse proxy
# COMPOSE_PROFILES=nginx
EOF

# Copy example to actual .env if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    print_status "Environment file created from template"
fi

# Create workspace network if it doesn't exist
print_status "Creating workspace network..."
docker network create omnispace-workspace-network 2>/dev/null || print_warning "Network already exists"

print_success "Guacamole setup completed!"

echo ""
print_status "Next steps:"
echo "1. Review and modify .env file if needed"
echo "2. Start Guacamole: docker-compose up -d"
echo "3. Access Guacamole at: http://localhost:8080"
echo "4. Default login: guacadmin / guacadmin"
echo "5. Configure workspace connections in Guacamole admin panel"
echo ""
print_status "To use with SSL (nginx):"
echo "1. Set COMPOSE_PROFILES=nginx in .env"
echo "2. Access via: https://localhost:8443"
