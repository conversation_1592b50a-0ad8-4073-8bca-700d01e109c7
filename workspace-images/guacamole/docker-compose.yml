# Apache Guacamole Setup for Omnispace
# This provides clientless remote desktop access to workspace containers

version: '3.8'

services:
  # PostgreSQL database for Guacamole
  guacamole-db:
    image: postgres:15
    container_name: omnispace-guacamole-db
    environment:
      POSTGRES_DB: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: ${GUACAMOLE_DB_PASSWORD:-guacamole_secure_pass_2025}
      PGDATA: /var/lib/postgresql/data/guacamole
    volumes:
      - guacamole-db-data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d:ro
    restart: unless-stopped
    networks:
      - guacamole-network

  # Guacamole daemon (guacd)
  guacd:
    image: guacamole/guacd:1.5.5
    container_name: omnispace-guacd
    restart: unless-stopped
    volumes:
      - guacamole-drive:/drive:rw
      - guacamole-record:/record:rw
    networks:
      - guacamole-network

  # Guacamole web application
  guacamole:
    image: guacamole/guacamole:1.5.5
    container_name: omnispace-guacamole
    depends_on:
      - guacamole-db
      - guacd
    ports:
      - "${GUACAMOLE_PORT:-8080}:8080"
    environment:
      # Database configuration
      POSTGRES_HOSTNAME: guacamole-db
      POSTGRES_DATABASE: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: ${GUACAMOLE_DB_PASSWORD:-guacamole_secure_pass_2025}
      
      # Guacd configuration
      GUACD_HOSTNAME: guacd
      GUACD_PORT: 4822
      
      # Web application configuration
      WEBAPP_CONTEXT: ${WEBAPP_CONTEXT:-ROOT}
      
      # Security features
      TOTP_ENABLED: ${TOTP_ENABLED:-true}
      
      # Recording and drive mapping
      RECORDING_SEARCH_PATH: /record
      DRIVE_PATH: /drive
      
      # Additional security
      HEADER_ENABLED: true
      
    volumes:
      - guacamole-drive:/drive:rw
      - guacamole-record:/record:rw
    restart: unless-stopped
    networks:
      - guacamole-network
      - workspace-network

  # Nginx reverse proxy (optional, for SSL termination)
  nginx:
    image: nginx:alpine
    container_name: omnispace-guacamole-nginx
    ports:
      - "${GUACAMOLE_HTTPS_PORT:-8443}:443"
      - "${GUACAMOLE_HTTP_PORT:-8080}:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - guacamole
    restart: unless-stopped
    networks:
      - guacamole-network
    profiles:
      - nginx

volumes:
  guacamole-db-data:
    name: omnispace-guacamole-db-data
  guacamole-drive:
    name: omnispace-guacamole-drive
  guacamole-record:
    name: omnispace-guacamole-record

networks:
  guacamole-network:
    name: omnispace-guacamole-network
    driver: bridge
  workspace-network:
    name: omnispace-workspace-network
    external: true
