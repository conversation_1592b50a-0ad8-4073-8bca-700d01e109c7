#!/bin/bash
set -e

echo "Starting GNOME desktop session..."

# Set up environment
export XKL_XMODMAP_DISABLE=1
export XDG_CURRENT_DESKTOP=GNOME
export XDG_SESSION_DESKTOP=gnome
export XDG_SESSION_TYPE=x11
export GNOME_SHELL_SESSION_MODE=ubuntu

# Start D-Bus if not running
if [ -z "$DBUS_SESSION_BUS_ADDRESS" ]; then
    eval $(dbus-launch --sh-syntax)
    export DBUS_SESSION_BUS_ADDRESS
fi

# Start GNOME session
exec gnome-session
