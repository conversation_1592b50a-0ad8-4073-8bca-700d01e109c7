#!/bin/bash
set -e

echo "Starting Ubuntu Desktop VNC server..."
echo "Display: $DISPLAY"
echo "Resolution: ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}"
echo "Depth: ${VNC_DEPTH}"

# Kill any existing VNC servers
vncserver -kill $DISPLAY 2>/dev/null || true

# Wait a moment for cleanup
sleep 2

# Start VNC server
vncserver $DISPLAY \
    -geometry ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT} \
    -depth ${VNC_DEPTH} \
    -localhost no \
    -SecurityTypes VncAuth \
    -rfbauth ~/.vnc/passwd \
    -log ~/.vnc/logs/vnc.log

echo "VNC server started on display $DISPLAY"
echo "Connect via VNC client to: <container-ip>:5901"
echo "VNC password: $VNC_PASSWORD"

# Keep the container running and monitor VNC server
while true; do
    if ! pgrep -f "Xvnc.*$DISPLAY" > /dev/null; then
        echo "VNC server stopped, restarting..."
        vncserver $DISPLAY \
            -geometry ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT} \
            -depth ${VNC_DEPTH} \
            -localhost no \
            -SecurityTypes VncAuth \
            -rfbauth ~/.vnc/passwd \
            -log ~/.vnc/logs/vnc.log
    fi
    sleep 30
done
