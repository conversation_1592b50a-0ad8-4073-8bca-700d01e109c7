# Ubuntu Desktop Workspace for Omnispace
# Base: Ubuntu 22.04 LTS with GNOME desktop and TigerVNC
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set default environment variables
ENV VNC_PASSWORD=""
ENV DISPLAY_WIDTH=1920
ENV DISPLAY_HEIGHT=1080
ENV VNC_DEPTH=24
ENV USER_NAME=workspace
ENV USER_PASSWORD=""
ENV DISPLAY=:1

# Install system packages and desktop environment
RUN apt-get update && apt-get install -y \
    # Base system
    sudo curl wget git vim nano htop tree \
    # Desktop environment
    ubuntu-desktop-minimal \
    gnome-terminal gnome-system-monitor \
    nautilus gedit firefox \
    # VNC server and X11
    tigervnc-standalone-server tigervnc-common \
    xvfb x11vnc dbus-x11 \
    # Fonts and themes
    fonts-liberation fonts-dejavu-core \
    # Audio support
    pulseaudio pulseaudio-utils \
    # Additional utilities
    libreoffice-writer libreoffice-calc \
    file-roller unzip zip \
    # Network tools
    net-tools iputils-ping \
    # Cleanup
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# Create workspace user with sudo privileges
RUN useradd -m -s /bin/bash -G sudo ${USER_NAME} \
    && echo "${USER_NAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set up VNC directories and permissions
RUN mkdir -p /home/<USER>/.vnc \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>

# Copy startup scripts
COPY scripts/start-vnc.sh /usr/local/bin/start-vnc.sh
COPY scripts/start-desktop.sh /usr/local/bin/start-desktop.sh
COPY scripts/entrypoint.sh /usr/local/bin/entrypoint.sh

# Make scripts executable
RUN chmod +x /usr/local/bin/*.sh

# Create VNC configuration
RUN mkdir -p /home/<USER>/.vnc \
    && echo "#!/bin/bash" > /home/<USER>/.vnc/xstartup \
    && echo "unset SESSION_MANAGER" >> /home/<USER>/.vnc/xstartup \
    && echo "unset DBUS_SESSION_BUS_ADDRESS" >> /home/<USER>/.vnc/xstartup \
    && echo "export XKL_XMODMAP_DISABLE=1" >> /home/<USER>/.vnc/xstartup \
    && echo "export XDG_CURRENT_DESKTOP=GNOME" >> /home/<USER>/.vnc/xstartup \
    && echo "export XDG_SESSION_DESKTOP=gnome" >> /home/<USER>/.vnc/xstartup \
    && echo "export XDG_SESSION_TYPE=x11" >> /home/<USER>/.vnc/xstartup \
    && echo "dbus-launch --exit-with-session gnome-session &" >> /home/<USER>/.vnc/xstartup \
    && chmod +x /home/<USER>/.vnc/xstartup \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.vnc

# Configure GNOME for VNC
RUN mkdir -p /home/<USER>/.config/gnome-initial-setup \
    && echo "yes" > /home/<USER>/.config/gnome-initial-setup/done \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config

# Set up desktop shortcuts and configuration
RUN mkdir -p /home/<USER>/Desktop \
    && echo "[Desktop Entry]" > /home/<USER>/Desktop/Terminal.desktop \
    && echo "Type=Application" >> /home/<USER>/Desktop/Terminal.desktop \
    && echo "Name=Terminal" >> /home/<USER>/Desktop/Terminal.desktop \
    && echo "Exec=gnome-terminal" >> /home/<USER>/Desktop/Terminal.desktop \
    && echo "Icon=utilities-terminal" >> /home/<USER>/Desktop/Terminal.desktop \
    && chmod +x /home/<USER>/Desktop/Terminal.desktop \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/Desktop

# Expose VNC port
EXPOSE 5901

# Set working directory
WORKDIR /home/<USER>

# Switch to workspace user
USER ${USER_NAME}

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["start-vnc"]
