# Minimal Desktop Workspace for Omnispace
# Base: Alpine Linux with XFCE desktop and x11vnc
FROM alpine:3.19

# Set default environment variables
ENV VNC_PASSWORD=""
ENV DISPLAY_WIDTH=1920
ENV DISPLAY_HEIGHT=1080
ENV VNC_DEPTH=24
ENV USER_NAME=workspace
ENV USER_PASSWORD=""
ENV DISPLAY=:1

# Install system packages and desktop environment
RUN apk update && apk add --no-cache \
    # Base system
    sudo bash curl wget git vim nano htop tree \
    # XFCE desktop environment
    xfce4 xfce4-terminal xfce4-screensaver \
    thunar mousepad firefox \
    # VNC server and X11
    x11vnc xvfb \
    # Fonts
    font-dejavu font-liberation \
    # Audio support (basic)
    alsa-utils \
    # Additional utilities
    file-roller unzip zip \
    # Network tools
    net-tools iputils \
    # Process management
    procps \
    # OpenSSL for password generation
    openssl \
    && rm -rf /var/cache/apk/*

# Create workspace user with sudo privileges
RUN adduser -D -s /bin/bash ${USER_NAME} \
    && echo "${USER_NAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
    && addgroup ${USER_NAME} wheel

# Set up VNC directories and permissions
RUN mkdir -p /home/<USER>/.vnc \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>

# Copy startup scripts
COPY scripts/start-vnc.sh /usr/local/bin/start-vnc.sh
COPY scripts/start-desktop.sh /usr/local/bin/start-desktop.sh
COPY scripts/entrypoint.sh /usr/local/bin/entrypoint.sh

# Make scripts executable
RUN chmod +x /usr/local/bin/*.sh

# Create XFCE configuration
RUN mkdir -p /home/<USER>/.config/xfce4/xfconf/xfce-perchannel-xml \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config

# Set up desktop shortcuts and configuration
RUN mkdir -p /home/<USER>/Desktop \
    && echo "[Desktop Entry]" > /home/<USER>/Desktop/Terminal.desktop \
    && echo "Type=Application" >> /home/<USER>/Desktop/Terminal.desktop \
    && echo "Name=Terminal" >> /home/<USER>/Desktop/Terminal.desktop \
    && echo "Exec=xfce4-terminal" >> /home/<USER>/Desktop/Terminal.desktop \
    && echo "Icon=utilities-terminal" >> /home/<USER>/Desktop/Terminal.desktop \
    && chmod +x /home/<USER>/Desktop/Terminal.desktop \
    && echo "[Desktop Entry]" > /home/<USER>/Desktop/FileManager.desktop \
    && echo "Type=Application" >> /home/<USER>/Desktop/FileManager.desktop \
    && echo "Name=File Manager" >> /home/<USER>/Desktop/FileManager.desktop \
    && echo "Exec=thunar" >> /home/<USER>/Desktop/FileManager.desktop \
    && echo "Icon=file-manager" >> /home/<USER>/Desktop/FileManager.desktop \
    && chmod +x /home/<USER>/Desktop/FileManager.desktop \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/Desktop

# Configure XFCE for minimal resource usage
RUN mkdir -p /home/<USER>/.config/xfce4/xfconf/xfce-perchannel-xml \
    && cat > /home/<USER>/.config/xfce4/xfconf/xfce-perchannel-xml/xfce4-power-manager.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<channel name="xfce4-power-manager" version="1.0">
  <property name="xfce4-power-manager" type="empty">
    <property name="power-button-action" type="uint" value="3"/>
    <property name="sleep-button-action" type="uint" value="1"/>
    <property name="hibernate-button-action" type="uint" value="2"/>
    <property name="lid-action-on-battery" type="uint" value="1"/>
    <property name="lid-action-on-ac" type="uint" value="1"/>
    <property name="brightness-on-battery" type="uint" value="9"/>
    <property name="brightness-on-ac" type="uint" value="9"/>
  </property>
</channel>
EOF

# Disable screensaver
RUN cat > /home/<USER>/.config/xfce4/xfconf/xfce-perchannel-xml/xfce4-screensaver.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<channel name="xfce4-screensaver" version="1.0">
  <property name="saver" type="empty">
    <property name="enabled" type="bool" value="false"/>
    <property name="idle-activation" type="empty">
      <property name="enabled" type="bool" value="false"/>
    </property>
  </property>
</channel>
EOF

# Set ownership of all config files
RUN chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config

# Expose VNC port
EXPOSE 5901

# Set working directory
WORKDIR /home/<USER>

# Switch to workspace user
USER ${USER_NAME}

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["start-vnc"]
