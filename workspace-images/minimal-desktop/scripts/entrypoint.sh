#!/bin/bash
set -e

# Generate random passwords if not provided
if [ -z "$VNC_PASSWORD" ]; then
    VNC_PASSWORD=$(openssl rand -base64 12)
    echo "Generated VNC password: $VNC_PASSWORD"
fi

if [ -z "$USER_PASSWORD" ]; then
    USER_PASSWORD=$(openssl rand -base64 12)
    echo "Generated user password: $USER_PASSWORD"
fi

# Set user password
echo "$USER_NAME:$USER_PASSWORD" | sudo chpasswd

# Create VNC password file for x11vnc
mkdir -p ~/.vnc
echo "$VNC_PASSWORD" | x11vnc -storepasswd - ~/.vnc/passwd

# Create log directory
mkdir -p ~/.vnc/logs

# Set up XFCE environment
export XDG_CURRENT_DESKTOP=XFCE
export XDG_SESSION_DESKTOP=xfce
export XDG_SESSION_TYPE=x11

# Execute the command
exec "$@"
