#!/bin/bash
set -e

echo "Starting Minimal Desktop VNC server..."
echo "Display: $DISPLAY"
echo "Resolution: ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}"
echo "Depth: ${VNC_DEPTH}"

# Kill any existing processes
pkill -f x11vnc || true
pkill -f Xvfb || true

# Wait a moment for cleanup
sleep 2

# Start Xvfb (virtual framebuffer)
Xvfb $DISPLAY -screen 0 ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}x${VNC_DEPTH} -ac -nolisten tcp &
XVFB_PID=$!

# Wait for Xvfb to start
sleep 3

# Set DISPLAY environment variable
export DISPLAY=$DISPLAY

# Start XFCE desktop in background
startxfce4 &
XFCE_PID=$!

# Wait for desktop to start
sleep 5

# Start x11vnc VNC server
x11vnc -display $DISPLAY \
    -forever \
    -usepw \
    -rfbauth ~/.vnc/passwd \
    -rfbport 5901 \
    -shared \
    -logfile ~/.vnc/logs/vnc.log &
VNC_PID=$!

echo "VNC server started on display $DISPLAY"
echo "Connect via VNC client to: <container-ip>:5901"
echo "VNC password: $VNC_PASSWORD"
echo ""
echo "Minimal desktop features:"
echo "- XFCE4 lightweight desktop"
echo "- Firefox web browser"
echo "- File manager (Thunar)"
echo "- Text editor (Mousepad)"
echo "- Terminal (xfce4-terminal)"

# Function to cleanup on exit
cleanup() {
    echo "Shutting down services..."
    kill $VNC_PID 2>/dev/null || true
    kill $XFCE_PID 2>/dev/null || true
    kill $XVFB_PID 2>/dev/null || true
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Keep the container running and monitor processes
while true; do
    if ! kill -0 $XVFB_PID 2>/dev/null; then
        echo "Xvfb stopped, restarting..."
        Xvfb $DISPLAY -screen 0 ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}x${VNC_DEPTH} -ac -nolisten tcp &
        XVFB_PID=$!
        sleep 3
    fi
    
    if ! kill -0 $VNC_PID 2>/dev/null; then
        echo "VNC server stopped, restarting..."
        x11vnc -display $DISPLAY \
            -forever \
            -usepw \
            -rfbauth ~/.vnc/passwd \
            -rfbport 5901 \
            -shared \
            -logfile ~/.vnc/logs/vnc.log &
        VNC_PID=$!
    fi
    
    sleep 30
done
