# Omnispace Workspace Images - Deployment Guide

This guide covers how to deploy and integrate the Omnispace workspace images with Apache Guacamole for production use.

## Quick Start

### 1. Build Workspace Images
```bash
# Make scripts executable
chmod +x *.sh
chmod +x */scripts/*.sh

# Build all workspace images
./build-all.sh
```

### 2. Set Up Apache Guacamole
```bash
cd guacamole
chmod +x setup.sh
./setup.sh

# Start Guacamole
docker-compose up -d

# Access Guacamole at http://localhost:8080
# Default login: guacadmin / guacadmin
```

### 3. Test Workspace Images
```bash
# Test individual workspaces
./test-workspace.sh ubuntu start
./test-workspace.sh development start
./test-workspace.sh minimal start

# Check status
./test-workspace.sh all status
```

## Production Deployment

### Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Omnispace     │    │  Apache          │    │  Workspace          │
│   Frontend      │───▶│  Guacamole       │───▶│  Containers         │
│   (Next.js)     │    │  (Web Gateway)   │    │  (Ubuntu/Dev/Min)   │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

### 1. Network Configuration

Create dedicated networks for security:

```bash
# Create networks
docker network create omnispace-frontend-network
docker network create omnispace-guacamole-network
docker network create omnispace-workspace-network

# Guacamole connects to both guacamole and workspace networks
# Workspaces only connect to workspace network
# Frontend only connects to frontend and guacamole networks
```

### 2. Environment Variables

Create production environment files:

**guacamole/.env**:
```env
GUACAMOLE_DB_PASSWORD=your_secure_database_password_here
GUACAMOLE_PORT=8080
WEBAPP_CONTEXT=ROOT
TOTP_ENABLED=true
COMPOSE_PROFILES=nginx
```

### 3. SSL Configuration

For production, use proper SSL certificates:

```bash
# Replace self-signed certificates with real ones
cp your-domain.crt guacamole/nginx/ssl/guacamole.crt
cp your-domain.key guacamole/nginx/ssl/guacamole.key
```

### 4. Database Backup

Set up regular backups for Guacamole database:

```bash
# Create backup script
cat > backup-guacamole-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups/guacamole"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

docker exec omnispace-guacamole-db pg_dump -U guacamole_user guacamole_db > \
  $BACKUP_DIR/guacamole_backup_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "guacamole_backup_*.sql" -mtime +7 -delete
EOF

chmod +x backup-guacamole-db.sh

# Add to crontab for daily backups
echo "0 2 * * * /path/to/backup-guacamole-db.sh" | crontab -
```

## Integration with Omnispace

### 1. Container Management API

The Omnispace backend should integrate with these workspace images:

```typescript
// Example integration in Omnispace Docker service
export interface WorkspaceTemplate {
  id: string;
  name: string;
  image: string;
  defaultResources: {
    cpu: number;
    memory: number;
    storage: number;
  };
  vncPort: number;
  features: string[];
}

export const WORKSPACE_TEMPLATES: WorkspaceTemplate[] = [
  {
    id: 'ubuntu-desktop',
    name: 'Ubuntu Desktop',
    image: 'omnispace/ubuntu-desktop:latest',
    defaultResources: { cpu: 2, memory: 2048, storage: 20 },
    vncPort: 5901,
    features: ['GNOME Desktop', 'Firefox', 'LibreOffice']
  },
  {
    id: 'development-env',
    name: 'Development Environment',
    image: 'omnispace/development-env:latest',
    defaultResources: { cpu: 4, memory: 4096, storage: 40 },
    vncPort: 5901,
    features: ['VS Code', 'Docker', 'Node.js', 'Python', 'Git']
  },
  {
    id: 'minimal-desktop',
    name: 'Minimal Desktop',
    image: 'omnispace/minimal-desktop:latest',
    defaultResources: { cpu: 1, memory: 1024, storage: 10 },
    vncPort: 5901,
    features: ['XFCE Desktop', 'Firefox', 'File Manager']
  }
];
```

### 2. Guacamole Connection Management

Automatically register workspace containers with Guacamole:

```typescript
// Example Guacamole API integration
export class GuacamoleManager {
  async createConnection(workspace: WorkspaceInstance) {
    const connection = {
      name: workspace.name,
      protocol: 'vnc',
      parameters: {
        hostname: workspace.containerIP,
        port: '5901',
        password: workspace.vncPassword,
        'color-depth': '24',
        'cursor': 'remote',
        'swap-red-blue': 'false',
        'dest-port': '5901'
      }
    };
    
    return await this.guacamoleAPI.createConnection(connection);
  }
}
```

### 3. Dynamic Port Management

Assign unique VNC ports for each workspace:

```typescript
export class PortManager {
  private usedPorts = new Set<number>();
  private basePort = 5901;
  
  allocatePort(): number {
    let port = this.basePort;
    while (this.usedPorts.has(port)) {
      port++;
    }
    this.usedPorts.add(port);
    return port;
  }
  
  releasePort(port: number): void {
    this.usedPorts.delete(port);
  }
}
```

## Monitoring and Logging

### 1. Container Health Checks

Add health checks to workspace containers:

```dockerfile
# Add to Dockerfiles
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pgrep -f "Xvnc.*:1" > /dev/null || exit 1
```

### 2. Log Aggregation

Set up centralized logging:

```yaml
# Add to docker-compose.yml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 3. Metrics Collection

Monitor workspace resource usage:

```bash
# Script to collect workspace metrics
#!/bin/bash
for container in $(docker ps --filter "label=omnispace.workspace=true" --format "{{.Names}}"); do
  echo "Container: $container"
  docker stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" $container
done
```

## Security Considerations

### 1. Network Isolation
- Workspaces should only access necessary networks
- Use Docker network policies to restrict inter-container communication
- Implement firewall rules for VNC port access

### 2. Resource Limits
- Set CPU and memory limits for all workspace containers
- Implement disk quotas to prevent storage abuse
- Monitor resource usage and implement alerts

### 3. User Access Control
- Integrate Guacamole with your authentication system (LDAP, OAuth, etc.)
- Implement role-based access control
- Enable session recording for audit purposes

### 4. Regular Updates
- Keep base images updated with security patches
- Implement automated vulnerability scanning
- Regular backup and disaster recovery testing

## Troubleshooting

### Common Issues

1. **VNC Connection Failed**
   - Check if container is running: `docker ps`
   - Verify VNC server is running: `docker exec <container> pgrep Xvnc`
   - Check logs: `docker logs <container>`

2. **Guacamole Connection Issues**
   - Verify Guacamole can reach workspace container
   - Check network connectivity between containers
   - Validate VNC credentials in Guacamole connection settings

3. **Performance Issues**
   - Monitor resource usage: `docker stats`
   - Adjust CPU/memory limits
   - Consider using hardware acceleration for graphics

### Log Locations

- Workspace VNC logs: `/home/<USER>/.vnc/logs/vnc.log`
- Guacamole logs: `docker logs omnispace-guacamole`
- Database logs: `docker logs omnispace-guacamole-db`
