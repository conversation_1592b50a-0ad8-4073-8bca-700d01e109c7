# Python Development Environment Workspace for Omnispace
# Specialized for Python web development with Django, Flask, FastAPI, Streamlit, and Gradio
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set default environment variables
ENV VNC_PASSWORD=""
ENV DISPLAY_WIDTH=1920
ENV DISPLAY_HEIGHT=1080
ENV VNC_DEPTH=24
ENV USER_NAME=workspace
ENV USER_PASSWORD=""
ENV DISPLAY=:1

# Python environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system packages and desktop environment
RUN apt-get update && apt-get install -y \
    # Base system
    sudo curl wget git vim nano htop tree \
    software-properties-common apt-transport-https ca-certificates \
    gnupg lsb-release unzip zip \
    # Desktop environment
    ubuntu-desktop-minimal \
    gnome-terminal gnome-system-monitor \
    nautilus gedit firefox \
    # VNC server and X11
    tigervnc-standalone-server tigervnc-common \
    xvfb x11vnc dbus-x11 \
    # Development tools
    build-essential cmake make gcc g++ \
    pkg-config libffi-dev libssl-dev \
    # Python development dependencies
    python3 python3-pip python3-venv python3-dev \
    python3-setuptools python3-wheel \
    # Database development libraries
    libpq-dev libmysqlclient-dev libsqlite3-dev \
    postgresql-client mysql-client sqlite3 \
    redis-tools \
    # Network and system tools
    net-tools iputils-ping telnet netcat \
    # Fonts and themes
    fonts-liberation fonts-dejavu-core fonts-firacode \
    # Audio support
    pulseaudio alsa-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js (for some Python web frameworks that use Node.js tools)
RUN curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - \
    && apt-get install -y nodejs

# Install pnpm
RUN npm install -g pnpm

# Install Docker CE (for containerized development)
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python package managers
RUN pip3 install --upgrade pip setuptools wheel \
    && pip3 install pipenv poetry virtualenv virtualenvwrapper

# Install Conda (Miniconda)
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh \
    && bash /tmp/miniconda.sh -b -p /opt/miniconda \
    && rm /tmp/miniconda.sh \
    && /opt/miniconda/bin/conda init bash \
    && /opt/miniconda/bin/conda config --set auto_activate_base false

# Install VS Code
RUN wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg \
    && install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/ \
    && echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list \
    && apt-get update \
    && apt-get install -y code \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create workspace user
RUN useradd -m -s /bin/bash -G sudo,docker ${USER_NAME} \
    && echo "${USER_NAME}:${USER_NAME}" | chpasswd \
    && echo "${USER_NAME} ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Copy configuration scripts
COPY scripts/ /usr/local/bin/
RUN chmod +x /usr/local/bin/*.sh

# Set up VNC configuration
RUN mkdir -p /home/<USER>/.vnc \
    && mkdir -p /home/<USER>/.vnc/logs \
    && echo "#!/bin/bash\nexec gnome-session" > /home/<USER>/.vnc/xstartup \
    && chmod +x /home/<USER>/.vnc/xstartup \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.vnc

# Configure GNOME for VNC
RUN mkdir -p /home/<USER>/.config/gnome-initial-setup \
    && echo "yes" > /home/<USER>/.config/gnome-initial-setup/done \
    && chown -R ${USER_NAME}:${USER_NAME} /home/<USER>/.config

# Set up Python development environment
RUN /usr/local/bin/setup-python-env.sh

# Expose VNC port and common Python web framework ports
EXPOSE 5901 8000 8080 5000 8501 7860

# Set working directory
WORKDIR /home/<USER>

# Switch to workspace user
USER ${USER_NAME}

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["start-vnc"]
