#!/bin/bash
# Entrypoint script for Python Development Workspace

set -e

USER_NAME=${USER_NAME:-workspace}
USER_HOME="/home/<USER>"
VNC_PASSWORD=${VNC_PASSWORD:-}
DISPLAY_WIDTH=${DISPLAY_WIDTH:-1920}
DISPLAY_HEIGHT=${DISPLAY_HEIGHT:-1080}
VNC_DEPTH=${VNC_DEPTH:-24}

echo "Starting Python Development Workspace..."
echo "User: ${USER_NAME}"
echo "Display: ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}@${VNC_DEPTH}"

# Function to generate random password if not provided
generate_password() {
    if [ -z "$VNC_PASSWORD" ]; then
        VNC_PASSWORD=$(openssl rand -base64 12)
        echo "Generated VNC password: $VNC_PASSWORD"
    fi
}

# Function to set up VNC password
setup_vnc_password() {
    if [ -n "$VNC_PASSWORD" ]; then
        echo "$VNC_PASSWORD" | vncpasswd -f > ~/.vnc/passwd
        chmod 600 ~/.vnc/passwd
        echo "VNC password configured"
    else
        echo "No VNC password set - using no authentication"
        touch ~/.vnc/passwd
        chmod 600 ~/.vnc/passwd
    fi
}

# Function to start VNC server
start_vnc_server() {
    echo "Starting VNC server..."
    
    # Kill any existing VNC servers
    vncserver -kill $DISPLAY 2>/dev/null || true
    
    # Start VNC server
    vncserver $DISPLAY \
        -geometry ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT} \
        -depth ${VNC_DEPTH} \
        -localhost no \
        -SecurityTypes VncAuth \
        -rfbauth ~/.vnc/passwd \
        -log ~/.vnc/logs/vnc.log
    
    echo "VNC server started on display $DISPLAY"
}

# Function to start Python development services
start_python_services() {
    echo "Starting Python development services..."
    
    # Start Jupyter Lab in the background if requested
    if [ "$START_JUPYTER" = "true" ]; then
        echo "Starting Jupyter Lab..."
        cd $USER_HOME
        nohup jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password='' > ~/.vnc/logs/jupyter.log 2>&1 &
        echo "Jupyter Lab started on port 8888"
    fi
    
    # Initialize conda for the user
    if [ -f "/opt/miniconda/bin/conda" ]; then
        echo "Initializing conda for user..."
        /opt/miniconda/bin/conda init bash
        source ~/.bashrc
    fi
}

# Function to set up development environment
setup_dev_environment() {
    echo "Setting up development environment..."
    
    # Create common directories
    mkdir -p $USER_HOME/projects
    mkdir -p $USER_HOME/workspace
    mkdir -p $USER_HOME/.local/bin
    
    # Set up Git configuration if not exists
    if [ ! -f $USER_HOME/.gitconfig ]; then
        git config --global user.name "Workspace User"
        git config --global user.email "<EMAIL>"
        git config --global init.defaultBranch main
        git config --global pull.rebase false
    fi
    
    # Create a welcome script
    cat > $USER_HOME/welcome.py << 'EOF'
#!/usr/bin/env python3
"""
Welcome to your Python Development Workspace!

This workspace includes:
- Django, Flask, FastAPI, Streamlit, and Gradio frameworks
- VS Code with Python extensions
- Jupyter Lab
- Multiple Python package managers (pip, conda, poetry)
- Database clients and development tools

Quick start templates are available in ~/templates/
"""

import sys
import subprocess

def main():
    print(__doc__)
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    # Check installed frameworks
    frameworks = {
        'Django': 'django',
        'Flask': 'flask',
        'FastAPI': 'fastapi',
        'Streamlit': 'streamlit',
        'Gradio': 'gradio'
    }
    
    print("\nInstalled Python frameworks:")
    for name, module in frameworks.items():
        try:
            __import__(module)
            print(f"  ✓ {name}")
        except ImportError:
            print(f"  ✗ {name} (not installed)")
    
    print("\nAvailable project templates:")
    import os
    templates_dir = os.path.expanduser("~/templates")
    if os.path.exists(templates_dir):
        for template in os.listdir(templates_dir):
            if os.path.isdir(os.path.join(templates_dir, template)):
                print(f"  - {template}")
    
    print("\nTo create a new project:")
    print("  cd ~/templates/<framework>-template")
    print("  ./create_project.sh <project_name>")
    
    print("\nHappy coding! 🐍")

if __name__ == "__main__":
    main()
EOF
    chmod +x $USER_HOME/welcome.py
    
    # Add welcome message to bashrc
    if ! grep -q "welcome.py" $USER_HOME/.bashrc; then
        echo "" >> $USER_HOME/.bashrc
        echo "# Welcome message" >> $USER_HOME/.bashrc
        echo "python3 ~/welcome.py" >> $USER_HOME/.bashrc
    fi
}

# Function to monitor services
monitor_services() {
    echo "Monitoring services..."
    
    while true; do
        # Check if VNC server is running
        if ! pgrep -f "Xvnc.*$DISPLAY" > /dev/null; then
            echo "VNC server stopped, restarting..."
            start_vnc_server
        fi
        
        # Check if Jupyter is requested and running
        if [ "$START_JUPYTER" = "true" ] && ! pgrep -f "jupyter-lab" > /dev/null; then
            echo "Jupyter Lab stopped, restarting..."
            cd $USER_HOME
            nohup jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password='' > ~/.vnc/logs/jupyter.log 2>&1 &
        fi
        
        sleep 30
    done
}

# Main execution
case "${1:-start-vnc}" in
    "start-vnc")
        generate_password
        setup_vnc_password
        setup_dev_environment
        start_python_services
        start_vnc_server
        monitor_services
        ;;
    "bash")
        exec /bin/bash
        ;;
    *)
        exec "$@"
        ;;
esac
