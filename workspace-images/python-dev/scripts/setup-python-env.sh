#!/bin/bash
# Setup Python Development Environment

set -e

USER_NAME=${USER_NAME:-workspace}
USER_HOME="/home/<USER>"

echo "Setting up Python development environment for user: ${USER_NAME}"

# Install essential Python packages globally
pip3 install --upgrade \
    # Core development tools
    ipython jupyter jupyterlab \
    black isort flake8 mypy pylint \
    pytest pytest-cov pytest-mock \
    # Web frameworks
    django djangorestframework \
    flask flask-restful flask-cors \
    fastapi uvicorn gunicorn \
    streamlit gradio \
    # Database libraries
    psycopg2-binary pymongo sqlalchemy \
    redis celery \
    # Utility libraries
    requests httpx aiohttp \
    python-dotenv pydantic \
    click typer rich \
    # Data science libraries
    numpy pandas matplotlib seaborn \
    scikit-learn plotly dash \
    # Development utilities
    pre-commit cookiecutter \
    python-decouple

# Create Python project templates directory
mkdir -p ${USER_HOME}/templates

# Create Django template
mkdir -p ${USER_HOME}/templates/django-template
cat > ${USER_HOME}/templates/django-template/requirements.txt << 'EOF'
Django>=4.2.0
djangorestframework>=3.14.0
django-cors-headers>=4.0.0
python-decouple>=3.8
psycopg2-binary>=2.9.0
celery>=5.3.0
redis>=4.5.0
gunicorn>=21.0.0
whitenoise>=6.5.0
EOF

cat > ${USER_HOME}/templates/django-template/create_project.sh << 'EOF'
#!/bin/bash
PROJECT_NAME=${1:-myproject}
echo "Creating Django project: $PROJECT_NAME"
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
django-admin startproject $PROJECT_NAME .
python manage.py migrate
echo "Django project '$PROJECT_NAME' created successfully!"
echo "To run: source venv/bin/activate && python manage.py runserver 0.0.0.0:8000"
EOF

# Create Flask template
mkdir -p ${USER_HOME}/templates/flask-template
cat > ${USER_HOME}/templates/flask-template/requirements.txt << 'EOF'
Flask>=2.3.0
Flask-RESTful>=0.3.10
Flask-CORS>=4.0.0
Flask-SQLAlchemy>=3.0.0
python-decouple>=3.8
psycopg2-binary>=2.9.0
gunicorn>=21.0.0
EOF

cat > ${USER_HOME}/templates/flask-template/app.py << 'EOF'
from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/')
def hello():
    return jsonify({"message": "Hello from Flask!"})

@app.route('/api/health')
def health():
    return jsonify({"status": "healthy"})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
EOF

cat > ${USER_HOME}/templates/flask-template/create_project.sh << 'EOF'
#!/bin/bash
PROJECT_NAME=${1:-myproject}
echo "Creating Flask project: $PROJECT_NAME"
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
echo "Flask project '$PROJECT_NAME' created successfully!"
echo "To run: source venv/bin/activate && python app.py"
EOF

# Create FastAPI template
mkdir -p ${USER_HOME}/templates/fastapi-template
cat > ${USER_HOME}/templates/fastapi-template/requirements.txt << 'EOF'
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.4.0
python-decouple>=3.8
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
alembic>=1.12.0
python-multipart>=0.0.6
EOF

cat > ${USER_HOME}/templates/fastapi-template/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="My FastAPI App", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hello from FastAPI!"}

@app.get("/api/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

cat > ${USER_HOME}/templates/fastapi-template/create_project.sh << 'EOF'
#!/bin/bash
PROJECT_NAME=${1:-myproject}
echo "Creating FastAPI project: $PROJECT_NAME"
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
echo "FastAPI project '$PROJECT_NAME' created successfully!"
echo "To run: source venv/bin/activate && uvicorn main:app --host 0.0.0.0 --port 8000 --reload"
EOF

# Create Streamlit template
mkdir -p ${USER_HOME}/templates/streamlit-template
cat > ${USER_HOME}/templates/streamlit-template/requirements.txt << 'EOF'
streamlit>=1.28.0
pandas>=2.0.0
numpy>=1.24.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
requests>=2.31.0
EOF

cat > ${USER_HOME}/templates/streamlit-template/app.py << 'EOF'
import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

st.title("My Streamlit App")

st.write("Welcome to your Streamlit application!")

# Sample data visualization
chart_data = pd.DataFrame(
    np.random.randn(20, 3),
    columns=['a', 'b', 'c']
)

st.line_chart(chart_data)

# Interactive widgets
name = st.text_input("Enter your name:")
if name:
    st.write(f"Hello, {name}!")

# Sidebar
st.sidebar.title("Navigation")
page = st.sidebar.selectbox("Choose a page", ["Home", "Data", "About"])

if page == "Data":
    st.write("This is the data page")
elif page == "About":
    st.write("This is the about page")
EOF

cat > ${USER_HOME}/templates/streamlit-template/create_project.sh << 'EOF'
#!/bin/bash
PROJECT_NAME=${1:-myproject}
echo "Creating Streamlit project: $PROJECT_NAME"
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
echo "Streamlit project '$PROJECT_NAME' created successfully!"
echo "To run: source venv/bin/activate && streamlit run app.py --server.address 0.0.0.0 --server.port 8501"
EOF

# Create Gradio template
mkdir -p ${USER_HOME}/templates/gradio-template
cat > ${USER_HOME}/templates/gradio-template/requirements.txt << 'EOF'
gradio>=4.0.0
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
pillow>=10.0.0
requests>=2.31.0
EOF

cat > ${USER_HOME}/templates/gradio-template/app.py << 'EOF'
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt

def greet(name, intensity):
    return f"Hello, {name}!" * int(intensity)

def plot_function(equation, x_min, x_max):
    x = np.linspace(x_min, x_max, 100)
    try:
        y = eval(equation.replace('x', 'x'))
        plt.figure(figsize=(8, 6))
        plt.plot(x, y)
        plt.grid(True)
        plt.title(f"Plot of {equation}")
        plt.xlabel("x")
        plt.ylabel("y")
        return plt
    except:
        return "Invalid equation"

# Create interface
with gr.Blocks() as demo:
    gr.Markdown("# My Gradio App")
    
    with gr.Tab("Greeting"):
        name_input = gr.Textbox(label="Name")
        intensity_input = gr.Slider(1, 10, value=1, label="Intensity")
        greet_output = gr.Textbox(label="Greeting")
        greet_btn = gr.Button("Greet")
        greet_btn.click(greet, inputs=[name_input, intensity_input], outputs=greet_output)
    
    with gr.Tab("Plot"):
        equation_input = gr.Textbox(label="Equation (use 'x' as variable)", value="x**2")
        x_min_input = gr.Number(label="X Min", value=-10)
        x_max_input = gr.Number(label="X Max", value=10)
        plot_output = gr.Plot(label="Plot")
        plot_btn = gr.Button("Plot")
        plot_btn.click(plot_function, inputs=[equation_input, x_min_input, x_max_input], outputs=plot_output)

if __name__ == "__main__":
    demo.launch(server_name="0.0.0.0", server_port=7860)
EOF

cat > ${USER_HOME}/templates/gradio-template/create_project.sh << 'EOF'
#!/bin/bash
PROJECT_NAME=${1:-myproject}
echo "Creating Gradio project: $PROJECT_NAME"
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
echo "Gradio project '$PROJECT_NAME' created successfully!"
echo "To run: source venv/bin/activate && python app.py"
EOF

# Make all create_project.sh scripts executable
find ${USER_HOME}/templates -name "create_project.sh" -exec chmod +x {} \;

# Set up VS Code extensions for Python development
mkdir -p ${USER_HOME}/.vscode
cat > ${USER_HOME}/.vscode/extensions.json << 'EOF'
{
    "recommendations": [
        "ms-python.python",
        "ms-python.flake8",
        "ms-python.black-formatter",
        "ms-python.isort",
        "ms-python.mypy-type-checker",
        "ms-toolsai.jupyter",
        "batisteo.vscode-django",
        "ms-python.debugpy",
        "charliermarsh.ruff",
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "bradlc.vscode-tailwindcss"
    ]
}
EOF

# Create Python workspace settings
cat > ${USER_HOME}/.vscode/settings.json << 'EOF'
{
    "python.defaultInterpreterPath": "/usr/bin/python3",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.nosetestsEnabled": false,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/venv": true,
        "**/.env": true
    }
}
EOF

# Set ownership
chown -R ${USER_NAME}:${USER_NAME} ${USER_HOME}/templates
chown -R ${USER_NAME}:${USER_NAME} ${USER_HOME}/.vscode

echo "Python development environment setup completed!"
