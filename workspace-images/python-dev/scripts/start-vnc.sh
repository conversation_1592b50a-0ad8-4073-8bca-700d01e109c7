#!/bin/bash
# Start VNC server for Python Development Workspace

set -e

USER_NAME=${USER_NAME:-workspace}
DISPLAY_WIDTH=${DISPLAY_WIDTH:-1920}
DISPLAY_HEIGHT=${DISPLAY_HEIGHT:-1080}
VNC_DEPTH=${VNC_DEPTH:-24}
VNC_PASSWORD=${VNC_PASSWORD:-}

echo "Starting VNC server for Python development..."

# Set up VNC password
if [ -n "$VNC_PASSWORD" ]; then
    echo "$VNC_PASSWORD" | vncpasswd -f > ~/.vnc/passwd
    chmod 600 ~/.vnc/passwd
else
    # Create empty password file for no authentication
    touch ~/.vnc/passwd
    chmod 600 ~/.vnc/passwd
fi

# Kill any existing VNC servers
vncserver -kill $DISPLAY 2>/dev/null || true

# Start VNC server
vncserver $DISPLAY \
    -geometry ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT} \
    -depth ${VNC_DEPTH} \
    -localhost no \
    -SecurityTypes VncAuth \
    -rfbauth ~/.vnc/passwd \
    -log ~/.vnc/logs/vnc.log

echo "VNC server started on display $DISPLAY"
echo "Resolution: ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT}@${VNC_DEPTH}"

# Keep the container running and monitor VNC server
while true; do
    if ! pgrep -f "Xvnc.*$DISPLAY" > /dev/null; then
        echo "VNC server stopped, restarting..."
        vncserver $DISPLAY \
            -geometry ${DISPLAY_WIDTH}x${DISPLAY_HEIGHT} \
            -depth ${VNC_DEPTH} \
            -localhost no \
            -SecurityTypes VncAuth \
            -rfbauth ~/.vnc/passwd \
            -log ~/.vnc/logs/vnc.log
    fi
    sleep 30
done
