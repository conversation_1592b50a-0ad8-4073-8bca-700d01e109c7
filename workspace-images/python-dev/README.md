# Python Development Workspace

A specialized Docker image for Python web development with comprehensive framework support and AI-powered development tools.

## Features

### Python Frameworks
- **Django** - Full-stack web framework with REST API support
- **Flask** - Lightweight web framework with extensions
- **FastAPI** - Modern, fast API framework with automatic documentation
- **Streamlit** - Data science web applications
- **Gradio** - Machine learning model interfaces

### Development Environment
- **Python 3.11** with pip, conda, and poetry package managers
- **VS Code** with Python extensions pre-installed
- **Jupyter Lab** for interactive development
- **Docker-in-Docker** support for containerized development
- **Git** with common configurations

### Database Support
- PostgreSQL, MySQL, SQLite clients
- Redis tools
- Database development libraries pre-installed

### AI-Powered Features
- Integration with Omnispace AI code editor
- Natural language code generation
- Framework-aware code completion
- Intelligent error analysis and suggestions

## Quick Start

### Using Docker Compose
```bash
# Start Python development workspace
docker-compose --profile python up -d python-dev

# Access via VNC: localhost:5904 (password: test123)
# Access Jupyter: http://localhost:8888
# Django/FastAPI: http://localhost:8000
# Flask: http://localhost:5000
# Streamlit: http://localhost:8501
# Gradio: http://localhost:7860
```

### Using Docker Run
```bash
docker run -d \
  --name python-workspace \
  -p 5901:5901 \
  -p 8000:8000 \
  -p 5000:5000 \
  -p 8501:8501 \
  -p 7860:7860 \
  -p 8888:8888 \
  -e VNC_PASSWORD=secure123 \
  -e DISPLAY_WIDTH=1920 \
  -e DISPLAY_HEIGHT=1080 \
  -e START_JUPYTER=true \
  -v /var/run/docker.sock:/var/run/docker.sock \
  omnispace/python-dev:latest
```

## Project Templates

The workspace includes pre-configured project templates in `~/templates/`:

### Django Template
```bash
cd ~/templates/django-template
./create_project.sh myproject
cd myproject
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

### Flask Template
```bash
cd ~/templates/flask-template
./create_project.sh myproject
cd myproject
source venv/bin/activate
python app.py
```

### FastAPI Template
```bash
cd ~/templates/fastapi-template
./create_project.sh myproject
cd myproject
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Streamlit Template
```bash
cd ~/templates/streamlit-template
./create_project.sh myproject
cd myproject
source venv/bin/activate
streamlit run app.py --server.address 0.0.0.0 --server.port 8501
```

### Gradio Template
```bash
cd ~/templates/gradio-template
./create_project.sh myproject
cd myproject
source venv/bin/activate
python app.py
```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `VNC_PASSWORD` | (empty) | VNC access password |
| `DISPLAY_WIDTH` | 1920 | Desktop width |
| `DISPLAY_HEIGHT` | 1080 | Desktop height |
| `VNC_DEPTH` | 24 | Color depth |
| `USER_NAME` | workspace | Username |
| `START_JUPYTER` | false | Auto-start Jupyter Lab |
| `PYTHONUNBUFFERED` | 1 | Python output buffering |

## Ports

| Port | Service | Description |
|------|---------|-------------|
| 5901 | VNC | Remote desktop access |
| 8000 | Django/FastAPI | Web framework default |
| 5000 | Flask | Flask default port |
| 8501 | Streamlit | Streamlit applications |
| 7860 | Gradio | Gradio interfaces |
| 8888 | Jupyter | Jupyter Lab |

## Package Managers

### pip (Default)
```bash
pip install package-name
pip install -r requirements.txt
```

### conda
```bash
conda create -n myenv python=3.11
conda activate myenv
conda install package-name
```

### poetry
```bash
poetry init
poetry add package-name
poetry install
poetry shell
```

## VS Code Extensions

Pre-installed extensions:
- Python
- Pylance
- Python Debugger
- Jupyter
- Black Formatter
- isort
- Flake8
- MyPy
- Django
- Ruff

## Database Connections

### PostgreSQL
```python
import psycopg2
conn = psycopg2.connect(
    host="localhost",
    database="mydb",
    user="user",
    password="password"
)
```

### MySQL
```python
import mysql.connector
conn = mysql.connector.connect(
    host="localhost",
    user="user",
    password="password",
    database="mydb"
)
```

### Redis
```python
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
```

## Integration with Omnispace

This workspace is designed to integrate seamlessly with the Omnispace platform:

1. **AI Code Editor** - Natural language code generation and editing
2. **Live Preview** - Real-time application preview with port forwarding
3. **File Management** - Cloud-based file storage and synchronization
4. **Collaboration** - Real-time collaborative editing
5. **Version Control** - Integrated Git workflow
6. **Environment Management** - Automated dependency and environment setup

## Troubleshooting

### VNC Connection Issues
- Ensure VNC password is set correctly
- Check if port 5901 is accessible
- Verify container is running: `docker ps`

### Python Package Issues
- Update pip: `pip install --upgrade pip`
- Clear pip cache: `pip cache purge`
- Use virtual environments to avoid conflicts

### Framework-Specific Issues
- **Django**: Run migrations after creating project
- **FastAPI**: Install uvicorn for ASGI server
- **Streamlit**: Use `--server.address 0.0.0.0` for external access
- **Gradio**: Set `server_name="0.0.0.0"` in launch()

## Building the Image

```bash
cd workspace-images/python-dev
docker build -t omnispace/python-dev:latest .
```

## Contributing

This workspace is part of the Omnispace platform. For contributions and issues, please refer to the main Omnispace repository.
