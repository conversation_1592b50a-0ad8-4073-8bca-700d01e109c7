#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [ubuntu|development|minimal|guacamole|all] [start|stop|logs|status]"
    echo ""
    echo "Workspace types:"
    echo "  ubuntu      - Ubuntu Desktop workspace"
    echo "  development - Development Environment workspace"
    echo "  minimal     - Minimal Desktop workspace"
    echo "  guacamole   - Apache Guacamole server"
    echo "  all         - All workspace types"
    echo ""
    echo "Actions:"
    echo "  start       - Start the workspace(s)"
    echo "  stop        - Stop the workspace(s)"
    echo "  logs        - Show logs"
    echo "  status      - Show status"
    echo ""
    echo "Examples:"
    echo "  $0 ubuntu start       # Start Ubuntu Desktop workspace"
    echo "  $0 development logs   # Show Development Environment logs"
    echo "  $0 all stop          # Stop all workspaces"
}

# Check arguments
if [ $# -lt 2 ]; then
    show_usage
    exit 1
fi

WORKSPACE_TYPE=$1
ACTION=$2

# Validate workspace type
case $WORKSPACE_TYPE in
    ubuntu|development|minimal|guacamole|all)
        ;;
    *)
        print_error "Invalid workspace type: $WORKSPACE_TYPE"
        show_usage
        exit 1
        ;;
esac

# Validate action
case $ACTION in
    start|stop|logs|status)
        ;;
    *)
        print_error "Invalid action: $ACTION"
        show_usage
        exit 1
        ;;
esac

# Execute action
case $ACTION in
    start)
        print_status "Starting $WORKSPACE_TYPE workspace(s)..."
        if [ "$WORKSPACE_TYPE" = "all" ]; then
            docker-compose --profile ubuntu --profile development --profile minimal up -d
        else
            docker-compose --profile $WORKSPACE_TYPE up -d
        fi
        print_success "$WORKSPACE_TYPE workspace(s) started"
        
        # Show connection information
        if [ "$WORKSPACE_TYPE" = "ubuntu" ] || [ "$WORKSPACE_TYPE" = "all" ]; then
            echo ""
            print_status "Ubuntu Desktop: VNC port 5901, password: test123"
        fi
        if [ "$WORKSPACE_TYPE" = "development" ] || [ "$WORKSPACE_TYPE" = "all" ]; then
            echo ""
            print_status "Development Environment: VNC port 5902, password: test123"
        fi
        if [ "$WORKSPACE_TYPE" = "minimal" ] || [ "$WORKSPACE_TYPE" = "all" ]; then
            echo ""
            print_status "Minimal Desktop: VNC port 5903, password: test123"
        fi
        if [ "$WORKSPACE_TYPE" = "guacamole" ] || [ "$WORKSPACE_TYPE" = "all" ]; then
            echo ""
            print_status "Guacamole: http://localhost:8080 (admin/admin)"
        fi
        ;;
        
    stop)
        print_status "Stopping $WORKSPACE_TYPE workspace(s)..."
        if [ "$WORKSPACE_TYPE" = "all" ]; then
            docker-compose --profile ubuntu --profile development --profile minimal --profile guacamole down
        else
            docker-compose --profile $WORKSPACE_TYPE down
        fi
        print_success "$WORKSPACE_TYPE workspace(s) stopped"
        ;;
        
    logs)
        print_status "Showing logs for $WORKSPACE_TYPE workspace(s)..."
        if [ "$WORKSPACE_TYPE" = "all" ]; then
            docker-compose logs -f
        else
            case $WORKSPACE_TYPE in
                ubuntu)
                    docker-compose logs -f ubuntu-desktop
                    ;;
                development)
                    docker-compose logs -f development-env
                    ;;
                minimal)
                    docker-compose logs -f minimal-desktop
                    ;;
                guacamole)
                    docker-compose logs -f guacamole guacd guacamole-db
                    ;;
            esac
        fi
        ;;
        
    status)
        print_status "Status of $WORKSPACE_TYPE workspace(s):"
        docker-compose ps
        ;;
esac
