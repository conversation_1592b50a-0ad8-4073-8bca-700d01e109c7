# Omnispace .dockerignore
# Exclude unnecessary files to improve build performance and reduce image size

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# Next.js build output
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md

# Testing
coverage/
.nyc_output/
.jest/
test/
tests/
__tests__/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# Linting and formatting
.eslintrc*
.prettierrc*
.stylelintrc*

# TypeScript
*.tsbuildinfo

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Workspace images (these are separate containers)
workspace-images/

# Docker files (avoid recursive Docker builds)
Dockerfile*
docker-compose*.yml
.dockerignore

# Development tools
.husky/
.commitlintrc*

# Vercel
.vercel

# Local development files
.env.example
docker-compose.override.yml
