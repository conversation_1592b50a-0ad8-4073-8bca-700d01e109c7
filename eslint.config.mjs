import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Prevent server-side imports in client-side code
      "no-restricted-imports": [
        "error",
        {
          patterns: [
            {
              group: ["@/services/server/*", "@/services/docker"],
              message: "Server-side services cannot be imported in client-side code. Use client-side services from @/services/client instead."
            },
            {
              group: ["dockerode", "ssh2", "cpu-features", "node-pty"],
              message: "Native modules cannot be imported in client-side code. These modules are server-side only."
            }
          ]
        }
      ]
    }
  }
];

export default eslintConfig;
