# Omnispace - Docker Compose Configuration
# Complete setup for Omnispace platform with workspace management

version: '3.8'

services:
  # Omnispace Frontend Application
  omnispace:
    build:
      context: .
      dockerfile: Dockerfile
    image: omnispace/app:latest
    container_name: omnispace-app
    ports:
      - "${OMNISPACE_PORT:-3000}:3000"
    environment:
      # Next.js Configuration
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      
      # AI SDK Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_GENERATIVE_AI_API_KEY=${GOOGLE_GENERATIVE_AI_API_KEY}
      - AI_SDK_LOG_LEVEL=${AI_SDK_LOG_LEVEL:-info}
      
      # Docker Configuration
      - DOCKER_SOCKET_PATH=/var/run/docker.sock
      - DOCKER_HOST=${DOCKER_HOST}
      - DOCKER_PORT=${DOCKER_PORT}
      
      # VNC Configuration
      - VNC_BASE_PORT=${VNC_BASE_PORT:-5900}
      - VNC_HOST=${VNC_HOST:-localhost}
      
      # API Configuration
      - API_BASE_URL=${API_BASE_URL:-http://localhost:3000}
      - WEBSOCKET_URL=${WEBSOCKET_URL:-ws://localhost:3000}
      
      # Guacamole Integration
      - GUACAMOLE_URL=${GUACAMOLE_URL:-http://guacamole:8080}
      - GUACAMOLE_USERNAME=${GUACAMOLE_USERNAME:-guacadmin}
      - GUACAMOLE_PASSWORD=${GUACAMOLE_PASSWORD:-guacadmin}
      
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - omnispace-network
      - workspace-network
    restart: unless-stopped
    depends_on:
      - guacamole
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL Database for Guacamole
  guacamole-db:
    image: postgres:15
    container_name: omnispace-guacamole-db
    environment:
      POSTGRES_DB: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: ${GUACAMOLE_DB_PASSWORD:-guacamole_secure_pass}
      PGDATA: /var/lib/postgresql/data/guacamole
    volumes:
      - guacamole-db-data:/var/lib/postgresql/data
      - ./workspace-images/guacamole/init:/docker-entrypoint-initdb.d:ro
    networks:
      - omnispace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U guacamole_user -d guacamole_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Guacamole Daemon
  guacd:
    image: guacamole/guacd:1.5.5
    container_name: omnispace-guacd
    volumes:
      - guacamole-drive:/drive:rw
      - guacamole-record:/record:rw
    networks:
      - omnispace-network
      - workspace-network
    restart: unless-stopped

  # Guacamole Web Application
  guacamole:
    image: guacamole/guacamole:1.5.5
    container_name: omnispace-guacamole
    depends_on:
      - guacamole-db
      - guacd
    ports:
      - "${GUACAMOLE_PORT:-8080}:8080"
    environment:
      # Database Configuration
      POSTGRES_HOSTNAME: guacamole-db
      POSTGRES_DATABASE: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: ${GUACAMOLE_DB_PASSWORD:-guacamole_secure_pass}
      
      # Guacd Configuration
      GUACD_HOSTNAME: guacd
      GUACD_PORT: 4822
      
      # Web Application Configuration
      WEBAPP_CONTEXT: ${WEBAPP_CONTEXT:-ROOT}
      
      # Security Features
      TOTP_ENABLED: ${TOTP_ENABLED:-true}
      
      # Recording and Drive Mapping
      RECORDING_SEARCH_PATH: /record
      DRIVE_PATH: /drive
      
    volumes:
      - guacamole-drive:/drive:rw
      - guacamole-record:/record:rw
    networks:
      - omnispace-network
      - workspace-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for session management and caching (optional)
  redis:
    image: redis:7-alpine
    container_name: omnispace-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
    networks:
      - omnispace-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    profiles:
      - redis

volumes:
  guacamole-db-data:
    name: omnispace-guacamole-db-data
  guacamole-drive:
    name: omnispace-guacamole-drive
  guacamole-record:
    name: omnispace-guacamole-record
  redis-data:
    name: omnispace-redis-data

networks:
  omnispace-network:
    name: omnispace-network
    driver: bridge
  workspace-network:
    name: omnispace-workspace-network
    driver: bridge
    external: true
