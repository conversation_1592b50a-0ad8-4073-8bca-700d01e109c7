/**
 * Workspace File Management Hook
 * Provides file operations within workspaces
 */

import { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  WorkspaceFile,
  FilePermission,
  FileVersion,
  PaginationParams
} from '@/types/workspace';

// API functions for workspace files
const workspaceFilesApi = {
  // List files in workspace
  listFiles: async (workspaceId: string, params?: {
    path?: string;
    type?: 'file' | 'directory';
    search?: string;
    pagination?: PaginationParams;
  }) => {
    const searchParams = new URLSearchParams();
    
    if (params?.path) searchParams.set('path', params.path);
    if (params?.type) searchParams.set('type', params.type);
    if (params?.search) searchParams.set('search', params.search);
    if (params?.pagination) {
      searchParams.set('page', (params.pagination.offset / params.pagination.limit + 1).toString());
      searchParams.set('limit', params.pagination.limit.toString());
    }

    const response = await fetch(`/api/workspace/${workspaceId}/files?${searchParams}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch files');
    }
    
    return response.json();
  },

  // Get file by ID
  getFile: async (fileId: string): Promise<WorkspaceFile> => {
    const response = await fetch(`/api/workspace-files/${fileId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch file');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Create file or directory
  createFile: async (workspaceId: string, data: {
    name: string;
    path: string;
    type: 'file' | 'directory';
    content?: string;
    mimeType?: string;
    encoding?: string;
    permissions?: FilePermission[];
  }): Promise<WorkspaceFile> => {
    const response = await fetch(`/api/workspace/${workspaceId}/files`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to create file');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Update file
  updateFile: async (workspaceId: string, data: {
    fileId: string;
    content?: string;
    name?: string;
    path?: string;
    versionMessage?: string;
    permissions?: FilePermission[];
  }): Promise<WorkspaceFile> => {
    const response = await fetch(`/api/workspace/${workspaceId}/files`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update file');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Delete file
  deleteFile: async (workspaceId: string, fileId: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${workspaceId}/files?fileId=${fileId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to delete file');
    }
  },

  // Lock file
  lockFile: async (workspaceId: string, fileId: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${workspaceId}/files?action=lock`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fileId }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to lock file');
    }
  },

  // Unlock file
  unlockFile: async (workspaceId: string, fileId: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${workspaceId}/files?action=unlock`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fileId }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to unlock file');
    }
  },

  // Get file versions
  getFileVersions: async (fileId: string): Promise<FileVersion[]> => {
    const response = await fetch(`/api/workspace-files/${fileId}/versions`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch file versions');
    }
    
    const result = await response.json();
    return result.data;
  },
};

// Hook parameters
export interface UseWorkspaceFilesParams {
  workspaceId: string;
  path?: string;
  type?: 'file' | 'directory';
  search?: string;
  pagination?: PaginationParams;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseWorkspaceFilesReturn {
  // Data
  files: WorkspaceFile[];
  currentFile: WorkspaceFile | null;
  total: number;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isLocking: boolean;
  
  // Error states
  error: Error | null;
  
  // Actions
  createFile: (data: {
    name: string;
    path: string;
    type: 'file' | 'directory';
    content?: string;
    mimeType?: string;
    encoding?: string;
    permissions?: FilePermission[];
  }) => Promise<WorkspaceFile>;
  updateFile: (data: {
    fileId: string;
    content?: string;
    name?: string;
    path?: string;
    versionMessage?: string;
    permissions?: FilePermission[];
  }) => Promise<WorkspaceFile>;
  deleteFile: (fileId: string) => Promise<void>;
  lockFile: (fileId: string) => Promise<void>;
  unlockFile: (fileId: string) => Promise<void>;
  getFileVersions: (fileId: string) => Promise<FileVersion[]>;
  
  // Utilities
  refetch: () => void;
  setPath: (path: string) => void;
  setSearch: (search: string) => void;
  setPagination: (pagination: PaginationParams) => void;
  getFile: (fileId: string) => WorkspaceFile | undefined;
  
  // Computed values
  directories: WorkspaceFile[];
  regularFiles: WorkspaceFile[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  
  // File tree helpers
  buildFileTree: () => FileTreeNode[];
  getFilesByPath: (path: string) => WorkspaceFile[];
}

export interface FileTreeNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileTreeNode[];
  file?: WorkspaceFile;
}

// Main hook
export function useWorkspaceFiles(params: UseWorkspaceFilesParams): UseWorkspaceFilesReturn {
  const {
    workspaceId,
    path: initialPath = '',
    type,
    search: initialSearch = '',
    pagination: initialPagination = { limit: 50, offset: 0 },
    autoRefresh = false,
    refreshInterval = 30000,
  } = params;

  const queryClient = useQueryClient();
  const [path, setPath] = useState(initialPath);
  const [search, setSearch] = useState(initialSearch);
  const [pagination, setPagination] = useState(initialPagination);
  const [currentFile, setCurrentFile] = useState<WorkspaceFile | null>(null);

  // Query for files list
  const {
    data: filesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['workspace-files', workspaceId, path, type, search, pagination],
    queryFn: () => workspaceFilesApi.listFiles(workspaceId, { path, type, search, pagination }),
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 10000, // 10 seconds
    enabled: !!workspaceId,
  });

  // Create file mutation
  const createMutation = useMutation({
    mutationFn: (data: Parameters<typeof workspaceFilesApi.createFile>[1]) =>
      workspaceFilesApi.createFile(workspaceId, data),
    onSuccess: (newFile) => {
      queryClient.invalidateQueries({ queryKey: ['workspace-files', workspaceId] });
      toast.success(`${newFile.type === 'directory' ? 'Directory' : 'File'} "${newFile.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create file: ${error.message}`);
    },
  });

  // Update file mutation
  const updateMutation = useMutation({
    mutationFn: (data: Parameters<typeof workspaceFilesApi.updateFile>[1]) =>
      workspaceFilesApi.updateFile(workspaceId, data),
    onSuccess: (updatedFile) => {
      queryClient.invalidateQueries({ queryKey: ['workspace-files', workspaceId] });
      queryClient.setQueryData(['workspace-file', updatedFile.id], updatedFile);
      toast.success(`File "${updatedFile.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update file: ${error.message}`);
    },
  });

  // Delete file mutation
  const deleteMutation = useMutation({
    mutationFn: (fileId: string) => workspaceFilesApi.deleteFile(workspaceId, fileId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-files', workspaceId] });
      toast.success('File deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete file: ${error.message}`);
    },
  });

  // Lock file mutation
  const lockMutation = useMutation({
    mutationFn: (fileId: string) => workspaceFilesApi.lockFile(workspaceId, fileId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-files', workspaceId] });
      toast.success('File locked successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to lock file: ${error.message}`);
    },
  });

  // Unlock file mutation
  const unlockMutation = useMutation({
    mutationFn: (fileId: string) => workspaceFilesApi.unlockFile(workspaceId, fileId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-files', workspaceId] });
      toast.success('File unlocked successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to unlock file: ${error.message}`);
    },
  });

  // Computed values
  const files = filesData?.data?.files || [];
  const total = filesData?.data?.total || 0;

  const directories = useMemo(() => {
    return files.filter(file => file.type === 'directory');
  }, [files]);

  const regularFiles = useMemo(() => {
    return files.filter(file => file.type === 'file');
  }, [files]);

  const hasNextPage = pagination.offset + pagination.limit < total;
  const hasPreviousPage = pagination.offset > 0;

  // Action handlers
  const handleCreateFile = useCallback(
    async (data: Parameters<typeof workspaceFilesApi.createFile>[1]) => {
      return createMutation.mutateAsync(data);
    },
    [createMutation]
  );

  const handleUpdateFile = useCallback(
    async (data: Parameters<typeof workspaceFilesApi.updateFile>[1]) => {
      return updateMutation.mutateAsync(data);
    },
    [updateMutation]
  );

  const handleDeleteFile = useCallback(
    async (fileId: string) => {
      return deleteMutation.mutateAsync(fileId);
    },
    [deleteMutation]
  );

  const handleLockFile = useCallback(
    async (fileId: string) => {
      return lockMutation.mutateAsync(fileId);
    },
    [lockMutation]
  );

  const handleUnlockFile = useCallback(
    async (fileId: string) => {
      return unlockMutation.mutateAsync(fileId);
    },
    [unlockMutation]
  );

  const handleGetFileVersions = useCallback(
    async (fileId: string) => {
      return workspaceFilesApi.getFileVersions(fileId);
    },
    []
  );

  const getFile = useCallback(
    (fileId: string) => {
      return files.find(file => file.id === fileId);
    },
    [files]
  );

  // File tree builder
  const buildFileTree = useCallback((): FileTreeNode[] => {
    const tree: FileTreeNode[] = [];
    const pathMap = new Map<string, FileTreeNode>();

    // Sort files by path for consistent tree building
    const sortedFiles = [...files].sort((a, b) => a.path.localeCompare(b.path));

    for (const file of sortedFiles) {
      const pathParts = file.path.split('/').filter(Boolean);
      let currentPath = '';
      let currentLevel = tree;

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i];
        currentPath = currentPath ? `${currentPath}/${part}` : part;

        let node = pathMap.get(currentPath);
        if (!node) {
          node = {
            id: currentPath,
            name: part,
            path: currentPath,
            type: i === pathParts.length - 1 ? file.type : 'directory',
            children: [],
            file: i === pathParts.length - 1 ? file : undefined,
          };
          pathMap.set(currentPath, node);
          currentLevel.push(node);
        }

        if (node.children) {
          currentLevel = node.children;
        }
      }
    }

    return tree;
  }, [files]);

  const getFilesByPath = useCallback(
    (targetPath: string) => {
      return files.filter(file => file.path.startsWith(targetPath));
    },
    [files]
  );

  return {
    // Data
    files,
    currentFile,
    total,
    
    // Loading states
    isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isLocking: lockMutation.isPending || unlockMutation.isPending,
    
    // Error states
    error: error as Error | null,
    
    // Actions
    createFile: handleCreateFile,
    updateFile: handleUpdateFile,
    deleteFile: handleDeleteFile,
    lockFile: handleLockFile,
    unlockFile: handleUnlockFile,
    getFileVersions: handleGetFileVersions,
    
    // Utilities
    refetch,
    setPath,
    setSearch,
    setPagination,
    getFile,
    
    // Computed values
    directories,
    regularFiles,
    hasNextPage,
    hasPreviousPage,
    
    // File tree helpers
    buildFileTree,
    getFilesByPath,
  };
}
