'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  AIAnalysisRequest,
  CodeError,
  CodeFix,
  RefactoringSuggestion,
  CodeMetrics,
  UseCodeAnalysisReturn,
} from '@/types/ai-code-editor';

interface UseCodeAnalysisConfig {
  debounceMs?: number;
  autoAnalyze?: boolean;
  includeStyle?: boolean;
  includePerformance?: boolean;
  includeSecurity?: boolean;
  userId?: string;
  workspaceId?: string;
}

export function useCodeAnalysis(
  config: UseCodeAnalysisConfig = {}
): UseCodeAnalysisReturn {
  const {
    debounceMs = 1000,
    autoAnalyze = true,
    includeStyle = true,
    includePerformance = true,
    includeSecurity = true,
    userId,
    workspaceId,
  } = config;

  const [errors, setErrors] = useState<CodeError[]>([]);
  const [suggestions, setSuggestions] = useState<RefactoringSuggestion[]>([]);
  const [metrics, setMetrics] = useState<CodeMetrics>({
    complexity: 0,
    maintainability: 0,
    readability: 0,
    performance: 0,
    security: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const lastAnalysisRef = useRef<string>('');

  // Analyze code
  const analyzeCode = useCallback(async (request: AIAnalysisRequest) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Create request key for deduplication
    const requestKey = JSON.stringify({
      code: request.code,
      language: request.language,
      fileName: request.fileName,
    });

    // Skip if same request
    if (requestKey === lastAnalysisRef.current) {
      return;
    }

    lastAnalysisRef.current = requestKey;

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        setIsLoading(true);
        setError(undefined);

        // Create abort controller for this request
        abortControllerRef.current = new AbortController();

        const response = await fetch('/api/ai-code/analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...request,
            includeStyle,
            includePerformance,
            includeSecurity,
            userId,
            workspaceId,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to analyze code');
        }

        const data = await response.json();

        if (data.success) {
          setErrors(data.data.errors || []);
          setSuggestions(data.data.suggestions || []);
          setMetrics(data.data.metrics || {
            complexity: 0,
            maintainability: 0,
            readability: 0,
            performance: 0,
            security: 0,
          });
        } else {
          throw new Error(data.error?.message || 'Failed to analyze code');
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was cancelled, ignore
          return;
        }

        console.error('Code analysis error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setErrors([]);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);
  }, [
    debounceMs,
    includeStyle,
    includePerformance,
    includeSecurity,
    userId,
    workspaceId,
  ]);

  // Apply a fix
  const applyFix = useCallback(async (fix: CodeFix) => {
    try {
      // Emit custom event for editor to handle
      window.dispatchEvent(new CustomEvent('ai-fix-applied', {
        detail: { fix }
      }));

      // Remove the error that was fixed
      setErrors(prev => prev.filter(error => 
        !error.fixes?.some(f => f.id === fix.id)
      ));
    } catch (err) {
      console.error('Error applying fix:', err);
      setError(err instanceof Error ? err.message : 'Failed to apply fix');
    }
  }, []);

  // Dismiss an error
  const dismissError = useCallback((errorId: string) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  }, []);

  // Auto-analyze code on changes
  const handleCodeChange = useCallback((
    code: string,
    language: string,
    fileName?: string
  ) => {
    if (!autoAnalyze) return;

    analyzeCode({
      code,
      language,
      fileName,
    });
  }, [autoAnalyze, analyzeCode]);

  // Get errors by severity
  const getErrorsBySeverity = useCallback((severity: CodeError['severity']) => {
    return errors.filter(error => error.severity === severity);
  }, [errors]);

  // Get suggestions by type
  const getSuggestionsByType = useCallback((type: RefactoringSuggestion['type']) => {
    return suggestions.filter(suggestion => suggestion.type === type);
  }, [suggestions]);

  // Get overall code quality score
  const getQualityScore = useCallback(() => {
    const { complexity, maintainability, readability, performance, security } = metrics;
    return Math.round(
      (maintainability + readability + performance + security - complexity) / 4 * 10
    ) / 10;
  }, [metrics]);

  // Clear analysis results
  const clearAnalysis = useCallback(() => {
    setErrors([]);
    setSuggestions([]);
    setMetrics({
      complexity: 0,
      maintainability: 0,
      readability: 0,
      performance: 0,
      security: 0,
    });
    setError(undefined);

    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    errors,
    suggestions,
    metrics,
    isLoading,
    error,
    analyzeCode,
    applyFix,
    dismissError,
    handleCodeChange,
    getErrorsBySeverity,
    getSuggestionsByType,
    getQualityScore,
    clearAnalysis,
  };
}

// Extended return type with additional methods
interface ExtendedUseCodeAnalysisReturn extends UseCodeAnalysisReturn {
  handleCodeChange: (code: string, language: string, fileName?: string) => void;
  getErrorsBySeverity: (severity: CodeError['severity']) => CodeError[];
  getSuggestionsByType: (type: RefactoringSuggestion['type']) => RefactoringSuggestion[];
  getQualityScore: () => number;
  clearAnalysis: () => void;
}
