'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  AIAssistantMessage,
  AIAssistantRequest,
  AIAssistantAction,
  UseAIAssistantReturn,
  CodeSelection,
} from '@/types/ai-code-editor';

interface UseAIAssistantConfig {
  maxMessages?: number;
  persistConversation?: boolean;
  userId?: string;
  workspaceId?: string;
}

export function useAIAssistant(
  config: UseAIAssistantConfig = {}
): UseAIAssistantReturn {
  const {
    maxMessages = 50,
    persistConversation = true,
    userId,
    workspaceId,
  } = config;

  const [messages, setMessages] = useState<AIAssistantMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const abortControllerRef = useRef<AbortController>();
  const conversationIdRef = useRef<string>(`conversation-${Date.now()}`);

  // Load persisted conversation on mount
  useEffect(() => {
    if (persistConversation && typeof window !== 'undefined') {
      const storageKey = `ai-assistant-${userId || 'anonymous'}-${workspaceId || 'default'}`;
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        try {
          const parsedMessages = JSON.parse(saved);
          setMessages(parsedMessages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })));
        } catch (err) {
          console.error('Failed to load conversation:', err);
        }
      }
    }
  }, [persistConversation, userId, workspaceId]);

  // Persist conversation when messages change
  useEffect(() => {
    if (persistConversation && messages.length > 0 && typeof window !== 'undefined') {
      const storageKey = `ai-assistant-${userId || 'anonymous'}-${workspaceId || 'default'}`;
      localStorage.setItem(storageKey, JSON.stringify(messages));
    }
  }, [messages, persistConversation, userId, workspaceId]);

  // Send message to AI assistant
  const sendMessage = useCallback(async (request: AIAssistantRequest) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      setIsLoading(true);
      setError(undefined);

      // Add user message to conversation
      const userMessage: AIAssistantMessage = {
        id: `user-${Date.now()}`,
        type: 'user',
        content: request.message,
        timestamp: new Date(),
        codeContext: request.codeContext,
      };

      setMessages(prev => [...prev, userMessage]);

      // Create abort controller for this request
      abortControllerRef.current = new AbortController();

      // Prepare conversation history (limit to recent messages)
      const recentMessages = messages.slice(-10);
      const conversationHistory = [...recentMessages, userMessage];

      const response = await fetch('/api/ai-code/assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: request.message,
          codeContext: request.codeContext,
          conversationHistory: conversationHistory.map(msg => ({
            ...msg,
            timestamp: msg.timestamp.toISOString(),
          })),
          userId,
          workspaceId,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to get assistant response');
      }

      const data = await response.json();

      if (data.success) {
        const assistantMessage = data.data.message;
        setMessages(prev => {
          const updated = [...prev, assistantMessage];
          // Limit conversation length
          return updated.length > maxMessages 
            ? updated.slice(-maxMessages) 
            : updated;
        });
      } else {
        throw new Error(data.error?.message || 'Failed to get assistant response');
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was cancelled, ignore
        return;
      }

      console.error('AI assistant error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);

      // Add error message to conversation
      const errorMsg: AIAssistantMessage = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `Error: ${errorMessage}`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsLoading(false);
    }
  }, [messages, maxMessages, userId, workspaceId]);

  // Clear conversation
  const clearConversation = useCallback(() => {
    setMessages([]);
    setError(undefined);
    conversationIdRef.current = `conversation-${Date.now()}`;

    // Clear persisted conversation
    if (persistConversation && typeof window !== 'undefined') {
      const storageKey = `ai-assistant-${userId || 'anonymous'}-${workspaceId || 'default'}`;
      localStorage.removeItem(storageKey);
    }

    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, [persistConversation, userId, workspaceId]);

  // Execute an AI assistant action
  const executeAction = useCallback(async (action: AIAssistantAction) => {
    try {
      // Emit custom event for editor to handle
      window.dispatchEvent(new CustomEvent('ai-action-executed', {
        detail: { action }
      }));

      // Add system message about action execution
      const actionMessage: AIAssistantMessage = {
        id: `action-${Date.now()}`,
        type: 'system',
        content: `Executed action: ${action.title}`,
        timestamp: new Date(),
        actions: [action],
      };

      setMessages(prev => [...prev, actionMessage]);
    } catch (err) {
      console.error('Error executing action:', err);
      setError(err instanceof Error ? err.message : 'Failed to execute action');
    }
  }, []);

  // Quick actions for common tasks
  const explainCode = useCallback((selection: CodeSelection, fileName: string) => {
    sendMessage({
      message: 'Please explain this code',
      codeContext: {
        file: fileName,
        selection,
      },
    });
  }, [sendMessage]);

  const generateTests = useCallback((selection: CodeSelection, fileName: string) => {
    sendMessage({
      message: 'Generate unit tests for this code',
      codeContext: {
        file: fileName,
        selection,
      },
    });
  }, [sendMessage]);

  const optimizeCode = useCallback((selection: CodeSelection, fileName: string) => {
    sendMessage({
      message: 'How can I optimize this code for better performance?',
      codeContext: {
        file: fileName,
        selection,
      },
    });
  }, [sendMessage]);

  const findBugs = useCallback((selection: CodeSelection, fileName: string) => {
    sendMessage({
      message: 'Are there any bugs or issues in this code?',
      codeContext: {
        file: fileName,
        selection,
      },
    });
  }, [sendMessage]);

  // Get conversation statistics
  const getConversationStats = useCallback(() => {
    const userMessages = messages.filter(msg => msg.type === 'user').length;
    const assistantMessages = messages.filter(msg => msg.type === 'assistant').length;
    const systemMessages = messages.filter(msg => msg.type === 'system').length;

    return {
      total: messages.length,
      user: userMessages,
      assistant: assistantMessages,
      system: systemMessages,
      conversationId: conversationIdRef.current,
    };
  }, [messages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearConversation,
    executeAction,
    explainCode,
    generateTests,
    optimizeCode,
    findBugs,
    getConversationStats,
  };
}

// Extended return type with additional methods
interface ExtendedUseAIAssistantReturn extends UseAIAssistantReturn {
  explainCode: (selection: CodeSelection, fileName: string) => void;
  generateTests: (selection: CodeSelection, fileName: string) => void;
  optimizeCode: (selection: CodeSelection, fileName: string) => void;
  findBugs: (selection: CodeSelection, fileName: string) => void;
  getConversationStats: () => {
    total: number;
    user: number;
    assistant: number;
    system: number;
    conversationId: string;
  };
}
