'use client';

import { useState, useCallback, useRef } from 'react';
import {
  PythonAssistantRequest,
  PythonAssistantMessage,
  PythonAssistantAction,
  PythonAssistantIntent,
  UsePythonAssistantReturn,
  PythonFramework,
  PythonProjectStructure,
  PythonCodeGenerationRequest,
} from '@/types/python-ai-editor';

interface UsePythonAssistantConfig {
  framework?: PythonFramework;
  projectStructure?: PythonProjectStructure;
  maxMessages?: number;
  persistConversation?: boolean;
  userId?: string;
  workspaceId?: string;
}

export function usePythonAssistant(
  config: UsePythonAssistantConfig = {}
): UsePythonAssistantReturn {
  const {
    framework,
    projectStructure,
    maxMessages = 50,
    persistConversation = true,
    userId,
    workspaceId,
  } = config;

  const [messages, setMessages] = useState<PythonAssistantMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const abortControllerRef = useRef<AbortController>();

  const sendMessage = useCallback(async (request: PythonAssistantRequest) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      setIsLoading(true);
      setError(undefined);

      // Add user message to conversation
      const userMessage: PythonAssistantMessage = {
        id: `user-${Date.now()}`,
        role: 'user',
        content: request.message,
        timestamp: new Date(),
        framework,
      };

      setMessages(prev => [...prev, userMessage]);

      // Create abort controller for this request
      abortControllerRef.current = new AbortController();

      // Enhance request with Python-specific context
      const enhancedRequest: PythonAssistantRequest = {
        ...request,
        framework,
        projectStructure,
        conversationHistory: messages.slice(-10), // Include last 10 messages for context
      };

      const response = await fetch('/api/python-workspace/ai/assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...enhancedRequest,
          userId,
          workspaceId,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to get assistant response');
      }

      const data = await response.json();

      if (data.success) {
        const assistantMessage = data.data.message as PythonAssistantMessage;
        
        setMessages(prev => {
          const newMessages = [...prev, assistantMessage];
          // Limit message history
          return newMessages.slice(-maxMessages);
        });
      } else {
        throw new Error(data.error?.message || 'Failed to get assistant response');
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was cancelled, ignore
        return;
      }

      console.error('Python assistant error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // Add error message to conversation
      const errorMessage: PythonAssistantMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: `Sorry, I encountered an error: ${err instanceof Error ? err.message : 'Unknown error'}`,
        timestamp: new Date(),
        framework,
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [framework, projectStructure, messages, maxMessages, userId, workspaceId]);

  const applyAction = useCallback(async (action: PythonAssistantAction) => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/python-workspace/ai/apply-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          workspaceId,
          userId,
          framework,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to apply action');
      }

      const data = await response.json();

      if (data.success) {
        // Add confirmation message
        const confirmationMessage: PythonAssistantMessage = {
          id: `action-${Date.now()}`,
          role: 'assistant',
          content: `✅ Applied: ${action.title}\n\n${data.message || 'Action completed successfully.'}`,
          timestamp: new Date(),
          framework,
        };
        
        setMessages(prev => [...prev, confirmationMessage]);
      } else {
        throw new Error(data.error?.message || 'Failed to apply action');
      }
    } catch (err) {
      console.error('Error applying action:', err);
      
      // Add error message
      const errorMessage: PythonAssistantMessage = {
        id: `action-error-${Date.now()}`,
        role: 'assistant',
        content: `❌ Failed to apply action: ${err instanceof Error ? err.message : 'Unknown error'}`,
        timestamp: new Date(),
        framework,
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [workspaceId, userId, framework]);

  const generateCode = useCallback(async (intent: PythonAssistantIntent, context?: string) => {
    if (!framework) {
      throw new Error('Framework is required for code generation');
    }

    try {
      setIsLoading(true);

      const request: PythonCodeGenerationRequest = {
        framework,
        intent,
        context: {
          description: context || `Generate ${intent} for ${framework}`,
          projectStructure,
        },
        options: {
          includeTests: true,
          includeDocumentation: true,
          followBestPractices: true,
        },
      };

      const response = await fetch('/api/python-workspace/ai/generate-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          userId,
          workspaceId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to generate code');
      }

      const data = await response.json();

      if (data.success) {
        // Add generated code as message
        const codeMessage: PythonAssistantMessage = {
          id: `code-${Date.now()}`,
          role: 'assistant',
          content: `Generated ${intent} for ${framework}:\n\n${data.data.explanation || ''}`,
          timestamp: new Date(),
          framework,
          codeBlocks: data.data.files?.map((file: any) => ({
            language: 'python',
            code: file.content,
            filename: file.path,
            framework,
            description: `Generated ${file.type} file`,
            runnable: file.type === 'python',
          })) || (data.data.code ? [{
            language: 'python',
            code: data.data.code,
            framework,
            description: `Generated ${intent}`,
            runnable: true,
          }] : []),
          actions: data.data.files?.map((file: any, index: number) => ({
            id: `apply-${index}`,
            type: intent,
            title: `Apply ${file.path}`,
            description: `Apply the generated ${file.type} file`,
            framework,
            data: {
              code: file.content,
              filename: file.path,
            },
          })) || [],
        };
        
        setMessages(prev => [...prev, codeMessage]);
      } else {
        throw new Error(data.error?.message || 'Failed to generate code');
      }
    } catch (err) {
      console.error('Error generating code:', err);
      
      // Add error message
      const errorMessage: PythonAssistantMessage = {
        id: `gen-error-${Date.now()}`,
        role: 'assistant',
        content: `❌ Failed to generate code: ${err instanceof Error ? err.message : 'Unknown error'}`,
        timestamp: new Date(),
        framework,
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [framework, projectStructure, userId, workspaceId]);

  const clearConversation = useCallback(() => {
    setMessages([]);
    setError(undefined);
    
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Framework-specific helper methods
  const askFrameworkQuestion = useCallback((question: string) => {
    const frameworkContext = framework ? ` in ${framework}` : '';
    return sendMessage({
      message: `${question}${frameworkContext}`,
      framework,
      projectStructure,
    });
  }, [framework, projectStructure, sendMessage]);

  const explainCode = useCallback((code: string, filename?: string) => {
    return sendMessage({
      message: `Please explain this ${framework || 'Python'} code:\n\n\`\`\`python\n${code}\n\`\`\``,
      framework,
      projectStructure,
      codeContext: {
        file: filename || 'code.py',
        fullCode: code,
      },
    });
  }, [framework, projectStructure, sendMessage]);

  const debugError = useCallback((errorMessage: string, code?: string) => {
    const codeContext = code ? `\n\nCode context:\n\`\`\`python\n${code}\n\`\`\`` : '';
    return sendMessage({
      message: `I'm getting this error in my ${framework || 'Python'} code: ${errorMessage}${codeContext}\n\nCan you help me fix it?`,
      framework,
      projectStructure,
      intent: 'debug-error',
    });
  }, [framework, projectStructure, sendMessage]);

  const requestRefactoring = useCallback((code: string, reason: string) => {
    return sendMessage({
      message: `Please refactor this ${framework || 'Python'} code to ${reason}:\n\n\`\`\`python\n${code}\n\`\`\``,
      framework,
      projectStructure,
      intent: 'refactor-code',
    });
  }, [framework, projectStructure, sendMessage]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    applyAction,
    clearConversation,
    generateCode,
    // Additional Python-specific methods
    askFrameworkQuestion,
    explainCode,
    debugError,
    requestRefactoring,
  };
}
