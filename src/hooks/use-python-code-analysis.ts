'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  PythonAnalysisRequest,
  PythonCodeError,
  PythonRefactoringSuggestion,
  UsePythonCodeAnalysisReturn,
  PythonFramework,
  PythonProjectStructure,
} from '@/types/python-ai-editor';

interface UsePythonCodeAnalysisConfig {
  framework?: PythonFramework;
  pythonVersion?: string;
  projectStructure?: PythonProjectStructure;
  debounceMs?: number;
  autoAnalyze?: boolean;
  checkFrameworkBestPractices?: boolean;
  checkSecurityVulnerabilities?: boolean;
  checkPerformanceIssues?: boolean;
  enableLinting?: boolean;
  userId?: string;
  workspaceId?: string;
}

export function usePythonCodeAnalysis(
  config: UsePythonCodeAnalysisConfig = {}
): UsePythonCodeAnalysisReturn {
  const {
    framework,
    pythonVersion = '3.11',
    projectStructure,
    debounceMs = 1000,
    autoAnalyze = true,
    checkFrameworkBestPractices = true,
    checkSecurityVulnerabilities = true,
    checkPerformanceIssues = true,
    enableLinting = true,
    userId,
    workspaceId,
  } = config;

  const [errors, setErrors] = useState<PythonCodeError[]>([]);
  const [suggestions, setSuggestions] = useState<PythonRefactoringSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const analyzeCode = useCallback(async (request: PythonAnalysisRequest) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        setIsLoading(true);
        setError(undefined);

        // Create abort controller for this request
        abortControllerRef.current = new AbortController();

        // Enhance request with Python-specific context
        const enhancedRequest: PythonAnalysisRequest = {
          ...request,
          framework,
          pythonVersion,
          projectStructure,
          installedPackages: projectStructure?.installedPackages.map(p => p.name) || [],
          checkFrameworkBestPractices,
          checkSecurityVulnerabilities,
          checkPerformanceIssues,
        };

        const response = await fetch('/api/python-workspace/ai/analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...enhancedRequest,
            userId,
            workspaceId,
            enableLinting,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to analyze Python code');
        }

        const data = await response.json();

        if (data.success) {
          setErrors(data.data.errors || []);
          setSuggestions(data.data.suggestions || []);
        } else {
          throw new Error(data.error?.message || 'Failed to analyze Python code');
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was cancelled, ignore
          return;
        }

        console.error('Python code analysis error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setErrors([]);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);
  }, [
    framework,
    pythonVersion,
    projectStructure,
    debounceMs,
    checkFrameworkBestPractices,
    checkSecurityVulnerabilities,
    checkPerformanceIssues,
    enableLinting,
    userId,
    workspaceId,
  ]);

  const applyFix = useCallback(async (errorId: string) => {
    const errorToFix = errors.find(e => e.id === errorId);
    if (!errorToFix || !errorToFix.fixes || errorToFix.fixes.length === 0) {
      return;
    }

    try {
      // Apply the first available fix
      const fix = errorToFix.fixes[0];
      
      // This would be handled by the editor component
      // The fix contains the range and newText to apply
      
      // Remove the error from the list after applying fix
      setErrors(prev => prev.filter(e => e.id !== errorId));
    } catch (err) {
      console.error('Error applying fix:', err);
    }
  }, [errors]);

  const applySuggestion = useCallback(async (suggestionId: string) => {
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (!suggestion) {
      return;
    }

    try {
      // This would be handled by the editor component
      // The suggestion contains the range and afterCode to apply
      
      // Remove the suggestion from the list after applying
      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    } catch (err) {
      console.error('Error applying suggestion:', err);
    }
  }, [suggestions]);

  const dismissError = useCallback((errorId: string) => {
    setErrors(prev => prev.filter(e => e.id !== errorId));
  }, []);

  const dismissSuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  }, []);

  // Auto-analyze code changes
  const handleCodeChange = useCallback((
    code: string,
    language: string,
    fileName?: string
  ) => {
    if (!autoAnalyze || language !== 'python') return;

    analyzeCode({
      code,
      language,
      fileName: fileName || 'untitled.py',
    });
  }, [autoAnalyze, analyzeCode]);

  // Framework-specific analysis
  const analyzeFrameworkCode = useCallback((
    code: string,
    fileName: string,
    analysisType: 'models' | 'views' | 'urls' | 'templates' | 'tests'
  ) => {
    if (!framework) return;

    analyzeCode({
      code,
      language: 'python',
      fileName,
      framework,
      context: {
        analysisType,
        projectStructure,
      },
    });
  }, [framework, projectStructure, analyzeCode]);

  // Security-focused analysis
  const analyzeForSecurity = useCallback((
    code: string,
    fileName: string
  ) => {
    analyzeCode({
      code,
      language: 'python',
      fileName,
      framework,
      checkSecurityVulnerabilities: true,
      checkFrameworkBestPractices: false,
      checkPerformanceIssues: false,
    });
  }, [framework, analyzeCode]);

  // Performance-focused analysis
  const analyzeForPerformance = useCallback((
    code: string,
    fileName: string
  ) => {
    analyzeCode({
      code,
      language: 'python',
      fileName,
      framework,
      checkPerformanceIssues: true,
      checkFrameworkBestPractices: false,
      checkSecurityVulnerabilities: false,
    });
  }, [framework, analyzeCode]);

  // Get errors by category
  const getErrorsByCategory = useCallback((category: PythonCodeError['category']) => {
    return errors.filter(error => error.category === category);
  }, [errors]);

  // Get suggestions by category
  const getSuggestionsByCategory = useCallback((category: PythonRefactoringSuggestion['category']) => {
    return suggestions.filter(suggestion => suggestion.category === category);
  }, [suggestions]);

  // Get framework-specific issues
  const getFrameworkIssues = useCallback(() => {
    return {
      errors: errors.filter(error => error.frameworkSpecific),
      suggestions: suggestions.filter(suggestion => suggestion.framework === framework),
    };
  }, [errors, suggestions, framework]);

  return {
    errors,
    suggestions,
    isLoading,
    error,
    analyzeCode,
    applyFix,
    applySuggestion,
    dismissError,
    // Additional Python-specific methods
    dismissSuggestion,
    handleCodeChange,
    analyzeFrameworkCode,
    analyzeForSecurity,
    analyzeForPerformance,
    getErrorsByCategory,
    getSuggestionsByCategory,
    getFrameworkIssues,
  };
}
