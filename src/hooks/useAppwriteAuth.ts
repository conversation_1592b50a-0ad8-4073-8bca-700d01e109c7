import { useState, useEffect, useCallback } from 'react';
import { UserProfile } from '@/lib/appwrite';

// Re-export for compatibility with existing components
export { useAuth } from '@/contexts/auth-context';

interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

interface LoginParams {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface RegisterParams {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

interface UpdateProfileParams {
  name?: string;
  email?: string;
  phone?: string;
  bio?: string;
  company?: string;
  location?: string;
  website?: string;
}

interface PasswordUpdateParams {
  currentPassword: string;
  newPassword: string;
}

export function useAppwriteAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  });

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const response = await fetch('/api/appwrite/auth', {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthState({
            user: data.data,
            isLoading: false,
            isAuthenticated: true,
            error: null,
          });
        } else {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            error: null,
          });
        }
      } else {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
          error: null,
        });
      }
    } catch (error) {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: 'Failed to check authentication status',
      });
    }
  }, []);

  const login = useCallback(async (params: LoginParams) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch('/api/appwrite/auth?action=login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.success) {
        setAuthState({
          user: data.data.user,
          isLoading: false,
          isAuthenticated: true,
          error: null,
        });
        return { success: true, data: data.data };
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: data.error?.message || 'Login failed',
        }));
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during login';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return { success: false, error: { message: errorMessage } };
    }
  }, []);

  const register = useCallback(async (params: RegisterParams) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch('/api/appwrite/auth?action=register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.success) {
        // After successful registration, log the user in
        const loginResult = await login({
          email: params.email,
          password: params.password,
        });
        return loginResult;
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: data.error?.message || 'Registration failed',
        }));
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during registration';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return { success: false, error: { message: errorMessage } };
    }
  }, [login]);

  const logout = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch('/api/appwrite/auth?action=logout', {
        method: 'POST',
        credentials: 'include',
      });

      // Always clear local state regardless of API response
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
      });

      const data = await response.json();
      return { success: data.success || true };
    } catch (error) {
      // Still clear local state on network error
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null,
      });
      return { success: true }; // Treat as success since we cleared local state
    }
  }, []);

  const updateProfile = useCallback(async (params: UpdateProfileParams) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch('/api/appwrite/auth?action=profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.success) {
        setAuthState(prev => ({
          ...prev,
          user: data.data,
          isLoading: false,
          error: null,
        }));
        return { success: true, data: data.data };
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: data.error?.message || 'Profile update failed',
        }));
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during profile update';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return { success: false, error: { message: errorMessage } };
    }
  }, []);

  const updatePassword = useCallback(async (params: PasswordUpdateParams) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch('/api/appwrite/auth?action=password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.success) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: null,
        }));
        return { success: true };
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: data.error?.message || 'Password update failed',
        }));
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during password update';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return { success: false, error: { message: errorMessage } };
    }
  }, []);

  const sendPasswordReset = useCallback(async (email: string) => {
    try {
      const response = await fetch('/api/appwrite/auth?action=password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          url: `${window.location.origin}/reset-password`,
        }),
      });

      const data = await response.json();
      return { success: data.success, error: data.error };
    } catch (error) {
      return { success: false, error: { message: 'Network error' } };
    }
  }, []);

  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...authState,
    login,
    register,
    logout,
    updateProfile,
    updatePassword,
    sendPasswordReset,
    checkAuthStatus,
    clearError,
  };
}
