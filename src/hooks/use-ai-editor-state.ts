'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  AIEditorState,
  CodeFile,
  CodeSelection,
  CodePosition,
  AICodeEditorConfig,
  UseAIEditorStateReturn,
  AICompletionResponse,
  AIAnalysisResponse,
} from '@/types/ai-code-editor';

interface UseAIEditorStateConfig {
  persistState?: boolean;
  autoSave?: boolean;
  autoSaveInterval?: number;
  userId?: string;
  workspaceId?: string;
}

const DEFAULT_CONFIG: AICodeEditorConfig = {
  language: 'typescript',
  theme: 'dark',
  fontSize: 14,
  tabSize: 2,
  wordWrap: true,
  lineNumbers: true,
  minimap: true,
  autoCompletion: true,
  aiAssistance: true,
  errorAnalysis: true,
  refactoringSuggestions: true,
};

const DEFAULT_STATE: AIEditorState = {
  openFiles: [],
  cursor: { line: 1, column: 1 },
  completions: {
    suggestions: [],
    isLoading: false,
  },
  analysis: {
    errors: [],
    suggestions: [],
    metrics: {
      complexity: 0,
      maintainability: 0,
      readability: 0,
      performance: 0,
      security: 0,
    },
    isLoading: false,
  },
  assistant: {
    isOpen: false,
    messages: [],
    isLoading: false,
  },
  settings: DEFAULT_CONFIG,
};

export function useAIEditorState(
  config: UseAIEditorStateConfig = {}
): UseAIEditorStateReturn {
  const {
    persistState = true,
    autoSave = true,
    autoSaveInterval = 30000, // 30 seconds
    userId,
    workspaceId,
  } = config;

  const [state, setState] = useState<AIEditorState>(DEFAULT_STATE);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const stateRef = useRef(state);

  // Update state ref when state changes
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // Load persisted state on mount
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      const storageKey = `ai-editor-state-${userId || 'anonymous'}-${workspaceId || 'default'}`;
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        try {
          const parsedState = JSON.parse(saved);
          setState(prevState => ({
            ...prevState,
            ...parsedState,
            // Don't persist runtime state
            completions: DEFAULT_STATE.completions,
            analysis: DEFAULT_STATE.analysis,
            assistant: {
              ...parsedState.assistant,
              isLoading: false,
            },
          }));
        } catch (err) {
          console.error('Failed to load editor state:', err);
        }
      }
    }
  }, [persistState, userId, workspaceId]);

  // Persist state when it changes
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      const storageKey = `ai-editor-state-${userId || 'anonymous'}-${workspaceId || 'default'}`;
      const stateToSave = {
        ...state,
        // Don't persist runtime state
        completions: undefined,
        analysis: undefined,
        assistant: {
          ...state.assistant,
          isLoading: false,
        },
      };
      localStorage.setItem(storageKey, JSON.stringify(stateToSave));
    }
  }, [state, persistState, userId, workspaceId]);

  // Auto-save files
  useEffect(() => {
    if (autoSave && state.openFiles.some(file => file.isDirty)) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        const dirtyFiles = state.openFiles.filter(file => file.isDirty);
        dirtyFiles.forEach(file => {
          saveFile(file.id);
        });
      }, autoSaveInterval);
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [state.openFiles, autoSave, autoSaveInterval]);

  // Open a file
  const openFile = useCallback((file: CodeFile) => {
    setState(prev => {
      const existingIndex = prev.openFiles.findIndex(f => f.id === file.id);
      if (existingIndex >= 0) {
        // File already open, just set as active
        return {
          ...prev,
          activeFile: file,
        };
      } else {
        // Add new file
        return {
          ...prev,
          openFiles: [...prev.openFiles, file],
          activeFile: file,
        };
      }
    });
  }, []);

  // Close a file
  const closeFile = useCallback((fileId: string) => {
    setState(prev => {
      const fileToClose = prev.openFiles.find(f => f.id === fileId);
      const remainingFiles = prev.openFiles.filter(f => f.id !== fileId);
      
      // If closing the active file, set a new active file
      let newActiveFile = prev.activeFile;
      if (prev.activeFile?.id === fileId) {
        newActiveFile = remainingFiles.length > 0 ? remainingFiles[0] : undefined;
      }

      return {
        ...prev,
        openFiles: remainingFiles,
        activeFile: newActiveFile,
      };
    });
  }, []);

  // Save a file
  const saveFile = useCallback(async (fileId: string) => {
    const file = stateRef.current.openFiles.find(f => f.id === fileId);
    if (!file) return;

    try {
      // Emit custom event for editor to handle actual saving
      window.dispatchEvent(new CustomEvent('ai-editor-save-file', {
        detail: { file }
      }));

      // Mark file as saved
      setState(prev => ({
        ...prev,
        openFiles: prev.openFiles.map(f =>
          f.id === fileId
            ? { ...f, isDirty: false, lastModified: new Date() }
            : f
        ),
      }));
    } catch (err) {
      console.error('Error saving file:', err);
      throw err;
    }
  }, []);

  // Update file content
  const updateFileContent = useCallback((fileId: string, content: string) => {
    setState(prev => ({
      ...prev,
      openFiles: prev.openFiles.map(f =>
        f.id === fileId
          ? { ...f, content, isDirty: true, size: content.length }
          : f
      ),
      activeFile: prev.activeFile?.id === fileId
        ? { ...prev.activeFile, content, isDirty: true, size: content.length }
        : prev.activeFile,
    }));
  }, []);

  // Set selection
  const setSelection = useCallback((selection: CodeSelection) => {
    setState(prev => ({
      ...prev,
      selection,
    }));
  }, []);

  // Set cursor position
  const setCursor = useCallback((position: CodePosition) => {
    setState(prev => ({
      ...prev,
      cursor: position,
    }));
  }, []);

  // Update settings
  const updateSettings = useCallback((settings: Partial<AICodeEditorConfig>) => {
    setState(prev => ({
      ...prev,
      settings: { ...prev.settings, ...settings },
    }));
  }, []);

  // Toggle assistant panel
  const toggleAssistant = useCallback(() => {
    setState(prev => ({
      ...prev,
      assistant: {
        ...prev.assistant,
        isOpen: !prev.assistant.isOpen,
      },
    }));
  }, []);

  // Update completions
  const updateCompletions = useCallback((completions: Partial<AICompletionResponse>) => {
    setState(prev => ({
      ...prev,
      completions: { ...prev.completions, ...completions },
    }));
  }, []);

  // Update analysis
  const updateAnalysis = useCallback((analysis: Partial<AIAnalysisResponse>) => {
    setState(prev => ({
      ...prev,
      analysis: { ...prev.analysis, ...analysis },
    }));
  }, []);

  // Update assistant messages
  const updateAssistant = useCallback((updates: Partial<typeof state.assistant>) => {
    setState(prev => ({
      ...prev,
      assistant: { ...prev.assistant, ...updates },
    }));
  }, []);

  // Get file by ID
  const getFileById = useCallback((fileId: string) => {
    return state.openFiles.find(f => f.id === fileId);
  }, [state.openFiles]);

  // Get dirty files
  const getDirtyFiles = useCallback(() => {
    return state.openFiles.filter(f => f.isDirty);
  }, [state.openFiles]);

  // Reset state
  const resetState = useCallback(() => {
    setState(DEFAULT_STATE);
    
    // Clear persisted state
    if (persistState && typeof window !== 'undefined') {
      const storageKey = `ai-editor-state-${userId || 'anonymous'}-${workspaceId || 'default'}`;
      localStorage.removeItem(storageKey);
    }
  }, [persistState, userId, workspaceId]);

  return {
    state,
    actions: {
      openFile,
      closeFile,
      saveFile,
      updateFileContent,
      setSelection,
      setCursor,
      updateSettings,
      toggleAssistant,
    },
    updateCompletions,
    updateAnalysis,
    updateAssistant,
    getFileById,
    getDirtyFiles,
    resetState,
  };
}

// Extended return type with additional methods
interface ExtendedUseAIEditorStateReturn extends UseAIEditorStateReturn {
  updateCompletions: (completions: Partial<AICompletionResponse>) => void;
  updateAnalysis: (analysis: Partial<AIAnalysisResponse>) => void;
  updateAssistant: (updates: Partial<AIEditorState['assistant']>) => void;
  getFileById: (fileId: string) => CodeFile | undefined;
  getDirtyFiles: () => CodeFile[];
  resetState: () => void;
}
