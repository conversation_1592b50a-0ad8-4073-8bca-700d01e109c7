'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  AICompletionRequest,
  AICompletionSuggestion,
  UseAICodeCompletionReturn,
} from '@/types/ai-code-editor';

interface UseAICodeCompletionConfig {
  debounceMs?: number;
  maxSuggestions?: number;
  autoTrigger?: boolean;
  triggerCharacters?: string[];
  userId?: string;
  workspaceId?: string;
}

export function useAICodeCompletion(
  config: UseAICodeCompletionConfig = {}
): UseAICodeCompletionReturn {
  const {
    debounceMs = 300,
    maxSuggestions = 5,
    autoTrigger = true,
    triggerCharacters = ['.', '(', ' ', '\n'],
    userId,
    workspaceId,
  } = config;

  const [suggestions, setSuggestions] = useState<AICompletionSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const lastRequestRef = useRef<string>('');

  // Request completion suggestions
  const requestCompletion = useCallback(async (request: AICompletionRequest) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Create request key for deduplication
    const requestKey = JSON.stringify({
      code: request.code,
      position: request.position,
      language: request.language,
    });

    // Skip if same request
    if (requestKey === lastRequestRef.current) {
      return;
    }

    lastRequestRef.current = requestKey;

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        setIsLoading(true);
        setError(undefined);

        // Create abort controller for this request
        abortControllerRef.current = new AbortController();

        const response = await fetch('/api/ai-code/completion', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...request,
            userId,
            workspaceId,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to get completions');
        }

        const data = await response.json();

        if (data.success) {
          // Limit suggestions and sort by priority
          const limitedSuggestions = data.data.suggestions
            .sort((a: AICompletionSuggestion, b: AICompletionSuggestion) => 
              b.priority - a.priority || b.confidence - a.confidence
            )
            .slice(0, maxSuggestions);

          setSuggestions(limitedSuggestions);
        } else {
          throw new Error(data.error?.message || 'Failed to get completions');
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was cancelled, ignore
          return;
        }

        console.error('Code completion error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);
  }, [debounceMs, maxSuggestions, userId, workspaceId]);

  // Apply a suggestion
  const applySuggestion = useCallback((suggestion: AICompletionSuggestion) => {
    // This would typically be handled by the editor component
    // For now, we'll just clear suggestions
    setSuggestions([]);
    setError(undefined);

    // Emit custom event for editor to handle
    window.dispatchEvent(new CustomEvent('ai-completion-applied', {
      detail: { suggestion }
    }));
  }, []);

  // Dismiss suggestions
  const dismissSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(undefined);
    
    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Clear timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, []);

  // Auto-trigger completion based on typing
  const handleTextChange = useCallback((
    code: string,
    position: { line: number; column: number },
    language: string,
    lastChar?: string
  ) => {
    if (!autoTrigger) return;

    // Check if we should trigger completion
    const shouldTrigger = !lastChar || triggerCharacters.includes(lastChar);
    
    if (shouldTrigger) {
      requestCompletion({
        code,
        position,
        language,
        context: {
          fileName: undefined, // Would be provided by editor
          projectContext: undefined,
          recentChanges: [],
        },
      });
    }
  }, [autoTrigger, triggerCharacters, requestCompletion]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    suggestions,
    isLoading,
    error,
    requestCompletion,
    applySuggestion,
    dismissSuggestions,
    handleTextChange,
  };
}

// Extended return type with additional methods
interface ExtendedUseAICodeCompletionReturn extends UseAICodeCompletionReturn {
  handleTextChange: (
    code: string,
    position: { line: number; column: number },
    language: string,
    lastChar?: string
  ) => void;
}
