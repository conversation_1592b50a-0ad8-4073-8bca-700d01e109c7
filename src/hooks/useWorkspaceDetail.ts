/**
 * Individual Workspace Detail Hook
 * Provides detailed workspace management for a specific workspace
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Workspace,
  UpdateWorkspaceRequest,
  ShareWorkspaceRequest,
  WorkspaceStatus,
  WorkspaceCollaborator,
  WorkspaceStats
} from '@/types/workspace';

// API functions for individual workspace
const workspaceDetailApi = {
  // Get workspace by ID
  getWorkspace: async (id: string): Promise<Workspace> => {
    const response = await fetch(`/api/workspace/${id}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch workspace');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Update workspace
  updateWorkspace: async (id: string, data: UpdateWorkspaceRequest): Promise<Workspace> => {
    const response = await fetch(`/api/workspace/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update workspace');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Update workspace status
  updateStatus: async (id: string, status: WorkspaceStatus): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update workspace status');
    }
  },

  // Share workspace
  shareWorkspace: async (id: string, data: ShareWorkspaceRequest): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=share`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to share workspace');
    }
  },

  // Remove collaborator
  removeCollaborator: async (id: string, userId: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=remove-collaborator`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to remove collaborator');
    }
  },

  // Start workspace
  startWorkspace: async (id: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=start`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to start workspace');
    }
  },

  // Stop workspace
  stopWorkspace: async (id: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=stop`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to stop workspace');
    }
  },

  // Restart workspace
  restartWorkspace: async (id: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=restart`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to restart workspace');
    }
  },
};

// Hook parameters
export interface UseWorkspaceDetailParams {
  workspaceId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onStatusChange?: (status: WorkspaceStatus) => void;
  onError?: (error: Error) => void;
}

export interface UseWorkspaceDetailReturn {
  // Data
  workspace: Workspace | null;
  collaborators: WorkspaceCollaborator[];
  stats: WorkspaceStats | null;
  
  // Loading states
  isLoading: boolean;
  isUpdating: boolean;
  isStarting: boolean;
  isStopping: boolean;
  isRestarting: boolean;
  isSharing: boolean;
  
  // Error states
  error: Error | null;
  
  // Actions
  updateWorkspace: (data: UpdateWorkspaceRequest) => Promise<Workspace>;
  shareWorkspace: (data: ShareWorkspaceRequest) => Promise<void>;
  removeCollaborator: (userId: string) => Promise<void>;
  startWorkspace: () => Promise<void>;
  stopWorkspace: () => Promise<void>;
  restartWorkspace: () => Promise<void>;
  updateStatus: (status: WorkspaceStatus) => Promise<void>;
  
  // Utilities
  refetch: () => void;
  isOwner: boolean;
  canEdit: boolean;
  canExecute: boolean;
  canShare: boolean;
  
  // Status helpers
  isActive: boolean;
  isStopped: boolean;
  isCreating: boolean;
  hasError: boolean;
}

// Main hook
export function useWorkspaceDetail(params: UseWorkspaceDetailParams): UseWorkspaceDetailReturn {
  const { workspaceId, autoRefresh = true, refreshInterval = 10000, onStatusChange, onError } = params;
  const queryClient = useQueryClient();
  const [previousStatus, setPreviousStatus] = useState<WorkspaceStatus | null>(null);

  // Query for workspace details
  const {
    data: workspace,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['workspace', workspaceId],
    queryFn: () => workspaceDetailApi.getWorkspace(workspaceId),
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 5000, // 5 seconds
    enabled: !!workspaceId,
  });

  // Handle status changes
  useEffect(() => {
    if (workspace && workspace.status !== previousStatus) {
      if (previousStatus !== null && onStatusChange) {
        onStatusChange(workspace.status);
      }
      setPreviousStatus(workspace.status);
    }
  }, [workspace?.status, previousStatus, onStatusChange]);

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(error as Error);
    }
  }, [error, onError]);

  // Update workspace mutation
  const updateMutation = useMutation({
    mutationFn: (data: UpdateWorkspaceRequest) =>
      workspaceDetailApi.updateWorkspace(workspaceId, data),
    onSuccess: (updatedWorkspace) => {
      queryClient.setQueryData(['workspace', workspaceId], updatedWorkspace);
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success(`Workspace "${updatedWorkspace.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update workspace: ${error.message}`);
    },
  });

  // Share workspace mutation
  const shareMutation = useMutation({
    mutationFn: (data: ShareWorkspaceRequest) =>
      workspaceDetailApi.shareWorkspace(workspaceId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Workspace shared successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to share workspace: ${error.message}`);
    },
  });

  // Remove collaborator mutation
  const removeCollaboratorMutation = useMutation({
    mutationFn: (userId: string) =>
      workspaceDetailApi.removeCollaborator(workspaceId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Collaborator removed successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to remove collaborator: ${error.message}`);
    },
  });

  // Start workspace mutation
  const startMutation = useMutation({
    mutationFn: () => workspaceDetailApi.startWorkspace(workspaceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Workspace started successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to start workspace: ${error.message}`);
    },
  });

  // Stop workspace mutation
  const stopMutation = useMutation({
    mutationFn: () => workspaceDetailApi.stopWorkspace(workspaceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Workspace stopped successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to stop workspace: ${error.message}`);
    },
  });

  // Restart workspace mutation
  const restartMutation = useMutation({
    mutationFn: () => workspaceDetailApi.restartWorkspace(workspaceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Workspace restarted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to restart workspace: ${error.message}`);
    },
  });

  // Update status mutation
  const updateStatusMutation = useMutation({
    mutationFn: (status: WorkspaceStatus) =>
      workspaceDetailApi.updateStatus(workspaceId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update workspace status: ${error.message}`);
    },
  });

  // Action handlers
  const handleUpdateWorkspace = useCallback(
    async (data: UpdateWorkspaceRequest) => {
      return updateMutation.mutateAsync(data);
    },
    [updateMutation]
  );

  const handleShareWorkspace = useCallback(
    async (data: ShareWorkspaceRequest) => {
      return shareMutation.mutateAsync(data);
    },
    [shareMutation]
  );

  const handleRemoveCollaborator = useCallback(
    async (userId: string) => {
      return removeCollaboratorMutation.mutateAsync(userId);
    },
    [removeCollaboratorMutation]
  );

  const handleStartWorkspace = useCallback(
    async () => {
      return startMutation.mutateAsync();
    },
    [startMutation]
  );

  const handleStopWorkspace = useCallback(
    async () => {
      return stopMutation.mutateAsync();
    },
    [stopMutation]
  );

  const handleRestartWorkspace = useCallback(
    async () => {
      return restartMutation.mutateAsync();
    },
    [restartMutation]
  );

  const handleUpdateStatus = useCallback(
    async (status: WorkspaceStatus) => {
      return updateStatusMutation.mutateAsync(status);
    },
    [updateStatusMutation]
  );

  // Computed values
  const collaborators = workspace?.collaborators || [];
  const stats = workspace?.stats || null;
  
  // Permission checks (would need to be implemented based on user context)
  const isOwner = true; // TODO: Check if current user is owner
  const canEdit = true; // TODO: Check edit permissions
  const canExecute = true; // TODO: Check execute permissions
  const canShare = isOwner; // Only owners can share by default
  
  // Status helpers
  const isActive = workspace?.status === 'active';
  const isStopped = workspace?.status === 'stopped';
  const isCreating = workspace?.status === 'creating';
  const hasError = workspace?.status === 'error';

  return {
    // Data
    workspace: workspace || null,
    collaborators,
    stats,
    
    // Loading states
    isLoading,
    isUpdating: updateMutation.isPending,
    isStarting: startMutation.isPending,
    isStopping: stopMutation.isPending,
    isRestarting: restartMutation.isPending,
    isSharing: shareMutation.isPending,
    
    // Error states
    error: error as Error | null,
    
    // Actions
    updateWorkspace: handleUpdateWorkspace,
    shareWorkspace: handleShareWorkspace,
    removeCollaborator: handleRemoveCollaborator,
    startWorkspace: handleStartWorkspace,
    stopWorkspace: handleStopWorkspace,
    restartWorkspace: handleRestartWorkspace,
    updateStatus: handleUpdateStatus,
    
    // Utilities
    refetch,
    isOwner,
    canEdit,
    canExecute,
    canShare,
    
    // Status helpers
    isActive,
    isStopped,
    isCreating,
    hasError,
  };
}
