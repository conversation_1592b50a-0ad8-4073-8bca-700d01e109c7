/**
 * Workspace Templates Hook
 * Provides template management and selection functionality
 */

import { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  WorkspaceTemplate,
  TemplateReview,
  WorkspaceType,
  PaginationParams
} from '@/types/workspace';

// API functions for workspace templates
const workspaceTemplatesApi = {
  // List templates with filtering
  listTemplates: async (params?: {
    type?: WorkspaceType[];
    category?: string[];
    visibility?: ('public' | 'private' | 'team')[];
    tags?: string[];
    search?: string;
    authorId?: string;
    minRating?: number;
    pagination?: PaginationParams;
  }) => {
    const searchParams = new URLSearchParams();
    
    if (params?.type?.length) searchParams.set('type', params.type[0]);
    if (params?.category?.length) searchParams.set('category', params.category[0]);
    if (params?.visibility?.length) searchParams.set('visibility', params.visibility[0]);
    if (params?.tags?.length) searchParams.set('tags', params.tags.join(','));
    if (params?.search) searchParams.set('search', params.search);
    if (params?.authorId) searchParams.set('authorId', params.authorId);
    if (params?.minRating) searchParams.set('minRating', params.minRating.toString());
    if (params?.pagination) {
      searchParams.set('page', (params.pagination.offset / params.pagination.limit + 1).toString());
      searchParams.set('limit', params.pagination.limit.toString());
    }

    const response = await fetch(`/api/workspace-templates?${searchParams}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch templates');
    }
    
    return response.json();
  },

  // Get template by ID
  getTemplate: async (id: string): Promise<WorkspaceTemplate> => {
    const response = await fetch(`/api/workspace-templates/${id}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch template');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Create template
  createTemplate: async (data: {
    name: string;
    description: string;
    type: WorkspaceType;
    category: string;
    configuration: any;
    files: any[];
    structure: any;
    version: string;
    visibility?: 'public' | 'private' | 'team';
    tags?: string[];
    requirements?: any;
    screenshots?: string[];
    demoUrl?: string;
    installScript?: string;
    setupInstructions?: string;
  }): Promise<WorkspaceTemplate> => {
    const response = await fetch('/api/workspace-templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to create template');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Update template
  updateTemplate: async (id: string, data: Partial<WorkspaceTemplate>): Promise<WorkspaceTemplate> => {
    const response = await fetch('/api/workspace-templates', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ templateId: id, ...data }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update template');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Delete template
  deleteTemplate: async (id: string): Promise<void> => {
    const response = await fetch(`/api/workspace-templates?id=${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to delete template');
    }
  },

  // Add review
  addReview: async (templateId: string, rating: number, comment: string): Promise<void> => {
    const response = await fetch(`/api/workspace-templates/${templateId}/reviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ rating, comment }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to add review');
    }
  },

  // Increment usage count
  incrementUsage: async (templateId: string): Promise<void> => {
    const response = await fetch(`/api/workspace-templates/${templateId}/usage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to increment usage');
    }
  },
};

// Hook parameters
export interface UseWorkspaceTemplatesParams {
  type?: WorkspaceType[];
  category?: string[];
  visibility?: ('public' | 'private' | 'team')[];
  tags?: string[];
  search?: string;
  authorId?: string;
  minRating?: number;
  pagination?: PaginationParams;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseWorkspaceTemplatesReturn {
  // Data
  templates: WorkspaceTemplate[];
  template: WorkspaceTemplate | null;
  total: number;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  
  // Error states
  error: Error | null;
  
  // Actions
  createTemplate: (data: Parameters<typeof workspaceTemplatesApi.createTemplate>[0]) => Promise<WorkspaceTemplate>;
  updateTemplate: (id: string, data: Partial<WorkspaceTemplate>) => Promise<WorkspaceTemplate>;
  deleteTemplate: (id: string) => Promise<void>;
  addReview: (templateId: string, rating: number, comment: string) => Promise<void>;
  incrementUsage: (templateId: string) => Promise<void>;
  
  // Utilities
  refetch: () => void;
  setFilters: (filters: Partial<UseWorkspaceTemplatesParams>) => void;
  setPagination: (pagination: PaginationParams) => void;
  getTemplate: (id: string) => WorkspaceTemplate | undefined;
  
  // Computed values
  filteredTemplates: WorkspaceTemplate[];
  popularTemplates: WorkspaceTemplate[];
  recentTemplates: WorkspaceTemplate[];
  categorizedTemplates: Record<string, WorkspaceTemplate[]>;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  
  // Template helpers
  getTemplatesByType: (type: WorkspaceType) => WorkspaceTemplate[];
  getTemplatesByCategory: (category: string) => WorkspaceTemplate[];
  searchTemplates: (query: string) => WorkspaceTemplate[];
}

// Main hook
export function useWorkspaceTemplates(params: UseWorkspaceTemplatesParams = {}): UseWorkspaceTemplatesReturn {
  const {
    type,
    category,
    visibility,
    tags,
    search: initialSearch = '',
    authorId,
    minRating,
    pagination: initialPagination = { limit: 20, offset: 0 },
    autoRefresh = false,
    refreshInterval = 60000, // 1 minute
  } = params;

  const queryClient = useQueryClient();
  const [filters, setFilters] = useState({
    type,
    category,
    visibility,
    tags,
    search: initialSearch,
    authorId,
    minRating,
  });
  const [pagination, setPagination] = useState(initialPagination);

  // Query for templates list
  const {
    data: templatesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['workspace-templates', filters, pagination],
    queryFn: () => workspaceTemplatesApi.listTemplates({ ...filters, pagination }),
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 30000, // 30 seconds
  });

  // Create template mutation
  const createMutation = useMutation({
    mutationFn: workspaceTemplatesApi.createTemplate,
    onSuccess: (newTemplate) => {
      queryClient.invalidateQueries({ queryKey: ['workspace-templates'] });
      toast.success(`Template "${newTemplate.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create template: ${error.message}`);
    },
  });

  // Update template mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<WorkspaceTemplate> }) =>
      workspaceTemplatesApi.updateTemplate(id, data),
    onSuccess: (updatedTemplate) => {
      queryClient.invalidateQueries({ queryKey: ['workspace-templates'] });
      queryClient.setQueryData(['workspace-template', updatedTemplate.id], updatedTemplate);
      toast.success(`Template "${updatedTemplate.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update template: ${error.message}`);
    },
  });

  // Delete template mutation
  const deleteMutation = useMutation({
    mutationFn: workspaceTemplatesApi.deleteTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-templates'] });
      toast.success('Template deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete template: ${error.message}`);
    },
  });

  // Add review mutation
  const addReviewMutation = useMutation({
    mutationFn: ({ templateId, rating, comment }: { templateId: string; rating: number; comment: string }) =>
      workspaceTemplatesApi.addReview(templateId, rating, comment),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-templates'] });
      toast.success('Review added successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to add review: ${error.message}`);
    },
  });

  // Increment usage mutation
  const incrementUsageMutation = useMutation({
    mutationFn: workspaceTemplatesApi.incrementUsage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspace-templates'] });
    },
    onError: (error: Error) => {
      console.error('Failed to increment template usage:', error);
    },
  });

  // Computed values
  const templates = templatesData?.data?.templates || [];
  const total = templatesData?.data?.total || 0;
  const template = templates.length === 1 ? templates[0] : null;

  const filteredTemplates = useMemo(() => {
    return templates.filter((template) => {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          template.name.toLowerCase().includes(searchLower) ||
          template.description.toLowerCase().includes(searchLower) ||
          template.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }
      return true;
    });
  }, [templates, filters.search]);

  const popularTemplates = useMemo(() => {
    return [...templates]
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 10);
  }, [templates]);

  const recentTemplates = useMemo(() => {
    return [...templates]
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 10);
  }, [templates]);

  const categorizedTemplates = useMemo(() => {
    const categories: Record<string, WorkspaceTemplate[]> = {};
    templates.forEach((template) => {
      if (!categories[template.category]) {
        categories[template.category] = [];
      }
      categories[template.category].push(template);
    });
    return categories;
  }, [templates]);

  const hasNextPage = pagination.offset + pagination.limit < total;
  const hasPreviousPage = pagination.offset > 0;

  // Action handlers
  const handleCreateTemplate = useCallback(
    async (data: Parameters<typeof workspaceTemplatesApi.createTemplate>[0]) => {
      return createMutation.mutateAsync(data);
    },
    [createMutation]
  );

  const handleUpdateTemplate = useCallback(
    async (id: string, data: Partial<WorkspaceTemplate>) => {
      return updateMutation.mutateAsync({ id, data });
    },
    [updateMutation]
  );

  const handleDeleteTemplate = useCallback(
    async (id: string) => {
      return deleteMutation.mutateAsync(id);
    },
    [deleteMutation]
  );

  const handleAddReview = useCallback(
    async (templateId: string, rating: number, comment: string) => {
      return addReviewMutation.mutateAsync({ templateId, rating, comment });
    },
    [addReviewMutation]
  );

  const handleIncrementUsage = useCallback(
    async (templateId: string) => {
      return incrementUsageMutation.mutateAsync(templateId);
    },
    [incrementUsageMutation]
  );

  const getTemplate = useCallback(
    (id: string) => {
      return templates.find(template => template.id === id);
    },
    [templates]
  );

  // Template helper functions
  const getTemplatesByType = useCallback(
    (type: WorkspaceType) => {
      return templates.filter(template => template.type === type);
    },
    [templates]
  );

  const getTemplatesByCategory = useCallback(
    (category: string) => {
      return templates.filter(template => template.category === category);
    },
    [templates]
  );

  const searchTemplates = useCallback(
    (query: string) => {
      const queryLower = query.toLowerCase();
      return templates.filter(template =>
        template.name.toLowerCase().includes(queryLower) ||
        template.description.toLowerCase().includes(queryLower) ||
        template.tags.some(tag => tag.toLowerCase().includes(queryLower))
      );
    },
    [templates]
  );

  return {
    // Data
    templates,
    template,
    total,
    
    // Loading states
    isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    
    // Error states
    error: error as Error | null,
    
    // Actions
    createTemplate: handleCreateTemplate,
    updateTemplate: handleUpdateTemplate,
    deleteTemplate: handleDeleteTemplate,
    addReview: handleAddReview,
    incrementUsage: handleIncrementUsage,
    
    // Utilities
    refetch,
    setFilters,
    setPagination,
    getTemplate,
    
    // Computed values
    filteredTemplates,
    popularTemplates,
    recentTemplates,
    categorizedTemplates,
    hasNextPage,
    hasPreviousPage,
    
    // Template helpers
    getTemplatesByType,
    getTemplatesByCategory,
    searchTemplates,
  };
}
