'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  PythonCompletionRequest,
  PythonCompletionSuggestion,
  UsePythonCodeCompletionReturn,
  PythonFramework,
  PythonProjectStructure,
} from '@/types/python-ai-editor';

interface UsePythonCodeCompletionConfig {
  framework?: PythonFramework;
  pythonVersion?: string;
  projectStructure?: PythonProjectStructure;
  debounceMs?: number;
  maxSuggestions?: number;
  autoTrigger?: boolean;
  triggerCharacters?: string[];
  enableFrameworkCompletion?: boolean;
  enableImportSuggestions?: boolean;
  userId?: string;
  workspaceId?: string;
}

export function usePythonCodeCompletion(
  config: UsePythonCodeCompletionConfig = {}
): UsePythonCodeCompletionReturn {
  const {
    framework,
    pythonVersion = '3.11',
    projectStructure,
    debounceMs = 300,
    maxSuggestions = 8,
    autoTrigger = true,
    triggerCharacters = ['.', '(', ' ', '\n', ':', '='],
    enableFrameworkCompletion = true,
    enableImportSuggestions = true,
    userId,
    workspaceId,
  } = config;

  const [suggestions, setSuggestions] = useState<PythonCompletionSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const requestCompletion = useCallback(async (request: PythonCompletionRequest) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        setIsLoading(true);
        setError(undefined);

        // Create abort controller for this request
        abortControllerRef.current = new AbortController();

        // Enhance request with Python-specific context
        const enhancedRequest: PythonCompletionRequest = {
          ...request,
          framework,
          pythonVersion,
          projectStructure,
          installedPackages: projectStructure?.installedPackages.map(p => p.name) || [],
        };

        const response = await fetch('/api/python-workspace/ai/completion', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...enhancedRequest,
            userId,
            workspaceId,
            enableFrameworkCompletion,
            enableImportSuggestions,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to get Python completions');
        }

        const data = await response.json();

        if (data.success) {
          // Filter and sort suggestions
          let filteredSuggestions = data.data.suggestions;

          // Prioritize framework-specific suggestions if framework is set
          if (framework && enableFrameworkCompletion) {
            filteredSuggestions = filteredSuggestions.sort((a: PythonCompletionSuggestion, b: PythonCompletionSuggestion) => {
              if (a.isFrameworkSpecific && !b.isFrameworkSpecific) return -1;
              if (!a.isFrameworkSpecific && b.isFrameworkSpecific) return 1;
              return b.priority - a.priority || b.confidence - a.confidence;
            });
          }

          // Limit suggestions
          const limitedSuggestions = filteredSuggestions.slice(0, maxSuggestions);

          setSuggestions(limitedSuggestions);
        } else {
          throw new Error(data.error?.message || 'Failed to get Python completions');
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was cancelled, ignore
          return;
        }

        console.error('Python code completion error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);
  }, [
    framework,
    pythonVersion,
    projectStructure,
    debounceMs,
    maxSuggestions,
    enableFrameworkCompletion,
    enableImportSuggestions,
    userId,
    workspaceId,
  ]);

  const applySuggestion = useCallback((suggestion: PythonCompletionSuggestion) => {
    // This would be handled by the editor component
    // The suggestion contains the insertText to apply
    setSuggestions([]);
  }, []);

  const dismissSuggestions = useCallback(() => {
    setSuggestions([]);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  const installPackage = useCallback(async (packageName: string) => {
    try {
      if (!workspaceId) {
        throw new Error('Workspace ID is required to install packages');
      }

      const response = await fetch(`/api/python-workspace/workspaces/${workspaceId}/packages/install`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageName,
          packageManager: projectStructure?.packageManager || 'pip',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to install package');
      }

      // Package installed successfully
      // TODO: Update project structure with new package
    } catch (err) {
      console.error('Error installing package:', err);
      throw err;
    }
  }, [workspaceId, projectStructure?.packageManager]);

  // Auto-trigger completion based on typing
  const handleTextChange = useCallback((
    code: string,
    position: { line: number; column: number },
    language: string,
    lastChar?: string
  ) => {
    if (!autoTrigger || language !== 'python') return;

    // Check if we should trigger completion
    const shouldTrigger = !lastChar || triggerCharacters.includes(lastChar);
    
    if (shouldTrigger) {
      requestCompletion({
        code,
        position,
        language,
        context: {
          fileName: undefined, // Would be provided by editor
          projectContext: projectStructure,
          recentChanges: [],
        },
      });
    }
  }, [autoTrigger, triggerCharacters, requestCompletion, projectStructure]);

  // Framework-specific completion triggers
  const triggerFrameworkCompletion = useCallback((
    code: string,
    position: { line: number; column: number },
    intent: 'import' | 'model' | 'view' | 'api' | 'test'
  ) => {
    if (!framework || !enableFrameworkCompletion) return;

    requestCompletion({
      code,
      position,
      language: 'python',
      framework,
      context: {
        fileName: undefined,
        projectContext: projectStructure,
        intent,
      },
    });
  }, [framework, enableFrameworkCompletion, requestCompletion, projectStructure]);

  return {
    suggestions,
    isLoading,
    error,
    requestCompletion,
    applySuggestion,
    dismissSuggestions,
    installPackage,
    // Additional Python-specific methods
    handleTextChange,
    triggerFrameworkCompletion,
  };
}
