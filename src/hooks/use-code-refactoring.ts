'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  RefactoringSuggestion,
  UseCodeRefactoringReturn,
  CodeEdit,
} from '@/types/ai-code-editor';

interface UseCodeRefactoringConfig {
  debounceMs?: number;
  autoSuggest?: boolean;
  maxSuggestions?: number;
  userId?: string;
  workspaceId?: string;
}

export function useCodeRefactoring(
  config: UseCodeRefactoringConfig = {}
): UseCodeRefactoringReturn {
  const {
    debounceMs = 2000,
    autoSuggest = false,
    maxSuggestions = 10,
    userId,
    workspaceId,
  } = config;

  const [suggestions, setSuggestions] = useState<RefactoringSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const lastRequestRef = useRef<string>('');

  // Get refactoring suggestions
  const getSuggestions = useCallback(async (code: string, language: string) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Create request key for deduplication
    const requestKey = JSON.stringify({ code, language });

    // Skip if same request
    if (requestKey === lastRequestRef.current) {
      return;
    }

    lastRequestRef.current = requestKey;

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        setIsLoading(true);
        setError(undefined);

        // Create abort controller for this request
        abortControllerRef.current = new AbortController();

        const response = await fetch('/api/ai-code/analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code,
            language,
            includeStyle: true,
            includePerformance: true,
            includeSecurity: false, // Focus on refactoring, not security
            userId,
            workspaceId,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to get refactoring suggestions');
        }

        const data = await response.json();

        if (data.success) {
          // Filter and sort suggestions
          const refactoringSuggestions = (data.data.suggestions || [])
            .filter((suggestion: RefactoringSuggestion) => 
              suggestion.type !== 'style' || suggestion.confidence > 0.7
            )
            .sort((a: RefactoringSuggestion, b: RefactoringSuggestion) => 
              b.confidence - a.confidence
            )
            .slice(0, maxSuggestions);

          setSuggestions(refactoringSuggestions);
        } else {
          throw new Error(data.error?.message || 'Failed to get refactoring suggestions');
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          // Request was cancelled, ignore
          return;
        }

        console.error('Refactoring suggestions error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);
  }, [debounceMs, maxSuggestions, userId, workspaceId]);

  // Apply a refactoring suggestion
  const applySuggestion = useCallback(async (suggestion: RefactoringSuggestion) => {
    try {
      // Emit custom event for editor to handle
      window.dispatchEvent(new CustomEvent('ai-refactoring-applied', {
        detail: { suggestion }
      }));

      // Remove the applied suggestion
      setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));

      // Optionally, re-analyze the code after refactoring
      // This would be handled by the editor component
    } catch (err) {
      console.error('Error applying refactoring:', err);
      setError(err instanceof Error ? err.message : 'Failed to apply refactoring');
    }
  }, []);

  // Preview a refactoring suggestion
  const previewSuggestion = useCallback((suggestion: RefactoringSuggestion): string => {
    return suggestion.preview || suggestion.edits.map(edit => edit.newText).join('\n');
  }, []);

  // Auto-suggest refactoring on code changes
  const handleCodeChange = useCallback((code: string, language: string) => {
    if (!autoSuggest) return;

    // Only suggest for substantial code (avoid triggering on small changes)
    if (code.length < 100) return;

    getSuggestions(code, language);
  }, [autoSuggest, getSuggestions]);

  // Get suggestions by type
  const getSuggestionsByType = useCallback((type: RefactoringSuggestion['type']) => {
    return suggestions.filter(suggestion => suggestion.type === type);
  }, [suggestions]);

  // Get suggestions by impact
  const getSuggestionsByImpact = useCallback((impact: RefactoringSuggestion['impact']) => {
    return suggestions.filter(suggestion => suggestion.impact === impact);
  }, [suggestions]);

  // Get high-confidence suggestions
  const getHighConfidenceSuggestions = useCallback((threshold: number = 0.8) => {
    return suggestions.filter(suggestion => suggestion.confidence >= threshold);
  }, [suggestions]);

  // Dismiss a suggestion
  const dismissSuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  }, []);

  // Clear all suggestions
  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(undefined);

    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Clear timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  }, []);

  // Get refactoring statistics
  const getRefactoringStats = useCallback(() => {
    const byType = suggestions.reduce((acc, suggestion) => {
      acc[suggestion.type] = (acc[suggestion.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byImpact = suggestions.reduce((acc, suggestion) => {
      acc[suggestion.impact] = (acc[suggestion.impact] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageConfidence = suggestions.length > 0
      ? suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length
      : 0;

    return {
      total: suggestions.length,
      byType,
      byImpact,
      averageConfidence: Math.round(averageConfidence * 100) / 100,
      highConfidence: suggestions.filter(s => s.confidence >= 0.8).length,
    };
  }, [suggestions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    suggestions,
    isLoading,
    error,
    getSuggestions,
    applySuggestion,
    previewSuggestion,
    handleCodeChange,
    getSuggestionsByType,
    getSuggestionsByImpact,
    getHighConfidenceSuggestions,
    dismissSuggestion,
    clearSuggestions,
    getRefactoringStats,
  };
}

// Extended return type with additional methods
interface ExtendedUseCodeRefactoringReturn extends UseCodeRefactoringReturn {
  handleCodeChange: (code: string, language: string) => void;
  getSuggestionsByType: (type: RefactoringSuggestion['type']) => RefactoringSuggestion[];
  getSuggestionsByImpact: (impact: RefactoringSuggestion['impact']) => RefactoringSuggestion[];
  getHighConfidenceSuggestions: (threshold?: number) => RefactoringSuggestion[];
  dismissSuggestion: (suggestionId: string) => void;
  clearSuggestions: () => void;
  getRefactoringStats: () => {
    total: number;
    byType: Record<string, number>;
    byImpact: Record<string, number>;
    averageConfidence: number;
    highConfidence: number;
  };
}
