import { useState, useCallback } from 'react';

interface DatabaseState {
  isLoading: boolean;
  error: string | null;
}

interface PaginationParams {
  limit?: number;
  offset?: number;
  cursor?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

interface QueryParams {
  filters?: Record<string, any>;
  search?: string;
  pagination?: PaginationParams;
}

interface CreateDocumentParams {
  databaseId: string;
  collectionId: string;
  documentId?: string;
  data: Record<string, any>;
  permissions?: string[];
}

interface UpdateDocumentParams {
  databaseId: string;
  collectionId: string;
  documentId: string;
  data: Record<string, any>;
  permissions?: string[];
}

export function useAppwriteDatabase() {
  const [state, setState] = useState<DatabaseState>({
    isLoading: false,
    error: null,
  });

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const createDocument = useCallback(async (params: CreateDocumentParams) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/appwrite/database?action=document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to create document');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during document creation';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const getDocument = useCallback(async (
    databaseId: string,
    collectionId: string,
    documentId: string
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'document',
        databaseId,
        collectionId,
        documentId,
      });

      const response = await fetch(`/api/appwrite/database?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to get document');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during document retrieval';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const listDocuments = useCallback(async (
    databaseId: string,
    collectionId: string,
    queryParams?: QueryParams
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'documents',
        databaseId,
        collectionId,
      });

      // Add pagination parameters
      if (queryParams?.pagination?.limit) {
        params.append('limit', queryParams.pagination.limit.toString());
      }
      if (queryParams?.pagination?.offset) {
        params.append('offset', queryParams.pagination.offset.toString());
      }
      if (queryParams?.pagination?.cursor) {
        params.append('cursor', queryParams.pagination.cursor);
      }
      if (queryParams?.pagination?.orderBy) {
        params.append('orderBy', queryParams.pagination.orderBy);
      }
      if (queryParams?.pagination?.orderDirection) {
        params.append('orderDirection', queryParams.pagination.orderDirection);
      }

      // Add filter parameters
      if (queryParams?.filters) {
        Object.entries(queryParams.filters).forEach(([key, value]) => {
          params.append(`filter.${key}`, value.toString());
        });
      }

      // Add search parameter
      if (queryParams?.search) {
        params.append('search', queryParams.search);
      }

      const response = await fetch(`/api/appwrite/database?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to list documents');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during document listing';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const updateDocument = useCallback(async (params: UpdateDocumentParams) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/appwrite/database?action=document', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to update document');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during document update';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const deleteDocument = useCallback(async (
    databaseId: string,
    collectionId: string,
    documentId: string
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'document',
        databaseId,
        collectionId,
        documentId,
      });

      const response = await fetch(`/api/appwrite/database?${params}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true };
      } else {
        setError(data.error?.message || 'Failed to delete document');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during document deletion';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const listCollections = useCallback(async (
    databaseId: string,
    pagination?: PaginationParams
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'collections',
        databaseId,
      });

      if (pagination?.limit) {
        params.append('limit', pagination.limit.toString());
      }
      if (pagination?.offset) {
        params.append('offset', pagination.offset.toString());
      }

      const response = await fetch(`/api/appwrite/database?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to list collections');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during collection listing';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  return {
    ...state,
    createDocument,
    getDocument,
    listDocuments,
    updateDocument,
    deleteDocument,
    listCollections,
    clearError,
  };
}
