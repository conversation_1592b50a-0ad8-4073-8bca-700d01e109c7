import { useState, useEffect, useCallback } from 'react';
import { ContainerInfo, CreateContainerOptions } from '@/types/docker';
import { clientDockerAPIService } from '@/services/client';

interface UseDockerReturn {
  containers: ContainerInfo[];
  loading: boolean;
  error: string | null;
  systemInfo: any;
  connected: boolean;
  refreshContainers: () => Promise<void>;
  createContainer: (options: CreateContainerOptions) => Promise<string>;
  startContainer: (id: string) => Promise<void>;
  stopContainer: (id: string, timeout?: number) => Promise<void>;
  restartContainer: (id: string) => Promise<void>;
  removeContainer: (id: string, force?: boolean) => Promise<void>;
  getContainerLogs: (id: string, tail?: number) => Promise<string>;
  getContainerStats: (id: string) => Promise<any>;
  execInContainer: (id: string, cmd: string[]) => Promise<string>;
  pullImage: (imageName: string) => Promise<void>;
  getSystemInfo: () => Promise<void>;
}

export function useDocker(): UseDockerReturn {
  const [containers, setContainers] = useState<ContainerInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemInfo, setSystemInfo] = useState<any>(null);
  const [connected, setConnected] = useState(false);



  const refreshContainers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const containers = await clientDockerAPIService.listContainers();
      setContainers(containers);
    } catch (error) {
      console.error('Error fetching containers:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setContainers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const getSystemInfo = useCallback(async () => {
    try {
      const systemInfo = await clientDockerAPIService.getSystemInfo();
      setSystemInfo(systemInfo);
      setConnected(systemInfo.connected);
    } catch (error) {
      setConnected(false);
      setSystemInfo(null);
    }
  }, []);

  const createContainer = useCallback(async (options: CreateContainerOptions): Promise<string> => {
    const containerId = await clientDockerAPIService.createContainer(options);
    await refreshContainers();
    return containerId;
  }, [refreshContainers]);

  const startContainer = useCallback(async (id: string): Promise<void> => {
    await clientDockerAPIService.startContainer(id);
    await refreshContainers();
  }, [refreshContainers]);

  const stopContainer = useCallback(async (id: string, timeout = 10): Promise<void> => {
    await clientDockerAPIService.stopContainer(id, timeout);
    await refreshContainers();
  }, [refreshContainers]);

  const restartContainer = useCallback(async (id: string): Promise<void> => {
    await clientDockerAPIService.restartContainer(id);
    await refreshContainers();
  }, [refreshContainers]);

  const removeContainer = useCallback(async (id: string, force = false): Promise<void> => {
    await clientDockerAPIService.removeContainer(id, force);
    await refreshContainers();
  }, [refreshContainers]);

  const getContainerLogs = useCallback(async (id: string, tail = 100): Promise<string> => {
    return await clientDockerAPIService.getContainerLogs(id, tail);
  }, []);

  const getContainerStats = useCallback(async (id: string): Promise<any> => {
    return await clientDockerAPIService.getContainerStats(id);
  }, []);

  const execInContainer = useCallback(async (id: string, cmd: string[]): Promise<string> => {
    return await clientDockerAPIService.execInContainer(id, cmd);
  }, []);

  const pullImage = useCallback(async (imageName: string): Promise<void> => {
    await clientDockerAPIService.pullImage(imageName);
  }, []);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      await getSystemInfo();
      await refreshContainers();
    };
    
    initializeData();
  }, [getSystemInfo, refreshContainers]);

  return {
    containers,
    loading,
    error,
    systemInfo,
    connected,
    refreshContainers,
    createContainer,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    getContainerLogs,
    getContainerStats,
    execInContainer,
    pullImage,
    getSystemInfo,
  };
}

// Utility hook for managing a specific container
export function useContainer(containerId: string) {
  const [container, setContainer] = useState<ContainerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshContainer = useCallback(async () => {
    if (!containerId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/containers/${containerId}`);
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to fetch container');
      }
      
      setContainer(data.data);
    } catch (error) {
      console.error('Error fetching container:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setContainer(null);
    } finally {
      setLoading(false);
    }
  }, [containerId]);

  useEffect(() => {
    refreshContainer();
  }, [refreshContainer]);

  return {
    container,
    loading,
    error,
    refreshContainer,
  };
}
