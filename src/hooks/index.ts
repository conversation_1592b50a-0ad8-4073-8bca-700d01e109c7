/**
 * Hooks Index
 * Centralized exports for all custom hooks
 */

// Workspace management hooks
export { useWorkspace } from './useWorkspace';
export { useWorkspaceDetail } from './useWorkspaceDetail';
export { useWorkspaceFiles } from './useWorkspaceFiles';
export { useWorkspaceTemplates } from './useWorkspaceTemplates';

// Re-export hook types for convenience
export type {
  UseWorkspaceParams,
  UseWorkspaceReturn,
} from './useWorkspace';

export type {
  UseWorkspaceDetailParams,
  UseWorkspaceDetailReturn,
} from './useWorkspaceDetail';

export type {
  UseWorkspaceFilesParams,
  UseWorkspaceFilesReturn,
  FileTreeNode,
} from './useWorkspaceFiles';

export type {
  UseWorkspaceTemplatesParams,
  UseWorkspaceTemplatesReturn,
} from './useWorkspaceTemplates';
