import { useState, useCallback } from 'react';

interface StorageState {
  isLoading: boolean;
  uploadProgress: number;
  error: string | null;
}

interface UploadFileParams {
  bucketId: string;
  fileId?: string;
  file: File;
  permissions?: string[];
  onProgress?: (progress: number) => void;
}

interface ImageTransformOptions {
  width?: number;
  height?: number;
  gravity?: 'center' | 'top-left' | 'top' | 'top-right' | 'left' | 'right' | 'bottom-left' | 'bottom' | 'bottom-right';
  quality?: number;
  borderWidth?: number;
  borderColor?: string;
  borderRadius?: number;
  opacity?: number;
  rotation?: number;
  background?: string;
  output?: 'jpg' | 'jpeg' | 'png' | 'gif' | 'webp';
}

interface PaginationParams {
  limit?: number;
  offset?: number;
  cursor?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export function useAppwriteStorage() {
  const [state, setState] = useState<StorageState>({
    isLoading: false,
    uploadProgress: 0,
    error: null,
  });

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setProgress = useCallback((progress: number) => {
    setState(prev => ({ ...prev, uploadProgress: progress }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const uploadFile = useCallback(async (params: UploadFileParams) => {
    try {
      setLoading(true);
      setProgress(0);
      setError(null);

      const formData = new FormData();
      formData.append('bucketId', params.bucketId);
      formData.append('file', params.file);
      
      if (params.fileId) {
        formData.append('fileId', params.fileId);
      }
      
      if (params.permissions) {
        formData.append('permissions', JSON.stringify(params.permissions));
      }

      const xhr = new XMLHttpRequest();

      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setProgress(progress);
            params.onProgress?.(progress);
          }
        });

        xhr.addEventListener('load', () => {
          try {
            const data = JSON.parse(xhr.responseText);
            
            if (xhr.status === 200 && data.success) {
              setLoading(false);
              setProgress(100);
              resolve({ success: true, data: data.data });
            } else {
              setError(data.error?.message || 'Upload failed');
              setLoading(false);
              setProgress(0);
              resolve({ success: false, error: data.error });
            }
          } catch (error) {
            setError('Invalid response from server');
            setLoading(false);
            setProgress(0);
            resolve({ success: false, error: { message: 'Invalid response' } });
          }
        });

        xhr.addEventListener('error', () => {
          setError('Network error during upload');
          setLoading(false);
          setProgress(0);
          resolve({ success: false, error: { message: 'Network error' } });
        });

        xhr.open('POST', '/api/appwrite/storage?action=file');
        xhr.setRequestHeader('credentials', 'include');
        xhr.send(formData);
      });
    } catch (error) {
      const errorMessage = 'Failed to start upload';
      setError(errorMessage);
      setLoading(false);
      setProgress(0);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setProgress, setError]);

  const getFile = useCallback(async (bucketId: string, fileId: string) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'file',
        bucketId,
        fileId,
      });

      const response = await fetch(`/api/appwrite/storage?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to get file');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during file retrieval';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const listFiles = useCallback(async (
    bucketId: string,
    pagination?: PaginationParams
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'files',
        bucketId,
      });

      if (pagination?.limit) {
        params.append('limit', pagination.limit.toString());
      }
      if (pagination?.offset) {
        params.append('offset', pagination.offset.toString());
      }
      if (pagination?.cursor) {
        params.append('cursor', pagination.cursor);
      }

      const response = await fetch(`/api/appwrite/storage?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to list files');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during file listing';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const getFilePreview = useCallback(async (
    bucketId: string,
    fileId: string,
    transformOptions?: ImageTransformOptions
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'file-preview',
        bucketId,
        fileId,
      });

      // Add transform options
      if (transformOptions) {
        Object.entries(transformOptions).forEach(([key, value]) => {
          if (value !== undefined) {
            params.append(`transform.${key}`, value.toString());
          }
        });
      }

      const response = await fetch(`/api/appwrite/storage?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to get file preview');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during preview generation';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const getFileDownload = useCallback(async (bucketId: string, fileId: string) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'file-download',
        bucketId,
        fileId,
      });

      const response = await fetch(`/api/appwrite/storage?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true, data: data.data };
      } else {
        setError(data.error?.message || 'Failed to get download URL');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during download URL generation';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const deleteFile = useCallback(async (bucketId: string, fileId: string) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        action: 'file',
        bucketId,
        fileId,
      });

      const response = await fetch(`/api/appwrite/storage?${params}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        setLoading(false);
        return { success: true };
      } else {
        setError(data.error?.message || 'Failed to delete file');
        setLoading(false);
        return { success: false, error: data.error };
      }
    } catch (error) {
      const errorMessage = 'Network error during file deletion';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: { message: errorMessage } };
    }
  }, [setLoading, setError]);

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  const resetProgress = useCallback(() => {
    setProgress(0);
  }, [setProgress]);

  return {
    ...state,
    uploadFile,
    getFile,
    listFiles,
    getFilePreview,
    getFileDownload,
    deleteFile,
    clearError,
    resetProgress,
  };
}
