/**
 * Workspace Management Hook
 * Provides comprehensive workspace state management and operations
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Workspace,
  CreateWorkspaceRequest,
  UpdateWorkspaceRequest,
  ShareWorkspaceRequest,
  WorkspaceStatus,
  FilterParams,
  PaginationParams
} from '@/types/workspace';

// API functions
const workspaceApi = {
  // Get workspace by ID
  getWorkspace: async (id: string): Promise<Workspace> => {
    const response = await fetch(`/api/workspace/${id}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch workspace');
    }
    
    const result = await response.json();
    return result.data;
  },

  // List workspaces with filtering and pagination
  listWorkspaces: async (params?: {
    filters?: FilterParams;
    pagination?: PaginationParams;
  }) => {
    const searchParams = new URLSearchParams();
    
    if (params?.pagination) {
      searchParams.set('page', params.pagination.offset.toString());
      searchParams.set('limit', params.pagination.limit.toString());
      if (params.pagination.orderBy) {
        searchParams.set('sortBy', params.pagination.orderBy);
        searchParams.set('sortOrder', params.pagination.orderDirection || 'desc');
      }
    }
    
    if (params?.filters) {
      const { type, status, visibility, tags, search, ownerId } = params.filters;
      if (type?.[0]) searchParams.set('type', type[0]);
      if (status?.[0]) searchParams.set('status', status[0]);
      if (visibility?.[0]) searchParams.set('visibility', visibility[0]);
      if (tags?.length) searchParams.set('tags', tags.join(','));
      if (search) searchParams.set('search', search);
      if (ownerId) searchParams.set('ownerId', ownerId);
    }

    const response = await fetch(`/api/workspace?${searchParams}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to fetch workspaces');
    }
    
    return response.json();
  },

  // Create new workspace
  createWorkspace: async (data: CreateWorkspaceRequest): Promise<Workspace> => {
    const response = await fetch('/api/workspace', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to create workspace');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Update workspace
  updateWorkspace: async (id: string, data: UpdateWorkspaceRequest): Promise<Workspace> => {
    const response = await fetch(`/api/workspace/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update workspace');
    }
    
    const result = await response.json();
    return result.data;
  },

  // Delete workspace
  deleteWorkspace: async (id: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to delete workspace');
    }
  },

  // Update workspace status
  updateStatus: async (id: string, status: WorkspaceStatus): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to update workspace status');
    }
  },

  // Share workspace
  shareWorkspace: async (id: string, data: ShareWorkspaceRequest): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=share`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to share workspace');
    }
  },

  // Remove collaborator
  removeCollaborator: async (id: string, userId: string): Promise<void> => {
    const response = await fetch(`/api/workspace/${id}?action=remove-collaborator`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to remove collaborator');
    }
  },
};

// Hook parameters
export interface UseWorkspaceParams {
  filters?: FilterParams;
  pagination?: PaginationParams;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseWorkspaceReturn {
  // Data
  workspaces: Workspace[];
  workspace: Workspace | null;
  total: number;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isSharing: boolean;
  
  // Error states
  error: Error | null;
  
  // Actions
  createWorkspace: (data: CreateWorkspaceRequest) => Promise<Workspace>;
  updateWorkspace: (id: string, data: UpdateWorkspaceRequest) => Promise<Workspace>;
  deleteWorkspace: (id: string) => Promise<void>;
  shareWorkspace: (id: string, data: ShareWorkspaceRequest) => Promise<void>;
  removeCollaborator: (id: string, userId: string) => Promise<void>;
  updateStatus: (id: string, status: WorkspaceStatus) => Promise<void>;
  
  // Utilities
  refetch: () => void;
  setFilters: (filters: FilterParams) => void;
  setPagination: (pagination: PaginationParams) => void;
  getWorkspace: (id: string) => Workspace | undefined;
  
  // Computed values
  filteredWorkspaces: Workspace[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Main hook
export function useWorkspace(params: UseWorkspaceParams = {}): UseWorkspaceReturn {
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<FilterParams>(params.filters || {});
  const [pagination, setPagination] = useState<PaginationParams>(
    params.pagination || { limit: 20, offset: 0 }
  );

  // Query for workspace list
  const {
    data: workspaceData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['workspaces', filters, pagination],
    queryFn: () => workspaceApi.listWorkspaces({ filters, pagination }),
    refetchInterval: params.autoRefresh ? params.refreshInterval || 30000 : false,
    staleTime: 10000, // 10 seconds
  });

  // Create workspace mutation
  const createMutation = useMutation({
    mutationFn: workspaceApi.createWorkspace,
    onSuccess: (newWorkspace) => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success(`Workspace "${newWorkspace.name}" created successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to create workspace: ${error.message}`);
    },
  });

  // Update workspace mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateWorkspaceRequest }) =>
      workspaceApi.updateWorkspace(id, data),
    onSuccess: (updatedWorkspace) => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      queryClient.invalidateQueries({ queryKey: ['workspace', updatedWorkspace.id] });
      toast.success(`Workspace "${updatedWorkspace.name}" updated successfully`);
    },
    onError: (error: Error) => {
      toast.error(`Failed to update workspace: ${error.message}`);
    },
  });

  // Delete workspace mutation
  const deleteMutation = useMutation({
    mutationFn: workspaceApi.deleteWorkspace,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success('Workspace deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete workspace: ${error.message}`);
    },
  });

  // Share workspace mutation
  const shareMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: ShareWorkspaceRequest }) =>
      workspaceApi.shareWorkspace(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success('Workspace shared successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to share workspace: ${error.message}`);
    },
  });

  // Remove collaborator mutation
  const removeCollaboratorMutation = useMutation({
    mutationFn: ({ id, userId }: { id: string; userId: string }) =>
      workspaceApi.removeCollaborator(id, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success('Collaborator removed successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to remove collaborator: ${error.message}`);
    },
  });

  // Update status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: WorkspaceStatus }) =>
      workspaceApi.updateStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success('Workspace status updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update workspace status: ${error.message}`);
    },
  });

  // Computed values
  const workspaces = workspaceData?.data?.workspaces || [];
  const total = workspaceData?.data?.total || 0;
  const workspace = workspaces.length === 1 ? workspaces[0] : null;

  const filteredWorkspaces = useMemo(() => {
    return workspaces.filter((ws) => {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          ws.name.toLowerCase().includes(searchLower) ||
          ws.description?.toLowerCase().includes(searchLower) ||
          ws.tags.some(tag => tag.toLowerCase().includes(searchLower))
        );
      }
      return true;
    });
  }, [workspaces, filters.search]);

  const hasNextPage = pagination.offset + pagination.limit < total;
  const hasPreviousPage = pagination.offset > 0;

  // Action handlers
  const handleCreateWorkspace = useCallback(
    async (data: CreateWorkspaceRequest) => {
      return createMutation.mutateAsync(data);
    },
    [createMutation]
  );

  const handleUpdateWorkspace = useCallback(
    async (id: string, data: UpdateWorkspaceRequest) => {
      return updateMutation.mutateAsync({ id, data });
    },
    [updateMutation]
  );

  const handleDeleteWorkspace = useCallback(
    async (id: string) => {
      return deleteMutation.mutateAsync(id);
    },
    [deleteMutation]
  );

  const handleShareWorkspace = useCallback(
    async (id: string, data: ShareWorkspaceRequest) => {
      return shareMutation.mutateAsync({ id, data });
    },
    [shareMutation]
  );

  const handleRemoveCollaborator = useCallback(
    async (id: string, userId: string) => {
      return removeCollaboratorMutation.mutateAsync({ id, userId });
    },
    [removeCollaboratorMutation]
  );

  const handleUpdateStatus = useCallback(
    async (id: string, status: WorkspaceStatus) => {
      return updateStatusMutation.mutateAsync({ id, status });
    },
    [updateStatusMutation]
  );

  const getWorkspace = useCallback(
    (id: string) => {
      return workspaces.find(ws => ws.id === id);
    },
    [workspaces]
  );

  return {
    // Data
    workspaces,
    workspace,
    total,
    
    // Loading states
    isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isSharing: shareMutation.isPending,
    
    // Error states
    error: error as Error | null,
    
    // Actions
    createWorkspace: handleCreateWorkspace,
    updateWorkspace: handleUpdateWorkspace,
    deleteWorkspace: handleDeleteWorkspace,
    shareWorkspace: handleShareWorkspace,
    removeCollaborator: handleRemoveCollaborator,
    updateStatus: handleUpdateStatus,
    
    // Utilities
    refetch,
    setFilters,
    setPagination,
    getWorkspace,
    
    // Computed values
    filteredWorkspaces,
    hasNextPage,
    hasPreviousPage,
  };
}
