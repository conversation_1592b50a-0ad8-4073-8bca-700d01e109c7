/**
 * VM Docker Hook
 * Custom React hook for Docker container operations and management
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { VMDockerInfo } from '@/types/vm';
import { 
  UseVMDockerReturn, 
  VMDockerContainerState, 
  VMDockerCreateContainerData,
  VMDockerEventHandler 
} from '@/types/vm-frontend';

interface VMDockerHookOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onDockerEvent?: VMDockerEventHandler;
}

export function useVMDocker(options: VMDockerHookOptions = {}): UseVMDockerReturn {
  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    onDockerEvent
  } = options;

  const [containers, setContainers] = useState<VMDockerContainerState[]>([]);
  const [dockerInfo, setDockerInfo] = useState<VMDockerInfo | undefined>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();

  // Cleanup function
  const cleanup = useCallback(() => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // API call helper
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: abortControllerRef.current.signal,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API call failed');
      }

      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      throw error;
    }
  }, []);

  // Update container state
  const updateContainerState = useCallback((containerId: string, updates: Partial<VMDockerContainerState>) => {
    setContainers(prev => 
      prev.map(container => 
        container.id === containerId 
          ? { ...container, ...updates }
          : container
      )
    );
  }, []);

  // Create container
  const createContainer = useCallback(async (
    vmId: string,
    connectionId: string,
    options: VMDockerCreateContainerData
  ): Promise<string> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall(
        `/api/vm/docker?action=create-container&vmId=${vmId}&connectionId=${connectionId}`,
        {
          method: 'POST',
          body: JSON.stringify({ 
            config: {}, // Config would be retrieved from storage
            options 
          }),
        }
      );

      const { containerId } = response.data;

      // Add new container to state
      const newContainer: VMDockerContainerState = {
        id: containerId,
        name: options.name || containerId.substring(0, 12),
        image: options.image,
        status: 'created',
        state: 'created',
        ports: [],
        created: new Date(),
        isLoading: false
      };

      setContainers(prev => [...prev, newContainer]);
      onDockerEvent?.(vmId, containerId, 'created', newContainer);

      return containerId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Container creation failed';
      setError(errorMessage);
      onDockerEvent?.(vmId, '', 'error', { error: errorMessage });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onDockerEvent]);

  // Start container
  const startContainer = useCallback(async (
    vmId: string,
    connectionId: string,
    containerId: string
  ): Promise<void> => {
    try {
      updateContainerState(containerId, { isLoading: true, error: undefined });

      await apiCall(
        `/api/vm/docker?action=start-container&vmId=${vmId}&connectionId=${connectionId}&containerId=${containerId}`,
        {
          method: 'POST',
          body: JSON.stringify({ config: {} }),
        }
      );

      updateContainerState(containerId, { 
        status: 'running',
        state: 'running',
        startedAt: new Date(),
        isLoading: false 
      });

      onDockerEvent?.(vmId, containerId, 'started', { containerId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Container start failed';
      updateContainerState(containerId, { 
        isLoading: false, 
        error: errorMessage 
      });
      onDockerEvent?.(vmId, containerId, 'error', { error: errorMessage });
      throw error;
    }
  }, [apiCall, updateContainerState, onDockerEvent]);

  // Stop container
  const stopContainer = useCallback(async (
    vmId: string,
    connectionId: string,
    containerId: string,
    timeout: number = 10
  ): Promise<void> => {
    try {
      updateContainerState(containerId, { isLoading: true, error: undefined });

      await apiCall(
        `/api/vm/docker?action=stop-container&vmId=${vmId}&connectionId=${connectionId}&containerId=${containerId}&timeout=${timeout}`,
        {
          method: 'POST',
          body: JSON.stringify({ config: {} }),
        }
      );

      updateContainerState(containerId, { 
        status: 'exited',
        state: 'exited',
        finishedAt: new Date(),
        isLoading: false 
      });

      onDockerEvent?.(vmId, containerId, 'stopped', { containerId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Container stop failed';
      updateContainerState(containerId, { 
        isLoading: false, 
        error: errorMessage 
      });
      onDockerEvent?.(vmId, containerId, 'error', { error: errorMessage });
      throw error;
    }
  }, [apiCall, updateContainerState, onDockerEvent]);

  // Restart container
  const restartContainer = useCallback(async (
    vmId: string,
    connectionId: string,
    containerId: string
  ): Promise<void> => {
    try {
      updateContainerState(containerId, { isLoading: true, error: undefined });

      await apiCall(
        `/api/vm/docker?action=restart-container&vmId=${vmId}&connectionId=${connectionId}&containerId=${containerId}`,
        {
          method: 'POST',
          body: JSON.stringify({ config: {} }),
        }
      );

      updateContainerState(containerId, { 
        status: 'running',
        state: 'running',
        startedAt: new Date(),
        isLoading: false 
      });

      onDockerEvent?.(vmId, containerId, 'started', { containerId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Container restart failed';
      updateContainerState(containerId, { 
        isLoading: false, 
        error: errorMessage 
      });
      onDockerEvent?.(vmId, containerId, 'error', { error: errorMessage });
      throw error;
    }
  }, [apiCall, updateContainerState, onDockerEvent]);

  // Remove container
  const removeContainer = useCallback(async (
    vmId: string,
    connectionId: string,
    containerId: string,
    force: boolean = false
  ): Promise<void> => {
    try {
      updateContainerState(containerId, { isLoading: true, error: undefined });

      await apiCall(
        `/api/vm/docker?action=remove-container&vmId=${vmId}&connectionId=${connectionId}&containerId=${containerId}&force=${force}`,
        {
          method: 'DELETE',
          body: JSON.stringify({ config: {} }),
        }
      );

      // Remove container from state
      setContainers(prev => prev.filter(container => container.id !== containerId));
      onDockerEvent?.(vmId, containerId, 'removed', { containerId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Container removal failed';
      updateContainerState(containerId, { 
        isLoading: false, 
        error: errorMessage 
      });
      onDockerEvent?.(vmId, containerId, 'error', { error: errorMessage });
      throw error;
    }
  }, [apiCall, updateContainerState, onDockerEvent]);

  // Get container logs
  const getContainerLogs = useCallback(async (
    vmId: string,
    connectionId: string,
    containerId: string,
    options?: {
      follow?: boolean;
      tail?: number;
      since?: string;
      until?: string;
      timestamps?: boolean;
    }
  ): Promise<string> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'container-logs',
        vmId,
        connectionId,
        containerId,
        config: btoa(JSON.stringify({})), // Base64 encoded config
        ...Object.fromEntries(
          Object.entries(options || {}).map(([key, value]) => [key, String(value)])
        )
      });

      const response = await apiCall(`/api/vm/docker?${queryParams}`, {
        method: 'GET',
      });

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get container logs';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Execute command in container
  const execInContainer = useCallback(async (
    vmId: string,
    connectionId: string,
    containerId: string,
    command: string[],
    options?: {
      interactive?: boolean;
      tty?: boolean;
      user?: string;
      workingDir?: string;
      environment?: Record<string, string>;
    }
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    try {
      setError(null);

      const response = await apiCall(
        `/api/vm/docker?action=exec-container&vmId=${vmId}&connectionId=${connectionId}&containerId=${containerId}`,
        {
          method: 'POST',
          body: JSON.stringify({ 
            config: {},
            command,
            options 
          }),
        }
      );

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Container exec failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Refresh containers list
  const refreshContainers = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'containers',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({})),
        all: 'true'
      });

      const response = await apiCall(`/api/vm/docker?${queryParams}`, {
        method: 'GET',
      });

      const containerData = response.data.map((container: any) => ({
        id: container.id,
        name: container.name,
        image: container.image,
        status: container.status,
        state: container.state,
        ports: container.ports || [],
        created: new Date(container.created),
        startedAt: container.startedAt ? new Date(container.startedAt) : undefined,
        finishedAt: container.finishedAt ? new Date(container.finishedAt) : undefined,
        isLoading: false
      }));

      setContainers(containerData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh containers';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Refresh Docker info
  const refreshDockerInfo = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<void> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'info',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({}))
      });

      const response = await apiCall(`/api/vm/docker?${queryParams}`, {
        method: 'GET',
      });

      setDockerInfo(response.data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh Docker info';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        // Auto-refresh would need VM ID and connection ID
        // This would be handled by the parent component
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval]);

  return {
    containers,
    dockerInfo,
    loading,
    error,
    createContainer,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    getContainerLogs,
    execInContainer,
    refreshContainers,
    refreshDockerInfo,
  };
}

// Utility hook for managing Docker operations for a specific VM
export function useVMDockerForVM(
  vmId: string,
  connectionId: string,
  options: VMDockerHookOptions = {}
) {
  const dockerHook = useVMDocker(options);

  const createContainerForVM = useCallback(
    (options: VMDockerCreateContainerData) => 
      dockerHook.createContainer(vmId, connectionId, options),
    [dockerHook.createContainer, vmId, connectionId]
  );

  const startContainerForVM = useCallback(
    (containerId: string) => 
      dockerHook.startContainer(vmId, connectionId, containerId),
    [dockerHook.startContainer, vmId, connectionId]
  );

  const stopContainerForVM = useCallback(
    (containerId: string, timeout?: number) => 
      dockerHook.stopContainer(vmId, connectionId, containerId, timeout),
    [dockerHook.stopContainer, vmId, connectionId]
  );

  const restartContainerForVM = useCallback(
    (containerId: string) => 
      dockerHook.restartContainer(vmId, connectionId, containerId),
    [dockerHook.restartContainer, vmId, connectionId]
  );

  const removeContainerForVM = useCallback(
    (containerId: string, force?: boolean) => 
      dockerHook.removeContainer(vmId, connectionId, containerId, force),
    [dockerHook.removeContainer, vmId, connectionId]
  );

  const getContainerLogsForVM = useCallback(
    (containerId: string, options?: any) => 
      dockerHook.getContainerLogs(vmId, connectionId, containerId, options),
    [dockerHook.getContainerLogs, vmId, connectionId]
  );

  const execInContainerForVM = useCallback(
    (containerId: string, command: string[], options?: any) => 
      dockerHook.execInContainer(vmId, connectionId, containerId, command, options),
    [dockerHook.execInContainer, vmId, connectionId]
  );

  const refreshContainersForVM = useCallback(
    () => dockerHook.refreshContainers(vmId, connectionId),
    [dockerHook.refreshContainers, vmId, connectionId]
  );

  const refreshDockerInfoForVM = useCallback(
    () => dockerHook.refreshDockerInfo(vmId, connectionId),
    [dockerHook.refreshDockerInfo, vmId, connectionId]
  );

  // Auto-refresh on mount
  useEffect(() => {
    refreshContainersForVM();
    refreshDockerInfoForVM();
  }, [refreshContainersForVM, refreshDockerInfoForVM]);

  return {
    ...dockerHook,
    createContainer: createContainerForVM,
    startContainer: startContainerForVM,
    stopContainer: stopContainerForVM,
    restartContainer: restartContainerForVM,
    removeContainer: removeContainerForVM,
    getContainerLogs: getContainerLogsForVM,
    execInContainer: execInContainerForVM,
    refreshContainers: refreshContainersForVM,
    refreshDockerInfo: refreshDockerInfoForVM,
  };
}
