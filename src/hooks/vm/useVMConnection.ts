/**
 * VM Connection Hook
 * Custom React hook for managing VM connections and SSH sessions
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  VMConnectionConfig, 
  VMConnectionStatus, 
  VMAuthCredentials, 
  VMSession 
} from '@/types/vm';
import { 
  UseVMConnectionReturn, 
  VMConnectionState, 
  VMConnectionEventHandler 
} from '@/types/vm-frontend';

interface VMConnectionHookOptions {
  autoReconnect?: boolean;
  maxRetryAttempts?: number;
  retryDelay?: number;
  onConnectionEvent?: VMConnectionEventHandler;
}

export function useVMConnection(options: VMConnectionHookOptions = {}): UseVMConnectionReturn {
  const {
    autoReconnect = false,
    maxRetryAttempts = 3,
    retryDelay = 2000,
    onConnectionEvent
  } = options;

  const [connections, setConnections] = useState<Map<string, VMConnectionState>>(new Map());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const retryTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const abortControllers = useRef<Map<string, AbortController>>(new Map());

  // Cleanup function
  const cleanup = useCallback(() => {
    // Clear all retry timeouts
    retryTimeouts.current.forEach(timeout => clearTimeout(timeout));
    retryTimeouts.current.clear();
    
    // Abort all pending requests
    abortControllers.current.forEach(controller => controller.abort());
    abortControllers.current.clear();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // API call helper with error handling
  const apiCall = useCallback(async (
    url: string, 
    options: RequestInit = {},
    vmId?: string
  ) => {
    const controller = new AbortController();
    if (vmId) {
      abortControllers.current.set(vmId, controller);
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: controller.signal,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API call failed');
      }

      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      throw error;
    } finally {
      if (vmId) {
        abortControllers.current.delete(vmId);
      }
    }
  }, []);

  // Update connection state
  const updateConnectionState = useCallback((vmId: string, updates: Partial<VMConnectionState>) => {
    setConnections(prev => {
      const newConnections = new Map(prev);
      const current = newConnections.get(vmId) || {
        isConnected: false,
        isConnecting: false,
        retryCount: 0
      };
      newConnections.set(vmId, { ...current, ...updates });
      return newConnections;
    });
  }, []);

  // Handle connection retry
  const scheduleRetry = useCallback((
    vmId: string, 
    config: VMConnectionConfig, 
    credentials?: VMAuthCredentials
  ) => {
    const connectionState = connections.get(vmId);
    if (!connectionState || connectionState.retryCount >= maxRetryAttempts) {
      updateConnectionState(vmId, { 
        isConnecting: false, 
        error: 'Max retry attempts reached' 
      });
      onConnectionEvent?.(vmId, 'error', { error: 'Max retry attempts reached' });
      return;
    }

    const timeout = setTimeout(async () => {
      try {
        await connect(vmId, config, credentials);
      } catch (error) {
        console.error(`Retry ${connectionState.retryCount + 1} failed for VM ${vmId}:`, error);
      }
    }, retryDelay * Math.pow(2, connectionState.retryCount)); // Exponential backoff

    retryTimeouts.current.set(vmId, timeout);
  }, [connections, maxRetryAttempts, retryDelay, onConnectionEvent]);

  // Connect to VM
  const connect = useCallback(async (
    vmId: string, 
    config: VMConnectionConfig, 
    credentials?: VMAuthCredentials
  ): Promise<string> => {
    try {
      setLoading(true);
      setError(null);
      
      updateConnectionState(vmId, { 
        isConnecting: true, 
        error: undefined 
      });

      const response = await apiCall(
        `/api/vm?action=connect&vmId=${vmId}`,
        {
          method: 'POST',
          body: JSON.stringify({ config, credentials }),
        },
        vmId
      );

      const connectionStatus: VMConnectionStatus = response.data;
      
      updateConnectionState(vmId, {
        isConnected: true,
        isConnecting: false,
        connectionId: connectionStatus.connectionId,
        status: connectionStatus,
        lastConnected: new Date(),
        retryCount: 0,
        error: undefined
      });

      onConnectionEvent?.(vmId, 'connected', connectionStatus);
      
      return connectionStatus.connectionId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';
      
      updateConnectionState(vmId, {
        isConnected: false,
        isConnecting: false,
        error: errorMessage,
        retryCount: (connections.get(vmId)?.retryCount || 0) + 1
      });

      setError(errorMessage);
      onConnectionEvent?.(vmId, 'error', { error: errorMessage });

      // Auto-retry if enabled
      if (autoReconnect) {
        scheduleRetry(vmId, config, credentials);
      }

      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, updateConnectionState, onConnectionEvent, autoReconnect, scheduleRetry, connections]);

  // Disconnect from VM
  const disconnect = useCallback(async (vmId: string, connectionId: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // Clear any pending retry
      const timeout = retryTimeouts.current.get(vmId);
      if (timeout) {
        clearTimeout(timeout);
        retryTimeouts.current.delete(vmId);
      }

      // Abort any pending requests
      const controller = abortControllers.current.get(vmId);
      if (controller) {
        controller.abort();
        abortControllers.current.delete(vmId);
      }

      await apiCall(
        `/api/vm?action=disconnect&vmId=${vmId}&connectionId=${connectionId}`,
        { method: 'DELETE' },
        vmId
      );

      updateConnectionState(vmId, {
        isConnected: false,
        isConnecting: false,
        connectionId: undefined,
        status: undefined,
        retryCount: 0,
        error: undefined
      });

      onConnectionEvent?.(vmId, 'disconnected', { connectionId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Disconnect failed';
      setError(errorMessage);
      onConnectionEvent?.(vmId, 'error', { error: errorMessage });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, updateConnectionState, onConnectionEvent]);

  // Execute command on VM
  const executeCommand = useCallback(async (
    vmId: string,
    connectionId: string,
    command: string,
    options?: {
      timeout?: number;
      cwd?: string;
      env?: Record<string, string>;
    }
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall(
        `/api/vm?action=execute&vmId=${vmId}&connectionId=${connectionId}`,
        {
          method: 'POST',
          body: JSON.stringify({ 
            config: {}, // Config would be retrieved from storage in real implementation
            command,
            options 
          }),
        },
        vmId
      );

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Command execution failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Create session
  const createSession = useCallback(async (
    vmId: string,
    connectionId: string,
    userId: string,
    metadata?: any
  ): Promise<VMSession> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall(
        `/api/vm?action=create-session&vmId=${vmId}&connectionId=${connectionId}`,
        {
          method: 'POST',
          body: JSON.stringify({ 
            config: {}, // Config would be retrieved from storage in real implementation
            userId,
            metadata 
          }),
        },
        vmId
      );

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Session creation failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Close session
  const closeSession = useCallback(async (vmId: string, sessionId: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm?action=close-session&vmId=${vmId}&sessionId=${sessionId}`,
        { method: 'DELETE' },
        vmId
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Session close failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Get connection status
  const getConnectionStatus = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<VMConnectionStatus> => {
    try {
      setError(null);

      const response = await apiCall(
        `/api/vm?action=connection-status&vmId=${vmId}&connectionId=${connectionId}`,
        { method: 'GET' },
        vmId
      );

      const status: VMConnectionStatus = response.data;
      
      // Update local state
      updateConnectionState(vmId, { status });
      
      return status;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get connection status';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, updateConnectionState]);

  return {
    connections,
    connect,
    disconnect,
    executeCommand,
    createSession,
    closeSession,
    getConnectionStatus,
    loading,
    error,
  };
}

// Utility hook for managing a single VM connection
export function useVMSingleConnection(
  vmId: string,
  config: VMConnectionConfig,
  options: VMConnectionHookOptions = {}
) {
  const {
    connections,
    connect,
    disconnect,
    executeCommand,
    createSession,
    closeSession,
    getConnectionStatus,
    loading,
    error,
  } = useVMConnection(options);

  const connectionState = connections.get(vmId);

  const connectToVM = useCallback(
    (credentials?: VMAuthCredentials) => connect(vmId, config, credentials),
    [connect, vmId, config]
  );

  const disconnectFromVM = useCallback(() => {
    if (connectionState?.connectionId) {
      return disconnect(vmId, connectionState.connectionId);
    }
    return Promise.resolve();
  }, [disconnect, vmId, connectionState?.connectionId]);

  const executeVMCommand = useCallback(
    (command: string, options?: any) => {
      if (!connectionState?.connectionId) {
        throw new Error('No active connection');
      }
      return executeCommand(vmId, connectionState.connectionId, command, options);
    },
    [executeCommand, vmId, connectionState?.connectionId]
  );

  const createVMSession = useCallback(
    (userId: string, metadata?: any) => {
      if (!connectionState?.connectionId) {
        throw new Error('No active connection');
      }
      return createSession(vmId, connectionState.connectionId, userId, metadata);
    },
    [createSession, vmId, connectionState?.connectionId]
  );

  const getVMConnectionStatus = useCallback(() => {
    if (!connectionState?.connectionId) {
      throw new Error('No active connection');
    }
    return getConnectionStatus(vmId, connectionState.connectionId);
  }, [getConnectionStatus, vmId, connectionState?.connectionId]);

  return {
    connectionState,
    connect: connectToVM,
    disconnect: disconnectFromVM,
    executeCommand: executeVMCommand,
    createSession: createVMSession,
    closeSession,
    getConnectionStatus: getVMConnectionStatus,
    loading,
    error,
  };
}
