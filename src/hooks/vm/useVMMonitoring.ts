/**
 * VM Monitoring Hook
 * Custom React hook for real-time system monitoring and metrics
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { VMResourceMetrics, VMSystemInfo, VMHealthCheck } from '@/types/vm';
import { 
  UseVMMonitoringReturn, 
  VMMonitoringMetrics, 
  VMMonitoringAlert,
  VMMonitoringEventHandler 
} from '@/types/vm-frontend';

interface VMMonitoringHookOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealTime?: boolean;
  alertThresholds?: {
    cpu: number;
    memory: number;
    disk: number;
  };
  onMonitoringEvent?: VMMonitoringEventHandler;
}

export function useVMMonitoring(options: VMMonitoringHookOptions = {}): UseVMMonitoringReturn {
  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    enableRealTime = false,
    alertThresholds = { cpu: 80, memory: 85, disk: 90 },
    onMonitoringEvent
  } = options;

  const [metrics, setMetrics] = useState<VMMonitoringMetrics | undefined>();
  const [systemInfo, setSystemInfo] = useState<VMSystemInfo | undefined>();
  const [alerts, setAlerts] = useState<VMMonitoringAlert[]>([]);
  const [processes, setProcesses] = useState<Array<any>>([]);
  const [services, setServices] = useState<Array<any>>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const metricsHistoryRef = useRef<VMResourceMetrics[]>([]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // API call helper
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: abortControllerRef.current.signal,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API call failed');
      }

      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      throw error;
    }
  }, []);

  // Check for alerts based on metrics
  const checkAlerts = useCallback((newMetrics: VMResourceMetrics) => {
    const newAlerts: VMMonitoringAlert[] = [];

    // CPU alert
    if (newMetrics.cpu.usage > alertThresholds.cpu) {
      newAlerts.push({
        id: `cpu-${Date.now()}`,
        type: 'cpu',
        severity: newMetrics.cpu.usage > 95 ? 'critical' : 'high',
        message: `CPU usage is ${newMetrics.cpu.usage.toFixed(1)}%`,
        value: newMetrics.cpu.usage,
        threshold: alertThresholds.cpu,
        triggeredAt: new Date(),
        acknowledged: false
      });
    }

    // Memory alert
    if (newMetrics.memory.usagePercent > alertThresholds.memory) {
      newAlerts.push({
        id: `memory-${Date.now()}`,
        type: 'memory',
        severity: newMetrics.memory.usagePercent > 95 ? 'critical' : 'high',
        message: `Memory usage is ${newMetrics.memory.usagePercent.toFixed(1)}%`,
        value: newMetrics.memory.usagePercent,
        threshold: alertThresholds.memory,
        triggeredAt: new Date(),
        acknowledged: false
      });
    }

    // Disk alert
    if (newMetrics.disk.usagePercent > alertThresholds.disk) {
      newAlerts.push({
        id: `disk-${Date.now()}`,
        type: 'disk',
        severity: newMetrics.disk.usagePercent > 95 ? 'critical' : 'high',
        message: `Disk usage is ${newMetrics.disk.usagePercent.toFixed(1)}%`,
        value: newMetrics.disk.usagePercent,
        threshold: alertThresholds.disk,
        triggeredAt: new Date(),
        acknowledged: false
      });
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...prev, ...newAlerts]);
      newAlerts.forEach(alert => {
        onMonitoringEvent?.('', 'alert', alert);
      });
    }
  }, [alertThresholds, onMonitoringEvent]);

  // Start monitoring
  const startMonitoring = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm/monitoring?action=start-monitoring&vmId=${vmId}&connectionId=${connectionId}`,
        {
          method: 'POST',
          body: JSON.stringify({ config: {} }),
        }
      );

      setIsMonitoring(true);
      onMonitoringEvent?.(vmId, 'monitoring_started', { vmId, connectionId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start monitoring';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onMonitoringEvent]);

  // Stop monitoring
  const stopMonitoring = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm/monitoring?action=stop-monitoring&vmId=${vmId}&connectionId=${connectionId}`,
        {
          method: 'POST',
          body: JSON.stringify({ config: {} }),
        }
      );

      setIsMonitoring(false);
      
      // Clear refresh interval
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }

      onMonitoringEvent?.(vmId, 'monitoring_stopped', { vmId, connectionId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop monitoring';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onMonitoringEvent]);

  // Get metrics
  const getMetrics = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<VMResourceMetrics> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'metrics',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({}))
      });

      const response = await apiCall(`/api/vm/monitoring?${queryParams}`, {
        method: 'GET',
      });

      const rawMetrics: VMResourceMetrics = response.data;
      const enhancedMetrics: VMMonitoringMetrics = {
        ...rawMetrics,
        isRealTime: enableRealTime,
        lastUpdated: new Date()
      };

      setMetrics(enhancedMetrics);
      
      // Store in history
      metricsHistoryRef.current.push(rawMetrics);
      if (metricsHistoryRef.current.length > 100) {
        metricsHistoryRef.current = metricsHistoryRef.current.slice(-100);
      }

      // Check for alerts
      checkAlerts(rawMetrics);

      onMonitoringEvent?.(vmId, 'metrics_updated', enhancedMetrics);

      return rawMetrics;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get metrics';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, enableRealTime, checkAlerts, onMonitoringEvent]);

  // Get system info
  const getSystemInfo = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<VMSystemInfo> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'system-info',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({}))
      });

      const response = await apiCall(`/api/vm/monitoring?${queryParams}`, {
        method: 'GET',
      });

      const sysInfo: VMSystemInfo = response.data;
      setSystemInfo(sysInfo);

      return sysInfo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get system info';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Get processes
  const getProcesses = useCallback(async (
    vmId: string,
    connectionId: string,
    options?: {
      sortBy?: 'cpu' | 'memory' | 'name';
      limit?: number;
      filter?: string;
    }
  ): Promise<any[]> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'processes',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({})),
        ...(options?.sortBy && { sortBy: options.sortBy }),
        ...(options?.limit && { limit: options.limit.toString() }),
        ...(options?.filter && { filter: options.filter })
      });

      const response = await apiCall(`/api/vm/monitoring?${queryParams}`, {
        method: 'GET',
      });

      const processList = response.data;
      setProcesses(processList);

      return processList;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get processes';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Get services
  const getServices = useCallback(async (
    vmId: string,
    connectionId: string,
    serviceNames?: string[]
  ): Promise<any[]> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'services',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({})),
        ...(serviceNames && { serviceNames: JSON.stringify(serviceNames) })
      });

      const response = await apiCall(`/api/vm/monitoring?${queryParams}`, {
        method: 'GET',
      });

      const servicesList = response.data;
      setServices(servicesList);

      return servicesList;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get services';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Acknowledge alert
  const acknowledgeAlert = useCallback(async (
    vmId: string,
    connectionId: string,
    alertId: string
  ): Promise<void> => {
    try {
      setError(null);

      await apiCall(
        `/api/vm/monitoring?action=acknowledge-alert&vmId=${vmId}&connectionId=${connectionId}&alertId=${alertId}`,
        {
          method: 'POST',
          body: JSON.stringify({ config: {} }),
        }
      );

      // Update local alert state
      setAlerts(prev => 
        prev.map(alert => 
          alert.id === alertId 
            ? { ...alert, acknowledged: true }
            : alert
        )
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to acknowledge alert';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Perform health check
  const performHealthCheck = useCallback(async (
    vmId: string,
    connectionId: string
  ): Promise<VMHealthCheck> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'health',
        vmId,
        connectionId,
        config: btoa(JSON.stringify({}))
      });

      const response = await apiCall(`/api/vm/monitoring?${queryParams}`, {
        method: 'GET',
      });

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Health check failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0 && isMonitoring) {
      refreshIntervalRef.current = setInterval(() => {
        // Auto-refresh would need VM ID and connection ID
        // This would be handled by the parent component
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, isMonitoring]);

  return {
    metrics,
    systemInfo,
    alerts,
    processes,
    services,
    isMonitoring,
    loading,
    error,
    startMonitoring,
    stopMonitoring,
    getMetrics,
    getSystemInfo,
    getProcesses,
    getServices,
    acknowledgeAlert,
    performHealthCheck,
  };
}

// Utility hook for monitoring a specific VM
export function useVMMonitoringForVM(
  vmId: string,
  connectionId: string,
  options: VMMonitoringHookOptions = {}
) {
  const monitoringHook = useVMMonitoring(options);

  const startMonitoringForVM = useCallback(
    () => monitoringHook.startMonitoring(vmId, connectionId),
    [monitoringHook.startMonitoring, vmId, connectionId]
  );

  const stopMonitoringForVM = useCallback(
    () => monitoringHook.stopMonitoring(vmId, connectionId),
    [monitoringHook.stopMonitoring, vmId, connectionId]
  );

  const getMetricsForVM = useCallback(
    () => monitoringHook.getMetrics(vmId, connectionId),
    [monitoringHook.getMetrics, vmId, connectionId]
  );

  const getSystemInfoForVM = useCallback(
    () => monitoringHook.getSystemInfo(vmId, connectionId),
    [monitoringHook.getSystemInfo, vmId, connectionId]
  );

  const getProcessesForVM = useCallback(
    (options?: any) => monitoringHook.getProcesses(vmId, connectionId, options),
    [monitoringHook.getProcesses, vmId, connectionId]
  );

  const getServicesForVM = useCallback(
    (serviceNames?: string[]) => monitoringHook.getServices(vmId, connectionId, serviceNames),
    [monitoringHook.getServices, vmId, connectionId]
  );

  const acknowledgeAlertForVM = useCallback(
    (alertId: string) => monitoringHook.acknowledgeAlert(vmId, connectionId, alertId),
    [monitoringHook.acknowledgeAlert, vmId, connectionId]
  );

  const performHealthCheckForVM = useCallback(
    () => monitoringHook.performHealthCheck(vmId, connectionId),
    [monitoringHook.performHealthCheck, vmId, connectionId]
  );

  // Auto-load initial data
  useEffect(() => {
    getSystemInfoForVM();
    getMetricsForVM();
    getProcessesForVM();
    getServicesForVM();
  }, [getSystemInfoForVM, getMetricsForVM, getProcessesForVM, getServicesForVM]);

  return {
    ...monitoringHook,
    startMonitoring: startMonitoringForVM,
    stopMonitoring: stopMonitoringForVM,
    getMetrics: getMetricsForVM,
    getSystemInfo: getSystemInfoForVM,
    getProcesses: getProcessesForVM,
    getServices: getServicesForVM,
    acknowledgeAlert: acknowledgeAlertForVM,
    performHealthCheck: performHealthCheckForVM,
  };
}
