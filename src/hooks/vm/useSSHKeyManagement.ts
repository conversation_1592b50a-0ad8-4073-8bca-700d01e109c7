/**
 * SSH Key Management Hook
 * Custom React hook for managing SSH keys, assignments, and usage
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  SSHKey, 
  SSHKeyGenerationOptions, 
  SSHKeyImportOptions, 
  SSHKeyUsageLog, 
  SSHKeyStats,
  UseSSHKeyManagementReturn,
  SSHKeyEventHandler
} from '@/types/vm-frontend';

interface SSHKeyManagementHookOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onKeyEvent?: SSHKeyEventHandler;
}

export function useSSHKeyManagement(options: SSHKeyManagementHookOptions = {}): UseSSHKeyManagementReturn {
  const {
    autoRefresh = false,
    refreshInterval = 60000, // 1 minute
    onKeyEvent
  } = options;

  const [keys, setKeys] = useState<SSHKey[]>([]);
  const [stats, setStats] = useState<SSHKeyStats>({
    totalKeys: 0,
    activeKeys: 0,
    expiredKeys: 0,
    encryptedKeys: 0,
    keysByType: {},
    recentUsage: [],
    topUsedKeys: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();

  // Cleanup function
  const cleanup = useCallback(() => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // API call helper
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: abortControllerRef.current.signal,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API call failed');
      }

      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      throw error;
    }
  }, []);

  // Generate SSH key pair
  const generateKey = useCallback(async (options: SSHKeyGenerationOptions): Promise<SSHKey> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall('/api/ssh-keys/generate', {
        method: 'POST',
        body: JSON.stringify(options),
      });

      const newKey: SSHKey = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt),
        lastUsed: response.data.lastUsed ? new Date(response.data.lastUsed) : undefined,
        expiresAt: response.data.expiresAt ? new Date(response.data.expiresAt) : undefined,
      };

      setKeys(prev => [...prev, newKey]);
      onKeyEvent?.(newKey.id, 'created', newKey);

      return newKey;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key generation failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onKeyEvent]);

  // Import existing SSH key
  const importKey = useCallback(async (options: SSHKeyImportOptions): Promise<SSHKey> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall('/api/ssh-keys/import', {
        method: 'POST',
        body: JSON.stringify(options),
      });

      const newKey: SSHKey = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        updatedAt: new Date(response.data.updatedAt),
        lastUsed: response.data.lastUsed ? new Date(response.data.lastUsed) : undefined,
        expiresAt: response.data.expiresAt ? new Date(response.data.expiresAt) : undefined,
      };

      setKeys(prev => [...prev, newKey]);
      onKeyEvent?.(newKey.id, 'created', newKey);

      return newKey;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key import failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onKeyEvent]);

  // Update SSH key
  const updateKey = useCallback(async (keyId: string, updates: Partial<SSHKey>): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(`/api/ssh-keys/${keyId}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });

      setKeys(prev => 
        prev.map(key => 
          key.id === keyId 
            ? { ...key, ...updates, updatedAt: new Date() }
            : key
        )
      );

      onKeyEvent?.(keyId, 'updated', updates);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key update failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onKeyEvent]);

  // Delete SSH key
  const deleteKey = useCallback(async (keyId: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(`/api/ssh-keys/${keyId}`, {
        method: 'DELETE',
      });

      setKeys(prev => prev.filter(key => key.id !== keyId));
      onKeyEvent?.(keyId, 'deleted', { keyId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key deletion failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onKeyEvent]);

  // Assign key to VM
  const assignKeyToVM = useCallback(async (keyId: string, vmId: string): Promise<void> => {
    try {
      setError(null);

      await apiCall(`/api/ssh-keys/${keyId}/assign-vm`, {
        method: 'POST',
        body: JSON.stringify({ vmId }),
      });

      setKeys(prev => 
        prev.map(key => 
          key.id === keyId 
            ? { 
                ...key, 
                vmAssignments: [...key.vmAssignments, vmId],
                updatedAt: new Date()
              }
            : key
        )
      );

      onKeyEvent?.(keyId, 'assigned', { vmId, type: 'vm' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key assignment failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, onKeyEvent]);

  // Unassign key from VM
  const unassignKeyFromVM = useCallback(async (keyId: string, vmId: string): Promise<void> => {
    try {
      setError(null);

      await apiCall(`/api/ssh-keys/${keyId}/unassign-vm`, {
        method: 'POST',
        body: JSON.stringify({ vmId }),
      });

      setKeys(prev => 
        prev.map(key => 
          key.id === keyId 
            ? { 
                ...key, 
                vmAssignments: key.vmAssignments.filter(id => id !== vmId),
                updatedAt: new Date()
              }
            : key
        )
      );

      onKeyEvent?.(keyId, 'unassigned', { vmId, type: 'vm' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key unassignment failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, onKeyEvent]);

  // Assign key to user
  const assignKeyToUser = useCallback(async (keyId: string, userId: string): Promise<void> => {
    try {
      setError(null);

      await apiCall(`/api/ssh-keys/${keyId}/assign-user`, {
        method: 'POST',
        body: JSON.stringify({ userId }),
      });

      setKeys(prev => 
        prev.map(key => 
          key.id === keyId 
            ? { 
                ...key, 
                userAssignments: [...key.userAssignments, userId],
                updatedAt: new Date()
              }
            : key
        )
      );

      onKeyEvent?.(keyId, 'assigned', { userId, type: 'user' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'User assignment failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, onKeyEvent]);

  // Unassign key from user
  const unassignKeyFromUser = useCallback(async (keyId: string, userId: string): Promise<void> => {
    try {
      setError(null);

      await apiCall(`/api/ssh-keys/${keyId}/unassign-user`, {
        method: 'POST',
        body: JSON.stringify({ userId }),
      });

      setKeys(prev => 
        prev.map(key => 
          key.id === keyId 
            ? { 
                ...key, 
                userAssignments: key.userAssignments.filter(id => id !== userId),
                updatedAt: new Date()
              }
            : key
        )
      );

      onKeyEvent?.(keyId, 'unassigned', { userId, type: 'user' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'User unassignment failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, onKeyEvent]);

  // Get key usage logs
  const getKeyUsage = useCallback(async (keyId: string): Promise<SSHKeyUsageLog[]> => {
    try {
      setError(null);

      const response = await apiCall(`/api/ssh-keys/${keyId}/usage`, {
        method: 'GET',
      });

      return response.data.map((log: any) => ({
        ...log,
        timestamp: new Date(log.timestamp)
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get key usage';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Export SSH key
  const exportKey = useCallback(async (keyId: string, includePrivate: boolean): Promise<string> => {
    try {
      setError(null);

      const response = await apiCall(`/api/ssh-keys/${keyId}/export`, {
        method: 'POST',
        body: JSON.stringify({ includePrivate }),
      });

      onKeyEvent?.(keyId, 'exported', { includePrivate });
      return response.data.keyData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key export failed';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, onKeyEvent]);

  // Test SSH key connection
  const testKey = useCallback(async (keyId: string, vmId: string): Promise<boolean> => {
    try {
      setError(null);

      const response = await apiCall(`/api/ssh-keys/${keyId}/test`, {
        method: 'POST',
        body: JSON.stringify({ vmId }),
      });

      onKeyEvent?.(keyId, 'tested', { vmId, success: response.data.success });
      return response.data.success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Key test failed';
      setError(errorMessage);
      onKeyEvent?.(keyId, 'tested', { vmId, success: false, error: errorMessage });
      return false;
    }
  }, [apiCall, onKeyEvent]);

  // Refresh keys and stats
  const refreshKeys = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const [keysResponse, statsResponse] = await Promise.all([
        apiCall('/api/ssh-keys', { method: 'GET' }),
        apiCall('/api/ssh-keys/stats', { method: 'GET' })
      ]);

      const keysData = keysResponse.data.map((key: any) => ({
        ...key,
        createdAt: new Date(key.createdAt),
        updatedAt: new Date(key.updatedAt),
        lastUsed: key.lastUsed ? new Date(key.lastUsed) : undefined,
        expiresAt: key.expiresAt ? new Date(key.expiresAt) : undefined,
      }));

      const statsData = {
        ...statsResponse.data,
        recentUsage: statsResponse.data.recentUsage.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }))
      };

      setKeys(keysData);
      setStats(statsData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh keys';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        refreshKeys();
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, refreshKeys]);

  // Initial load
  useEffect(() => {
    refreshKeys();
  }, [refreshKeys]);

  return {
    keys,
    stats,
    loading,
    error,
    generateKey,
    importKey,
    updateKey,
    deleteKey,
    assignKeyToVM,
    unassignKeyFromVM,
    assignKeyToUser,
    unassignKeyFromUser,
    getKeyUsage,
    exportKey,
    testKey,
    refreshKeys,
  };
}
