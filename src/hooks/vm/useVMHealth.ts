/**
 * VM Health Hook
 * Custom React hook for health checks and status monitoring
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { 
  UseVMHealthReturn, 
  VMHealthStatus,
  VMHealthEventHandler 
} from '@/types/vm-frontend';

interface VMHealthHookOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onHealthEvent?: VMHealthEventHandler;
}

export function useVMHealth(options: VMHealthHookOptions = {}): UseVMHealthReturn {
  const {
    autoRefresh = false,
    refreshInterval = 60000, // 1 minute
    onHealthEvent
  } = options;

  const [health, setHealth] = useState<VMHealthStatus | undefined>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoRefreshEnabled, setAutoRefresh] = useState(autoRefresh);
  const [currentRefreshInterval, setRefreshInterval] = useState(refreshInterval);

  const refreshIntervalRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const healthHistoryRef = useRef<VMHealthStatus['history']>([]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // API call helper
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: abortControllerRef.current.signal,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API call failed');
      }

      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      throw error;
    }
  }, []);

  // Update health history
  const updateHealthHistory = useCallback((newHealth: VMHealthStatus) => {
    const historyEntry = {
      timestamp: new Date(),
      status: newHealth.overall,
      responseTime: newHealth.responseTime
    };

    healthHistoryRef.current.push(historyEntry);
    
    // Keep only last 100 entries
    if (healthHistoryRef.current.length > 100) {
      healthHistoryRef.current = healthHistoryRef.current.slice(-100);
    }

    // Update health with history
    setHealth(prev => ({
      ...newHealth,
      history: [...healthHistoryRef.current]
    }));
  }, []);

  // Run health check
  const runHealthCheck = useCallback(async (
    vmId: string,
    detailed: boolean = false
  ): Promise<VMHealthStatus> => {
    try {
      setLoading(true);
      setError(null);

      const startTime = Date.now();
      
      const queryParams = new URLSearchParams({
        vmId,
        detailed: detailed.toString()
      });

      const response = await apiCall(`/api/vm/health?${queryParams}`, {
        method: 'GET',
      });

      const responseTime = Date.now() - startTime;
      
      let healthData: VMHealthStatus;

      if (detailed && response.data.overall) {
        // Detailed health check response
        healthData = {
          overall: response.data.overall,
          services: response.data.services,
          lastCheck: new Date(),
          responseTime: response.data.responseTime || responseTime,
          details: response.data.details,
          history: healthHistoryRef.current
        };
      } else {
        // Basic health check response
        const status = response.data.status === 'healthy' ? 'healthy' : 
                     response.data.status === 'degraded' ? 'degraded' : 'unhealthy';
        
        healthData = {
          overall: status,
          services: {
            connection: response.data.connection !== false,
            ssh: true,
            docker: true,
            system: true,
            resources: true,
            monitoring: true,
            security: true
          },
          lastCheck: new Date(),
          responseTime,
          history: healthHistoryRef.current
        };
      }

      updateHealthHistory(healthData);
      onHealthEvent?.(vmId, 'health_check_completed', healthData);

      // Check if status changed
      if (health && health.overall !== healthData.overall) {
        onHealthEvent?.(vmId, 'health_status_changed', {
          previous: health.overall,
          current: healthData.overall
        });
      }

      return healthData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Health check failed';
      setError(errorMessage);
      
      // Create error health status
      const errorHealth: VMHealthStatus = {
        overall: 'unknown',
        services: {
          connection: false,
          ssh: false,
          docker: false,
          system: false,
          resources: false,
          monitoring: false,
          security: false
        },
        lastCheck: new Date(),
        responseTime: 0,
        details: { error: errorMessage },
        history: healthHistoryRef.current
      };

      setHealth(errorHealth);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, updateHealthHistory, onHealthEvent, health]);

  // Get health history
  const getHealthHistory = useCallback(async (
    vmId: string,
    limit: number = 50
  ): Promise<VMHealthStatus['history']> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'history',
        vmId,
        limit: limit.toString()
      });

      const response = await apiCall(`/api/vm/health?${queryParams}`, {
        method: 'GET',
      });

      const history = response.data.map((entry: any) => ({
        timestamp: new Date(entry.timestamp),
        status: entry.status,
        responseTime: entry.responseTime
      }));

      healthHistoryRef.current = history;
      
      // Update current health with new history
      if (health) {
        setHealth(prev => prev ? { ...prev, history } : prev);
      }

      return history;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get health history';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall, health]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefreshEnabled && currentRefreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        // Auto-refresh would need VM ID
        // This would be handled by the parent component
      }, currentRefreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefreshEnabled, currentRefreshInterval]);

  return {
    health,
    loading,
    error,
    runHealthCheck,
    getHealthHistory,
    autoRefresh: autoRefreshEnabled,
    setAutoRefresh,
    refreshInterval: currentRefreshInterval,
    setRefreshInterval,
  };
}

// Utility hook for health monitoring of a specific VM
export function useVMHealthForVM(
  vmId: string,
  options: VMHealthHookOptions = {}
) {
  const healthHook = useVMHealth(options);

  const runHealthCheckForVM = useCallback(
    (detailed?: boolean) => healthHook.runHealthCheck(vmId, detailed),
    [healthHook.runHealthCheck, vmId]
  );

  const getHealthHistoryForVM = useCallback(
    (limit?: number) => healthHook.getHealthHistory(vmId, limit),
    [healthHook.getHealthHistory, vmId]
  );

  // Auto-run initial health check
  useEffect(() => {
    runHealthCheckForVM(false);
  }, [runHealthCheckForVM]);

  // Auto-refresh effect
  useEffect(() => {
    if (healthHook.autoRefresh && healthHook.refreshInterval > 0) {
      const interval = setInterval(() => {
        runHealthCheckForVM(false);
      }, healthHook.refreshInterval);

      return () => clearInterval(interval);
    }
  }, [healthHook.autoRefresh, healthHook.refreshInterval, runHealthCheckForVM]);

  return {
    ...healthHook,
    runHealthCheck: runHealthCheckForVM,
    getHealthHistory: getHealthHistoryForVM,
  };
}

// Hook for comprehensive VM health monitoring with detailed checks
export function useVMHealthMonitor(
  vmId: string,
  options: VMHealthHookOptions & {
    enableDetailedChecks?: boolean;
    alertOnStatusChange?: boolean;
  } = {}
) {
  const {
    enableDetailedChecks = true,
    alertOnStatusChange = true,
    ...healthOptions
  } = options;

  const healthHook = useVMHealthForVM(vmId, {
    ...healthOptions,
    onHealthEvent: (vmId, event, data) => {
      if (alertOnStatusChange && event === 'health_status_changed') {
        // Could trigger notifications here
        console.warn(`VM ${vmId} health status changed:`, data);
      }
      healthOptions.onHealthEvent?.(vmId, event, data);
    }
  });

  const runDetailedHealthCheck = useCallback(
    () => healthHook.runHealthCheck(enableDetailedChecks),
    [healthHook.runHealthCheck, enableDetailedChecks]
  );

  // Enhanced health status with additional computed properties
  const enhancedHealth = healthHook.health ? {
    ...healthHook.health,
    isHealthy: healthHook.health.overall === 'healthy',
    isDegraded: healthHook.health.overall === 'degraded',
    isUnhealthy: healthHook.health.overall === 'unhealthy',
    serviceCount: Object.keys(healthHook.health.services).length,
    healthyServiceCount: Object.values(healthHook.health.services).filter(Boolean).length,
    healthPercentage: Math.round(
      (Object.values(healthHook.health.services).filter(Boolean).length / 
       Object.keys(healthHook.health.services).length) * 100
    ),
    lastCheckAgo: Date.now() - healthHook.health.lastCheck.getTime(),
    averageResponseTime: healthHook.health.history.length > 0 
      ? healthHook.health.history.reduce((sum, entry) => sum + entry.responseTime, 0) / healthHook.health.history.length
      : healthHook.health.responseTime
  } : undefined;

  return {
    ...healthHook,
    health: enhancedHealth,
    runHealthCheck: runDetailedHealthCheck,
  };
}
