/**
 * VM Security Hook
 * Custom React hook for authentication and access control
 */

import { useState, useCallback, useRef } from 'react';
import { VMAuthCredentials } from '@/types/vm';
import { 
  UseVMSecurityReturn, 
  VMSecurityUser, 
  VMSecurityAuditEntry, 
  VMSecurityEvent,
  VMSecurityEventHandler 
} from '@/types/vm-frontend';

interface VMSecurityHookOptions {
  onSecurityEvent?: VMSecurityEventHandler;
}

export function useVMSecurity(options: VMSecurityHookOptions = {}): UseVMSecurityReturn {
  const { onSecurityEvent } = options;

  const [users, setUsers] = useState<VMSecurityUser[]>([]);
  const [auditLog, setAuditLog] = useState<VMSecurityAuditEntry[]>([]);
  const [securityEvents, setSecurityEvents] = useState<VMSecurityEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const abortControllerRef = useRef<AbortController>();

  // API call helper
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        signal: abortControllerRef.current.signal,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error?.message || 'API call failed');
      }

      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request was cancelled');
      }
      throw error;
    }
  }, []);

  // Create user
  const createUser = useCallback(async (
    vmId: string,
    userData: Omit<VMSecurityUser, 'id' | 'createdAt' | 'lastLogin'>
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall(
        `/api/vm/security?action=create-user&vmId=${vmId}`,
        {
          method: 'POST',
          body: JSON.stringify({ userData }),
        }
      );

      const newUser: VMSecurityUser = {
        ...userData,
        id: response.data.id,
        createdAt: new Date(response.data.createdAt),
        lastLogin: response.data.lastLogin ? new Date(response.data.lastLogin) : undefined
      };

      setUsers(prev => [...prev, newUser]);
      onSecurityEvent?.(vmId, 'user_created', newUser);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create user';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onSecurityEvent]);

  // Update user
  const updateUser = useCallback(async (
    vmId: string,
    userId: string,
    updates: Partial<VMSecurityUser>
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm/security?action=update-user&vmId=${vmId}&userId=${userId}`,
        {
          method: 'PUT',
          body: JSON.stringify({ updates }),
        }
      );

      setUsers(prev => 
        prev.map(user => 
          user.id === userId 
            ? { ...user, ...updates }
            : user
        )
      );

      onSecurityEvent?.(vmId, 'user_updated', { userId, updates });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onSecurityEvent]);

  // Delete user
  const deleteUser = useCallback(async (
    vmId: string,
    userId: string
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm/security?action=delete-user&vmId=${vmId}&userId=${userId}`,
        { method: 'DELETE' }
      );

      setUsers(prev => prev.filter(user => user.id !== userId));
      onSecurityEvent?.(vmId, 'user_deleted', { userId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete user';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onSecurityEvent]);

  // Authenticate user
  const authenticateUser = useCallback(async (
    vmId: string,
    userId: string,
    credentials: VMAuthCredentials,
    clientInfo?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<{ token: string; expiresAt: Date }> => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall(
        `/api/vm/security?action=authenticate&vmId=${vmId}&userId=${userId}`,
        {
          method: 'POST',
          body: JSON.stringify({ credentials, clientInfo }),
        }
      );

      return {
        token: response.data.token,
        expiresAt: new Date(response.data.expiresAt)
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Validate token
  const validateToken = useCallback(async (
    vmId: string,
    token: string
  ): Promise<boolean> => {
    try {
      setError(null);

      const response = await apiCall(
        `/api/vm/security?action=validate-token&vmId=${vmId}`,
        {
          method: 'POST',
          body: JSON.stringify({ token }),
        }
      );

      return response.data.valid;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Token validation failed';
      setError(errorMessage);
      return false;
    }
  }, [apiCall]);

  // Revoke token
  const revokeToken = useCallback(async (
    vmId: string,
    token: string
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm/security?action=revoke-token&vmId=${vmId}`,
        {
          method: 'POST',
          body: JSON.stringify({ token }),
        }
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to revoke token';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  // Get audit log
  const getAuditLog = useCallback(async (
    vmId: string,
    options?: {
      userId?: string;
      action?: string;
      startTime?: Date;
      endTime?: Date;
      limit?: number;
    }
  ): Promise<VMSecurityAuditEntry[]> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'audit-log',
        vmId,
        ...(options?.userId && { userId: options.userId }),
        ...(options?.action && { actionFilter: options.action }),
        ...(options?.startTime && { startTime: options.startTime.toISOString() }),
        ...(options?.endTime && { endTime: options.endTime.toISOString() }),
        ...(options?.limit && { limit: options.limit.toString() })
      });

      const response = await apiCall(`/api/vm/security?${queryParams}`, {
        method: 'GET',
      });

      const auditEntries = response.data.map((entry: any) => ({
        ...entry,
        timestamp: new Date(entry.timestamp)
      }));

      setAuditLog(auditEntries);
      return auditEntries;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get audit log';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Get security events
  const getSecurityEvents = useCallback(async (
    vmId: string,
    options?: {
      type?: VMSecurityEvent['type'];
      severity?: VMSecurityEvent['severity'];
      resolved?: boolean;
      limit?: number;
    }
  ): Promise<VMSecurityEvent[]> => {
    try {
      setError(null);

      const queryParams = new URLSearchParams({
        action: 'security-events',
        vmId,
        ...(options?.type && { type: options.type }),
        ...(options?.severity && { severity: options.severity }),
        ...(options?.resolved !== undefined && { resolved: options.resolved.toString() }),
        ...(options?.limit && { limit: options.limit.toString() })
      });

      const response = await apiCall(`/api/vm/security?${queryParams}`, {
        method: 'GET',
      });

      const events = response.data.map((event: any) => ({
        ...event,
        timestamp: new Date(event.timestamp),
        resolvedAt: event.resolvedAt ? new Date(event.resolvedAt) : undefined
      }));

      setSecurityEvents(events);
      return events;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get security events';
      setError(errorMessage);
      throw error;
    }
  }, [apiCall]);

  // Resolve security event
  const resolveSecurityEvent = useCallback(async (
    vmId: string,
    eventId: string
  ): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(
        `/api/vm/security?action=resolve-event&vmId=${vmId}&eventId=${eventId}`,
        { method: 'PUT' }
      );

      setSecurityEvents(prev => 
        prev.map(event => 
          event.id === eventId 
            ? { ...event, resolved: true, resolvedAt: new Date() }
            : event
        )
      );

      onSecurityEvent?.(vmId, 'security_event', { eventId, resolved: true });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to resolve security event';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [apiCall, onSecurityEvent]);

  return {
    users,
    auditLog,
    securityEvents,
    loading,
    error,
    createUser,
    updateUser,
    deleteUser,
    authenticateUser,
    validateToken,
    revokeToken,
    getAuditLog,
    getSecurityEvents,
    resolveSecurityEvent,
  };
}

// Utility hook for managing security for a specific VM
export function useVMSecurityForVM(
  vmId: string,
  options: VMSecurityHookOptions = {}
) {
  const securityHook = useVMSecurity(options);

  const createUserForVM = useCallback(
    (userData: Omit<VMSecurityUser, 'id' | 'createdAt' | 'lastLogin'>) => 
      securityHook.createUser(vmId, userData),
    [securityHook.createUser, vmId]
  );

  const updateUserForVM = useCallback(
    (userId: string, updates: Partial<VMSecurityUser>) => 
      securityHook.updateUser(vmId, userId, updates),
    [securityHook.updateUser, vmId]
  );

  const deleteUserForVM = useCallback(
    (userId: string) => securityHook.deleteUser(vmId, userId),
    [securityHook.deleteUser, vmId]
  );

  const authenticateUserForVM = useCallback(
    (userId: string, credentials: VMAuthCredentials, clientInfo?: any) => 
      securityHook.authenticateUser(vmId, userId, credentials, clientInfo),
    [securityHook.authenticateUser, vmId]
  );

  const validateTokenForVM = useCallback(
    (token: string) => securityHook.validateToken(vmId, token),
    [securityHook.validateToken, vmId]
  );

  const revokeTokenForVM = useCallback(
    (token: string) => securityHook.revokeToken(vmId, token),
    [securityHook.revokeToken, vmId]
  );

  const getAuditLogForVM = useCallback(
    (options?: any) => securityHook.getAuditLog(vmId, options),
    [securityHook.getAuditLog, vmId]
  );

  const getSecurityEventsForVM = useCallback(
    (options?: any) => securityHook.getSecurityEvents(vmId, options),
    [securityHook.getSecurityEvents, vmId]
  );

  const resolveSecurityEventForVM = useCallback(
    (eventId: string) => securityHook.resolveSecurityEvent(vmId, eventId),
    [securityHook.resolveSecurityEvent, vmId]
  );

  return {
    ...securityHook,
    createUser: createUserForVM,
    updateUser: updateUserForVM,
    deleteUser: deleteUserForVM,
    authenticateUser: authenticateUserForVM,
    validateToken: validateTokenForVM,
    revokeToken: revokeTokenForVM,
    getAuditLog: getAuditLogForVM,
    getSecurityEvents: getSecurityEventsForVM,
    resolveSecurityEvent: resolveSecurityEventForVM,
  };
}
