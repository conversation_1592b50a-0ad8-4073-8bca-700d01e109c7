/**
 * VM Hooks Index
 * Exports all VM-related custom React hooks
 */

// Export VM Connection hooks
export { 
  useVMConnection, 
  useVMSingleConnection 
} from './useVMConnection';

// Export VM Docker hooks
export { 
  useVMDocker, 
  useVMDockerForVM 
} from './useVMDocker';

// Export VM Monitoring hooks
export { 
  useVMMonitoring, 
  useVMMonitoringForVM 
} from './useVMMonitoring';

// Export VM Security hooks
export { 
  useVMSecurity, 
  useVMSecurityForVM 
} from './useVMSecurity';

// Export VM Health hooks
export {
  useVMHealth,
  useVMHealthForVM,
  useVMHealthMonitor
} from './useVMHealth';

// Export SSH Key Management hooks
export {
  useSSHKeyManagement
} from './useSSHKeyManagement';

// Re-export types for convenience
export type {
  UseVMConnectionReturn,
  UseVMDockerReturn,
  UseVMMonitoringReturn,
  UseVMSecurityReturn,
  UseVMHealthReturn,
  UseSSHKeyManagementReturn,
  VMConnectionEventHandler,
  VMDockerEventHandler,
  VMMonitoringEventHandler,
  VMSecurityEventHandler,
  VMHealthEventHandler,
  SSHKeyEventHandler
} from '@/types/vm-frontend';
