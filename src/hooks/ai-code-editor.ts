// Export all AI Code Editor hooks
export { useAICodeCompletion } from './use-ai-code-completion';
export { useCodeAnalysis } from './use-code-analysis';
export { useAIAssistant } from './use-ai-assistant';
export { useCodeRefactoring } from './use-code-refactoring';
export { useAIEditorState } from './use-ai-editor-state';

// Re-export types for convenience
export type {
  UseAICodeCompletionReturn,
  UseCodeAnalysisReturn,
  UseAIAssistantReturn,
  UseCodeRefactoringReturn,
  UseAIEditorStateReturn,
} from '@/types/ai-code-editor';
