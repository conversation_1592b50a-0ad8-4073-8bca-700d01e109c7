/**
 * Node/React AI Editor Types
 * Types and interfaces for AI-powered Node.js and React code editor functionality
 */

import { NodeFramework } from './node-react-workspace';

export interface NodeReactProjectStructure {
  name: string;
  framework: NodeFramework;
  rootPath: string;
  packageJson: any;
  files: NodeReactFileInfo[];
  dependencies: string[];
  devDependencies: string[];
  scripts: Record<string, string>;
  tsConfig?: any;
  eslintConfig?: any;
  prettier?: any;
}

export interface NodeReactFileInfo {
  path: string;
  name: string;
  type: 'javascript' | 'typescript' | 'jsx' | 'tsx' | 'json' | 'css' | 'scss' | 'less' | 'html' | 'md' | 'config';
  size: number;
  lastModified: Date;
  isDirectory: boolean;
  children?: NodeReactFileInfo[];
  content?: string;
  language?: string;
}

export interface NodeReactCodeCompletion {
  text: string;
  displayText: string;
  type: 'function' | 'variable' | 'class' | 'interface' | 'component' | 'hook' | 'import' | 'snippet';
  detail?: string;
  documentation?: string;
  insertText?: string;
  range?: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
  sortText?: string;
  filterText?: string;
  additionalTextEdits?: Array<{
    range: { start: { line: number; character: number }; end: { line: number; character: number } };
    newText: string;
  }>;
  framework?: NodeFramework;
  category?: 'react' | 'node' | 'framework' | 'library' | 'builtin';
}

export interface NodeReactCodeAnalysis {
  file: string;
  framework: NodeFramework;
  issues: NodeReactCodeIssue[];
  suggestions: NodeReactCodeSuggestion[];
  metrics: NodeReactCodeMetrics;
  dependencies: NodeReactDependencyAnalysis[];
  components?: NodeReactComponentAnalysis[];
  hooks?: NodeReactHookAnalysis[];
  apis?: NodeReactAPIAnalysis[];
}

export interface NodeReactCodeIssue {
  id: string;
  severity: 'error' | 'warning' | 'info' | 'hint';
  message: string;
  range: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
  source: 'typescript' | 'eslint' | 'ai-analysis' | 'framework-specific';
  code?: string;
  fixes?: NodeReactCodeFix[];
  category: 'syntax' | 'type' | 'performance' | 'security' | 'accessibility' | 'best-practice' | 'framework';
}

export interface NodeReactCodeFix {
  title: string;
  description: string;
  edits: Array<{
    range: { start: { line: number; character: number }; end: { line: number; character: number } };
    newText: string;
  }>;
  kind: 'quickfix' | 'refactor' | 'source' | 'organize-imports';
}

export interface NodeReactCodeSuggestion {
  id: string;
  title: string;
  description: string;
  type: 'optimization' | 'refactoring' | 'modernization' | 'framework-upgrade' | 'best-practice';
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  framework?: NodeFramework;
  code?: string;
  before?: string;
  after?: string;
  benefits: string[];
}

export interface NodeReactCodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainabilityIndex: number;
  testCoverage?: number;
  dependencies: number;
  components?: number;
  hooks?: number;
  apiEndpoints?: number;
  bundleSize?: {
    raw: number;
    gzipped: number;
    parsed: number;
  };
}

export interface NodeReactDependencyAnalysis {
  name: string;
  version: string;
  type: 'dependency' | 'devDependency' | 'peerDependency';
  usage: 'used' | 'unused' | 'unknown';
  security: {
    vulnerabilities: number;
    severity: 'low' | 'medium' | 'high' | 'critical' | 'none';
  };
  updates: {
    current: string;
    latest: string;
    wanted: string;
    type: 'major' | 'minor' | 'patch' | 'none';
  };
  size: {
    bundled: number;
    gzipped: number;
  };
}

export interface NodeReactComponentAnalysis {
  name: string;
  type: 'functional' | 'class' | 'memo' | 'forwardRef';
  props: Array<{
    name: string;
    type: string;
    required: boolean;
    defaultValue?: string;
  }>;
  hooks: string[];
  complexity: number;
  renderCount?: number;
  performance: {
    reRenders: number;
    avgRenderTime: number;
    memoryUsage: number;
  };
  accessibility: {
    score: number;
    issues: string[];
  };
  testCoverage?: number;
}

export interface NodeReactHookAnalysis {
  name: string;
  type: 'built-in' | 'custom';
  usage: Array<{
    component: string;
    line: number;
    dependencies?: string[];
  }>;
  performance: {
    avgExecutionTime: number;
    memoryUsage: number;
  };
  bestPractices: {
    score: number;
    violations: string[];
  };
}

export interface NodeReactAPIAnalysis {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  framework: 'express' | 'nestjs' | 'nextjs';
  parameters: Array<{
    name: string;
    type: string;
    required: boolean;
    source: 'query' | 'body' | 'params' | 'headers';
  }>;
  responses: Array<{
    status: number;
    description: string;
    schema?: any;
  }>;
  middleware: string[];
  security: {
    authentication: boolean;
    authorization: boolean;
    validation: boolean;
    rateLimit: boolean;
  };
  performance: {
    avgResponseTime: number;
    throughput: number;
  };
  documentation: {
    hasSwagger: boolean;
    hasJSDoc: boolean;
    completeness: number;
  };
}

export interface NodeReactRefactoringAction {
  id: string;
  title: string;
  description: string;
  type: 'extract-component' | 'extract-hook' | 'optimize-imports' | 'modernize-syntax' | 'add-types' | 'split-file';
  framework: NodeFramework;
  scope: 'file' | 'component' | 'function' | 'selection';
  changes: Array<{
    file: string;
    edits: Array<{
      range: { start: { line: number; character: number }; end: { line: number; character: number } };
      newText: string;
    }>;
  }>;
  newFiles?: Array<{
    path: string;
    content: string;
  }>;
  impact: {
    filesChanged: number;
    linesAdded: number;
    linesRemoved: number;
    complexity: 'reduced' | 'same' | 'increased';
  };
}

export interface NodeReactCodeGeneration {
  type: 'component' | 'hook' | 'api' | 'test' | 'config' | 'utility';
  framework: NodeFramework;
  template: string;
  options: Record<string, any>;
  files: Array<{
    path: string;
    content: string;
    language: string;
  }>;
  dependencies?: string[];
  devDependencies?: string[];
  instructions?: string[];
}

export interface NodeReactAIAssistantContext {
  workspaceId: string;
  projectStructure: NodeReactProjectStructure;
  currentFile?: NodeReactFileInfo;
  selectedText?: string;
  cursorPosition?: { line: number; character: number };
  recentFiles: string[];
  framework: NodeFramework;
  typescript: boolean;
  packageManager: 'npm' | 'yarn' | 'pnpm';
}

export interface NodeReactAIAssistantRequest {
  type: 'completion' | 'analysis' | 'refactoring' | 'generation' | 'explanation' | 'debugging';
  context: NodeReactAIAssistantContext;
  prompt: string;
  options?: {
    includeTests?: boolean;
    includeTypes?: boolean;
    includeDocumentation?: boolean;
    optimizePerformance?: boolean;
    followBestPractices?: boolean;
  };
}

export interface NodeReactAIAssistantResponse {
  type: 'completion' | 'analysis' | 'refactoring' | 'generation' | 'explanation' | 'debugging';
  success: boolean;
  data?: any;
  error?: string;
  suggestions?: NodeReactCodeSuggestion[];
  completions?: NodeReactCodeCompletion[];
  analysis?: NodeReactCodeAnalysis;
  refactoring?: NodeReactRefactoringAction;
  generation?: NodeReactCodeGeneration;
  explanation?: string;
  confidence: number;
  processingTime: number;
}
