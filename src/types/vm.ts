export interface MicroVM {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  cpu: number;
  memory: number; // in MB
  diskSize: number; // in GB
  vncPort: number;
  createdAt: Date;
  lastAccessed?: Date;
  ipAddress?: string;
  osType?: 'ubuntu' | 'debian' | 'alpine' | 'windows';
}

export interface VMConfiguration {
  name: string;
  cpu: number;
  memory: number;
  diskSize: number;
  osType?: string;
  networkConfig?: NetworkConfig;
}

export interface NetworkConfig {
  bridge?: string;
  ipAddress?: string;
  gateway?: string;
  dns?: string[];
}

export interface VNCConnection {
  url: string;
  port: number;
  password?: string;
  quality?: 'high' | 'medium' | 'low';
  compression?: boolean;
}

export interface FirecrackerConfig {
  kernelImagePath: string;
  rootfsImagePath: string;
  bootArgs: string;
  vcpuCount: number;
  memSizeMib: number;
  htEnabled: boolean;
  networkInterfaces: NetworkInterface[];
  drives: Drive[];
}

export interface NetworkInterface {
  ifaceId: string;
  guestMac: string;
  hostDevName: string;
}

export interface Drive {
  driveId: string;
  pathOnHost: string;
  isRootDevice: boolean;
  isReadOnly: boolean;
}

// VM Connection and Service Types
export interface VMConnectionConfig {
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  passphrase?: string;
  timeout?: number;
  keepAlive?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export interface VMConnectionStatus {
  isConnected: boolean;
  connectionId: string;
  establishedAt?: Date;
  lastActivity?: Date;
  latency?: number;
  error?: string;
}

export interface VMAuthCredentials {
  type: 'password' | 'key' | 'certificate';
  username: string;
  password?: string;
  privateKey?: string;
  publicKey?: string;
  certificate?: string;
  passphrase?: string;
  expiresAt?: Date;
}

export interface VMSystemInfo {
  hostname: string;
  platform: string;
  arch: string;
  release: string;
  uptime: number;
  loadAverage: number[];
  totalMemory: number;
  freeMemory: number;
  cpuCount: number;
  diskUsage: DiskUsage[];
  networkInterfaces: NetworkInterfaceInfo[];
}

export interface DiskUsage {
  filesystem: string;
  size: number;
  used: number;
  available: number;
  usePercent: number;
  mountPoint: string;
}

export interface NetworkInterfaceInfo {
  name: string;
  address: string;
  netmask: string;
  family: 'IPv4' | 'IPv6';
  mac: string;
  internal: boolean;
  cidr: string;
}

export interface VMResourceMetrics {
  timestamp: Date;
  cpu: {
    usage: number;
    loadAverage: number[];
    processes: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    cached: number;
    buffers: number;
    usagePercent: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usagePercent: number;
    ioStats: {
      readBytes: number;
      writeBytes: number;
      readOps: number;
      writeOps: number;
    };
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    errorsIn: number;
    errorsOut: number;
  };
}

export interface VMDockerInfo {
  version: string;
  apiVersion: string;
  gitCommit: string;
  goVersion: string;
  os: string;
  arch: string;
  kernelVersion: string;
  buildTime: string;
  containers: number;
  containersRunning: number;
  containersPaused: number;
  containersStopped: number;
  images: number;
  driver: string;
  driverStatus: Array<[string, string]>;
  systemStatus: Array<[string, string]>;
  plugins: {
    volume: string[];
    network: string[];
    authorization: string[];
    log: string[];
  };
  memoryLimit: boolean;
  swapLimit: boolean;
  kernelMemory: boolean;
  cpuCfsPeriod: boolean;
  cpuCfsQuota: boolean;
  cpuShares: boolean;
  cpuSet: boolean;
  pidsLimit: boolean;
  ipv4Forwarding: boolean;
  bridgeNfIptables: boolean;
  bridgeNfIp6tables: boolean;
  debug: boolean;
  nfd: number;
  oomKillDisable: boolean;
  ngoroutines: number;
  systemTime: string;
  loggingDriver: string;
  cgroupDriver: string;
  nEventsListener: number;
  defaultRuntime: string;
  liveRestoreEnabled: boolean;
  isolation: string;
  initBinary: string;
  containerdCommit: {
    id: string;
    expected: string;
  };
  runcCommit: {
    id: string;
    expected: string;
  };
  initCommit: {
    id: string;
    expected: string;
  };
  securityOptions: string[];
}

export interface VMContainerOperation {
  operation: 'create' | 'start' | 'stop' | 'restart' | 'remove' | 'exec' | 'logs';
  containerId?: string;
  containerName?: string;
  image?: string;
  command?: string[];
  environment?: Record<string, string>;
  ports?: Record<string, string>;
  volumes?: Record<string, string>;
  workingDir?: string;
  user?: string;
  privileged?: boolean;
  networkMode?: string;
  restartPolicy?: string;
  autoRemove?: boolean;
  detach?: boolean;
  interactive?: boolean;
  tty?: boolean;
}

export interface VMOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: Date;
    operation: string;
    duration: number;
    vmId: string;
    connectionId: string;
  };
}

export interface VMHealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded' | 'unknown';
  checks: {
    connection: boolean;
    ssh: boolean;
    docker: boolean;
    system: boolean;
    resources: boolean;
  };
  lastCheck: Date;
  nextCheck: Date;
  details?: {
    connection?: string;
    ssh?: string;
    docker?: string;
    system?: string;
    resources?: string;
  };
}

export interface VMSecurityConfig {
  allowedUsers: string[];
  allowedCommands: string[];
  restrictedPaths: string[];
  maxConcurrentConnections: number;
  sessionTimeout: number;
  requireKeyAuth: boolean;
  allowPasswordAuth: boolean;
  enableAuditLog: boolean;
  firewallRules: FirewallRule[];
}

export interface FirewallRule {
  id: string;
  action: 'allow' | 'deny';
  protocol: 'tcp' | 'udp' | 'icmp' | 'all';
  sourceIp?: string;
  sourcePort?: string;
  destinationIp?: string;
  destinationPort?: string;
  description?: string;
  enabled: boolean;
}

export interface VMSession {
  id: string;
  vmId: string;
  userId: string;
  connectionId: string;
  startTime: Date;
  lastActivity: Date;
  isActive: boolean;
  operations: VMSessionOperation[];
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    location?: string;
  };
}

export interface VMSessionOperation {
  id: string;
  type: 'command' | 'file_transfer' | 'docker_operation' | 'system_query';
  command?: string;
  result?: any;
  timestamp: Date;
  duration: number;
  success: boolean;
  error?: string;
}
