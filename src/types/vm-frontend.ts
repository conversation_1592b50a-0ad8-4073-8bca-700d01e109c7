/**
 * VM Frontend Types and Interfaces
 * Comprehensive TypeScript types for VM frontend components and hooks
 */

import { ReactNode } from 'react';
import { 
  VMConnectionConfig, 
  VMConnectionStatus, 
  VMResourceMetrics, 
  VMHealthCheck,
  VMSystemInfo,
  VMDockerInfo,
  VMSession,
  VMAuthCredentials,
  VMSecurityConfig
} from '@/types/vm';

// Base component props
export interface BaseVMComponentProps {
  className?: string;
  children?: ReactNode;
  animate?: boolean;
  variant?: 'default' | 'glass' | 'gradient';
}

// VM Connection Frontend Types
export interface VMConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  connectionId?: string;
  status?: VMConnectionStatus;
  error?: string;
  lastConnected?: Date;
  retryCount: number;
}

export interface VMConnectionFormData {
  vmId: string;
  host: string;
  port: number;
  username: string;
  authType: 'password' | 'key';
  password?: string;
  privateKey?: string;
  passphrase?: string;
  saveCredentials: boolean;
}

export interface VMConnectionCardProps extends BaseVMComponentProps {
  vmId: string;
  config: VMConnectionConfig;
  status: VMConnectionState;
  onConnect: (config: VMConnectionConfig) => Promise<void>;
  onDisconnect: () => Promise<void>;
  onEdit: () => void;
  onDelete: () => void;
}

export interface VMConnectionFormProps extends BaseVMComponentProps {
  initialData?: Partial<VMConnectionFormData>;
  onSubmit: (data: VMConnectionFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

// VM Docker Frontend Types
export interface VMDockerContainerState {
  id: string;
  name: string;
  image: string;
  status: 'created' | 'running' | 'paused' | 'restarting' | 'removing' | 'exited' | 'dead';
  state: string;
  ports: Array<{
    privatePort: number;
    publicPort?: number;
    type: 'tcp' | 'udp';
  }>;
  created: Date;
  startedAt?: Date;
  finishedAt?: Date;
  isLoading?: boolean;
  error?: string;
}

export interface VMDockerCreateContainerData {
  name: string;
  image: string;
  command?: string[];
  environment?: Record<string, string>;
  ports?: Record<string, string>;
  volumes?: Record<string, string>;
  networks?: string[];
  restartPolicy?: 'no' | 'always' | 'unless-stopped' | 'on-failure';
  memory?: number;
  cpu?: number;
  privileged?: boolean;
  autoRemove?: boolean;
}

export interface VMDockerDashboardProps extends BaseVMComponentProps {
  vmId: string;
  connectionId: string;
  containers: VMDockerContainerState[];
  dockerInfo?: VMDockerInfo;
  loading: boolean;
  error?: string;
  onCreateContainer: (data: VMDockerCreateContainerData) => Promise<void>;
  onStartContainer: (containerId: string) => Promise<void>;
  onStopContainer: (containerId: string) => Promise<void>;
  onRestartContainer: (containerId: string) => Promise<void>;
  onRemoveContainer: (containerId: string, force?: boolean) => Promise<void>;
  onViewLogs: (containerId: string) => void;
  onExecInContainer: (containerId: string, command: string[]) => Promise<void>;
}

export interface VMDockerContainerCardProps extends BaseVMComponentProps {
  container: VMDockerContainerState;
  onStart: () => Promise<void>;
  onStop: () => Promise<void>;
  onRestart: () => Promise<void>;
  onRemove: (force?: boolean) => Promise<void>;
  onViewLogs: () => void;
  onExec: (command: string[]) => Promise<void>;
}

// VM Monitoring Frontend Types
export interface VMMonitoringMetrics extends VMResourceMetrics {
  isRealTime: boolean;
  lastUpdated: Date;
}

export interface VMMonitoringAlert {
  id: string;
  type: 'cpu' | 'memory' | 'disk' | 'network' | 'service';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  value: number;
  threshold: number;
  triggeredAt: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
}

export interface VMMonitoringDashboardProps extends BaseVMComponentProps {
  vmId: string;
  connectionId: string;
  metrics?: VMMonitoringMetrics;
  systemInfo?: VMSystemInfo;
  alerts: VMMonitoringAlert[];
  processes: Array<{
    pid: number;
    name: string;
    cpu: number;
    memory: number;
    user: string;
    status: string;
  }>;
  services: Array<{
    name: string;
    status: 'active' | 'inactive' | 'failed' | 'unknown';
    enabled: boolean;
    description: string;
  }>;
  loading: boolean;
  error?: string;
  onStartMonitoring: () => Promise<void>;
  onStopMonitoring: () => Promise<void>;
  onAcknowledgeAlert: (alertId: string) => Promise<void>;
  onRefreshMetrics: () => Promise<void>;
}

export interface VMMetricsChartProps extends BaseVMComponentProps {
  title: string;
  data: Array<{
    timestamp: Date;
    value: number;
  }>;
  unit: string;
  color?: string;
  threshold?: number;
  height?: number;
}

export interface VMSystemInfoCardProps extends BaseVMComponentProps {
  systemInfo: VMSystemInfo;
  loading?: boolean;
}

// VM Security Frontend Types
export interface VMSecurityUser {
  id: string;
  username: string;
  permissions: Array<'read' | 'write' | 'admin' | 'owner'>;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  expiresAt?: Date;
  ipWhitelist?: string[];
  sessionTimeout: number;
  maxConcurrentSessions: number;
}

export interface VMSecurityAuditEntry {
  id: string;
  userId: string;
  username: string;
  action: string;
  resource: string;
  timestamp: Date;
  success: boolean;
  ipAddress?: string;
  userAgent?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface VMSecurityEvent {
  id: string;
  type: 'authentication_failure' | 'unauthorized_access' | 'suspicious_activity' | 'policy_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  username?: string;
  description: string;
  timestamp: Date;
  ipAddress?: string;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface VMSecurityDashboardProps extends BaseVMComponentProps {
  vmId: string;
  users: VMSecurityUser[];
  auditLog: VMSecurityAuditEntry[];
  securityEvents: VMSecurityEvent[];
  loading: boolean;
  error?: string;
  onCreateUser: (userData: Omit<VMSecurityUser, 'id' | 'createdAt' | 'lastLogin'>) => Promise<void>;
  onUpdateUser: (userId: string, updates: Partial<VMSecurityUser>) => Promise<void>;
  onDeleteUser: (userId: string) => Promise<void>;
  onResolveSecurityEvent: (eventId: string) => Promise<void>;
  onRefreshAuditLog: () => Promise<void>;
}

export interface VMUserFormProps extends BaseVMComponentProps {
  initialData?: Partial<VMSecurityUser>;
  onSubmit: (userData: Omit<VMSecurityUser, 'id' | 'createdAt' | 'lastLogin'>) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

// VM Health Check Frontend Types
export interface VMHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  services: {
    connection: boolean;
    ssh: boolean;
    docker: boolean;
    system: boolean;
    resources: boolean;
    monitoring: boolean;
    security: boolean;
  };
  lastCheck: Date;
  nextCheck?: Date;
  responseTime: number;
  details?: Record<string, string>;
  history: Array<{
    timestamp: Date;
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime: number;
  }>;
}

export interface VMHealthCheckProps extends BaseVMComponentProps {
  vmId: string;
  health: VMHealthStatus;
  loading: boolean;
  error?: string;
  onRunHealthCheck: () => Promise<void>;
  onViewDetails: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface VMServiceStatusProps extends BaseVMComponentProps {
  serviceName: string;
  status: boolean;
  description?: string;
  lastCheck?: Date;
  error?: string;
}

// VM Dashboard Integration Types
export interface VMDashboardProps extends BaseVMComponentProps {
  vmId: string;
  initialTab?: 'overview' | 'connection' | 'docker' | 'monitoring' | 'security' | 'health';
}

export interface VMOverviewProps extends BaseVMComponentProps {
  vmId: string;
  connectionStatus: VMConnectionState;
  health: VMHealthStatus;
  metrics?: VMMonitoringMetrics;
  containerCount: number;
  activeAlerts: number;
  lastActivity?: Date;
}

// Hook Return Types
export interface UseVMConnectionReturn {
  connections: Map<string, VMConnectionState>;
  connect: (vmId: string, config: VMConnectionConfig, credentials?: VMAuthCredentials) => Promise<string>;
  disconnect: (vmId: string, connectionId: string) => Promise<void>;
  executeCommand: (vmId: string, connectionId: string, command: string, options?: any) => Promise<{ stdout: string; stderr: string; exitCode: number }>;
  createSession: (vmId: string, connectionId: string, userId: string, metadata?: any) => Promise<VMSession>;
  closeSession: (vmId: string, sessionId: string) => Promise<void>;
  getConnectionStatus: (vmId: string, connectionId: string) => Promise<VMConnectionStatus>;
  loading: boolean;
  error: string | null;
}

export interface UseVMDockerReturn {
  containers: VMDockerContainerState[];
  dockerInfo?: VMDockerInfo;
  loading: boolean;
  error: string | null;
  createContainer: (vmId: string, connectionId: string, options: VMDockerCreateContainerData) => Promise<string>;
  startContainer: (vmId: string, connectionId: string, containerId: string) => Promise<void>;
  stopContainer: (vmId: string, connectionId: string, containerId: string, timeout?: number) => Promise<void>;
  restartContainer: (vmId: string, connectionId: string, containerId: string) => Promise<void>;
  removeContainer: (vmId: string, connectionId: string, containerId: string, force?: boolean) => Promise<void>;
  getContainerLogs: (vmId: string, connectionId: string, containerId: string, options?: any) => Promise<string>;
  execInContainer: (vmId: string, connectionId: string, containerId: string, command: string[], options?: any) => Promise<{ stdout: string; stderr: string; exitCode: number }>;
  refreshContainers: (vmId: string, connectionId: string) => Promise<void>;
  refreshDockerInfo: (vmId: string, connectionId: string) => Promise<void>;
}

export interface UseVMMonitoringReturn {
  metrics?: VMMonitoringMetrics;
  systemInfo?: VMSystemInfo;
  alerts: VMMonitoringAlert[];
  processes: Array<any>;
  services: Array<any>;
  isMonitoring: boolean;
  loading: boolean;
  error: string | null;
  startMonitoring: (vmId: string, connectionId: string) => Promise<void>;
  stopMonitoring: (vmId: string, connectionId: string) => Promise<void>;
  getMetrics: (vmId: string, connectionId: string) => Promise<VMResourceMetrics>;
  getSystemInfo: (vmId: string, connectionId: string) => Promise<VMSystemInfo>;
  getProcesses: (vmId: string, connectionId: string, options?: any) => Promise<any[]>;
  getServices: (vmId: string, connectionId: string, serviceNames?: string[]) => Promise<any[]>;
  acknowledgeAlert: (vmId: string, connectionId: string, alertId: string) => Promise<void>;
  performHealthCheck: (vmId: string, connectionId: string) => Promise<VMHealthCheck>;
}

export interface UseVMSecurityReturn {
  users: VMSecurityUser[];
  auditLog: VMSecurityAuditEntry[];
  securityEvents: VMSecurityEvent[];
  loading: boolean;
  error: string | null;
  createUser: (vmId: string, userData: Omit<VMSecurityUser, 'id' | 'createdAt' | 'lastLogin'>) => Promise<void>;
  updateUser: (vmId: string, userId: string, updates: Partial<VMSecurityUser>) => Promise<void>;
  deleteUser: (vmId: string, userId: string) => Promise<void>;
  authenticateUser: (vmId: string, userId: string, credentials: VMAuthCredentials, clientInfo?: any) => Promise<{ token: string; expiresAt: Date }>;
  validateToken: (vmId: string, token: string) => Promise<boolean>;
  revokeToken: (vmId: string, token: string) => Promise<void>;
  getAuditLog: (vmId: string, options?: any) => Promise<VMSecurityAuditEntry[]>;
  getSecurityEvents: (vmId: string, options?: any) => Promise<VMSecurityEvent[]>;
  resolveSecurityEvent: (vmId: string, eventId: string) => Promise<void>;
}

export interface UseVMHealthReturn {
  health?: VMHealthStatus;
  loading: boolean;
  error: string | null;
  runHealthCheck: (vmId: string, detailed?: boolean) => Promise<VMHealthStatus>;
  getHealthHistory: (vmId: string, limit?: number) => Promise<VMHealthStatus['history']>;
  autoRefresh: boolean;
  setAutoRefresh: (enabled: boolean) => void;
  refreshInterval: number;
  setRefreshInterval: (interval: number) => void;
}

// Event and Callback Types
export type VMConnectionEventHandler = (vmId: string, event: 'connected' | 'disconnected' | 'error', data?: any) => void;
export type VMDockerEventHandler = (vmId: string, containerId: string, event: 'created' | 'started' | 'stopped' | 'removed' | 'error', data?: any) => void;
export type VMMonitoringEventHandler = (vmId: string, event: 'alert' | 'metrics_updated' | 'monitoring_started' | 'monitoring_stopped', data?: any) => void;
export type VMSecurityEventHandler = (vmId: string, event: 'user_created' | 'user_updated' | 'user_deleted' | 'security_event', data?: any) => void;
export type VMHealthEventHandler = (vmId: string, event: 'health_check_completed' | 'health_status_changed', data?: any) => void;

// Utility Types
export type VMTabType = 'overview' | 'connection' | 'docker' | 'monitoring' | 'security' | 'health';
export type VMStatusType = 'connected' | 'disconnected' | 'connecting' | 'error' | 'unknown';
export type VMAlertSeverity = 'low' | 'medium' | 'high' | 'critical';
export type VMPermissionLevel = 'read' | 'write' | 'admin' | 'owner';

// Animation and Theme Types
export interface VMAnimationProps {
  initial?: any;
  animate?: any;
  exit?: any;
  transition?: any;
  whileHover?: any;
  whileTap?: any;
}

export interface VMThemeProps {
  theme?: 'light' | 'dark' | 'auto';
  colorScheme?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
}

// Configuration Types
export interface VMFrontendConfig {
  autoRefreshInterval: number;
  maxRetryAttempts: number;
  connectionTimeout: number;
  enableAnimations: boolean;
  enableRealTimeUpdates: boolean;
  defaultTheme: 'light' | 'dark' | 'auto';
  defaultColorScheme: string;
  enableNotifications: boolean;
  enableSounds: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

// SSH Key Management Types
export interface SSHKey {
  id: string;
  name: string;
  description?: string;
  publicKey: string;
  privateKey?: string; // Only available to key owner/admin
  fingerprint: string;
  keyType: 'rsa' | 'ed25519' | 'ecdsa';
  keySize: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isEncrypted: boolean;
  passphrase?: string;
  tags: string[];
  vmAssignments: string[]; // VM IDs where this key is assigned
  userAssignments: string[]; // User IDs who have access to this key
  lastUsed?: Date;
  usageCount: number;
  expiresAt?: Date;
  isActive: boolean;
}

export interface SSHKeyPair {
  publicKey: string;
  privateKey: string;
  fingerprint: string;
}

export interface SSHKeyGenerationOptions {
  name: string;
  description?: string;
  keyType: 'rsa' | 'ed25519' | 'ecdsa';
  keySize?: number;
  passphrase?: string;
  tags?: string[];
  expiresAt?: Date;
}

export interface SSHKeyImportOptions {
  name: string;
  description?: string;
  publicKey: string;
  privateKey?: string;
  passphrase?: string;
  tags?: string[];
}

export interface SSHKeyAssignment {
  keyId: string;
  vmId: string;
  userId: string;
  assignedAt: Date;
  assignedBy: string;
  isActive: boolean;
}

export interface SSHKeyUsageLog {
  id: string;
  keyId: string;
  vmId: string;
  userId: string;
  action: 'connect' | 'authenticate' | 'assign' | 'unassign' | 'view' | 'export';
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

export interface SSHKeyStats {
  totalKeys: number;
  activeKeys: number;
  expiredKeys: number;
  encryptedKeys: number;
  keysByType: Record<string, number>;
  recentUsage: SSHKeyUsageLog[];
  topUsedKeys: Array<{
    keyId: string;
    name: string;
    usageCount: number;
  }>;
}

// SSH Key Management Hook Types
export interface UseSSHKeyManagementReturn {
  keys: SSHKey[];
  stats: SSHKeyStats;
  loading: boolean;
  error: string | null;
  generateKey: (options: SSHKeyGenerationOptions) => Promise<SSHKey>;
  importKey: (options: SSHKeyImportOptions) => Promise<SSHKey>;
  updateKey: (keyId: string, updates: Partial<SSHKey>) => Promise<void>;
  deleteKey: (keyId: string) => Promise<void>;
  assignKeyToVM: (keyId: string, vmId: string) => Promise<void>;
  unassignKeyFromVM: (keyId: string, vmId: string) => Promise<void>;
  assignKeyToUser: (keyId: string, userId: string) => Promise<void>;
  unassignKeyFromUser: (keyId: string, userId: string) => Promise<void>;
  getKeyUsage: (keyId: string) => Promise<SSHKeyUsageLog[]>;
  exportKey: (keyId: string, includePrivate: boolean) => Promise<string>;
  testKey: (keyId: string, vmId: string) => Promise<boolean>;
  refreshKeys: () => Promise<void>;
}

// SSH Key Component Props
export interface SSHKeyCardProps extends BaseVMComponentProps {
  sshKey: SSHKey;
  onEdit?: (key: SSHKey) => void;
  onDelete?: (keyId: string) => void;
  onAssign?: (keyId: string) => void;
  onExport?: (keyId: string) => void;
  onTest?: (keyId: string) => void;
  showPrivateKey?: boolean;
  showUsage?: boolean;
}

export interface SSHKeyFormProps extends BaseVMComponentProps {
  initialData?: Partial<SSHKey>;
  mode: 'create' | 'edit' | 'import';
  onSubmit: (data: SSHKeyGenerationOptions | SSHKeyImportOptions) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export interface SSHKeyListProps extends BaseVMComponentProps {
  keys: SSHKey[];
  onKeySelect?: (key: SSHKey) => void;
  onKeyEdit?: (key: SSHKey) => void;
  onKeyDelete?: (keyId: string) => void;
  selectable?: boolean;
  selectedKeys?: string[];
  showUsage?: boolean;
  showAssignments?: boolean;
}

export interface SSHKeyAssignmentProps extends BaseVMComponentProps {
  keyId: string;
  availableVMs: Array<{ id: string; name: string }>;
  availableUsers: Array<{ id: string; name: string; email: string }>;
  currentAssignments: {
    vms: string[];
    users: string[];
  };
  onAssignVM: (vmId: string) => Promise<void>;
  onUnassignVM: (vmId: string) => Promise<void>;
  onAssignUser: (userId: string) => Promise<void>;
  onUnassignUser: (userId: string) => Promise<void>;
}

export interface SSHKeyUsageProps extends BaseVMComponentProps {
  keyId: string;
  usage: SSHKeyUsageLog[];
  onRefresh?: () => void;
}

export interface SSHKeyStatsProps extends BaseVMComponentProps {
  stats: SSHKeyStats;
  onRefresh?: () => void;
}

// SSH Key Event Handlers
export type SSHKeyEventHandler = (
  keyId: string,
  event: 'created' | 'updated' | 'deleted' | 'assigned' | 'unassigned' | 'used' | 'exported' | 'tested',
  data?: any
) => void;
