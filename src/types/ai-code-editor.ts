// AI Code Editor Types
export interface AICodeEditorConfig {
  language: string;
  theme: 'light' | 'dark';
  fontSize: number;
  tabSize: number;
  wordWrap: boolean;
  lineNumbers: boolean;
  minimap: boolean;
  autoCompletion: boolean;
  aiAssistance: boolean;
  errorAnalysis: boolean;
  refactoringSuggestions: boolean;
}

export interface CodeFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isDirty: boolean;
  lastModified: Date;
  size: number;
}

export interface CodePosition {
  line: number;
  column: number;
}

export interface CodeRange {
  start: CodePosition;
  end: CodePosition;
}

export interface CodeSelection {
  range: CodeRange;
  text: string;
}

// AI Completion Types
export interface AICompletionRequest {
  code: string;
  position: CodePosition;
  language: string;
  context?: {
    fileName?: string;
    projectContext?: string;
    recentChanges?: string[];
  };
}

export interface AICompletionSuggestion {
  id: string;
  text: string;
  insertText: string;
  range: CodeRange;
  confidence: number;
  type: 'completion' | 'snippet' | 'import' | 'function' | 'variable';
  description?: string;
  documentation?: string;
  priority: number;
}

export interface AICompletionResponse {
  suggestions: AICompletionSuggestion[];
  isLoading: boolean;
  error?: string;
}

// AI Analysis Types
export interface CodeError {
  id: string;
  message: string;
  severity: 'error' | 'warning' | 'info' | 'hint';
  range: CodeRange;
  code?: string;
  source: string;
  fixes?: CodeFix[];
}

export interface CodeFix {
  id: string;
  title: string;
  description: string;
  edits: CodeEdit[];
  confidence: number;
}

export interface CodeEdit {
  range: CodeRange;
  newText: string;
}

export interface AIAnalysisRequest {
  code: string;
  language: string;
  fileName?: string;
  includeStyle?: boolean;
  includePerformance?: boolean;
  includeSecurity?: boolean;
}

export interface AIAnalysisResponse {
  errors: CodeError[];
  suggestions: RefactoringSuggestion[];
  metrics: CodeMetrics;
  isLoading: boolean;
  error?: string;
}

// Refactoring Types
export interface RefactoringSuggestion {
  id: string;
  title: string;
  description: string;
  type: 'extract-method' | 'rename' | 'inline' | 'move' | 'optimize' | 'style';
  range: CodeRange;
  preview: string;
  edits: CodeEdit[];
  confidence: number;
  impact: 'low' | 'medium' | 'high';
}

export interface CodeMetrics {
  complexity: number;
  maintainability: number;
  readability: number;
  performance: number;
  security: number;
  testCoverage?: number;
}

// AI Assistant Types
export interface AIAssistantMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  codeContext?: {
    file: string;
    range?: CodeRange;
    code?: string;
  };
  suggestions?: AICompletionSuggestion[];
  actions?: AIAssistantAction[];
}

export interface AIAssistantAction {
  id: string;
  type: 'apply-fix' | 'apply-refactoring' | 'insert-code' | 'explain-code' | 'generate-tests';
  title: string;
  description: string;
  data: any;
}

export interface AIAssistantRequest {
  message: string;
  codeContext?: {
    file: string;
    selection?: CodeSelection;
    fullCode?: string;
  };
  conversationHistory?: AIAssistantMessage[];
}

// Editor State Types
export interface AIEditorState {
  activeFile?: CodeFile;
  openFiles: CodeFile[];
  selection?: CodeSelection;
  cursor: CodePosition;
  completions: AICompletionResponse;
  analysis: AIAnalysisResponse;
  assistant: {
    isOpen: boolean;
    messages: AIAssistantMessage[];
    isLoading: boolean;
  };
  settings: AICodeEditorConfig;
}

// Service Response Types
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  metadata?: {
    timestamp: string;
    duration: number;
    requestId: string;
  };
}

// Event Types
export interface AIEditorEvent {
  type: 'file-opened' | 'file-saved' | 'selection-changed' | 'completion-requested' | 'analysis-completed';
  data: any;
  timestamp: Date;
}

export interface AIEditorEventHandler {
  (event: AIEditorEvent): void;
}

// Hook Return Types
export interface UseAICodeCompletionReturn {
  suggestions: AICompletionSuggestion[];
  isLoading: boolean;
  error?: string;
  requestCompletion: (request: AICompletionRequest) => Promise<void>;
  applySuggestion: (suggestion: AICompletionSuggestion) => void;
  dismissSuggestions: () => void;
}

export interface UseCodeAnalysisReturn {
  errors: CodeError[];
  suggestions: RefactoringSuggestion[];
  metrics: CodeMetrics;
  isLoading: boolean;
  error?: string;
  analyzeCode: (request: AIAnalysisRequest) => Promise<void>;
  applyFix: (fix: CodeFix) => void;
  dismissError: (errorId: string) => void;
}

export interface UseAIAssistantReturn {
  messages: AIAssistantMessage[];
  isLoading: boolean;
  error?: string;
  sendMessage: (request: AIAssistantRequest) => Promise<void>;
  clearConversation: () => void;
  executeAction: (action: AIAssistantAction) => Promise<void>;
}

export interface UseCodeRefactoringReturn {
  suggestions: RefactoringSuggestion[];
  isLoading: boolean;
  error?: string;
  getSuggestions: (code: string, language: string) => Promise<void>;
  applySuggestion: (suggestion: RefactoringSuggestion) => Promise<void>;
  previewSuggestion: (suggestion: RefactoringSuggestion) => string;
}

export interface UseAIEditorStateReturn {
  state: AIEditorState;
  actions: {
    openFile: (file: CodeFile) => void;
    closeFile: (fileId: string) => void;
    saveFile: (fileId: string) => Promise<void>;
    updateFileContent: (fileId: string, content: string) => void;
    setSelection: (selection: CodeSelection) => void;
    setCursor: (position: CodePosition) => void;
    updateSettings: (settings: Partial<AICodeEditorConfig>) => void;
    toggleAssistant: () => void;
  };
}
