/**
 * Python Workspace Types
 * Types and interfaces for Python development workspace functionality
 */

export type PythonFramework = 'django' | 'flask' | 'fastapi' | 'streamlit' | 'gradio';

export interface PythonProjectTemplate {
  id: string;
  name: string;
  framework: PythonFramework;
  description: string;
  icon: string;
  defaultPort: number;
  features: string[];
  dependencies: string[];
  devDependencies?: string[];
  files: PythonTemplateFile[];
  commands: PythonProjectCommands;
  documentation: string;
  tags: string[];
}

export interface PythonTemplateFile {
  path: string;
  content: string;
  type: 'python' | 'config' | 'requirements' | 'script' | 'markdown';
  executable?: boolean;
}

export interface PythonProjectCommands {
  create: string;
  install: string;
  dev: string;
  build?: string;
  test?: string;
  lint?: string;
  format?: string;
}

export interface PythonWorkspaceConfig {
  framework: PythonFramework;
  projectName: string;
  template: string;
  pythonVersion: string;
  packageManager: 'pip' | 'conda' | 'poetry';
  virtualEnv: boolean;
  database?: 'postgresql' | 'mysql' | 'sqlite' | 'redis';
  features: string[];
  ports: { [key: string]: number };
  environment: { [key: string]: string };
}

export interface PythonPackageManager {
  name: 'pip' | 'conda' | 'poetry';
  displayName: string;
  installCommand: string;
  createEnvCommand: string;
  activateCommand: string;
  requirementsFile: string;
  lockFile?: string;
}

export interface PythonEnvironment {
  name: string;
  pythonVersion: string;
  packageManager: PythonPackageManager;
  packages: PythonPackage[];
  isActive: boolean;
  path: string;
}

export interface PythonPackage {
  name: string;
  version: string;
  description?: string;
  category: 'framework' | 'database' | 'testing' | 'development' | 'utility' | 'ml' | 'web';
  required: boolean;
}

export interface PythonLivePreview {
  framework: PythonFramework;
  port: number;
  url: string;
  status: 'starting' | 'running' | 'stopped' | 'error';
  logs: string[];
  process?: {
    pid: number;
    command: string;
    startTime: Date;
  };
}

export interface PythonWorkspaceState {
  activeProject?: {
    name: string;
    framework: PythonFramework;
    path: string;
    config: PythonWorkspaceConfig;
  };
  environments: PythonEnvironment[];
  activeEnvironment?: string;
  livePreview?: PythonLivePreview;
  templates: PythonProjectTemplate[];
  recentProjects: Array<{
    name: string;
    path: string;
    framework: PythonFramework;
    lastAccessed: Date;
  }>;
}

// Framework-specific configurations
export interface DjangoConfig extends PythonWorkspaceConfig {
  framework: 'django';
  features: Array<'rest_framework' | 'cors_headers' | 'admin' | 'auth' | 'celery' | 'channels'>;
  database: 'postgresql' | 'mysql' | 'sqlite';
  staticFiles: boolean;
  mediaFiles: boolean;
}

export interface FlaskConfig extends PythonWorkspaceConfig {
  framework: 'flask';
  features: Array<'restful' | 'cors' | 'sqlalchemy' | 'migrate' | 'login' | 'wtf'>;
  templateEngine: 'jinja2' | 'none';
  database?: 'postgresql' | 'mysql' | 'sqlite';
}

export interface FastAPIConfig extends PythonWorkspaceConfig {
  framework: 'fastapi';
  features: Array<'docs' | 'cors' | 'sqlalchemy' | 'alembic' | 'auth' | 'websockets'>;
  database?: 'postgresql' | 'mysql' | 'sqlite';
  asyncDatabase: boolean;
}

export interface StreamlitConfig extends PythonWorkspaceConfig {
  framework: 'streamlit';
  features: Array<'charts' | 'maps' | 'forms' | 'sidebar' | 'caching' | 'session_state'>;
  dataLibraries: Array<'pandas' | 'numpy' | 'matplotlib' | 'plotly' | 'seaborn'>;
}

export interface GradioConfig extends PythonWorkspaceConfig {
  framework: 'gradio';
  features: Array<'interface' | 'blocks' | 'chatbot' | 'file_upload' | 'audio' | 'video'>;
  mlLibraries: Array<'transformers' | 'torch' | 'tensorflow' | 'sklearn' | 'opencv'>;
}

// API Response types
export interface CreatePythonProjectRequest {
  template: string;
  projectName: string;
  config: PythonWorkspaceConfig;
  workspaceId: string;
  userId: string;
}

export interface CreatePythonProjectResponse {
  success: boolean;
  project?: {
    id: string;
    name: string;
    path: string;
    framework: PythonFramework;
    config: PythonWorkspaceConfig;
  };
  error?: string;
  message?: string;
}

export interface PythonWorkspaceStatus {
  workspaceId: string;
  status: 'initializing' | 'ready' | 'error';
  pythonVersion: string;
  availablePackageManagers: PythonPackageManager[];
  installedFrameworks: Array<{
    framework: PythonFramework;
    version: string;
    installed: boolean;
  }>;
  runningServices: Array<{
    name: string;
    port: number;
    status: 'running' | 'stopped';
    url?: string;
  }>;
}

// Event types for real-time updates
export interface PythonWorkspaceEvent {
  type: 'project_created' | 'environment_changed' | 'preview_started' | 'preview_stopped' | 'package_installed' | 'error';
  workspaceId: string;
  timestamp: Date;
  data: any;
}

// Utility types
export type PythonFrameworkFeatures = {
  [K in PythonFramework]: string[];
};

export type PythonFrameworkPorts = {
  [K in PythonFramework]: number;
};

// Constants
export const PYTHON_FRAMEWORK_DEFAULTS: PythonFrameworkPorts = {
  django: 8000,
  flask: 5000,
  fastapi: 8000,
  streamlit: 8501,
  gradio: 7860,
};

export const PYTHON_PACKAGE_MANAGERS: PythonPackageManager[] = [
  {
    name: 'pip',
    displayName: 'pip',
    installCommand: 'pip install',
    createEnvCommand: 'python -m venv',
    activateCommand: 'source venv/bin/activate',
    requirementsFile: 'requirements.txt',
  },
  {
    name: 'conda',
    displayName: 'Conda',
    installCommand: 'conda install',
    createEnvCommand: 'conda create -n',
    activateCommand: 'conda activate',
    requirementsFile: 'environment.yml',
    lockFile: 'conda-lock.yml',
  },
  {
    name: 'poetry',
    displayName: 'Poetry',
    installCommand: 'poetry add',
    createEnvCommand: 'poetry init',
    activateCommand: 'poetry shell',
    requirementsFile: 'pyproject.toml',
    lockFile: 'poetry.lock',
  },
];

export const PYTHON_FRAMEWORK_FEATURES: PythonFrameworkFeatures = {
  django: ['ORM', 'Admin Interface', 'Authentication', 'REST API', 'Templates', 'Static Files'],
  flask: ['Lightweight', 'Flexible', 'Extensions', 'Templates', 'RESTful', 'Blueprints'],
  fastapi: ['Fast', 'Async', 'Auto Docs', 'Type Hints', 'Validation', 'Modern'],
  streamlit: ['Data Apps', 'Interactive', 'Widgets', 'Charts', 'ML Models', 'Rapid Prototyping'],
  gradio: ['ML Interfaces', 'Demos', 'Sharing', 'Components', 'Blocks', 'Chatbots'],
};
