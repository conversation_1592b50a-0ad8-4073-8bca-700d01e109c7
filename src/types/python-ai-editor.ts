/**
 * Python AI Editor Types
 * Extended types for Python-specific AI code editor functionality
 */

import { 
  AICompletionRequest, 
  AICompletionSuggestion, 
  AIAnalysisRequest,
  CodeError,
  RefactoringSuggestion,
  AIAssistantRequest,
  AIAssistantMessage
} from '@/types/ai-code-editor';
import { PythonFramework } from '@/types/python-workspace';

// Python-specific completion types
export interface PythonCompletionRequest extends AICompletionRequest {
  framework?: PythonFramework;
  pythonVersion?: string;
  virtualEnv?: string;
  installedPackages?: string[];
  projectStructure?: PythonProjectStructure;
}

export interface PythonCompletionSuggestion extends AICompletionSuggestion {
  framework?: PythonFramework;
  packageName?: string;
  importStatement?: string;
  isFrameworkSpecific?: boolean;
  pythonVersion?: string;
  category: 'framework' | 'builtin' | 'package' | 'variable' | 'function' | 'class' | 'method';
}

// Python-specific analysis types
export interface PythonAnalysisRequest extends AIAnalysisRequest {
  framework?: PythonFramework;
  pythonVersion?: string;
  virtualEnv?: string;
  installedPackages?: string[];
  projectStructure?: PythonProjectStructure;
  checkFrameworkBestPractices?: boolean;
  checkSecurityVulnerabilities?: boolean;
  checkPerformanceIssues?: boolean;
}

export interface PythonCodeError extends CodeError {
  framework?: PythonFramework;
  category: 'syntax' | 'import' | 'framework' | 'security' | 'performance' | 'style' | 'type';
  pythonVersion?: string;
  packageRequired?: string;
  frameworkSpecific?: boolean;
}

export interface PythonRefactoringSuggestion extends RefactoringSuggestion {
  framework?: PythonFramework;
  category: 'framework-pattern' | 'performance' | 'security' | 'style' | 'structure';
  pythonVersion?: string;
  beforeCode: string;
  afterCode: string;
  explanation: string;
  benefits: string[];
}

// Python project structure
export interface PythonProjectStructure {
  framework: PythonFramework;
  rootPath: string;
  pythonVersion: string;
  packageManager: 'pip' | 'conda' | 'poetry';
  virtualEnv?: string;
  installedPackages: PythonPackageInfo[];
  files: PythonFileInfo[];
  settings: PythonProjectSettings;
}

export interface PythonPackageInfo {
  name: string;
  version: string;
  isFramework?: boolean;
  isDevelopment?: boolean;
  dependencies?: string[];
}

export interface PythonFileInfo {
  path: string;
  type: 'python' | 'config' | 'template' | 'static' | 'test';
  framework?: PythonFramework;
  isMainModule?: boolean;
  imports: string[];
  classes: string[];
  functions: string[];
}

export interface PythonProjectSettings {
  framework: PythonFramework;
  pythonVersion: string;
  packageManager: 'pip' | 'conda' | 'poetry';
  linting: {
    enabled: boolean;
    tools: ('flake8' | 'pylint' | 'mypy' | 'black' | 'isort')[];
  };
  testing: {
    framework: 'pytest' | 'unittest' | 'nose';
    coverage: boolean;
  };
  database?: {
    type: 'postgresql' | 'mysql' | 'sqlite' | 'redis';
    host?: string;
    port?: number;
  };
}

// Python-specific AI assistant types
export interface PythonAssistantRequest extends AIAssistantRequest {
  framework?: PythonFramework;
  projectStructure?: PythonProjectStructure;
  intent?: PythonAssistantIntent;
}

export type PythonAssistantIntent = 
  | 'generate-model'
  | 'generate-view'
  | 'generate-api-endpoint'
  | 'generate-test'
  | 'fix-import'
  | 'optimize-query'
  | 'add-authentication'
  | 'create-migration'
  | 'setup-database'
  | 'deploy-config'
  | 'explain-framework-concept'
  | 'debug-error'
  | 'refactor-code';

export interface PythonAssistantMessage extends AIAssistantMessage {
  framework?: PythonFramework;
  codeBlocks?: PythonCodeBlock[];
  actions?: PythonAssistantAction[];
}

export interface PythonCodeBlock {
  language: 'python' | 'bash' | 'sql' | 'yaml' | 'json';
  code: string;
  filename?: string;
  framework?: PythonFramework;
  description?: string;
  runnable?: boolean;
}

export interface PythonAssistantAction {
  id: string;
  type: PythonAssistantIntent;
  title: string;
  description: string;
  framework?: PythonFramework;
  data: {
    code?: string;
    filename?: string;
    command?: string;
    imports?: string[];
    dependencies?: string[];
  };
}

// Framework-specific completion contexts
export interface DjangoCompletionContext {
  models?: string[];
  views?: string[];
  urls?: string[];
  templates?: string[];
  currentApp?: string;
  installedApps?: string[];
  middleware?: string[];
  databases?: string[];
}

export interface FlaskCompletionContext {
  routes?: string[];
  blueprints?: string[];
  extensions?: string[];
  config?: Record<string, any>;
  templates?: string[];
}

export interface FastAPICompletionContext {
  routes?: string[];
  dependencies?: string[];
  models?: string[];
  schemas?: string[];
  middleware?: string[];
  tags?: string[];
}

export interface StreamlitCompletionContext {
  widgets?: string[];
  charts?: string[];
  layouts?: string[];
  caching?: boolean;
  sessionState?: Record<string, any>;
}

export interface GradioCompletionContext {
  interfaces?: string[];
  components?: string[];
  blocks?: string[];
  themes?: string[];
  sharing?: boolean;
}

// Python AI editor configuration
export interface PythonAIEditorConfig {
  framework?: PythonFramework;
  pythonVersion?: string;
  enableFrameworkCompletion?: boolean;
  enableImportSuggestions?: boolean;
  enableFrameworkAnalysis?: boolean;
  enableSecurityChecks?: boolean;
  enablePerformanceHints?: boolean;
  autoInstallPackages?: boolean;
  preferredPackageManager?: 'pip' | 'conda' | 'poetry';
  linting?: {
    enabled?: boolean;
    tools?: ('flake8' | 'pylint' | 'mypy' | 'black' | 'isort')[];
  };
  testing?: {
    framework?: 'pytest' | 'unittest';
    autoGenerateTests?: boolean;
  };
}

// Hook return types for Python-specific functionality
export interface UsePythonCodeCompletionReturn {
  suggestions: PythonCompletionSuggestion[];
  isLoading: boolean;
  error?: string;
  requestCompletion: (request: PythonCompletionRequest) => Promise<void>;
  applySuggestion: (suggestion: PythonCompletionSuggestion) => void;
  dismissSuggestions: () => void;
  installPackage: (packageName: string) => Promise<void>;
}

export interface UsePythonCodeAnalysisReturn {
  errors: PythonCodeError[];
  suggestions: PythonRefactoringSuggestion[];
  isLoading: boolean;
  error?: string;
  analyzeCode: (request: PythonAnalysisRequest) => Promise<void>;
  applyFix: (errorId: string) => void;
  applySuggestion: (suggestionId: string) => void;
  dismissError: (errorId: string) => void;
}

export interface UsePythonAssistantReturn {
  messages: PythonAssistantMessage[];
  isLoading: boolean;
  error?: string;
  sendMessage: (request: PythonAssistantRequest) => Promise<void>;
  applyAction: (action: PythonAssistantAction) => Promise<void>;
  clearConversation: () => void;
  generateCode: (intent: PythonAssistantIntent, context?: string) => Promise<void>;
}

// Framework-specific code generation types
export interface PythonCodeGenerationRequest {
  framework: PythonFramework;
  intent: PythonAssistantIntent;
  context: {
    description: string;
    existingCode?: string;
    projectStructure?: PythonProjectStructure;
    requirements?: string[];
  };
  options?: {
    includeTests?: boolean;
    includeDocumentation?: boolean;
    followBestPractices?: boolean;
    optimizePerformance?: boolean;
  };
}

export interface PythonCodeGenerationResponse {
  success: boolean;
  code?: string;
  files?: Array<{
    path: string;
    content: string;
    type: 'python' | 'config' | 'template' | 'test';
  }>;
  dependencies?: string[];
  commands?: string[];
  explanation?: string;
  nextSteps?: string[];
  error?: string;
}

// Framework-specific patterns and snippets
export interface PythonFrameworkPattern {
  id: string;
  framework: PythonFramework;
  name: string;
  description: string;
  category: string;
  code: string;
  variables?: Array<{
    name: string;
    description: string;
    default?: string;
    type: 'string' | 'number' | 'boolean' | 'array';
  }>;
  dependencies?: string[];
  usage: string;
}

// Export utility types
export type PythonFrameworkContext = 
  | DjangoCompletionContext 
  | FlaskCompletionContext 
  | FastAPICompletionContext 
  | StreamlitCompletionContext 
  | GradioCompletionContext;
