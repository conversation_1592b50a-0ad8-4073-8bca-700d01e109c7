/**
 * Workspace Collaboration Types
 * Defines types for real-time collaboration features
 */

// ============================================================================
// Real-time Collaboration Types
// ============================================================================

export interface CollaborationSession {
  id: string;
  workspaceId: string;
  participants: SessionParticipant[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface SessionParticipant {
  userId: string;
  userName: string;
  userAvatar?: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  status: 'online' | 'offline' | 'away' | 'busy';
  joinedAt: Date;
  lastActivity: Date;
  
  // Current state
  activeFile?: string;
  cursor?: CursorState;
  selection?: SelectionState;
  viewport?: ViewportState;
  
  // Permissions
  canEdit: boolean;
  canExecute: boolean;
  canChat: boolean;
  canVoice: boolean;
  canScreenShare: boolean;
}

export interface CursorState {
  fileId: string;
  filePath: string;
  position: {
    line: number;
    column: number;
  };
  color: string;
  visible: boolean;
  timestamp: Date;
}

export interface SelectionState {
  fileId: string;
  filePath: string;
  range: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
  color: string;
  timestamp: Date;
}

export interface ViewportState {
  fileId: string;
  filePath: string;
  scrollTop: number;
  scrollLeft: number;
  visibleRange: {
    startLine: number;
    endLine: number;
  };
  timestamp: Date;
}

// ============================================================================
// Chat System Types
// ============================================================================

export interface ChatMessage {
  id: string;
  workspaceId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  
  // Message content
  content: string;
  type: 'text' | 'code' | 'file' | 'system' | 'ai';
  
  // Code-specific properties
  language?: string;
  fileName?: string;
  lineNumber?: number;
  
  // File-specific properties
  fileId?: string;
  filePath?: string;
  
  // Metadata
  createdAt: Date;
  updatedAt?: Date;
  editedBy?: string;
  
  // Reactions and replies
  reactions: MessageReaction[];
  replyTo?: string;
  replies: string[];
  
  // Status
  isDeleted: boolean;
  isPinned: boolean;
  
  // AI-specific properties
  aiContext?: {
    model: string;
    tokens: number;
    confidence: number;
  };
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  userName: string;
  createdAt: Date;
}

export interface ChatThread {
  id: string;
  workspaceId: string;
  title: string;
  messages: ChatMessage[];
  participants: string[];
  createdAt: Date;
  updatedAt: Date;
  isArchived: boolean;
}

// ============================================================================
// Voice and Video Types
// ============================================================================

export interface VoiceSession {
  id: string;
  workspaceId: string;
  participants: VoiceParticipant[];
  status: 'active' | 'paused' | 'ended';
  createdAt: Date;
  endedAt?: Date;
  
  // Settings
  isRecording: boolean;
  recordingUrl?: string;
  quality: 'low' | 'medium' | 'high';
}

export interface VoiceParticipant {
  userId: string;
  userName: string;
  status: 'speaking' | 'muted' | 'listening' | 'disconnected';
  isMuted: boolean;
  isDeafened: boolean;
  volume: number;
  joinedAt: Date;
  leftAt?: Date;
}

export interface ScreenShareSession {
  id: string;
  workspaceId: string;
  sharerId: string;
  sharerName: string;
  viewers: ScreenShareViewer[];
  status: 'active' | 'paused' | 'ended';
  createdAt: Date;
  endedAt?: Date;
  
  // Settings
  shareType: 'screen' | 'window' | 'tab';
  quality: 'low' | 'medium' | 'high';
  frameRate: number;
  hasAudio: boolean;
}

export interface ScreenShareViewer {
  userId: string;
  userName: string;
  joinedAt: Date;
  leftAt?: Date;
  hasControl: boolean;
}

// ============================================================================
// Conflict Resolution Types
// ============================================================================

export interface EditConflict {
  id: string;
  fileId: string;
  filePath: string;
  
  // Conflicting edits
  edits: ConflictingEdit[];
  
  // Resolution
  status: 'pending' | 'resolved' | 'ignored';
  resolvedBy?: string;
  resolvedAt?: Date;
  resolution?: ConflictResolution;
  
  // Metadata
  createdAt: Date;
}

export interface ConflictingEdit {
  userId: string;
  userName: string;
  timestamp: Date;
  
  // Edit details
  range: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
  oldContent: string;
  newContent: string;
  
  // Context
  operation: 'insert' | 'delete' | 'replace';
  version: number;
}

export interface ConflictResolution {
  type: 'accept_mine' | 'accept_theirs' | 'merge' | 'custom';
  finalContent: string;
  mergedBy: string;
  mergedAt: Date;
  notes?: string;
}

// ============================================================================
// Presence and Awareness Types
// ============================================================================

export interface PresenceState {
  userId: string;
  userName: string;
  userAvatar?: string;
  
  // Current activity
  status: 'online' | 'away' | 'busy' | 'offline';
  activity: UserActivity;
  
  // Location in workspace
  currentFile?: string;
  currentLine?: number;
  
  // Timestamps
  lastActivity: Date;
  statusChangedAt: Date;
}

export interface UserActivity {
  type: 'editing' | 'viewing' | 'executing' | 'debugging' | 'idle';
  description: string;
  fileId?: string;
  filePath?: string;
  startedAt: Date;
}

export interface AwarenessUpdate {
  userId: string;
  type: 'cursor' | 'selection' | 'viewport' | 'status' | 'activity';
  data: any;
  timestamp: Date;
}

// ============================================================================
// Collaboration Events
// ============================================================================

export interface CollaborationEvent {
  id: string;
  type: CollaborationEventType;
  workspaceId: string;
  userId: string;
  userName: string;
  timestamp: Date;
  data: any;
  
  // Delivery tracking
  deliveredTo: string[];
  acknowledgedBy: string[];
}

export type CollaborationEventType =
  | 'user.joined'
  | 'user.left'
  | 'user.status_changed'
  | 'cursor.moved'
  | 'selection.changed'
  | 'viewport.changed'
  | 'file.opened'
  | 'file.closed'
  | 'file.edited'
  | 'file.saved'
  | 'chat.message'
  | 'chat.reaction'
  | 'voice.joined'
  | 'voice.left'
  | 'voice.muted'
  | 'voice.unmuted'
  | 'screen.share_started'
  | 'screen.share_ended'
  | 'conflict.detected'
  | 'conflict.resolved';

// ============================================================================
// Collaboration Settings
// ============================================================================

export interface CollaborationSettings {
  // General settings
  enabled: boolean;
  maxParticipants: number;
  allowAnonymous: boolean;
  requireApproval: boolean;
  
  // Chat settings
  chat: {
    enabled: boolean;
    allowFileSharing: boolean;
    allowCodeSnippets: boolean;
    retentionDays: number;
    moderationEnabled: boolean;
  };
  
  // Voice settings
  voice: {
    enabled: boolean;
    quality: 'low' | 'medium' | 'high';
    recordingEnabled: boolean;
    pushToTalk: boolean;
    noiseReduction: boolean;
  };
  
  // Screen sharing settings
  screenShare: {
    enabled: boolean;
    allowControl: boolean;
    quality: 'low' | 'medium' | 'high';
    frameRate: number;
    recordingEnabled: boolean;
  };
  
  // Conflict resolution settings
  conflictResolution: {
    autoResolve: boolean;
    strategy: 'last_write_wins' | 'manual' | 'merge';
    notifyOnConflict: boolean;
    lockTimeout: number; // seconds
  };
  
  // Presence settings
  presence: {
    showCursors: boolean;
    showSelections: boolean;
    showViewports: boolean;
    updateInterval: number; // milliseconds
    cursorTimeout: number; // seconds
  };
}

// ============================================================================
// Collaboration Permissions
// ============================================================================

export interface CollaborationPermissions {
  userId: string;
  
  // Basic permissions
  canView: boolean;
  canEdit: boolean;
  canExecute: boolean;
  canDebug: boolean;
  
  // Communication permissions
  canChat: boolean;
  canVoice: boolean;
  canScreenShare: boolean;
  canReceiveControl: boolean;
  canGiveControl: boolean;
  
  // Administrative permissions
  canInvite: boolean;
  canKick: boolean;
  canModerate: boolean;
  canChangeSettings: boolean;
  
  // File-specific permissions
  filePermissions: {
    [filePath: string]: {
      canRead: boolean;
      canWrite: boolean;
      canDelete: boolean;
      canExecute: boolean;
    };
  };
}

// ============================================================================
// Real-time Synchronization Types
// ============================================================================

export interface SyncState {
  version: number;
  checksum: string;
  lastSyncAt: Date;
  pendingOperations: SyncOperation[];
  conflictingOperations: SyncOperation[];
}

export interface SyncOperation {
  id: string;
  type: 'insert' | 'delete' | 'replace' | 'move';
  userId: string;
  timestamp: Date;
  
  // Operation details
  fileId: string;
  position: { line: number; column: number };
  content?: string;
  length?: number;
  
  // Versioning
  baseVersion: number;
  targetVersion: number;
  
  // Status
  status: 'pending' | 'applied' | 'rejected' | 'conflicted';
  appliedAt?: Date;
  rejectedReason?: string;
}
