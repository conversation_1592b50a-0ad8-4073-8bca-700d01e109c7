/**
 * Workspace Code Execution Types
 * Defines types for secure code execution and runtime management
 */

// ============================================================================
// Code Execution Types
// ============================================================================

export interface ExecutionEnvironment {
  id: string;
  workspaceId: string;
  type: 'python' | 'nodejs' | 'bash' | 'docker';
  version: string;
  
  // Configuration
  configuration: EnvironmentConfig;
  
  // State
  status: 'initializing' | 'ready' | 'busy' | 'error' | 'stopped';
  containerId?: string;
  processId?: number;
  
  // Resources
  resources: ResourceUsage;
  limits: ResourceLimits;
  
  // Metadata
  createdAt: Date;
  lastUsedAt: Date;
  createdBy: string;
}

export interface EnvironmentConfig {
  // Runtime settings
  runtime: {
    workingDirectory: string;
    environment: Record<string, string>;
    pathExtensions: string[];
    startupScript?: string;
  };
  
  // Security settings
  security: {
    allowNetworking: boolean;
    allowFileSystem: boolean;
    allowSubprocesses: boolean;
    restrictedCommands: string[];
    allowedPorts: number[];
    timeoutSeconds: number;
  };
  
  // Package management
  packages: {
    installed: InstalledPackage[];
    allowInstallation: boolean;
    packageManager: string;
    requirements?: string;
  };
  
  // Debugging
  debugging: {
    enabled: boolean;
    port?: number;
    breakpointsEnabled: boolean;
    stepDebugging: boolean;
  };
}

export interface InstalledPackage {
  name: string;
  version: string;
  installedAt: Date;
  installedBy: string;
  size: number;
  dependencies: string[];
}

export interface ResourceUsage {
  cpu: {
    current: number; // percentage
    average: number; // percentage
    peak: number; // percentage
  };
  memory: {
    current: number; // MB
    peak: number; // MB
    available: number; // MB
  };
  disk: {
    used: number; // MB
    available: number; // MB
    reads: number;
    writes: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    connectionsActive: number;
  };
}

export interface ResourceLimits {
  cpu: number; // max percentage
  memory: number; // max MB
  disk: number; // max MB
  networkBandwidth: number; // max Mbps
  executionTime: number; // max seconds per execution
  concurrentExecutions: number; // max simultaneous executions
}

// ============================================================================
// Code Execution Request/Response Types
// ============================================================================

export interface ExecutionRequest {
  id: string;
  workspaceId: string;
  userId: string;
  
  // Code details
  code: string;
  language: string;
  fileName?: string;
  filePath?: string;
  
  // Execution options
  options: ExecutionOptions;
  
  // Context
  workingDirectory?: string;
  environment?: Record<string, string>;
  
  // Metadata
  createdAt: Date;
  priority: 'low' | 'normal' | 'high';
}

export interface ExecutionOptions {
  // Runtime options
  timeout: number; // seconds
  captureOutput: boolean;
  captureErrors: boolean;
  streamOutput: boolean;
  
  // Input/Output
  stdin?: string;
  arguments?: string[];
  
  // Debugging
  debug: boolean;
  breakpoints?: Breakpoint[];
  
  // Resource limits
  maxMemory?: number; // MB
  maxCpuTime?: number; // seconds
  
  // Collaboration
  shareExecution: boolean;
  allowInterruption: boolean;
}

export interface ExecutionResult {
  id: string;
  requestId: string;
  
  // Status
  status: 'completed' | 'error' | 'timeout' | 'cancelled' | 'killed';
  exitCode?: number;
  
  // Output
  stdout?: string;
  stderr?: string;
  output?: ExecutionOutput[];
  
  // Timing
  startTime: Date;
  endTime: Date;
  duration: number; // milliseconds
  
  // Resources
  resourceUsage: ResourceUsage;
  
  // Error details
  error?: ExecutionError;
  
  // Debugging
  debugInfo?: DebugInfo;
}

export interface ExecutionOutput {
  type: 'stdout' | 'stderr' | 'return' | 'exception' | 'debug';
  content: string;
  timestamp: Date;
  lineNumber?: number;
  
  // Formatting
  mimeType?: string;
  encoding?: string;
  
  // Rich output (for Jupyter-style outputs)
  data?: {
    'text/plain'?: string;
    'text/html'?: string;
    'image/png'?: string;
    'application/json'?: any;
  };
}

export interface ExecutionError {
  type: 'syntax' | 'runtime' | 'timeout' | 'memory' | 'permission' | 'system';
  message: string;
  details?: string;
  
  // Location information
  fileName?: string;
  lineNumber?: number;
  columnNumber?: number;
  
  // Stack trace
  stackTrace?: StackFrame[];
  
  // Suggestions
  suggestions?: ErrorSuggestion[];
}

export interface StackFrame {
  fileName: string;
  functionName: string;
  lineNumber: number;
  columnNumber: number;
  code?: string;
}

export interface ErrorSuggestion {
  type: 'fix' | 'explanation' | 'documentation';
  title: string;
  description: string;
  code?: string;
  confidence: number;
}

// ============================================================================
// Debugging Types
// ============================================================================

export interface DebugSession {
  id: string;
  executionId: string;
  workspaceId: string;
  userId: string;
  
  // State
  status: 'starting' | 'running' | 'paused' | 'stopped' | 'error';
  currentFrame?: StackFrame;
  
  // Breakpoints
  breakpoints: Breakpoint[];
  
  // Variables
  variables: DebugVariable[];
  watchExpressions: WatchExpression[];
  
  // Control
  canStep: boolean;
  canContinue: boolean;
  canStop: boolean;
  
  // Metadata
  createdAt: Date;
  stoppedAt?: Date;
}

export interface Breakpoint {
  id: string;
  fileName: string;
  lineNumber: number;
  columnNumber?: number;
  condition?: string;
  hitCount?: number;
  enabled: boolean;
  verified: boolean;
}

export interface DebugVariable {
  name: string;
  value: string;
  type: string;
  scope: 'local' | 'global' | 'builtin';
  
  // Nested variables
  children?: DebugVariable[];
  hasChildren: boolean;
  
  // Metadata
  isReadonly: boolean;
  isExpandable: boolean;
}

export interface WatchExpression {
  id: string;
  expression: string;
  value?: string;
  error?: string;
  type?: string;
  enabled: boolean;
}

export interface DebugInfo {
  sessionId?: string;
  breakpointsHit: string[];
  variableSnapshots: VariableSnapshot[];
  stepCount: number;
  totalSteps: number;
}

export interface VariableSnapshot {
  timestamp: Date;
  frameId: string;
  variables: DebugVariable[];
}

// ============================================================================
// Package Management Types
// ============================================================================

export interface PackageManager {
  type: 'pip' | 'npm' | 'yarn' | 'pnpm' | 'conda' | 'apt';
  version: string;
  configFile?: string;
  lockFile?: string;
}

export interface PackageInstallRequest {
  packages: PackageSpec[];
  manager: PackageManager;
  options: PackageInstallOptions;
}

export interface PackageSpec {
  name: string;
  version?: string;
  source?: string; // URL or registry
  extras?: string[]; // For Python packages with extras
}

export interface PackageInstallOptions {
  upgrade: boolean;
  force: boolean;
  dev: boolean;
  global: boolean;
  userInstall: boolean;
  noDeps: boolean;
  timeout: number;
}

export interface PackageInstallResult {
  success: boolean;
  packages: InstalledPackage[];
  output: string;
  error?: string;
  duration: number;
  warnings: string[];
}

// ============================================================================
// Terminal Types
// ============================================================================

export interface TerminalSession {
  id: string;
  workspaceId: string;
  userId: string;
  
  // Configuration
  shell: string;
  workingDirectory: string;
  environment: Record<string, string>;
  
  // State
  status: 'starting' | 'running' | 'stopped' | 'error';
  processId?: number;
  
  // History
  history: TerminalCommand[];
  output: TerminalOutput[];
  
  // Settings
  settings: TerminalSettings;
  
  // Metadata
  createdAt: Date;
  lastActivity: Date;
}

export interface TerminalCommand {
  id: string;
  command: string;
  workingDirectory: string;
  timestamp: Date;
  exitCode?: number;
  duration?: number;
}

export interface TerminalOutput {
  id: string;
  commandId?: string;
  type: 'stdout' | 'stderr' | 'system';
  content: string;
  timestamp: Date;
  
  // Formatting
  ansiEscapes?: boolean;
  color?: string;
  style?: string;
}

export interface TerminalSettings {
  fontSize: number;
  fontFamily: string;
  theme: string;
  cursorStyle: 'block' | 'underline' | 'bar';
  cursorBlink: boolean;
  scrollback: number;
  bellStyle: 'none' | 'sound' | 'visual';
}

// ============================================================================
// Execution Queue Types
// ============================================================================

export interface ExecutionQueue {
  id: string;
  workspaceId: string;
  
  // Queue state
  pending: QueuedExecution[];
  running: QueuedExecution[];
  completed: QueuedExecution[];
  failed: QueuedExecution[];
  
  // Configuration
  maxConcurrent: number;
  priority: boolean;
  
  // Statistics
  totalExecutions: number;
  averageWaitTime: number;
  averageExecutionTime: number;
}

export interface QueuedExecution {
  id: string;
  request: ExecutionRequest;
  priority: number;
  queuedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  result?: ExecutionResult;
  
  // Queue metadata
  position: number;
  estimatedWaitTime: number;
  retryCount: number;
  maxRetries: number;
}

// ============================================================================
// Execution Events
// ============================================================================

export interface ExecutionEvent {
  id: string;
  type: ExecutionEventType;
  executionId: string;
  workspaceId: string;
  userId: string;
  timestamp: Date;
  data: any;
}

export type ExecutionEventType =
  | 'execution.queued'
  | 'execution.started'
  | 'execution.output'
  | 'execution.error'
  | 'execution.completed'
  | 'execution.cancelled'
  | 'execution.timeout'
  | 'debug.started'
  | 'debug.paused'
  | 'debug.resumed'
  | 'debug.stopped'
  | 'breakpoint.hit'
  | 'variable.changed'
  | 'package.installed'
  | 'package.removed'
  | 'terminal.created'
  | 'terminal.command'
  | 'terminal.output';
