/**
 * Node/React Workspace Types
 * Types and interfaces for Node.js and React development workspace functionality
 */

export type NodeFramework = 'nextjs' | 'react' | 'express' | 'nestjs' | 'vue' | 'angular' | 'nuxt' | 'svelte' | 'remix' | 'gatsby';

export interface NodeProjectTemplate {
  id: string;
  name: string;
  framework: NodeFramework;
  description: string;
  icon: string;
  defaultPort: number;
  features: string[];
  dependencies: string[];
  devDependencies: string[];
  files: NodeTemplateFile[];
  commands: NodeProjectCommands;
  documentation: string;
  tags: string[];
  buildTool?: 'webpack' | 'vite' | 'rollup' | 'esbuild' | 'turbo' | 'parcel';
  cssFramework?: 'tailwind' | 'styled-components' | 'emotion' | 'sass' | 'css-modules' | 'chakra-ui' | 'material-ui';
  stateManagement?: 'redux' | 'zustand' | 'recoil' | 'context' | 'mobx' | 'jotai';
  testing?: 'jest' | 'vitest' | 'cypress' | 'playwright' | 'testing-library';
}

export interface NodeTemplateFile {
  path: string;
  content: string;
  type: 'javascript' | 'typescript' | 'json' | 'config' | 'markdown' | 'css' | 'html' | 'env';
  executable?: boolean;
}

export interface NodeProjectCommands {
  create: string;
  install: string;
  dev: string;
  build: string;
  start?: string;
  test?: string;
  lint?: string;
  format?: string;
  typecheck?: string;
  preview?: string;
}

export interface NodeWorkspaceConfig {
  framework: NodeFramework;
  projectName: string;
  template: string;
  nodeVersion: string;
  packageManager: 'npm' | 'yarn' | 'pnpm';
  typescript: boolean;
  database?: 'postgresql' | 'mysql' | 'sqlite' | 'mongodb' | 'redis' | 'prisma' | 'supabase';
  features: string[];
  ports: { [key: string]: number };
  environment: { [key: string]: string };
  buildTool?: string;
  cssFramework?: string;
  stateManagement?: string;
  testing?: string;
}

export interface NodePackageManager {
  name: 'npm' | 'yarn' | 'pnpm';
  displayName: string;
  installCommand: string;
  addCommand: string;
  removeCommand: string;
  runCommand: string;
  lockFile: string;
  configFile?: string;
}

export interface NodeEnvironment {
  name: string;
  nodeVersion: string;
  packageManager: NodePackageManager;
  packages: NodePackage[];
  isActive: boolean;
  path: string;
  globalPackages?: string[];
}

export interface NodePackage {
  name: string;
  version: string;
  description?: string;
  category: 'framework' | 'database' | 'testing' | 'development' | 'utility' | 'ui' | 'state' | 'build';
  required: boolean;
  isDev?: boolean;
}

export interface NodeLivePreview {
  framework: NodeFramework;
  port: number;
  url: string;
  status: 'starting' | 'running' | 'stopped' | 'error' | 'building';
  logs: string[];
  buildLogs?: string[];
  process?: {
    pid: number;
    command: string;
    startTime: Date;
  };
  hotReload: boolean;
  buildMode: 'development' | 'production' | 'preview';
}

export interface NodeWorkspaceState {
  activeProject?: {
    name: string;
    framework: NodeFramework;
    path: string;
    config: NodeWorkspaceConfig;
  };
  environments: NodeEnvironment[];
  activeEnvironment?: string;
  livePreview?: NodeLivePreview;
  templates: NodeProjectTemplate[];
  recentProjects: Array<{
    name: string;
    path: string;
    framework: NodeFramework;
    lastAccessed: Date;
  }>;
}

// Framework-specific configurations
export interface NextJSConfig extends NodeWorkspaceConfig {
  framework: 'nextjs';
  features: Array<'app-router' | 'pages-router' | 'api-routes' | 'middleware' | 'image-optimization' | 'font-optimization' | 'seo' | 'pwa'>;
  deployment: 'vercel' | 'netlify' | 'docker' | 'static';
  database?: 'postgresql' | 'mysql' | 'sqlite' | 'mongodb' | 'prisma' | 'supabase';
  auth?: 'nextauth' | 'clerk' | 'auth0' | 'supabase-auth';
}

export interface ReactConfig extends NodeWorkspaceConfig {
  framework: 'react';
  features: Array<'router' | 'hooks' | 'context' | 'suspense' | 'concurrent' | 'strict-mode'>;
  bundler: 'vite' | 'webpack' | 'parcel' | 'rollup';
  stateManagement: 'redux' | 'zustand' | 'recoil' | 'context' | 'mobx' | 'jotai';
}

export interface ExpressConfig extends NodeWorkspaceConfig {
  framework: 'express';
  features: Array<'cors' | 'helmet' | 'morgan' | 'compression' | 'rate-limiting' | 'jwt' | 'passport' | 'swagger'>;
  database?: 'postgresql' | 'mysql' | 'sqlite' | 'mongodb' | 'redis';
  orm?: 'prisma' | 'typeorm' | 'sequelize' | 'mongoose';
  apiType: 'rest' | 'graphql' | 'both';
}

export interface NestJSConfig extends NodeWorkspaceConfig {
  framework: 'nestjs';
  features: Array<'guards' | 'interceptors' | 'pipes' | 'decorators' | 'swagger' | 'graphql' | 'microservices' | 'websockets'>;
  database?: 'postgresql' | 'mysql' | 'sqlite' | 'mongodb' | 'redis';
  orm: 'typeorm' | 'prisma' | 'mongoose';
  apiType: 'rest' | 'graphql' | 'both';
}

export interface VueConfig extends NodeWorkspaceConfig {
  framework: 'vue';
  features: Array<'composition-api' | 'options-api' | 'router' | 'pinia' | 'vuex' | 'ssr' | 'pwa'>;
  version: '2' | '3';
  stateManagement: 'pinia' | 'vuex' | 'none';
}

export interface AngularConfig extends NodeWorkspaceConfig {
  framework: 'angular';
  features: Array<'routing' | 'forms' | 'http-client' | 'animations' | 'material' | 'pwa' | 'ssr' | 'standalone'>;
  version: string;
  stateManagement: 'ngrx' | 'akita' | 'ngxs' | 'none';
}

// API Response types
export interface CreateNodeProjectRequest {
  template: string;
  projectName: string;
  config: NodeWorkspaceConfig;
  workspaceId: string;
  userId: string;
}

export interface CreateNodeProjectResponse {
  success: boolean;
  project?: {
    id: string;
    name: string;
    path: string;
    framework: NodeFramework;
    config: NodeWorkspaceConfig;
  };
  error?: string;
  message?: string;
}

export interface NodeWorkspaceStatus {
  workspaceId: string;
  status: 'creating' | 'ready' | 'running' | 'stopped' | 'error';
  activeProjects: Array<{
    name: string;
    framework: NodeFramework;
    port?: number;
    status: 'running' | 'stopped' | 'building' | 'error';
  }>;
  environment: {
    nodeVersion: string;
    packageManager: string;
    availablePorts: number[];
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
  };
}

// Package manager constants
export const NODE_PACKAGE_MANAGERS: NodePackageManager[] = [
  {
    name: 'npm',
    displayName: 'npm',
    installCommand: 'npm install',
    addCommand: 'npm install',
    removeCommand: 'npm uninstall',
    runCommand: 'npm run',
    lockFile: 'package-lock.json',
  },
  {
    name: 'yarn',
    displayName: 'Yarn',
    installCommand: 'yarn install',
    addCommand: 'yarn add',
    removeCommand: 'yarn remove',
    runCommand: 'yarn',
    lockFile: 'yarn.lock',
    configFile: '.yarnrc.yml',
  },
  {
    name: 'pnpm',
    displayName: 'pnpm',
    installCommand: 'pnpm install',
    addCommand: 'pnpm add',
    removeCommand: 'pnpm remove',
    runCommand: 'pnpm',
    lockFile: 'pnpm-lock.yaml',
    configFile: '.npmrc',
  },
];

// Framework categories for organization
export const FRAMEWORK_CATEGORIES = {
  frontend: ['react', 'vue', 'angular', 'svelte'],
  fullstack: ['nextjs', 'nuxt', 'remix', 'gatsby'],
  backend: ['express', 'nestjs'],
  meta: ['nextjs', 'nuxt', 'remix', 'gatsby'],
} as const;

// Popular package categories
export const PACKAGE_CATEGORIES = {
  framework: 'Core Framework',
  ui: 'UI Components',
  state: 'State Management',
  database: 'Database & ORM',
  testing: 'Testing',
  development: 'Development Tools',
  utility: 'Utilities',
  build: 'Build Tools',
} as const;
