export interface ContainerInfo {
  id: string;
  name: string;
  image: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping' | 'paused' | 'restarting';
  created: Date;
  ports: { [key: string]: string };
  cpu?: number;
  memory?: number;
  networkMode?: string;
  mountedVolumes?: string[];
}

export interface WorkspaceInfo extends ContainerInfo {
  workspaceType: 'ubuntu-desktop' | 'development-env' | 'minimal-desktop' | 'python-dev';
  userId: string;
  vncPort: number;
  vncPassword?: string;
  displayWidth: number;
  displayHeight: number;
  guacamoleConnectionId?: string;
  resources: {
    cpuLimit: number;
    memoryLimit: number;
    storageLimit?: number;
  };
}

export interface CreateContainerOptions {
  name: string;
  image: string;
  cpu?: number;
  memory?: number; // in MB
  ports?: { [containerPort: string]: string }; // container:host mapping
  environment?: { [key: string]: string };
  volumes?: { [hostPath: string]: string }; // host:container mapping
  networkMode?: string;
  cmd?: string[];
  workingDir?: string;
  user?: string;
  privileged?: boolean;
  autoRemove?: boolean;
}

export interface CreateWorkspaceOptions {
  workspaceType: 'ubuntu-desktop' | 'development-env' | 'minimal-desktop' | 'python-dev';
  userId: string;
  name?: string;
  vncPassword?: string;
  displayWidth?: number;
  displayHeight?: number;
  resources?: {
    cpu?: number;
    memory?: number;
    storage?: number;
  };
  environment?: { [key: string]: string };
}