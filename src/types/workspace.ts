/**
 * Comprehensive Workspace System Types
 * Defines all TypeScript interfaces and types for the Omnispace workspace management system
 */

import { Models } from 'appwrite';

// ============================================================================
// Core Workspace Types
// ============================================================================

export type WorkspaceType = 'python' | 'nodejs' | 'general' | 'collaborative';
export type WorkspaceStatus = 'creating' | 'active' | 'stopped' | 'error' | 'archived';
export type WorkspaceVisibility = 'private' | 'team' | 'public';
export type WorkspaceRole = 'owner' | 'admin' | 'editor' | 'viewer';

export interface Workspace {
  id: string;
  name: string;
  description?: string;
  type: WorkspaceType;
  status: WorkspaceStatus;
  visibility: WorkspaceVisibility;
  
  // Owner and permissions
  ownerId: string;
  ownerName: string;
  permissions: WorkspacePermission[];
  
  // Configuration
  configuration: WorkspaceConfiguration;
  template?: WorkspaceTemplate;
  
  // Container information
  containerId?: string;
  containerStatus?: string;
  accessUrl?: string;
  vncPort?: number;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt?: Date;
  
  // Statistics
  stats: WorkspaceStats;
  
  // Collaboration
  collaborators: WorkspaceCollaborator[];
  activeUsers: ActiveUser[];
  
  // File system
  rootPath: string;
  fileCount: number;
  totalSize: number;
  
  // Tags and organization
  tags: string[];
  category?: string;
  
  // AI features
  aiEnabled: boolean;
  aiAssistantConfig?: AIAssistantConfig;
}

export interface WorkspaceConfiguration {
  // Runtime configuration
  runtime: {
    type: WorkspaceType;
    version: string;
    environment: Record<string, string>;
    startupScript?: string;
    dependencies: string[];
  };
  
  // Resource limits
  resources: {
    cpu: number; // CPU cores
    memory: number; // MB
    storage: number; // GB
    networkBandwidth?: number; // Mbps
  };
  
  // Display settings (for VNC workspaces)
  display?: {
    width: number;
    height: number;
    depth: number;
    dpi?: number;
  };
  
  // Security settings
  security: {
    allowedPorts: number[];
    restrictedCommands: string[];
    enableSudo: boolean;
    enableNetworking: boolean;
    enableFileUpload: boolean;
    enableFileDownload: boolean;
  };
  
  // Editor settings
  editor: {
    theme: string;
    fontSize: number;
    tabSize: number;
    wordWrap: boolean;
    minimap: boolean;
    lineNumbers: boolean;
    autoSave: boolean;
    autoComplete: boolean;
  };
  
  // Collaboration settings
  collaboration: {
    enabled: boolean;
    maxCollaborators: number;
    allowAnonymous: boolean;
    requireApproval: boolean;
    chatEnabled: boolean;
    voiceEnabled: boolean;
    screenShareEnabled: boolean;
  };
}

export interface WorkspaceStats {
  totalRuntime: number; // seconds
  cpuUsage: number; // percentage
  memoryUsage: number; // MB
  storageUsage: number; // GB
  networkUsage: number; // MB
  lastActivity: Date;
  sessionCount: number;
  fileOperations: number;
  codeExecutions: number;
}

// ============================================================================
// Permission System
// ============================================================================

export interface WorkspacePermission {
  userId: string;
  userName: string;
  userEmail: string;
  role: WorkspaceRole;
  permissions: Permission[];
  grantedAt: Date;
  grantedBy: string;
  expiresAt?: Date;
}

export interface Permission {
  resource: PermissionResource;
  actions: PermissionAction[];
}

export type PermissionResource = 
  | 'workspace' 
  | 'files' 
  | 'terminal' 
  | 'packages' 
  | 'preview' 
  | 'settings' 
  | 'collaboration'
  | 'ai-assistant';

export type PermissionAction = 
  | 'read' 
  | 'write' 
  | 'execute' 
  | 'delete' 
  | 'admin' 
  | 'share' 
  | 'export';

// ============================================================================
// Collaboration Types
// ============================================================================

export interface WorkspaceCollaborator {
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;
  role: WorkspaceRole;
  status: 'online' | 'offline' | 'away';
  joinedAt: Date;
  lastActivity: Date;
  cursor?: CursorPosition;
  selection?: SelectionRange;
}

export interface ActiveUser {
  userId: string;
  userName: string;
  userAvatar?: string;
  cursor?: CursorPosition;
  selection?: SelectionRange;
  activeFile?: string;
  lastActivity: Date;
}

export interface CursorPosition {
  fileId: string;
  line: number;
  column: number;
  color: string;
}

export interface SelectionRange {
  fileId: string;
  start: { line: number; column: number };
  end: { line: number; column: number };
  color: string;
}

// ============================================================================
// File System Types
// ============================================================================

export interface WorkspaceFile {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  mimeType?: string;
  size: number;
  content?: string;
  encoding?: string;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  
  // Permissions
  permissions: FilePermission[];
  
  // Version control
  version: number;
  versions: FileVersion[];
  
  // Collaboration
  isLocked: boolean;
  lockedBy?: string;
  lockedAt?: Date;
  
  // Editor state
  language?: string;
  isOpen: boolean;
  isDirty: boolean;
  
  // AI features
  aiSuggestions?: AISuggestion[];
}

export interface FilePermission {
  userId: string;
  permissions: ('read' | 'write' | 'execute' | 'delete')[];
}

export interface FileVersion {
  id: string;
  version: number;
  content: string;
  createdAt: Date;
  createdBy: string;
  message?: string;
  size: number;
}

export interface AISuggestion {
  id: string;
  type: 'completion' | 'refactor' | 'fix' | 'optimize';
  content: string;
  confidence: number;
  range: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
  createdAt: Date;
}

// ============================================================================
// Template System Types
// ============================================================================

export interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  type: WorkspaceType;
  category: string;
  
  // Template configuration
  configuration: Partial<WorkspaceConfiguration>;
  
  // Files and structure
  files: TemplateFile[];
  structure: DirectoryStructure;
  
  // Metadata
  version: string;
  author: string;
  authorId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Usage statistics
  usageCount: number;
  rating: number;
  reviews: TemplateReview[];
  
  // Visibility and sharing
  visibility: 'public' | 'private' | 'team';
  tags: string[];
  
  // Requirements
  requirements: {
    minCpu: number;
    minMemory: number;
    minStorage: number;
    dependencies: string[];
  };
  
  // Preview
  screenshots: string[];
  demoUrl?: string;
  
  // Installation
  installScript?: string;
  setupInstructions?: string;
}

export interface TemplateFile {
  path: string;
  content: string;
  type: 'file' | 'directory';
  permissions?: string;
  template?: boolean; // If true, content can contain template variables
}

export interface DirectoryStructure {
  name: string;
  type: 'file' | 'directory';
  children?: DirectoryStructure[];
  template?: boolean;
}

export interface TemplateReview {
  id: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  createdAt: Date;
}

// ============================================================================
// AI Assistant Types
// ============================================================================

export interface AIAssistantConfig {
  enabled: boolean;
  model: string;
  features: {
    codeCompletion: boolean;
    codeAnalysis: boolean;
    errorDetection: boolean;
    refactoring: boolean;
    documentation: boolean;
    testing: boolean;
    debugging: boolean;
    terminalAssistance: boolean;
  };
  customInstructions?: string;
  contextWindow: number;
  temperature: number;
}

// ============================================================================
// Code Execution Types
// ============================================================================

export interface CodeExecution {
  id: string;
  workspaceId: string;
  userId: string;

  // Code details
  code: string;
  language: string;
  fileName?: string;

  // Execution details
  status: 'pending' | 'running' | 'completed' | 'error' | 'timeout';
  startTime: Date;
  endTime?: Date;
  duration?: number;

  // Results
  output?: string;
  error?: string;
  exitCode?: number;

  // Resource usage
  cpuUsage?: number;
  memoryUsage?: number;

  // Environment
  environment: Record<string, string>;
  workingDirectory: string;

  // Metadata
  createdAt: Date;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface WorkspaceResponse {
  success: boolean;
  data?: Workspace;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
}

export interface WorkspaceListResponse {
  success: boolean;
  data?: {
    workspaces: Workspace[];
    total: number;
    page: number;
    limit: number;
  };
  error?: {
    message: string;
    code: string;
    details?: any;
  };
}

export interface FileOperationResponse {
  success: boolean;
  data?: WorkspaceFile | WorkspaceFile[];
  error?: {
    message: string;
    code: string;
    details?: any;
  };
}

// ============================================================================
// Request Types
// ============================================================================

export interface CreateWorkspaceRequest {
  name: string;
  description?: string;
  type: WorkspaceType;
  templateId?: string;
  configuration?: Partial<WorkspaceConfiguration>;
  visibility?: WorkspaceVisibility;
  tags?: string[];
}

export interface UpdateWorkspaceRequest {
  name?: string;
  description?: string;
  configuration?: Partial<WorkspaceConfiguration>;
  visibility?: WorkspaceVisibility;
  tags?: string[];
}

export interface ShareWorkspaceRequest {
  userIds: string[];
  role: WorkspaceRole;
  message?: string;
  expiresAt?: Date;
}

export interface FileOperationRequest {
  operation: 'create' | 'read' | 'update' | 'delete' | 'move' | 'copy';
  path: string;
  content?: string;
  newPath?: string;
  encoding?: string;
}

// ============================================================================
// Event Types for Real-time Updates
// ============================================================================

export interface WorkspaceEvent {
  type: WorkspaceEventType;
  workspaceId: string;
  userId: string;
  timestamp: Date;
  data: any;
}

export type WorkspaceEventType =
  | 'workspace.created'
  | 'workspace.updated'
  | 'workspace.deleted'
  | 'workspace.started'
  | 'workspace.stopped'
  | 'user.joined'
  | 'user.left'
  | 'file.created'
  | 'file.updated'
  | 'file.deleted'
  | 'file.locked'
  | 'file.unlocked'
  | 'cursor.moved'
  | 'selection.changed'
  | 'code.executed'
  | 'chat.message'
  | 'ai.suggestion';

// ============================================================================
// Utility Types
// ============================================================================

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  type?: WorkspaceType[];
  status?: WorkspaceStatus[];
  visibility?: WorkspaceVisibility[];
  tags?: string[];
  search?: string;
  ownerId?: string;
  collaboratorId?: string;
}

export interface WorkspaceSearchResult {
  workspace: Workspace;
  relevance: number;
  matchedFields: string[];
}

// ============================================================================
// Error Types
// ============================================================================

export interface WorkspaceError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  workspaceId?: string;
  userId?: string;
}

export type WorkspaceErrorCode =
  | 'WORKSPACE_NOT_FOUND'
  | 'WORKSPACE_ACCESS_DENIED'
  | 'WORKSPACE_CREATION_FAILED'
  | 'WORKSPACE_START_FAILED'
  | 'WORKSPACE_STOP_FAILED'
  | 'FILE_NOT_FOUND'
  | 'FILE_ACCESS_DENIED'
  | 'FILE_OPERATION_FAILED'
  | 'COLLABORATION_FAILED'
  | 'CODE_EXECUTION_FAILED'
  | 'TEMPLATE_NOT_FOUND'
  | 'INVALID_CONFIGURATION'
  | 'RESOURCE_LIMIT_EXCEEDED'
  | 'NETWORK_ERROR'
  | 'AUTHENTICATION_REQUIRED'
  | 'INSUFFICIENT_PERMISSIONS';
