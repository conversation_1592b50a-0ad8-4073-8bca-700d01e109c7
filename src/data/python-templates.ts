/**
 * Python Project Templates
 * Pre-configured templates for different Python frameworks
 */

import { PythonProjectTemplate } from '@/types/python-workspace';

export const PYTHON_PROJECT_TEMPLATES: PythonProjectTemplate[] = [
  {
    id: 'django-basic',
    name: 'Django Web Application',
    framework: 'django',
    description: 'Full-stack Django web application with REST API support',
    icon: '🎯',
    defaultPort: 8000,
    features: [
      'Django 4.2+',
      'Django REST Framework',
      'CORS Headers',
      'PostgreSQL Support',
      'Admin Interface',
      'User Authentication',
      'Static Files Handling',
    ],
    dependencies: [
      'Django>=4.2.0',
      'djangorestframework>=3.14.0',
      'django-cors-headers>=4.0.0',
      'python-decouple>=3.8',
      'psycopg2-binary>=2.9.0',
      'gunicorn>=21.0.0',
      'whitenoise>=6.5.0',
    ],
    devDependencies: [
      'pytest-django>=4.5.0',
      'black>=23.0.0',
      'flake8>=6.0.0',
      'isort>=5.12.0',
    ],
    files: [
      {
        path: 'requirements.txt',
        content: `Django>=4.2.0
djangorestframework>=3.14.0
django-cors-headers>=4.0.0
python-decouple>=3.8
psycopg2-binary>=2.9.0
gunicorn>=21.0.0
whitenoise>=6.5.0`,
        type: 'requirements',
      },
      {
        path: 'create_project.sh',
        content: `#!/bin/bash
PROJECT_NAME=\${1:-myproject}
echo "Creating Django project: $PROJECT_NAME"
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
django-admin startproject $PROJECT_NAME .
python manage.py migrate
echo "Django project '$PROJECT_NAME' created successfully!"
echo "To run: source venv/bin/activate && python manage.py runserver 0.0.0.0:8000"`,
        type: 'script',
        executable: true,
      },
      {
        path: 'README.md',
        content: `# Django Project

## Setup
1. Create virtual environment: \`python -m venv venv\`
2. Activate environment: \`source venv/bin/activate\`
3. Install dependencies: \`pip install -r requirements.txt\`
4. Run migrations: \`python manage.py migrate\`
5. Create superuser: \`python manage.py createsuperuser\`
6. Start server: \`python manage.py runserver 0.0.0.0:8000\`

## Features
- Django REST Framework for API development
- CORS headers for frontend integration
- PostgreSQL database support
- Admin interface at /admin/
- Static files handling with WhiteNoise`,
        type: 'markdown',
      },
    ],
    commands: {
      create: './create_project.sh',
      install: 'pip install -r requirements.txt',
      dev: 'python manage.py runserver 0.0.0.0:8000',
      test: 'python manage.py test',
      lint: 'flake8 .',
      format: 'black . && isort .',
    },
    documentation: 'https://docs.djangoproject.com/',
    tags: ['web', 'fullstack', 'orm', 'admin', 'rest-api'],
  },
  {
    id: 'flask-api',
    name: 'Flask REST API',
    framework: 'flask',
    description: 'Lightweight Flask REST API with SQLAlchemy',
    icon: '🌶️',
    defaultPort: 5000,
    features: [
      'Flask 2.3+',
      'Flask-RESTful',
      'Flask-CORS',
      'SQLAlchemy ORM',
      'JWT Authentication',
      'API Documentation',
    ],
    dependencies: [
      'Flask>=2.3.0',
      'Flask-RESTful>=0.3.10',
      'Flask-CORS>=4.0.0',
      'Flask-SQLAlchemy>=3.0.0',
      'Flask-JWT-Extended>=4.5.0',
      'python-decouple>=3.8',
      'psycopg2-binary>=2.9.0',
      'gunicorn>=21.0.0',
    ],
    files: [
      {
        path: 'app.py',
        content: `from flask import Flask, jsonify
from flask_cors import CORS
from flask_restful import Api, Resource

app = Flask(__name__)
CORS(app)
api = Api(app)

class HelloWorld(Resource):
    def get(self):
        return {"message": "Hello from Flask!"}

class HealthCheck(Resource):
    def get(self):
        return {"status": "healthy"}

api.add_resource(HelloWorld, '/')
api.add_resource(HealthCheck, '/api/health')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)`,
        type: 'python',
      },
      {
        path: 'requirements.txt',
        content: `Flask>=2.3.0
Flask-RESTful>=0.3.10
Flask-CORS>=4.0.0
Flask-SQLAlchemy>=3.0.0
Flask-JWT-Extended>=4.5.0
python-decouple>=3.8
psycopg2-binary>=2.9.0
gunicorn>=21.0.0`,
        type: 'requirements',
      },
    ],
    commands: {
      create: './create_project.sh',
      install: 'pip install -r requirements.txt',
      dev: 'python app.py',
      test: 'pytest',
      lint: 'flake8 .',
      format: 'black . && isort .',
    },
    documentation: 'https://flask.palletsprojects.com/',
    tags: ['api', 'lightweight', 'rest', 'microservice'],
  },
  {
    id: 'fastapi-modern',
    name: 'FastAPI Modern API',
    framework: 'fastapi',
    description: 'Modern FastAPI application with automatic documentation',
    icon: '⚡',
    defaultPort: 8000,
    features: [
      'FastAPI 0.104+',
      'Automatic API Documentation',
      'Pydantic Models',
      'Async Support',
      'SQLAlchemy Integration',
      'Alembic Migrations',
    ],
    dependencies: [
      'fastapi>=0.104.0',
      'uvicorn[standard]>=0.24.0',
      'pydantic>=2.4.0',
      'python-decouple>=3.8',
      'psycopg2-binary>=2.9.0',
      'sqlalchemy>=2.0.0',
      'alembic>=1.12.0',
      'python-multipart>=0.0.6',
    ],
    files: [
      {
        path: 'main.py',
        content: `from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="My FastAPI App", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hello from FastAPI!"}

@app.get("/api/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)`,
        type: 'python',
      },
    ],
    commands: {
      create: './create_project.sh',
      install: 'pip install -r requirements.txt',
      dev: 'uvicorn main:app --host 0.0.0.0 --port 8000 --reload',
      test: 'pytest',
      lint: 'flake8 .',
      format: 'black . && isort .',
    },
    documentation: 'https://fastapi.tiangolo.com/',
    tags: ['api', 'async', 'modern', 'docs', 'fast'],
  },
  {
    id: 'streamlit-data-app',
    name: 'Streamlit Data Application',
    framework: 'streamlit',
    description: 'Interactive data science web application',
    icon: '📊',
    defaultPort: 8501,
    features: [
      'Streamlit 1.28+',
      'Interactive Widgets',
      'Data Visualization',
      'Charts and Plots',
      'File Upload/Download',
      'Session State',
    ],
    dependencies: [
      'streamlit>=1.28.0',
      'pandas>=2.0.0',
      'numpy>=1.24.0',
      'matplotlib>=3.7.0',
      'seaborn>=0.12.0',
      'plotly>=5.17.0',
      'requests>=2.31.0',
    ],
    files: [
      {
        path: 'app.py',
        content: `import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

st.title("My Streamlit App")
st.write("Welcome to your Streamlit application!")

# Sample data visualization
chart_data = pd.DataFrame(
    np.random.randn(20, 3),
    columns=['a', 'b', 'c']
)

st.line_chart(chart_data)

# Interactive widgets
name = st.text_input("Enter your name:")
if name:
    st.write(f"Hello, {name}!")

# Sidebar
st.sidebar.title("Navigation")
page = st.sidebar.selectbox("Choose a page", ["Home", "Data", "About"])

if page == "Data":
    st.write("This is the data page")
elif page == "About":
    st.write("This is the about page")`,
        type: 'python',
      },
    ],
    commands: {
      create: './create_project.sh',
      install: 'pip install -r requirements.txt',
      dev: 'streamlit run app.py --server.address 0.0.0.0 --server.port 8501',
      test: 'pytest',
    },
    documentation: 'https://docs.streamlit.io/',
    tags: ['data-science', 'visualization', 'interactive', 'ml'],
  },
  {
    id: 'gradio-ml-interface',
    name: 'Gradio ML Interface',
    framework: 'gradio',
    description: 'Machine learning model interface with Gradio',
    icon: '🤖',
    defaultPort: 7860,
    features: [
      'Gradio 4.0+',
      'ML Model Interfaces',
      'File Upload Support',
      'Interactive Components',
      'Sharing Capabilities',
      'Custom Blocks',
    ],
    dependencies: [
      'gradio>=4.0.0',
      'numpy>=1.24.0',
      'pandas>=2.0.0',
      'matplotlib>=3.7.0',
      'pillow>=10.0.0',
      'requests>=2.31.0',
    ],
    files: [
      {
        path: 'app.py',
        content: `import gradio as gr
import numpy as np
import matplotlib.pyplot as plt

def greet(name, intensity):
    return f"Hello, {name}!" * int(intensity)

def plot_function(equation, x_min, x_max):
    x = np.linspace(x_min, x_max, 100)
    try:
        y = eval(equation.replace('x', 'x'))
        plt.figure(figsize=(8, 6))
        plt.plot(x, y)
        plt.grid(True)
        plt.title(f"Plot of {equation}")
        plt.xlabel("x")
        plt.ylabel("y")
        return plt
    except:
        return "Invalid equation"

# Create interface
with gr.Blocks() as demo:
    gr.Markdown("# My Gradio App")
    
    with gr.Tab("Greeting"):
        name_input = gr.Textbox(label="Name")
        intensity_input = gr.Slider(1, 10, value=1, label="Intensity")
        greet_output = gr.Textbox(label="Greeting")
        greet_btn = gr.Button("Greet")
        greet_btn.click(greet, inputs=[name_input, intensity_input], outputs=greet_output)
    
    with gr.Tab("Plot"):
        equation_input = gr.Textbox(label="Equation (use 'x' as variable)", value="x**2")
        x_min_input = gr.Number(label="X Min", value=-10)
        x_max_input = gr.Number(label="X Max", value=10)
        plot_output = gr.Plot(label="Plot")
        plot_btn = gr.Button("Plot")
        plot_btn.click(plot_function, inputs=[equation_input, x_min_input, x_max_input], outputs=plot_output)

if __name__ == "__main__":
    demo.launch(server_name="0.0.0.0", server_port=7860)`,
        type: 'python',
      },
    ],
    commands: {
      create: './create_project.sh',
      install: 'pip install -r requirements.txt',
      dev: 'python app.py',
      test: 'pytest',
    },
    documentation: 'https://gradio.app/docs/',
    tags: ['ml', 'interface', 'demo', 'sharing', 'ai'],
  },
];

// Helper function to get template by ID
export function getPythonTemplate(id: string): PythonProjectTemplate | undefined {
  return PYTHON_PROJECT_TEMPLATES.find(template => template.id === id);
}

// Helper function to get templates by framework
export function getPythonTemplatesByFramework(framework: string): PythonProjectTemplate[] {
  return PYTHON_PROJECT_TEMPLATES.filter(template => template.framework === framework);
}

// Helper function to get all available frameworks
export function getAvailableFrameworks(): string[] {
  return [...new Set(PYTHON_PROJECT_TEMPLATES.map(template => template.framework))];
}
