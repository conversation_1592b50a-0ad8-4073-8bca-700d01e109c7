/**
 * Node/React Project Templates
 * Pre-configured templates for various Node.js and React frameworks
 */

import { NodeProjectTemplate } from '@/types/node-react-workspace';

export const NODE_REACT_PROJECT_TEMPLATES: NodeProjectTemplate[] = [
  {
    id: 'nextjs-app-router',
    name: 'Next.js App Router',
    framework: 'nextjs',
    description: 'Modern Next.js application with App Router, TypeScript, and Tailwind CSS',
    icon: '⚡',
    defaultPort: 3000,
    features: [
      'Next.js 14+ with App Router',
      'TypeScript Support',
      'Tailwind CSS',
      'ESLint & Prettier',
      'API Routes',
      'Image Optimization',
      'Font Optimization',
      'SEO Ready',
    ],
    dependencies: [
      'next@latest',
      'react@latest',
      'react-dom@latest',
      'tailwindcss@latest',
      'autoprefixer@latest',
      'postcss@latest',
    ],
    devDependencies: [
      '@types/node@latest',
      '@types/react@latest',
      '@types/react-dom@latest',
      'typescript@latest',
      'eslint@latest',
      'eslint-config-next@latest',
      'prettier@latest',
      '@tailwindcss/typography@latest',
    ],
    files: [
      {
        path: 'package.json',
        content: `{
  "name": "nextjs-app",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {},
  "devDependencies": {}
}`,
        type: 'json',
      },
      {
        path: 'next.config.js',
        content: `/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: [],
  },
}

module.exports = nextConfig`,
        type: 'javascript',
      },
      {
        path: 'tailwind.config.js',
        content: `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`,
        type: 'javascript',
      },
      {
        path: 'app/layout.tsx',
        content: `import './globals.css'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Next.js App',
  description: 'Generated by create-next-app',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}`,
        type: 'typescript',
      },
      {
        path: 'app/page.tsx',
        content: `export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex">
        <h1 className="text-4xl font-bold">Welcome to Next.js!</h1>
      </div>
    </main>
  )
}`,
        type: 'typescript',
      },
      {
        path: 'app/globals.css',
        content: `@tailwind base;
@tailwind components;
@tailwind utilities;`,
        type: 'css',
      },
    ],
    commands: {
      create: 'npx create-next-app@latest',
      install: 'npm install',
      dev: 'npm run dev',
      build: 'npm run build',
      start: 'npm start',
      test: 'npm test',
      lint: 'npm run lint',
      typecheck: 'npm run type-check',
    },
    documentation: 'https://nextjs.org/docs',
    tags: ['fullstack', 'react', 'ssr', 'typescript', 'tailwind'],
    buildTool: 'webpack',
    cssFramework: 'tailwind',
    testing: 'jest',
  },

  {
    id: 'react-vite-ts',
    name: 'React + Vite + TypeScript',
    framework: 'react',
    description: 'Modern React application with Vite, TypeScript, and Tailwind CSS',
    icon: '⚛️',
    defaultPort: 5173,
    features: [
      'React 18+',
      'Vite Build Tool',
      'TypeScript Support',
      'Tailwind CSS',
      'ESLint & Prettier',
      'Hot Module Replacement',
      'Fast Refresh',
    ],
    dependencies: [
      'react@latest',
      'react-dom@latest',
    ],
    devDependencies: [
      '@types/react@latest',
      '@types/react-dom@latest',
      '@typescript-eslint/eslint-plugin@latest',
      '@typescript-eslint/parser@latest',
      '@vitejs/plugin-react@latest',
      'eslint@latest',
      'eslint-plugin-react-hooks@latest',
      'eslint-plugin-react-refresh@latest',
      'typescript@latest',
      'vite@latest',
      'tailwindcss@latest',
      'autoprefixer@latest',
      'postcss@latest',
    ],
    files: [
      {
        path: 'package.json',
        content: `{
  "name": "react-vite-app",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {},
  "devDependencies": {}
}`,
        type: 'json',
      },
      {
        path: 'vite.config.ts',
        content: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 5173,
  },
})`,
        type: 'typescript',
      },
      {
        path: 'src/App.tsx',
        content: `import { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          React + Vite + TypeScript
        </h1>
        <div className="flex items-center gap-4">
          <button
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            onClick={() => setCount((count) => count + 1)}
          >
            count is {count}
          </button>
        </div>
      </div>
    </div>
  )
}

export default App`,
        type: 'typescript',
      },
      {
        path: 'src/main.tsx',
        content: `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`,
        type: 'typescript',
      },
      {
        path: 'src/index.css',
        content: `@tailwind base;
@tailwind components;
@tailwind utilities;`,
        type: 'css',
      },
      {
        path: 'index.html',
        content: `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React + Vite + TS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`,
        type: 'html',
      },
    ],
    commands: {
      create: 'npm create vite@latest',
      install: 'npm install',
      dev: 'npm run dev',
      build: 'npm run build',
      preview: 'npm run preview',
      lint: 'npm run lint',
    },
    documentation: 'https://vitejs.dev/guide/',
    tags: ['frontend', 'react', 'vite', 'typescript', 'spa'],
    buildTool: 'vite',
    cssFramework: 'tailwind',
    testing: 'vitest',
  },
  {
    id: 'express-typescript-api',
    name: 'Express TypeScript API',
    framework: 'express',
    description: 'RESTful API with Express, TypeScript, and comprehensive middleware',
    icon: '🚀',
    defaultPort: 3001,
    features: [
      'Express.js Framework',
      'TypeScript Support',
      'CORS Middleware',
      'Helmet Security',
      'Morgan Logging',
      'Rate Limiting',
      'JWT Authentication',
      'Swagger Documentation',
      'Error Handling',
    ],
    dependencies: [
      'express@latest',
      'cors@latest',
      'helmet@latest',
      'morgan@latest',
      'express-rate-limit@latest',
      'jsonwebtoken@latest',
      'bcryptjs@latest',
      'dotenv@latest',
      'compression@latest',
    ],
    devDependencies: [
      '@types/express@latest',
      '@types/cors@latest',
      '@types/morgan@latest',
      '@types/jsonwebtoken@latest',
      '@types/bcryptjs@latest',
      '@types/node@latest',
      'typescript@latest',
      'ts-node@latest',
      'nodemon@latest',
      'eslint@latest',
      '@typescript-eslint/eslint-plugin@latest',
      '@typescript-eslint/parser@latest',
      'prettier@latest',
      'swagger-jsdoc@latest',
      'swagger-ui-express@latest',
      '@types/swagger-jsdoc@latest',
      '@types/swagger-ui-express@latest',
    ],
    files: [
      {
        path: 'package.json',
        content: `{
  "name": "express-typescript-api",
  "version": "1.0.0",
  "description": "Express TypeScript API",
  "main": "dist/index.js",
  "scripts": {
    "dev": "nodemon src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "lint": "eslint src/**/*.ts",
    "format": "prettier --write src/**/*.ts"
  },
  "dependencies": {},
  "devDependencies": {}
}`,
        type: 'json',
      },
      {
        path: 'src/index.ts',
        content: `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import dotenv from 'dotenv';

import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';
import apiRoutes from './routes/api';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(morgan('combined'));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Express TypeScript API is running!' });
});

app.use('/api', apiRoutes);

// Error handling
app.use(notFound);
app.use(errorHandler);

app.listen(PORT, () => {
  console.log(\`Server is running on port \${PORT}\`);
});

export default app;`,
        type: 'typescript',
      },
      {
        path: 'src/routes/api.ts',
        content: `import { Router } from 'express';

const router = Router();

router.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

router.get('/users', (req, res) => {
  res.json({ users: [] });
});

export default router;`,
        type: 'typescript',
      },
      {
        path: 'src/middleware/errorHandler.ts',
        content: `import { Request, Response, NextFunction } from 'express';

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error(err.stack);

  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'production' ? {} : err.stack,
  });
};`,
        type: 'typescript',
      },
      {
        path: 'src/middleware/notFound.ts',
        content: `import { Request, Response } from 'express';

export const notFound = (req: Request, res: Response) => {
  res.status(404).json({ message: 'Route not found' });
};`,
        type: 'typescript',
      },
      {
        path: 'tsconfig.json',
        content: `{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}`,
        type: 'json',
      },
      {
        path: '.env.example',
        content: `PORT=3001
NODE_ENV=development
JWT_SECRET=your-secret-key
DATABASE_URL=postgresql://user:password@localhost:5432/database`,
        type: 'env',
      },
    ],
    commands: {
      create: 'mkdir express-api && cd express-api',
      install: 'npm install',
      dev: 'npm run dev',
      build: 'npm run build',
      start: 'npm start',
      lint: 'npm run lint',
      format: 'npm run format',
    },
    documentation: 'https://expressjs.com/',
    tags: ['backend', 'api', 'rest', 'typescript', 'middleware'],
    buildTool: 'webpack',
    testing: 'jest',
  },

  {
    id: 'nestjs-api',
    name: 'NestJS API',
    framework: 'nestjs',
    description: 'Scalable Node.js server-side application with NestJS framework',
    icon: '🐱',
    defaultPort: 3000,
    features: [
      'NestJS Framework',
      'TypeScript Support',
      'Dependency Injection',
      'Decorators',
      'Guards & Interceptors',
      'Swagger Documentation',
      'TypeORM Integration',
      'JWT Authentication',
      'Validation Pipes',
    ],
    dependencies: [
      '@nestjs/common@latest',
      '@nestjs/core@latest',
      '@nestjs/platform-express@latest',
      '@nestjs/swagger@latest',
      '@nestjs/jwt@latest',
      '@nestjs/passport@latest',
      '@nestjs/typeorm@latest',
      'typeorm@latest',
      'passport@latest',
      'passport-jwt@latest',
      'class-validator@latest',
      'class-transformer@latest',
      'reflect-metadata@latest',
      'rxjs@latest',
    ],
    devDependencies: [
      '@nestjs/cli@latest',
      '@nestjs/schematics@latest',
      '@nestjs/testing@latest',
      '@types/express@latest',
      '@types/jest@latest',
      '@types/node@latest',
      '@types/passport-jwt@latest',
      '@typescript-eslint/eslint-plugin@latest',
      '@typescript-eslint/parser@latest',
      'eslint@latest',
      'eslint-config-prettier@latest',
      'eslint-plugin-prettier@latest',
      'jest@latest',
      'prettier@latest',
      'source-map-support@latest',
      'supertest@latest',
      'ts-jest@latest',
      'ts-loader@latest',
      'ts-node@latest',
      'tsconfig-paths@latest',
      'typescript@latest',
    ],
    files: [
      {
        path: 'package.json',
        content: `{
  "name": "nestjs-api",
  "version": "0.0.1",
  "description": "NestJS API application",
  "author": "",
  "private": true,
  "license": "UNLICENSED",
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \\"src/**/*.ts\\" \\"test/**/*.ts\\"",
    "start": "nest start",
    "dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "lint": "eslint \\"{src,apps,libs,test}/**/*.ts\\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json"
  },
  "dependencies": {},
  "devDependencies": {}
}`,
        type: 'json',
      },
      {
        path: 'src/main.ts',
        content: `import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  app.useGlobalPipes(new ValidationPipe());
  app.enableCors();

  const config = new DocumentBuilder()
    .setTitle('NestJS API')
    .setDescription('The NestJS API description')
    .setVersion('1.0')
    .addTag('api')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(3000);
}
bootstrap();`,
        type: 'typescript',
      },
      {
        path: 'src/app.module.ts',
        content: `import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}`,
        type: 'typescript',
      },
      {
        path: 'src/app.controller.ts',
        content: `import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getHealth() {
    return { status: 'healthy', timestamp: new Date().toISOString() };
  }
}`,
        type: 'typescript',
      },
      {
        path: 'src/app.service.ts',
        content: `import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHello(): string {
    return 'Hello World!';
  }
}`,
        type: 'typescript',
      },
    ],
    commands: {
      create: 'nest new',
      install: 'npm install',
      dev: 'npm run dev',
      build: 'npm run build',
      start: 'npm run start:prod',
      test: 'npm test',
      lint: 'npm run lint',
      format: 'npm run format',
    },
    documentation: 'https://docs.nestjs.com/',
    tags: ['backend', 'api', 'typescript', 'decorators', 'enterprise'],
    buildTool: 'webpack',
    testing: 'jest',
  },
];

// Helper function to get template by ID
export function getNodeTemplate(id: string): NodeProjectTemplate | undefined {
  return NODE_REACT_PROJECT_TEMPLATES.find(template => template.id === id);
}

// Helper function to get templates by framework
export function getNodeTemplatesByFramework(framework: string): NodeProjectTemplate[] {
  return NODE_REACT_PROJECT_TEMPLATES.filter(template => template.framework === framework);
}

// Helper function to get templates by category
export function getNodeTemplatesByCategory(category: 'frontend' | 'fullstack' | 'backend'): NodeProjectTemplate[] {
  const { FRAMEWORK_CATEGORIES } = require('@/types/node-react-workspace');
  const frameworks = FRAMEWORK_CATEGORIES[category] || [];
  return NODE_REACT_PROJECT_TEMPLATES.filter(template => frameworks.includes(template.framework));
}
