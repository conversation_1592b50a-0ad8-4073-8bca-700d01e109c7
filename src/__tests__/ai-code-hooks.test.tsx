import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { renderHook, act, waitFor } from '@testing-library/react';
import {
  useAICodeCompletion,
  useCodeAnalysis,
  useAIAssistant,
  useCodeRefactoring,
  useAIEditorState,
} from '@/hooks/ai-code-editor';

// Mock fetch
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('AI Code Editor Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('useAICodeCompletion', () => {
    it('should initialize with empty state', () => {
      const { result } = renderHook(() => useAICodeCompletion());

      expect(result.current.suggestions).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeUndefined();
    });

    it('should request completions', async () => {
      const mockResponse = {
        success: true,
        data: {
          suggestions: [
            {
              id: 'completion-1',
              text: 'console.log',
              insertText: 'console.log()',
              confidence: 0.9,
              type: 'function',
              priority: 8,
              range: {
                start: { line: 1, column: 1 },
                end: { line: 1, column: 1 },
              },
            },
          ],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const { result } = renderHook(() => useAICodeCompletion());

      await act(async () => {
        await result.current.requestCompletion({
          code: 'const test = "hello";\n',
          position: { line: 2, column: 1 },
          language: 'typescript',
        });
      });

      await waitFor(() => {
        expect(result.current.suggestions).toHaveLength(1);
        expect(result.current.suggestions[0].text).toBe('console.log');
      });
    });

    it('should handle completion errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({
          success: false,
          error: { message: 'API Error' },
        }),
      });

      const { result } = renderHook(() => useAICodeCompletion());

      await act(async () => {
        await result.current.requestCompletion({
          code: 'test',
          position: { line: 1, column: 1 },
          language: 'typescript',
        });
      });

      await waitFor(() => {
        expect(result.current.error).toBe('API Error');
        expect(result.current.suggestions).toEqual([]);
      });
    });

    it('should dismiss suggestions', () => {
      const { result } = renderHook(() => useAICodeCompletion());

      act(() => {
        result.current.dismissSuggestions();
      });

      expect(result.current.suggestions).toEqual([]);
      expect(result.current.error).toBeUndefined();
    });
  });

  describe('useCodeAnalysis', () => {
    it('should initialize with empty state', () => {
      const { result } = renderHook(() => useCodeAnalysis());

      expect(result.current.errors).toEqual([]);
      expect(result.current.suggestions).toEqual([]);
      expect(result.current.isLoading).toBe(false);
    });

    it('should analyze code', async () => {
      const mockResponse = {
        success: true,
        data: {
          errors: [
            {
              id: 'error-1',
              message: 'Unused variable',
              severity: 'warning',
              range: {
                start: { line: 1, column: 7 },
                end: { line: 1, column: 14 },
              },
              source: 'static-analysis',
            },
          ],
          suggestions: [],
          metrics: {
            complexity: 2,
            maintainability: 8,
            readability: 9,
            performance: 8,
            security: 9,
          },
        },
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const { result } = renderHook(() => useCodeAnalysis());

      await act(async () => {
        await result.current.analyzeCode({
          code: 'const unused = "test";',
          language: 'typescript',
        });
      });

      await waitFor(() => {
        expect(result.current.errors).toHaveLength(1);
        expect(result.current.errors[0].message).toBe('Unused variable');
        expect(result.current.metrics.complexity).toBe(2);
      });
    });

    it('should dismiss errors', () => {
      const { result } = renderHook(() => useCodeAnalysis());

      // Set initial state with an error
      act(() => {
        result.current.dismissError('error-1');
      });

      expect(result.current.errors.find(e => e.id === 'error-1')).toBeUndefined();
    });
  });

  describe('useAIAssistant', () => {
    it('should initialize with empty conversation', () => {
      const { result } = renderHook(() => useAIAssistant());

      expect(result.current.messages).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeUndefined();
    });

    it('should send messages', async () => {
      const mockResponse = {
        success: true,
        data: {
          message: {
            id: 'assistant-1',
            type: 'assistant',
            content: 'This is a helpful response',
            timestamp: new Date().toISOString(),
          },
        },
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const { result } = renderHook(() => useAIAssistant());

      await act(async () => {
        await result.current.sendMessage({
          message: 'Help me with this code',
        });
      });

      await waitFor(() => {
        expect(result.current.messages).toHaveLength(2); // User message + assistant response
        expect(result.current.messages[1].content).toBe('This is a helpful response');
      });
    });

    it('should clear conversation', () => {
      const { result } = renderHook(() => useAIAssistant());

      act(() => {
        result.current.clearConversation();
      });

      expect(result.current.messages).toEqual([]);
      expect(localStorageMock.removeItem).toHaveBeenCalled();
    });

    it('should persist conversation', () => {
      const { result } = renderHook(() => useAIAssistant({
        persistConversation: true,
        userId: 'test-user',
      }));

      // Should attempt to load from localStorage on mount
      expect(localStorageMock.getItem).toHaveBeenCalledWith(
        'ai-assistant-test-user-default'
      );
    });
  });

  describe('useCodeRefactoring', () => {
    it('should initialize with empty suggestions', () => {
      const { result } = renderHook(() => useCodeRefactoring());

      expect(result.current.suggestions).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeUndefined();
    });

    it('should get refactoring suggestions', async () => {
      const mockResponse = {
        success: true,
        data: {
          suggestions: [
            {
              id: 'refactor-1',
              title: 'Extract method',
              description: 'Extract this code into a separate method',
              type: 'extract-method',
              range: {
                start: { line: 1, column: 1 },
                end: { line: 3, column: 1 },
              },
              preview: 'function extractedMethod() { ... }',
              edits: [],
              confidence: 0.8,
              impact: 'medium',
            },
          ],
        },
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const { result } = renderHook(() => useCodeRefactoring());

      await act(async () => {
        await result.current.getSuggestions('function test() { console.log("hello"); }', 'javascript');
      });

      await waitFor(() => {
        expect(result.current.suggestions).toHaveLength(1);
        expect(result.current.suggestions[0].title).toBe('Extract method');
      });
    });

    it('should preview suggestions', () => {
      const { result } = renderHook(() => useCodeRefactoring());

      const mockSuggestion = {
        id: 'refactor-1',
        title: 'Test refactoring',
        description: 'Test description',
        type: 'style' as const,
        range: {
          start: { line: 1, column: 1 },
          end: { line: 1, column: 10 },
        },
        preview: 'const test = "preview";',
        edits: [],
        confidence: 0.9,
        impact: 'low' as const,
      };

      const preview = result.current.previewSuggestion(mockSuggestion);
      expect(preview).toBe('const test = "preview";');
    });
  });

  describe('useAIEditorState', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useAIEditorState());

      expect(result.current.state.openFiles).toEqual([]);
      expect(result.current.state.cursor).toEqual({ line: 1, column: 1 });
      expect(result.current.state.assistant.isOpen).toBe(false);
    });

    it('should open files', () => {
      const { result } = renderHook(() => useAIEditorState());

      const testFile = {
        id: 'test-file',
        name: 'test.ts',
        path: '/test.ts',
        content: 'const test = "hello";',
        language: 'typescript',
        isDirty: false,
        lastModified: new Date(),
        size: 20,
      };

      act(() => {
        result.current.actions.openFile(testFile);
      });

      expect(result.current.state.openFiles).toHaveLength(1);
      expect(result.current.state.activeFile).toEqual(testFile);
    });

    it('should close files', () => {
      const { result } = renderHook(() => useAIEditorState());

      const testFile = {
        id: 'test-file',
        name: 'test.ts',
        path: '/test.ts',
        content: 'const test = "hello";',
        language: 'typescript',
        isDirty: false,
        lastModified: new Date(),
        size: 20,
      };

      act(() => {
        result.current.actions.openFile(testFile);
      });

      act(() => {
        result.current.actions.closeFile('test-file');
      });

      expect(result.current.state.openFiles).toHaveLength(0);
      expect(result.current.state.activeFile).toBeUndefined();
    });

    it('should update file content', () => {
      const { result } = renderHook(() => useAIEditorState());

      const testFile = {
        id: 'test-file',
        name: 'test.ts',
        path: '/test.ts',
        content: 'const test = "hello";',
        language: 'typescript',
        isDirty: false,
        lastModified: new Date(),
        size: 20,
      };

      act(() => {
        result.current.actions.openFile(testFile);
      });

      act(() => {
        result.current.actions.updateFileContent('test-file', 'const test = "world";');
      });

      expect(result.current.state.activeFile?.content).toBe('const test = "world";');
      expect(result.current.state.activeFile?.isDirty).toBe(true);
    });

    it('should toggle assistant', () => {
      const { result } = renderHook(() => useAIEditorState());

      act(() => {
        result.current.actions.toggleAssistant();
      });

      expect(result.current.state.assistant.isOpen).toBe(true);

      act(() => {
        result.current.actions.toggleAssistant();
      });

      expect(result.current.state.assistant.isOpen).toBe(false);
    });

    it('should persist state', () => {
      const { result } = renderHook(() => useAIEditorState({
        persistState: true,
        userId: 'test-user',
      }));

      // Should attempt to load from localStorage on mount
      expect(localStorageMock.getItem).toHaveBeenCalledWith(
        'ai-editor-state-test-user-default'
      );
    });
  });
});
