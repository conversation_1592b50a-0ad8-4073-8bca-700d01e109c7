import { SessionManager } from '@/lib/auth-utils';
import { handleAppwriteError, AppwriteError } from '@/lib/appwrite';

// Mock the account module
jest.mock('@/lib/appwrite', () => ({
  account: {
    get: jest.fn(),
    listSessions: jest.fn(),
    createEmailPasswordSession: jest.fn(),
    deleteSession: jest.fn(),
  },
  handleAppwriteError: jest.fn(),
  AppwriteError: jest.fn(),
}));

describe('Authentication Fixes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('SessionManager', () => {
    describe('hasActiveSession', () => {
      it('should return true when there is an active session', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({
          sessions: [
            { $id: 'session1', current: true },
            { $id: 'session2', current: false }
          ]
        });

        const result = await SessionManager.hasActiveSession();
        expect(result).toBe(true);
      });

      it('should return false when there are no active sessions', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({
          sessions: [
            { $id: 'session1', current: false },
            { $id: 'session2', current: false }
          ]
        });

        const result = await SessionManager.hasActiveSession();
        expect(result).toBe(false);
      });

      it('should return false when listSessions throws an error', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockRejectedValue(new Error('Network error'));

        const result = await SessionManager.hasActiveSession();
        expect(result).toBe(false);
      });
    });

    describe('createSessionSafely', () => {
      it('should create session successfully when no active session exists', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({ sessions: [] });
        mockAccount.createEmailPasswordSession.mockResolvedValue({ $id: 'new-session' });

        const result = await SessionManager.createSessionSafely('<EMAIL>', 'password');
        
        expect(result.success).toBe(true);
        expect(result.session).toEqual({ $id: 'new-session' });
        expect(mockAccount.createEmailPasswordSession).toHaveBeenCalledWith('<EMAIL>', 'password');
      });

      it('should delete existing session before creating new one', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({
          sessions: [{ $id: 'existing-session', current: true }]
        });
        mockAccount.deleteSession.mockResolvedValue({});
        mockAccount.createEmailPasswordSession.mockResolvedValue({ $id: 'new-session' });

        const result = await SessionManager.createSessionSafely('<EMAIL>', 'password');
        
        expect(result.success).toBe(true);
        expect(mockAccount.deleteSession).toHaveBeenCalledWith('current');
        expect(mockAccount.createEmailPasswordSession).toHaveBeenCalledWith('<EMAIL>', 'password');
      });

      it('should handle session creation failure', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({ sessions: [] });
        mockAccount.createEmailPasswordSession.mockRejectedValue(new Error('Invalid credentials'));

        const result = await SessionManager.createSessionSafely('<EMAIL>', 'wrong-password');
        
        expect(result.success).toBe(false);
        expect(result.error).toBe('Invalid credentials');
      });

      it('should continue even if deleting existing session fails', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({
          sessions: [{ $id: 'existing-session', current: true }]
        });
        mockAccount.deleteSession.mockRejectedValue(new Error('Session already expired'));
        mockAccount.createEmailPasswordSession.mockResolvedValue({ $id: 'new-session' });

        const result = await SessionManager.createSessionSafely('<EMAIL>', 'password');
        
        expect(result.success).toBe(true);
        expect(result.session).toEqual({ $id: 'new-session' });
      });
    });

    describe('cleanupSessions', () => {
      it('should delete expired sessions', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        const pastDate = new Date(Date.now() - 1000000).toISOString();
        const futureDate = new Date(Date.now() + 1000000).toISOString();
        
        mockAccount.listSessions.mockResolvedValue({
          sessions: [
            { $id: 'expired-session', current: false, expire: pastDate },
            { $id: 'valid-session', current: false, expire: futureDate },
            { $id: 'current-session', current: true, expire: pastDate } // Current session should not be deleted
          ]
        });
        mockAccount.deleteSession.mockResolvedValue({});

        await SessionManager.cleanupSessions();
        
        expect(mockAccount.deleteSession).toHaveBeenCalledTimes(1);
        expect(mockAccount.deleteSession).toHaveBeenCalledWith('expired-session');
      });

      it('should handle cleanup errors gracefully', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockRejectedValue(new Error('Network error'));

        // Should not throw
        await expect(SessionManager.cleanupSessions()).resolves.toBeUndefined();
      });
    });

    describe('validateAndRefreshSession', () => {
      it('should return true for valid session', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24 hours from now
        
        mockAccount.listSessions.mockResolvedValue({
          sessions: [{ $id: 'session', current: true, expire: futureDate }]
        });

        const result = await SessionManager.validateAndRefreshSession();
        expect(result).toBe(true);
      });

      it('should return false for expired session', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        const pastDate = new Date(Date.now() - 1000).toISOString(); // 1 second ago
        
        mockAccount.listSessions.mockResolvedValue({
          sessions: [{ $id: 'session', current: true, expire: pastDate }]
        });

        const result = await SessionManager.validateAndRefreshSession();
        expect(result).toBe(false);
      });

      it('should return false when no session exists', async () => {
        const mockAccount = require('@/lib/appwrite').account;
        mockAccount.listSessions.mockResolvedValue({ sessions: [] });

        const result = await SessionManager.validateAndRefreshSession();
        expect(result).toBe(false);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', () => {
      const mockHandleAppwriteError = require('@/lib/appwrite').handleAppwriteError;
      const networkError = { name: 'NetworkError', message: 'fetch failed' };
      
      mockHandleAppwriteError.mockImplementation((error) => {
        if (error.name === 'NetworkError') {
          return new AppwriteError(
            'Network error. Please check your internet connection and try again.',
            'NETWORK_ERROR',
            'network_error'
          );
        }
        return new AppwriteError(error.message, 'UNKNOWN_ERROR');
      });

      const result = mockHandleAppwriteError(networkError);
      expect(result.message).toContain('Network error');
      expect(result.code).toBe('NETWORK_ERROR');
    });

    it('should handle timeout errors', () => {
      const mockHandleAppwriteError = require('@/lib/appwrite').handleAppwriteError;
      const timeoutError = { name: 'TimeoutError', message: 'Request timeout' };
      
      mockHandleAppwriteError.mockImplementation((error) => {
        if (error.name === 'TimeoutError') {
          return new AppwriteError(
            'Request timed out. Please try again.',
            'TIMEOUT_ERROR',
            'timeout_error'
          );
        }
        return new AppwriteError(error.message, 'UNKNOWN_ERROR');
      });

      const result = mockHandleAppwriteError(timeoutError);
      expect(result.message).toContain('timed out');
      expect(result.code).toBe('TIMEOUT_ERROR');
    });

    it('should handle Appwrite-specific errors', () => {
      const mockHandleAppwriteError = require('@/lib/appwrite').handleAppwriteError;
      const appwriteError = { 
        code: '401', 
        message: 'Invalid credentials', 
        type: 'user_unauthorized' 
      };
      
      mockHandleAppwriteError.mockImplementation((error) => {
        if (error.code && error.message) {
          return new AppwriteError(error.message, error.code, error.type);
        }
        return new AppwriteError(error.message, 'UNKNOWN_ERROR');
      });

      const result = mockHandleAppwriteError(appwriteError);
      expect(result.message).toBe('Invalid credentials');
      expect(result.code).toBe('401');
      expect(result.type).toBe('user_unauthorized');
    });
  });
});
