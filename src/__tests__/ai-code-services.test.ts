import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { AICodeServiceFactory } from '@/services/ai-code';
import { 
  AICompletionRequest, 
  AIAnalysisRequest, 
  AIAssistantRequest 
} from '@/types/ai-code-editor';

// Mock the AI SDK
jest.mock('ai', () => ({
  generateText: jest.fn(),
  generateObject: jest.fn(),
  streamText: jest.fn(),
}));

jest.mock('@ai-sdk/openai', () => ({
  openai: jest.fn(() => 'mocked-openai-model'),
}));

describe('AI Code Services', () => {
  beforeEach(() => {
    // Reset service instances before each test
    AICodeServiceFactory.reset();
    jest.clearAllMocks();
  });

  describe('AICodeService', () => {
    it('should generate code completions', async () => {
      const { generateObject } = require('ai');
      generateObject.mockResolvedValue({
        object: {
          suggestions: [
            {
              text: 'console.log',
              insertText: 'console.log()',
              confidence: 0.9,
              type: 'function',
              description: 'Log to console',
              priority: 8,
            },
          ],
        },
        usage: { totalTokens: 50 },
      });

      const aiCodeService = AICodeServiceFactory.getAICodeService();
      const request: AICompletionRequest = {
        code: 'const message = "Hello";\n',
        position: { line: 2, column: 1 },
        language: 'typescript',
      };

      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await aiCodeService.generateCompletions(request, context);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].text).toBe('console.log');
      expect(result.data![0].confidence).toBe(0.9);
    });

    it('should analyze code for errors and suggestions', async () => {
      const { generateObject } = require('ai');
      generateObject.mockResolvedValue({
        object: {
          errors: [
            {
              message: 'Variable is declared but never used',
              severity: 'warning',
              line: 1,
              column: 7,
              source: 'typescript',
            },
          ],
          suggestions: [
            {
              title: 'Remove unused variable',
              description: 'This variable is declared but never used',
              type: 'style',
              line: 1,
              column: 7,
              endLine: 1,
              endColumn: 14,
              preview: '// Variable removed',
              newText: '',
              confidence: 0.95,
              impact: 'low',
            },
          ],
          metrics: {
            complexity: 2,
            maintainability: 8,
            readability: 9,
            performance: 8,
            security: 9,
          },
        },
        usage: { totalTokens: 100 },
      });

      const aiCodeService = AICodeServiceFactory.getAICodeService();
      const request: AIAnalysisRequest = {
        code: 'const unused = "test";\nconsole.log("Hello");',
        language: 'typescript',
        fileName: 'test.ts',
      };

      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await aiCodeService.analyzeCode(request, context);

      expect(result.success).toBe(true);
      expect(result.data!.errors).toHaveLength(1);
      expect(result.data!.suggestions).toHaveLength(1);
      expect(result.data!.metrics.complexity).toBe(2);
    });

    it('should generate assistant responses', async () => {
      const { generateText } = require('ai');
      generateText.mockResolvedValue({
        text: 'This code creates a simple greeting function that takes a name parameter and returns a formatted greeting string.',
        usage: { totalTokens: 75 },
      });

      const aiCodeService = AICodeServiceFactory.getAICodeService();
      const request: AIAssistantRequest = {
        message: 'Explain this code',
        codeContext: {
          file: 'test.ts',
          selection: {
            range: {
              start: { line: 1, column: 1 },
              end: { line: 3, column: 1 },
            },
            text: 'function greet(name: string) {\n  return `Hello, ${name}!`;\n}',
          },
        },
      };

      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await aiCodeService.generateAssistantResponse(request, context);

      expect(result.success).toBe(true);
      expect(result.data!.type).toBe('assistant');
      expect(result.data!.content).toContain('greeting function');
    });

    it('should handle health check', async () => {
      const { generateText } = require('ai');
      generateText.mockResolvedValue({
        text: 'OK',
      });

      const aiCodeService = AICodeServiceFactory.getAICodeService();
      const isHealthy = await aiCodeService.healthCheck();

      expect(isHealthy).toBe(true);
    });
  });

  describe('CodeAnalysisService', () => {
    it('should analyze code structure', async () => {
      const codeAnalysisService = AICodeServiceFactory.getCodeAnalysisService();
      const code = `
        function testFunction(param: string) {
          console.log(param);
          return param.toUpperCase();
        }
        
        const testVar = "hello";
      `;

      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await codeAnalysisService.analyzeCodeStructure(
        code,
        'typescript',
        context
      );

      expect(result.success).toBe(true);
      expect(result.data!.errors).toBeDefined();
      expect(result.data!.suggestions).toBeDefined();
      expect(result.data!.metrics).toBeDefined();
    });

    it('should parse code symbols', async () => {
      const codeAnalysisService = AICodeServiceFactory.getCodeAnalysisService();
      const code = `
        import { Component } from 'react';
        
        class TestClass {
          method() {
            return 'test';
          }
        }
        
        const testFunction = () => {
          return 'function';
        };
        
        let testVariable = 'variable';
      `;

      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await codeAnalysisService.parseCodeSymbols(
        code,
        'typescript',
        context
      );

      expect(result.success).toBe(true);
      expect(result.data!.functions).toBeDefined();
      expect(result.data!.classes).toBeDefined();
      expect(result.data!.variables).toBeDefined();
      expect(result.data!.imports).toBeDefined();
    });
  });

  describe('AIModelService', () => {
    it('should generate text with specified model', async () => {
      const { generateText } = require('ai');
      generateText.mockResolvedValue({
        text: 'Generated text response',
        usage: { totalTokens: 25 },
      });

      const aiModelService = AICodeServiceFactory.getAIModelService();
      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await aiModelService.generateText(
        'Test prompt',
        { model: 'gpt-3.5-turbo', temperature: 0.1 },
        context
      );

      expect(result.success).toBe(true);
      expect(result.data).toBe('Generated text response');
    });

    it('should get model recommendations', () => {
      const aiModelService = AICodeServiceFactory.getAIModelService();
      
      expect(aiModelService.getModelRecommendation('completion')).toBe('gpt-3.5-turbo');
      expect(aiModelService.getModelRecommendation('analysis')).toBe('gpt-4');
      expect(aiModelService.getModelRecommendation('chat')).toBe('claude-3-sonnet');
    });

    it('should return available models', () => {
      const aiModelService = AICodeServiceFactory.getAIModelService();
      const models = aiModelService.getAvailableModels();
      
      expect(models).toContain('gpt-4');
      expect(models).toContain('gpt-3.5-turbo');
      expect(models).toContain('claude-3-sonnet');
    });
  });

  describe('CodeFormattingService', () => {
    it('should format code', async () => {
      const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
      const code = 'function test(){console.log("hello");}';
      
      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await codeFormattingService.formatCode(
        code,
        'javascript',
        { indentSize: 2, semicolons: true },
        context
      );

      expect(result.success).toBe(true);
      expect(result.data!.formattedCode).toBeDefined();
      expect(result.data!.edits).toBeDefined();
      expect(result.data!.suggestions).toBeDefined();
    });

    it('should get formatting suggestions', async () => {
      const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
      const code = 'function test()   {  console.log("hello")  }  ';
      
      const context = {
        userId: 'test-user',
        timestamp: new Date(),
      };

      const result = await codeFormattingService.getFormattingSuggestions(
        code,
        'javascript',
        context
      );

      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
    });

    it('should return default options for supported languages', () => {
      const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
      
      const jsOptions = codeFormattingService.getDefaultOptions('javascript');
      expect(jsOptions).toBeDefined();
      expect(jsOptions!.indentSize).toBe(2);
      
      const tsOptions = codeFormattingService.getDefaultOptions('typescript');
      expect(tsOptions).toBeDefined();
      
      const unsupportedOptions = codeFormattingService.getDefaultOptions('unsupported');
      expect(unsupportedOptions).toBeNull();
    });
  });

  describe('Service Factory', () => {
    it('should provide singleton instances', () => {
      const service1 = AICodeServiceFactory.getAICodeService();
      const service2 = AICodeServiceFactory.getAICodeService();
      
      expect(service1).toBe(service2);
    });

    it('should perform health check for all services', async () => {
      const { generateText } = require('ai');
      generateText.mockResolvedValue({ text: 'OK' });

      const healthStatus = await AICodeServiceFactory.healthCheck();
      
      expect(healthStatus.overall).toBeDefined();
      expect(healthStatus.aiCode).toBeDefined();
      expect(healthStatus.codeAnalysis).toBeDefined();
      expect(healthStatus.aiModel).toBeDefined();
      expect(healthStatus.codeFormatting).toBeDefined();
    });
  });
});
