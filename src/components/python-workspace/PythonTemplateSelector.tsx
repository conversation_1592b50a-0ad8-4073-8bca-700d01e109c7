'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  Code, 
  Globe, 
  Zap, 
  BarChart3, 
  Bot,
  ExternalLink,
  Clock,
  Users
} from 'lucide-react';
import { PythonProjectTemplate, PythonFramework } from '@/types/python-workspace';
import { pythonWorkspaceService } from '@/services/python-workspace';

interface PythonTemplateSelectorProps {
  onTemplateSelect: (template: PythonProjectTemplate) => void;
  selectedTemplate?: PythonProjectTemplate;
  className?: string;
}

const frameworkIcons: Record<PythonFramework, React.ReactNode> = {
  django: <Globe className="h-5 w-5" />,
  flask: <Code className="h-5 w-5" />,
  fastapi: <Zap className="h-5 w-5" />,
  streamlit: <BarChart3 className="h-5 w-5" />,
  gradio: <Bot className="h-5 w-5" />,
};

const frameworkColors: Record<PythonFramework, string> = {
  django: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  flask: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  fastapi: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  streamlit: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  gradio: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
};

export function PythonTemplateSelector({ 
  onTemplateSelect, 
  selectedTemplate,
  className = '' 
}: PythonTemplateSelectorProps) {
  const [templates, setTemplates] = useState<PythonProjectTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFramework, setSelectedFramework] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedTemplates = await pythonWorkspaceService.getTemplates();
      setTemplates(fetchedTemplates);
    } catch (err) {
      setError('Failed to load Python templates');
      console.error('Error loading templates:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFramework = selectedFramework === 'all' || template.framework === selectedFramework;
    
    return matchesSearch && matchesFramework;
  });

  const frameworks = [...new Set(templates.map(t => t.framework))];

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-destructive mb-4">{error}</p>
              <Button onClick={loadTemplates} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Choose a Python Framework</h2>
        <p className="text-muted-foreground">
          Select a template to get started with your Python web application
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedFramework} onValueChange={setSelectedFramework}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Frameworks" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Frameworks</SelectItem>
            {frameworks.map(framework => (
              <SelectItem key={framework} value={framework}>
                <div className="flex items-center gap-2">
                  {frameworkIcons[framework]}
                  <span className="capitalize">{framework}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      <AnimatePresence mode="wait">
        {filteredTemplates.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-12"
          >
            <p className="text-muted-foreground">No templates found matching your criteria.</p>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredTemplates.map((template) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                whileHover={{ y: -4 }}
                transition={{ duration: 0.2 }}
              >
                <Card 
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    selectedTemplate?.id === template.id 
                      ? 'ring-2 ring-primary shadow-lg' 
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => onTemplateSelect(template)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{template.icon}</div>
                        <div>
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          <Badge 
                            variant="secondary" 
                            className={`mt-1 ${frameworkColors[template.framework]}`}
                          >
                            <div className="flex items-center gap-1">
                              {frameworkIcons[template.framework]}
                              <span className="capitalize">{template.framework}</span>
                            </div>
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <CardDescription className="text-sm leading-relaxed">
                      {template.description}
                    </CardDescription>

                    {/* Features */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Key Features:</h4>
                      <div className="flex flex-wrap gap-1">
                        {template.features.slice(0, 3).map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                        {template.features.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{template.features.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Meta info */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Port {template.defaultPort}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        <span>{template.tags.length} tags</span>
                      </div>
                    </div>

                    {/* Action button */}
                    <Button 
                      className="w-full" 
                      variant={selectedTemplate?.id === template.id ? "default" : "outline"}
                      onClick={(e) => {
                        e.stopPropagation();
                        onTemplateSelect(template);
                      }}
                    >
                      {selectedTemplate?.id === template.id ? 'Selected' : 'Select Template'}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected template info */}
      {selectedTemplate && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6"
        >
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span>Selected: {selectedTemplate.name}</span>
                <Badge className={frameworkColors[selectedTemplate.framework]}>
                  {selectedTemplate.framework}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Ready to create your {selectedTemplate.framework} project
                </p>
                <Button variant="outline" size="sm" asChild>
                  <a 
                    href={selectedTemplate.documentation} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    Docs
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
