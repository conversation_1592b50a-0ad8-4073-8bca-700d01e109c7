'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Square, 
  RotateCcw, 
  ExternalLink, 
  Terminal, 
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Globe,
  Code,
  Zap,
  BarChart3,
  Bot
} from 'lucide-react';
import { PythonLivePreview as LivePreviewType, PythonFramework } from '@/types/python-workspace';
import { pythonWorkspaceService } from '@/services/python-workspace';

interface PythonLivePreviewProps {
  workspaceId: string;
  framework: PythonFramework;
  projectPath: string;
  className?: string;
  onStatusChange?: (status: LivePreviewType['status']) => void;
}

const frameworkIcons: Record<PythonFramework, React.ReactNode> = {
  django: <Globe className="h-4 w-4" />,
  flask: <Code className="h-4 w-4" />,
  fastapi: <Zap className="h-4 w-4" />,
  streamlit: <BarChart3 className="h-4 w-4" />,
  gradio: <Bot className="h-4 w-4" />,
};

const statusColors = {
  starting: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  running: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  stopped: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const statusIcons = {
  starting: <Clock className="h-3 w-3" />,
  running: <CheckCircle className="h-3 w-3" />,
  stopped: <Square className="h-3 w-3" />,
  error: <AlertCircle className="h-3 w-3" />,
};

export function PythonLivePreview({ 
  workspaceId, 
  framework, 
  projectPath, 
  className = '',
  onStatusChange 
}: PythonLivePreviewProps) {
  const [preview, setPreview] = useState<LivePreviewType | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch preview status
  const fetchStatus = useCallback(async () => {
    try {
      const status = await pythonWorkspaceService.getLivePreviewStatus(workspaceId);
      if (status && status.framework === framework) {
        setPreview(status);
        setLogs(status.logs || []);
        onStatusChange?.(status.status);
      } else {
        setPreview(null);
        setLogs([]);
        onStatusChange?.('stopped');
      }
      setError(null);
    } catch (err) {
      console.error('Error fetching preview status:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch status');
    }
  }, [workspaceId, framework, onStatusChange]);

  // Auto-refresh status
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchStatus, 5000);
    return () => clearInterval(interval);
  }, [fetchStatus, autoRefresh]);

  // Initial fetch
  useEffect(() => {
    fetchStatus();
  }, [fetchStatus]);

  const startPreview = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await pythonWorkspaceService.startLivePreview(
        workspaceId,
        projectPath,
        framework
      );
      
      setPreview(result);
      onStatusChange?.(result.status);
    } catch (err) {
      console.error('Error starting preview:', err);
      setError(err instanceof Error ? err.message : 'Failed to start preview');
    } finally {
      setLoading(false);
    }
  };

  const stopPreview = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await pythonWorkspaceService.stopLivePreview(workspaceId);
      
      setPreview(null);
      setLogs([]);
      onStatusChange?.('stopped');
    } catch (err) {
      console.error('Error stopping preview:', err);
      setError(err instanceof Error ? err.message : 'Failed to stop preview');
    } finally {
      setLoading(false);
    }
  };

  const restartPreview = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await stopPreview();
      // Wait a moment before restarting
      await new Promise(resolve => setTimeout(resolve, 2000));
      await startPreview();
    } catch (err) {
      console.error('Error restarting preview:', err);
      setError(err instanceof Error ? err.message : 'Failed to restart preview');
    } finally {
      setLoading(false);
    }
  };

  const openInNewTab = () => {
    if (preview?.url) {
      window.open(preview.url, '_blank');
    }
  };

  const isRunning = preview?.status === 'running';
  const canStart = !preview || preview.status === 'stopped' || preview.status === 'error';

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                {frameworkIcons[framework]}
                <CardTitle className="text-lg capitalize">{framework} Preview</CardTitle>
              </div>
              {preview && (
                <Badge className={statusColors[preview.status]}>
                  <div className="flex items-center gap-1">
                    {statusIcons[preview.status]}
                    <span className="capitalize">{preview.status}</span>
                  </div>
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {preview?.url && isRunning && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={openInNewTab}
                  className="flex items-center gap-1"
                >
                  <ExternalLink className="h-3 w-3" />
                  Open
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={autoRefresh ? 'bg-primary/10' : ''}
              >
                <Activity className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          <CardDescription>
            {preview?.url ? (
              <span className="font-mono text-sm">{preview.url}</span>
            ) : (
              `Live preview for ${framework} project at ${projectPath}`
            )}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md"
            >
              <div className="flex items-center gap-2 text-red-800 dark:text-red-300">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-sm text-red-700 dark:text-red-400 mt-1">{error}</p>
            </motion.div>
          )}

          {/* Controls */}
          <div className="flex items-center gap-2">
            {canStart ? (
              <Button
                onClick={startPreview}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                {loading ? 'Starting...' : 'Start Preview'}
              </Button>
            ) : (
              <Button
                onClick={stopPreview}
                disabled={loading}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <Square className="h-4 w-4" />
                {loading ? 'Stopping...' : 'Stop Preview'}
              </Button>
            )}

            {preview && (
              <Button
                onClick={restartPreview}
                disabled={loading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Restart
              </Button>
            )}
          </div>

          {/* Preview Info */}
          {preview && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Port:</span>
                <div className="font-mono">{preview.port}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Status:</span>
                <div className="capitalize">{preview.status}</div>
              </div>
              {preview.process?.startTime && (
                <div>
                  <span className="text-muted-foreground">Started:</span>
                  <div>{new Date(preview.process.startTime).toLocaleTimeString()}</div>
                </div>
              )}
              {preview.process?.pid && (
                <div>
                  <span className="text-muted-foreground">PID:</span>
                  <div className="font-mono">{preview.process.pid}</div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview Content */}
      {preview && (
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="logs" className="flex items-center gap-1">
              <Terminal className="h-3 w-3" />
              Logs
            </TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="mt-4">
            <Card>
              <CardContent className="p-0">
                {isRunning && preview.url ? (
                  <div className="relative">
                    <iframe
                      src={preview.url}
                      className="w-full h-96 border-0 rounded-md"
                      title={`${framework} Preview`}
                      onError={() => setError('Failed to load preview')}
                    />
                    <div className="absolute top-2 right-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={openInNewTab}
                        className="flex items-center gap-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        Open in New Tab
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="h-96 flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">Preview Not Available</p>
                      <p className="text-sm">
                        {preview.status === 'starting' 
                          ? 'Starting preview server...' 
                          : 'Start the preview to see your application'}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Server Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-black text-green-400 p-4 rounded-md font-mono text-xs h-64 overflow-y-auto">
                  <AnimatePresence>
                    {logs.length > 0 ? (
                      logs.map((log, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="mb-1"
                        >
                          {log}
                        </motion.div>
                      ))
                    ) : (
                      <div className="text-gray-500">No logs available</div>
                    )}
                  </AnimatePresence>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
