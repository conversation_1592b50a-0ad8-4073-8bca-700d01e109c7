'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  Package, 
  Download, 
  Trash2, 
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Plus,
  Settings,
  Terminal,
  BookOpen
} from 'lucide-react';
import { PythonPackage, PythonPackageManager } from '@/types/python-workspace';
import { pythonPackageManagerService } from '@/services/python-package-manager';

interface PythonPackageManagerProps {
  workspaceId: string;
  className?: string;
  onPackageChange?: (packages: PythonPackage[]) => void;
}

const categoryColors = {
  framework: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  database: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  testing: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  development: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  ml: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  web: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
  utility: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
};

export function PythonPackageManager({ 
  workspaceId, 
  className = '',
  onPackageChange 
}: PythonPackageManagerProps) {
  const [packages, setPackages] = useState<PythonPackage[]>([]);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [packageManagers, setPackageManagers] = useState<PythonPackageManager[]>([]);
  const [selectedManager, setSelectedManager] = useState<string>('pip');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [installing, setInstalling] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    loadPackageManagers();
    loadInstalledPackages();
  }, [workspaceId, selectedManager]);

  const loadPackageManagers = async () => {
    try {
      const managers = pythonPackageManagerService.getPackageManagers();
      setPackageManagers(managers);
    } catch (err) {
      console.error('Error loading package managers:', err);
    }
  };

  const loadInstalledPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(
        `/api/python-workspace/workspaces/${workspaceId}/packages?packageManager=${selectedManager}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to load packages');
      }
      
      const data = await response.json();
      if (data.success) {
        setPackages(data.data.packages);
        onPackageChange?.(data.data.packages);
      } else {
        throw new Error(data.message || 'Failed to load packages');
      }
    } catch (err) {
      console.error('Error loading packages:', err);
      setError(err instanceof Error ? err.message : 'Failed to load packages');
    } finally {
      setLoading(false);
    }
  };

  const searchPackages = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      
      const response = await fetch(
        `/api/python-workspace/workspaces/${workspaceId}/packages/search?q=${encodeURIComponent(query)}&packageManager=${selectedManager}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to search packages');
      }
      
      const data = await response.json();
      if (data.success) {
        setSearchResults(data.data.results);
      } else {
        throw new Error(data.message || 'Failed to search packages');
      }
    } catch (err) {
      console.error('Error searching packages:', err);
      setError(err instanceof Error ? err.message : 'Failed to search packages');
    } finally {
      setLoading(false);
    }
  };

  const installPackage = async (packageName: string, version?: string) => {
    try {
      setInstalling(prev => new Set(prev).add(packageName));
      setError(null);
      
      const response = await fetch(
        `/api/python-workspace/workspaces/${workspaceId}/packages/install`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            packageName,
            version,
            packageManager: selectedManager,
          }),
        }
      );
      
      if (!response.ok) {
        throw new Error('Failed to install package');
      }
      
      const data = await response.json();
      if (data.success) {
        await loadInstalledPackages();
        // Update search results to show installed status
        setSearchResults(prev => prev.map(pkg => 
          pkg.name === packageName 
            ? { ...pkg, isInstalled: true, installedVersion: version || pkg.version }
            : pkg
        ));
      } else {
        throw new Error(data.message || 'Failed to install package');
      }
    } catch (err) {
      console.error('Error installing package:', err);
      setError(err instanceof Error ? err.message : 'Failed to install package');
    } finally {
      setInstalling(prev => {
        const newSet = new Set(prev);
        newSet.delete(packageName);
        return newSet;
      });
    }
  };

  const uninstallPackage = async (packageName: string) => {
    try {
      setInstalling(prev => new Set(prev).add(packageName));
      setError(null);
      
      const response = await fetch(
        `/api/python-workspace/workspaces/${workspaceId}/packages`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            packageName,
            packageManager: selectedManager,
          }),
        }
      );
      
      if (!response.ok) {
        throw new Error('Failed to uninstall package');
      }
      
      const data = await response.json();
      if (data.success) {
        await loadInstalledPackages();
        // Update search results to show uninstalled status
        setSearchResults(prev => prev.map(pkg => 
          pkg.name === packageName 
            ? { ...pkg, isInstalled: false, installedVersion: undefined }
            : pkg
        ));
      } else {
        throw new Error(data.message || 'Failed to uninstall package');
      }
    } catch (err) {
      console.error('Error uninstalling package:', err);
      setError(err instanceof Error ? err.message : 'Failed to uninstall package');
    } finally {
      setInstalling(prev => {
        const newSet = new Set(prev);
        newSet.delete(packageName);
        return newSet;
      });
    }
  };

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    const timeoutId = setTimeout(() => {
      searchPackages(query);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  }, [selectedManager]);

  const filteredPackages = packages.filter(pkg => {
    const matchesCategory = selectedCategory === 'all' || pkg.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      pkg.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (pkg.description && pkg.description.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const packagesByCategory = packages.reduce((acc, pkg) => {
    if (!acc[pkg.category]) {
      acc[pkg.category] = [];
    }
    acc[pkg.category].push(pkg);
    return acc;
  }, {} as Record<string, PythonPackage[]>);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Package Manager</h2>
          <p className="text-muted-foreground">
            Manage Python packages and dependencies
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={selectedManager} onValueChange={setSelectedManager}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {packageManagers.map(manager => (
                <SelectItem key={manager.name} value={manager.name}>
                  {manager.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadInstalledPackages}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md"
        >
          <div className="flex items-center gap-2 text-red-800 dark:text-red-300">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Error</span>
          </div>
          <p className="text-sm text-red-700 dark:text-red-400 mt-1">{error}</p>
        </motion.div>
      )}

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search packages..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Tabs */}
      <Tabs defaultValue="installed" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="installed">
            Installed ({packages.length})
          </TabsTrigger>
          <TabsTrigger value="search">
            Search Results ({searchResults.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="installed" className="mt-6">
          <div className="space-y-4">
            {/* Category Filter */}
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {Object.keys(packagesByCategory).map(category => (
                  <SelectItem key={category} value={category}>
                    <div className="flex items-center gap-2">
                      <span className="capitalize">{category}</span>
                      <Badge variant="secondary" className="text-xs">
                        {packagesByCategory[category].length}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Package List */}
            <div className="grid gap-4">
              <AnimatePresence>
                {filteredPackages.map((pkg) => (
                  <motion.div
                    key={pkg.name}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Package className="h-5 w-5 text-muted-foreground" />
                            <div>
                              <h3 className="font-medium">{pkg.name}</h3>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  v{pkg.version}
                                </Badge>
                                <Badge 
                                  className={`text-xs ${categoryColors[pkg.category]}`}
                                >
                                  {pkg.category}
                                </Badge>
                                {pkg.required && (
                                  <Badge variant="secondary" className="text-xs">
                                    Required
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => uninstallPackage(pkg.name)}
                            disabled={installing.has(pkg.name) || pkg.required}
                          >
                            {installing.has(pkg.name) ? (
                              <RefreshCw className="h-3 w-3 animate-spin" />
                            ) : (
                              <Trash2 className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        
                        {pkg.description && (
                          <p className="text-sm text-muted-foreground mt-2">
                            {pkg.description}
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {filteredPackages.length === 0 && !loading && (
                <div className="text-center py-12">
                  <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <p className="text-lg font-medium mb-2">No packages found</p>
                  <p className="text-sm text-muted-foreground">
                    {searchQuery ? 'Try a different search term' : 'No packages installed yet'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="search" className="mt-6">
          <div className="grid gap-4">
            <AnimatePresence>
              {searchResults.map((result) => (
                <motion.div
                  key={result.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Package className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <h3 className="font-medium">{result.name}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                v{result.version}
                              </Badge>
                              {result.isInstalled && (
                                <Badge className="text-xs bg-green-100 text-green-800">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Installed
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <Button
                          variant={result.isInstalled ? "destructive" : "default"}
                          size="sm"
                          onClick={() => result.isInstalled 
                            ? uninstallPackage(result.name)
                            : installPackage(result.name, result.version)
                          }
                          disabled={installing.has(result.name)}
                        >
                          {installing.has(result.name) ? (
                            <RefreshCw className="h-3 w-3 animate-spin" />
                          ) : result.isInstalled ? (
                            <Trash2 className="h-3 w-3" />
                          ) : (
                            <Download className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                      
                      {result.description && (
                        <p className="text-sm text-muted-foreground mt-2">
                          {result.description}
                        </p>
                      )}
                      
                      {result.author && (
                        <p className="text-xs text-muted-foreground mt-1">
                          by {result.author}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {searchResults.length === 0 && searchQuery && !loading && (
              <div className="text-center py-12">
                <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                <p className="text-lg font-medium mb-2">No results found</p>
                <p className="text-sm text-muted-foreground">
                  Try searching for a different package name
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
