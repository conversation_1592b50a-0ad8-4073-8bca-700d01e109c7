'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppwriteDatabase } from '@/hooks/useAppwriteDatabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Loader2,
  AlertCircle,
  Database,
  FileText,
  Calendar,
  User
} from 'lucide-react';

interface DocumentManagerProps {
  databaseId: string;
  collectionId: string;
  title?: string;
  description?: string;
  className?: string;
}

interface Document {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  [key: string]: any;
}

export function DocumentManager({
  databaseId,
  collectionId,
  title = 'Documents',
  description = 'Manage your collection documents',
  className = '',
}: DocumentManagerProps) {
  const { 
    listDocuments, 
    createDocument, 
    updateDocument, 
    deleteDocument, 
    isLoading, 
    error, 
    clearError 
  } = useAppwriteDatabase();

  const [documents, setDocuments] = useState<Document[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [newDocumentData, setNewDocumentData] = useState<Record<string, any>>({});
  const [pagination, setPagination] = useState({
    limit: 10,
    offset: 0,
    hasMore: true,
  });

  // Load documents
  const loadDocuments = useCallback(async (reset = false) => {
    try {
      clearError();
      const offset = reset ? 0 : pagination.offset;
      
      const result = await listDocuments(databaseId, collectionId, {
        pagination: { limit: pagination.limit, offset },
        search: searchQuery || undefined,
      });

      if (result.success && result.data) {
        if (reset) {
          setDocuments(result.data.items);
        } else {
          setDocuments(prev => [...prev, ...result.data!.items]);
        }
        
        setPagination(prev => ({
          ...prev,
          offset: offset + result.data!.items.length,
          hasMore: result.data!.hasMore,
        }));
      }
    } catch (error) {
      console.error('Failed to load documents:', error);
    }
  }, [databaseId, collectionId, listDocuments, searchQuery, pagination.limit, pagination.offset, clearError]);

  // Load documents on mount and when search changes
  useEffect(() => {
    loadDocuments(true);
  }, [searchQuery]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, offset: 0, hasMore: true }));
  };

  // Handle create document
  const handleCreateDocument = async () => {
    try {
      clearError();
      
      const result = await createDocument({
        databaseId,
        collectionId,
        data: newDocumentData,
      });

      if (result.success && result.data) {
        setDocuments(prev => [result.data!, ...prev]);
        setIsCreating(false);
        setNewDocumentData({});
      }
    } catch (error) {
      console.error('Failed to create document:', error);
    }
  };

  // Handle update document
  const handleUpdateDocument = async (documentId: string, data: Record<string, any>) => {
    try {
      clearError();
      
      const result = await updateDocument({
        databaseId,
        collectionId,
        documentId,
        data,
      });

      if (result.success && result.data) {
        setDocuments(prev => 
          prev.map(doc => doc.$id === documentId ? result.data! : doc)
        );
        setEditingDocument(null);
      }
    } catch (error) {
      console.error('Failed to update document:', error);
    }
  };

  // Handle delete document
  const handleDeleteDocument = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      clearError();
      
      const result = await deleteDocument(databaseId, collectionId, documentId);

      if (result.success) {
        setDocuments(prev => prev.filter(doc => doc.$id !== documentId));
      }
    } catch (error) {
      console.error('Failed to delete document:', error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get document fields (excluding system fields)
  const getDocumentFields = (doc: Document) => {
    const systemFields = ['$id', '$createdAt', '$updatedAt', '$permissions', '$collectionId', '$databaseId'];
    return Object.entries(doc).filter(([key]) => !systemFields.includes(key));
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
          <p className="text-muted-foreground">{description}</p>
        </div>
        
        <Button
          onClick={() => setIsCreating(true)}
          disabled={isLoading}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Document
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search documents..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Create Document Form */}
      <AnimatePresence>
        {isCreating && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Create New Document</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setIsCreating(false);
                      setNewDocumentData({});
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="field-key">Field Name</Label>
                    <Input
                      id="field-key"
                      placeholder="Enter field name"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const key = e.currentTarget.value;
                          const valueInput = document.getElementById('field-value') as HTMLInputElement;
                          if (key && valueInput?.value) {
                            setNewDocumentData(prev => ({
                              ...prev,
                              [key]: valueInput.value,
                            }));
                            e.currentTarget.value = '';
                            valueInput.value = '';
                          }
                        }
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="field-value">Field Value</Label>
                    <Input
                      id="field-value"
                      placeholder="Enter field value"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const value = e.currentTarget.value;
                          const keyInput = document.getElementById('field-key') as HTMLInputElement;
                          if (keyInput?.value && value) {
                            setNewDocumentData(prev => ({
                              ...prev,
                              [keyInput.value]: value,
                            }));
                            keyInput.value = '';
                            e.currentTarget.value = '';
                          }
                        }
                      }}
                    />
                  </div>
                </div>

                {/* Preview fields */}
                {Object.keys(newDocumentData).length > 0 && (
                  <div className="space-y-2">
                    <Label>Document Preview</Label>
                    <div className="bg-muted/50 rounded-lg p-3 space-y-1">
                      {Object.entries(newDocumentData).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between text-sm">
                          <span className="font-medium">{key}:</span>
                          <div className="flex items-center gap-2">
                            <span className="text-muted-foreground">{String(value)}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setNewDocumentData(prev => {
                                  const { [key]: _, ...rest } = prev;
                                  return rest;
                                });
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsCreating(false);
                      setNewDocumentData({});
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateDocument}
                    disabled={isLoading || Object.keys(newDocumentData).length === 0}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Create Document
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Documents List */}
      <div className="space-y-4">
        {documents.length === 0 && !isLoading ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Database className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No documents found</h3>
              <p className="text-muted-foreground text-center mb-4">
                {searchQuery 
                  ? `No documents match "${searchQuery}"`
                  : 'Get started by creating your first document'
                }
              </p>
              {!searchQuery && (
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Document
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <>
            {documents.map((document) => (
              <motion.div
                key={document.$id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                layout
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="font-mono text-sm text-muted-foreground">
                            {document.$id}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Created: {formatDate(document.$createdAt)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Updated: {formatDate(document.$updatedAt)}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingDocument(document)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteDocument(document.$id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <Separator className="mb-4" />

                    {/* Document Fields */}
                    <div className="space-y-2">
                      {getDocumentFields(document).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <span className="text-sm font-medium">{key}:</span>
                          <Badge variant="secondary" className="font-mono text-xs">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* Load More */}
            {pagination.hasMore && (
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  onClick={() => loadDocuments(false)}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
