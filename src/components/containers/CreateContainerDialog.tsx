'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AnimatedButton } from '@/components/ui/enhanced-card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Plus,
  Minus,
  Container,
  Settings,
  Network,
  HardDrive,
  Shield,
  Terminal
} from 'lucide-react';
import { CreateContainerOptions } from '@/types/docker';

interface CreateContainerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateContainer: (options: CreateContainerOptions) => Promise<void>;
}

export const CreateContainerDialog: React.FC<CreateContainerDialogProps> = ({
  open,
  onOpenChange,
  onCreateContainer
}) => {
  const [formData, setFormData] = useState<CreateContainerOptions>({
    name: '',
    image: '',
    cpu: 1,
    memory: 512,
    ports: {},
    environment: {},
    volumes: {},
    networkMode: 'bridge',
    cmd: [],
    workingDir: '',
    user: '',
    privileged: false,
    autoRemove: false
  });

  const [portMappings, setPortMappings] = useState<Array<{ container: string; host: string }>>([]);
  const [envVars, setEnvVars] = useState<Array<{ key: string; value: string }>>([]);
  const [volumeMounts, setVolumeMounts] = useState<Array<{ host: string; container: string }>>([]);
  const [commands, setCommands] = useState<string>('');
  const [creating, setCreating] = useState(false);

  const handleSubmit = async () => {
    setCreating(true);
    try {
      // Convert arrays to objects
      const ports: { [key: string]: string } = {};
      portMappings.forEach(({ container, host }) => {
        if (container && host) {
          ports[container] = host;
        }
      });

      const environment: { [key: string]: string } = {};
      envVars.forEach(({ key, value }) => {
        if (key && value) {
          environment[key] = value;
        }
      });

      const volumes: { [key: string]: string } = {};
      volumeMounts.forEach(({ host, container }) => {
        if (host && container) {
          volumes[host] = container;
        }
      });

      const cmd = commands.trim() ? commands.split(' ').filter(c => c.length > 0) : [];

      const options: CreateContainerOptions = {
        ...formData,
        ports,
        environment,
        volumes,
        cmd
      };

      await onCreateContainer(options);
      
      // Reset form
      setFormData({
        name: '',
        image: '',
        cpu: 1,
        memory: 512,
        ports: {},
        environment: {},
        volumes: {},
        networkMode: 'bridge',
        cmd: [],
        workingDir: '',
        user: '',
        privileged: false,
        autoRemove: false
      });
      setPortMappings([]);
      setEnvVars([]);
      setVolumeMounts([]);
      setCommands('');
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to create container:', error);
    } finally {
      setCreating(false);
    }
  };

  const addPortMapping = () => {
    setPortMappings([...portMappings, { container: '', host: '' }]);
  };

  const removePortMapping = (index: number) => {
    setPortMappings(portMappings.filter((_, i) => i !== index));
  };

  const updatePortMapping = (index: number, field: 'container' | 'host', value: string) => {
    const updated = [...portMappings];
    updated[index][field] = value;
    setPortMappings(updated);
  };

  const addEnvVar = () => {
    setEnvVars([...envVars, { key: '', value: '' }]);
  };

  const removeEnvVar = (index: number) => {
    setEnvVars(envVars.filter((_, i) => i !== index));
  };

  const updateEnvVar = (index: number, field: 'key' | 'value', value: string) => {
    const updated = [...envVars];
    updated[index][field] = value;
    setEnvVars(updated);
  };

  const addVolumeMount = () => {
    setVolumeMounts([...volumeMounts, { host: '', container: '' }]);
  };

  const removeVolumeMount = (index: number) => {
    setVolumeMounts(volumeMounts.filter((_, i) => i !== index));
  };

  const updateVolumeMount = (index: number, field: 'host' | 'container', value: string) => {
    const updated = [...volumeMounts];
    updated[index][field] = value;
    setVolumeMounts(updated);
  };

  const isFormValid = formData.name.trim() && formData.image.trim();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Container className="h-5 w-5" />
            Create New Container
          </DialogTitle>
          <DialogDescription>
            Configure your new Docker container with custom specifications
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="basic" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="network">Network</TabsTrigger>
              <TabsTrigger value="storage">Storage</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto mt-4">
              <TabsContent value="basic" className="space-y-4 mt-0">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Container Name *</Label>
                    <Input
                      id="name"
                      placeholder="my-container"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="image">Image *</Label>
                    <Input
                      id="image"
                      placeholder="nginx:latest"
                      value={formData.image}
                      onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cpu">CPU Cores</Label>
                    <Input
                      id="cpu"
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={formData.cpu}
                      onChange={(e) => setFormData({ ...formData, cpu: parseFloat(e.target.value) })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="memory">Memory (MB)</Label>
                    <Input
                      id="memory"
                      type="number"
                      min="128"
                      step="128"
                      value={formData.memory}
                      onChange={(e) => setFormData({ ...formData, memory: parseInt(e.target.value) })}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="command">Command</Label>
                  <Input
                    id="command"
                    placeholder="npm start"
                    value={commands}
                    onChange={(e) => setCommands(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Space-separated command and arguments
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="network" className="space-y-4 mt-0">
                <div className="space-y-2">
                  <Label>Network Mode</Label>
                  <Select value={formData.networkMode} onValueChange={(value) => setFormData({ ...formData, networkMode: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bridge">Bridge</SelectItem>
                      <SelectItem value="host">Host</SelectItem>
                      <SelectItem value="none">None</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Port Mappings</Label>
                    <Button size="sm" variant="outline" onClick={addPortMapping}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {portMappings.map((mapping, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        placeholder="Host port"
                        value={mapping.host}
                        onChange={(e) => updatePortMapping(index, 'host', e.target.value)}
                      />
                      <span>:</span>
                      <Input
                        placeholder="Container port"
                        value={mapping.container}
                        onChange={(e) => updatePortMapping(index, 'container', e.target.value)}
                      />
                      <Button size="sm" variant="outline" onClick={() => removePortMapping(index)}>
                        <Minus className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <AnimatedButton 
            onClick={handleSubmit}
            disabled={!isFormValid || creating}
            loading={creating}
          >
            Create Container
          </AnimatedButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
