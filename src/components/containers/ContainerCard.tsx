'use client';


/**
 * Docker-related types for container management
 */

export interface ContainerPortMapping {
  [containerPort: string]: string; // containerPort -> hostPort
}

export interface ContainerResourceUsage {
  cpu?: number;
  memory?: number;
  networkMode?: string;
}

export interface ContainerStatusCounts {
  running: number;
  stopped: number;
  total: number;
}

export interface ContainerInfo {
  id: string;
  name: string;
  image: string;
  status: 'running' | 'stopped' | 'paused' | 'restarting' | 'starting' | 'stopping';
  ports: ContainerPortMapping;
  cpu?: number;
  memory?: number;
  networkMode?: string;
  created: Date;
}

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GlassCard, AnimatedButton } from '@/components/ui/enhanced-card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Play,
  Square,
  RotateCcw,
  Trash2,
  Terminal,
  FileText,
  Activity,
  Clock,
  Cpu,
  HardDrive,
  Network,
  MoreVertical,
  CheckCircle,
  XCircle,
  Pause,
  AlertTriangle,
  Loader2
} from 'lucide-react';

interface ContainerCardProps {
  container: ContainerInfo;
  onStart: (id: string) => void;
  onStop: (id: string) => void;
  onRestart: (id: string) => void;
  onRemove: (id: string) => void;
  onViewLogs: (container: ContainerInfo) => void;
  onViewStats: (container: ContainerInfo) => void;
  onConnect: (container: ContainerInfo) => void;
  index?: number;
}

export const ContainerCard: React.FC<ContainerCardProps> = ({
  container,
  onStart,
  onStop,
  onRestart,
  onRemove,
  onViewLogs,
  onViewStats,
  onConnect,
  index = 0
}) => {
  const getStatusColor = (status: ContainerInfo['status']) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'stopped': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'paused': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'restarting': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'starting': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'stopping': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getStatusIcon = (status: ContainerInfo['status']) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />;
      case 'stopped': return <XCircle className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      case 'restarting': return <RotateCcw className="h-4 w-4" />;
      case 'starting': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'stopping': return <Loader2 className="h-4 w-4 animate-spin" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const isTransitioning = container.status === 'starting' || container.status === 'stopping';

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
    >
      <GlassCard variant="glass" animate className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1 min-w-0 flex-1">
              <CardTitle className="text-lg font-semibold truncate" title={container.name}>
                {container.name}
              </CardTitle>
              <p className="text-sm text-muted-foreground truncate" title={container.image}>
                {container.image}
              </p>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 shrink-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {container.status === 'stopped' && (
                  <DropdownMenuItem onClick={() => onStart(container.id)}>
                    <Play className="h-4 w-4 mr-2" />
                    Start
                  </DropdownMenuItem>
                )}
                {container.status === 'running' && (
                  <>
                    <DropdownMenuItem onClick={() => onStop(container.id)}>
                      <Square className="h-4 w-4 mr-2" />
                      Stop
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onRestart(container.id)}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Restart
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onViewLogs(container)}>
                  <FileText className="h-4 w-4 mr-2" />
                  View Logs
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onViewStats(container)}>
                  <Activity className="h-4 w-4 mr-2" />
                  View Stats
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onConnect(container)}>
                  <Terminal className="h-4 w-4 mr-2" />
                  Terminal
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onRemove(container.id)}
                  className="text-destructive focus:text-destructive"
                  disabled={container.status === 'running'}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="flex items-center gap-2 mt-3">
            <Badge className={`${getStatusColor(container.status)} border-0`}>
              {getStatusIcon(container.status)}
              <span className="ml-1 capitalize">{container.status}</span>
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Resource Information */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Cpu className="h-4 w-4 text-muted-foreground" />
              <span>{container.cpu || 'N/A'} CPU</span>
            </div>
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-muted-foreground" />
              <span>{container.memory ? `${container.memory}MB` : 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Network className="h-4 w-4 text-muted-foreground" />
              <span>{container.networkMode || 'bridge'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>{container.created.toLocaleDateString()}</span>
            </div>
          </div>

          {/* Port Mappings */}
          {Object.keys(container.ports).length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Port Mappings</p>
              <div className="flex flex-wrap gap-1">
                {Object.entries(container.ports).map(([containerPort, hostPort]) => (
                  <Badge key={containerPort} variant="outline" className="text-xs">
                    {hostPort}:{containerPort}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex gap-2 pt-2">
            {container.status === 'stopped' && (
              <AnimatedButton
                size="sm"
                onClick={() => onStart(container.id)}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-1" />
                Start
              </AnimatedButton>
            )}
            
            {container.status === 'running' && (
              <>
                <AnimatedButton
                  size="sm"
                  onClick={() => onConnect(container)}
                  className="flex-1"
                >
                  <Terminal className="h-4 w-4 mr-1" />
                  Connect
                </AnimatedButton>
                <AnimatedButton
                  size="sm"
                  variant="outline"
                  onClick={() => onStop(container.id)}
                >
                  <Square className="h-4 w-4" />
                </AnimatedButton>
              </>
            )}

            {isTransitioning && (
              <AnimatedButton size="sm" disabled className="flex-1">
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                {container.status === 'starting' ? 'Starting...' : 'Stopping...'}
              </AnimatedButton>
            )}
          </div>
        </CardContent>
      </GlassCard>
    </motion.div>
  );
};
