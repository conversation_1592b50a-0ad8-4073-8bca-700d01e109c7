'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GlassCard } from '@/components/ui/enhanced-card';
import {
  Container,
  CheckCircle,
  XCircle,
  Pause,
  RotateCcw
} from 'lucide-react';

interface ContainerStatsProps {
  total: number;
  running: number;
  stopped: number;
  paused: number;
  restarting: number;
}

export const ContainerStats: React.FC<ContainerStatsProps> = ({
  total,
  running,
  stopped,
  paused,
  restarting
}) => {
  const stats = [
    {
      label: 'Total',
      value: total,
      icon: Container,
      color: 'text-primary',
      bgColor: 'bg-primary/10'
    },
    {
      label: 'Running',
      value: running,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-500/10'
    },
    {
      label: 'Stopped',
      value: stopped,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-500/10'
    },
    {
      label: 'Paused',
      value: paused,
      icon: Pause,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-500/10'
    },
    {
      label: 'Restarting',
      value: restarting,
      icon: RotateCcw,
      color: 'text-blue-600',
      bgColor: 'bg-blue-500/10'
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <GlassCard variant="glass" animate className="p-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 ${stat.bgColor} rounded-lg`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
              <div>
                <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                <p className="text-sm text-muted-foreground">{stat.label}</p>
              </div>
            </div>
          </GlassCard>
        </motion.div>
      ))}
    </div>
  );
};
