'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnimatedButton } from '@/components/ui/enhanced-card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  RefreshCw,
  Container,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { useDocker } from '@/hooks/useDocker';
import { ContainerInfo, CreateContainerOptions } from '@/types/docker';

// Import modular components
import { ContainerCard } from './ContainerCard';
import { ContainerStats } from './ContainerStats';
import { ContainerLogsDialog } from './ContainerLogsDialog';
import { CreateContainerDialog } from './CreateContainerDialog';
import { ContainerFilters } from './ContainerFilters';

interface ContainerManagerProps {
  onSelectContainer?: (container: ContainerInfo) => void;
}

export const ContainerManager: React.FC<ContainerManagerProps> = ({ onSelectContainer }) => {
  const {
    containers,
    loading,
    error,
    systemInfo,
    connected,
    refreshContainers,
    createContainer,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    getContainerLogs,
    getContainerStats,
    getSystemInfo
  } = useDocker();

  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState<ContainerInfo | null>(null);
  const [showLogs, setShowLogs] = useState(false);
  const [containerToDelete, setContainerToDelete] = useState<string | null>(null);
  const [containerLogs, setContainerLogs] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);

  // Filter and sort containers
  const filteredAndSortedContainers = useMemo(() => {
    let filtered = containers.filter(container => {
      const matchesSearch = container.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           container.image.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
      return matchesSearch && matchesStatus;
    });

    // Sort containers
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'created':
          aValue = a.created.getTime();
          bValue = b.created.getTime();
          break;
        case 'image':
          aValue = a.image.toLowerCase();
          bValue = b.image.toLowerCase();
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [containers, searchTerm, statusFilter, sortBy, sortOrder]);

  // Get status statistics
  const statusStats = useMemo(() => ({
    total: containers.length,
    running: containers.filter(c => c.status === 'running').length,
    stopped: containers.filter(c => c.status === 'stopped').length,
    paused: containers.filter(c => c.status === 'paused').length,
    restarting: containers.filter(c => c.status === 'restarting').length
  }), [containers]);

  // Event handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshContainers();
      await getSystemInfo();
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreateContainer = async (options: CreateContainerOptions) => {
    try {
      await createContainer(options);
    } catch (error) {
      console.error('Failed to create container:', error);
      throw error;
    }
  };

  const handleShowLogs = async (container: ContainerInfo) => {
    setSelectedContainer(container);
    try {
      const logs = await getContainerLogs(container.id);
      setContainerLogs(logs);
      setShowLogs(true);
    } catch (error) {
      console.error('Failed to get container logs:', error);
    }
  };

  const handleRefreshLogs = async (containerId: string, tail?: number) => {
    try {
      const logs = await getContainerLogs(containerId, tail);
      setContainerLogs(logs);
    } catch (error) {
      console.error('Failed to refresh container logs:', error);
      throw error;
    }
  };

  const handleRemoveContainer = async (containerId: string) => {
    try {
      await removeContainer(containerId);
      setContainerToDelete(null);
    } catch (error) {
      console.error('Failed to remove container:', error);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Container Management</h2>
          <p className="text-muted-foreground">
            Manage your Docker containers with advanced monitoring and control
          </p>
        </div>
        <div className="flex items-center gap-3">
          <AnimatedButton
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            loading={refreshing}
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </AnimatedButton>
          <AnimatedButton onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4" />
            Create Container
          </AnimatedButton>
        </div>
      </div>

      {/* Connection Status & System Info */}
      {!connected && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-destructive/10 border border-destructive/20 rounded-lg p-4"
        >
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">Docker Connection Failed</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Unable to connect to Docker daemon. Please ensure Docker is running.
          </p>
        </motion.div>
      )}

      {/* Statistics Cards */}
      <ContainerStats
        total={statusStats.total}
        running={statusStats.running}
        stopped={statusStats.stopped}
        paused={statusStats.paused}
        restarting={statusStats.restarting}
      />

      {/* Search and Filter Controls */}
      <ContainerFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        sortBy={sortBy}
        onSortChange={setSortBy}
        sortOrder={sortOrder}
        onSortOrderChange={setSortOrder}
      />

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-lg">Loading containers...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center"
        >
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-destructive mb-2">Error Loading Containers</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <AnimatedButton onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </AnimatedButton>
        </motion.div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredAndSortedContainers.length === 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12"
        >
          <Container className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">
            {containers.length === 0 ? 'No Containers Found' : 'No Matching Containers'}
          </h3>
          <p className="text-muted-foreground mb-6">
            {containers.length === 0
              ? 'Get started by creating your first container'
              : 'Try adjusting your search or filter criteria'
            }
          </p>
          {containers.length === 0 && (
            <AnimatedButton onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Container
            </AnimatedButton>
          )}
        </motion.div>
      )}

      {/* Container Grid */}
      {!loading && !error && filteredAndSortedContainers.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <AnimatePresence mode="popLayout">
            {filteredAndSortedContainers.map((container, index) => (
              <ContainerCard
                key={container.id}
                container={container}
                index={index}
                onStart={startContainer}
                onStop={stopContainer}
                onRestart={restartContainer}
                onRemove={(id) => setContainerToDelete(id)}
                onViewLogs={handleShowLogs}
                onViewStats={() => {}} // We'll implement this later
                onConnect={(container) => onSelectContainer?.(container)}
              />
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Dialogs */}
      <CreateContainerDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCreateContainer={handleCreateContainer}
      />

      <ContainerLogsDialog
        open={showLogs}
        onOpenChange={setShowLogs}
        container={selectedContainer}
        logs={containerLogs}
        onRefreshLogs={handleRefreshLogs}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!containerToDelete} onOpenChange={() => setContainerToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Container</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this container? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => containerToDelete && handleRemoveContainer(containerToDelete)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
