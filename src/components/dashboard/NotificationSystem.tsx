'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Bell,
  CheckCircle,
  AlertTriangle,
  Info,
  X,
  Settings,
  Trash2,
  MarkAsUnread,
  ExternalLink
} from 'lucide-react';

export interface Notification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  category?: 'system' | 'workspace' | 'security' | 'update';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  persistent?: boolean;
}

interface NotificationSystemProps {
  className?: string;
  maxNotifications?: number;
  autoMarkAsRead?: boolean;
  showCategories?: boolean;
}

export const NotificationSystem: React.FC<NotificationSystemProps> = ({
  className = '',
  maxNotifications = 50,
  autoMarkAsRead = true,
  showCategories = true
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // Mock notification data - in real app, this would come from API/WebSocket
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'success',
        title: 'Workspace Created',
        message: 'Ubuntu Desktop workspace "dev-env-001" is now ready and running',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        read: false,
        actionUrl: '/dashboard/workspaces',
        actionLabel: 'View Workspace',
        category: 'workspace',
        priority: 'medium'
      },
      {
        id: '2',
        type: 'warning',
        title: 'High CPU Usage',
        message: 'System CPU usage has reached 85%. Consider stopping unused workspaces.',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        read: false,
        actionUrl: '/dashboard/monitoring',
        actionLabel: 'View Monitoring',
        category: 'system',
        priority: 'high'
      },
      {
        id: '3',
        type: 'info',
        title: 'System Update Available',
        message: 'New workspace templates and security updates are available',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: true,
        actionUrl: '/dashboard/settings',
        actionLabel: 'Update Now',
        category: 'update',
        priority: 'medium'
      },
      {
        id: '4',
        type: 'error',
        title: 'Workspace Connection Failed',
        message: 'Unable to connect to workspace "test-env-002". Check network connectivity.',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        read: false,
        actionUrl: '/dashboard/workspaces',
        actionLabel: 'Retry Connection',
        category: 'workspace',
        priority: 'critical',
        persistent: true
      },
      {
        id: '5',
        type: 'info',
        title: 'Backup Completed',
        message: 'Weekly workspace backup completed successfully. 3 workspaces backed up.',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        read: true,
        category: 'system',
        priority: 'low'
      }
    ];

    setNotifications(mockNotifications);
  }, []);

  // Simulate real-time notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly add new notifications for demo
      if (Math.random() > 0.95) {
        const newNotification: Notification = {
          id: Date.now().toString(),
          type: ['info', 'success', 'warning'][Math.floor(Math.random() * 3)] as any,
          title: 'New Activity',
          message: 'A new event has occurred in your workspace',
          timestamp: new Date(),
          read: false,
          category: 'workspace',
          priority: 'medium'
        };

        setNotifications(prev => [newNotification, ...prev.slice(0, maxNotifications - 1)]);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [maxNotifications]);

  const unreadCount = notifications.filter(n => !n.read).length;
  const criticalCount = notifications.filter(n => n.priority === 'critical' && !n.read).length;

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'info': return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500 bg-red-50';
      case 'high': return 'border-l-orange-500 bg-orange-50';
      case 'medium': return 'border-l-blue-500 bg-blue-50';
      case 'low': return 'border-l-gray-500 bg-gray-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const groupedNotifications = showCategories 
    ? notifications.reduce((groups, notification) => {
        const category = notification.category || 'other';
        if (!groups[category]) groups[category] = [];
        groups[category].push(notification);
        return groups;
      }, {} as Record<string, Notification[]>)
    : { all: notifications };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className={`relative ${className}`}>
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge 
              className={`absolute -top-1 -right-1 h-5 w-5 p-0 text-xs ${
                criticalCount > 0 ? 'bg-red-500' : 'bg-blue-500'
              }`}
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-96">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {unreadCount} new
              </Badge>
            )}
            {criticalCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {criticalCount} critical
              </Badge>
            )}
          </div>
        </DropdownMenuLabel>
        
        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <div className="flex items-center justify-between px-2 py-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                disabled={unreadCount === 0}
                className="text-xs"
              >
                Mark all read
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllNotifications}
                className="text-xs text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Clear all
              </Button>
            </div>
          </>
        )}
        
        <DropdownMenuSeparator />
        
        <div className="max-h-96 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No notifications</p>
              <p className="text-xs text-gray-400">You're all caught up!</p>
            </div>
          ) : (
            Object.entries(groupedNotifications).map(([category, categoryNotifications]) => (
              <div key={category}>
                {showCategories && Object.keys(groupedNotifications).length > 1 && (
                  <div className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </div>
                )}
                {categoryNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`relative border-l-4 ${getPriorityColor(notification.priority)} ${
                      !notification.read ? 'bg-opacity-100' : 'bg-opacity-50'
                    }`}
                  >
                    <div className="p-3 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex items-start gap-3 flex-1 min-w-0">
                          {getNotificationIcon(notification.type)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <p className={`font-medium text-sm ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                                {notification.title}
                              </p>
                              {notification.persistent && (
                                <Badge variant="outline" className="text-xs">
                                  Persistent
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center justify-between">
                              <p className="text-xs text-gray-400">
                                {formatTimestamp(notification.timestamp)}
                              </p>
                              {notification.actionUrl && (
                                <Link
                                  href={notification.actionUrl}
                                  className="text-xs text-blue-600 hover:text-blue-700 flex items-center gap-1"
                                  onClick={() => {
                                    if (autoMarkAsRead) markAsRead(notification.id);
                                    setIsOpen(false);
                                  }}
                                >
                                  {notification.actionLabel || 'View'}
                                  <ExternalLink className="h-3 w-3" />
                                </Link>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(notification.id)}
                              className="h-6 w-6 p-0"
                              title="Mark as read"
                            >
                              <CheckCircle className="h-3 w-3" />
                            </Button>
                          )}
                          {!notification.persistent && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeNotification(notification.id)}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                              title="Remove notification"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ))
          )}
        </div>
        
        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/dashboard/notifications" className="text-center w-full">
                <Settings className="mr-2 h-4 w-4" />
                Notification Settings
              </Link>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
