'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { GlobalSearch } from './GlobalSearch';
import { NotificationSystem } from './NotificationSystem';
import { SystemStatus } from './SystemStatus';
import { UserProfileDropdown } from './UserProfileDropdown';
import { BreadcrumbNavigation } from './BreadcrumbNavigation';
import { QuickActionsToolbar } from './QuickActionsToolbar';

interface DashboardHeaderProps {
  title?: string;
  subtitle?: string;
  showBreadcrumbs?: boolean;
  showQuickActions?: boolean;
  showSystemStatus?: boolean;
  className?: string;
  variant?: 'full' | 'compact' | 'minimal';
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  subtitle,
  showBreadcrumbs = true,
  showQuickActions = true,
  showSystemStatus = true,
  className = '',
  variant = 'full'
}) => {
  const pathname = usePathname();

  // Determine header layout based on variant
  const isCompact = variant === 'compact' || variant === 'minimal';
  const showFullFeatures = variant === 'full';

  return (
    <header className={`bg-white border-b border-gray-200 sticky top-0 z-40 ${className}`}>
      <div className={`flex items-center justify-between px-4 ${isCompact ? 'py-2' : 'py-3'}`}>
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="h-6" />

          {/* Breadcrumbs */}
          {showBreadcrumbs && !isCompact && (
            <BreadcrumbNavigation
              className="hidden md:flex"
              showIcons={showFullFeatures}
            />
          )}

          {/* Title & Subtitle */}
          {(title || subtitle) && (
            <div className="hidden lg:block">
              {title && <h1 className="text-lg font-semibold text-gray-900">{title}</h1>}
              {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
            </div>
          )}
        </div>

        {/* Center Section - Search */}
        {showFullFeatures && (
          <div className="flex-1 max-w-md mx-4">
            <GlobalSearch />
          </div>
        )}

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* System Status */}
          {showSystemStatus && (
            <SystemStatus
              compact={isCompact}
              className="hidden xl:flex"
            />
          )}

          {/* Quick Actions */}
          {showQuickActions && (
            <QuickActionsToolbar
              variant={isCompact ? 'compact' : 'full'}
              showLabels={!isCompact}
              maxPrimaryActions={isCompact ? 2 : 4}
            />
          )}

          {/* Notifications */}
          <NotificationSystem />

          {/* User Profile */}
          <UserProfileDropdown />
        </div>
      </div>
    </header>
  );
};
