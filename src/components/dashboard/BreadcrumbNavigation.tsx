'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  ChevronRight,
  ChevronDown,
  Home,
  Monitor,
  BarChart3,
  Settings,
  Container,
  Image,
  Users,
  Shield,
  Bell,
  HelpCircle,
  FileText,
  Activity,
  Database,
  Network,
  Zap
} from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  isLast?: boolean;
  children?: BreadcrumbItem[];
}

interface BreadcrumbNavigationProps {
  className?: string;
  showIcons?: boolean;
  maxItems?: number;
  customItems?: BreadcrumbItem[];
}

export const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  className = '',
  showIcons = true,
  maxItems = 4,
  customItems
}) => {
  const pathname = usePathname();

  // Route configuration with icons and sub-routes
  const routeConfig: Record<string, { label: string; icon?: React.ComponentType<{ className?: string }>; children?: Record<string, { label: string; icon?: React.ComponentType<{ className?: string }> }> }> = {
    dashboard: {
      label: 'Dashboard',
      icon: Home,
      children: {
        workspaces: { label: 'Workspaces', icon: Monitor },
        monitoring: { label: 'Monitoring', icon: BarChart3 },
        settings: { 
          label: 'Settings', 
          icon: Settings,
        },
        containers: { label: 'Containers', icon: Container },
        images: { label: 'Images', icon: Image },
        notifications: { label: 'Notifications', icon: Bell },
        users: { label: 'Users', icon: Users },
        security: { label: 'Security', icon: Shield },
        help: { label: 'Help', icon: HelpCircle },
        docs: { label: 'Documentation', icon: FileText },
        activity: { label: 'Activity', icon: Activity },
        database: { label: 'Database', icon: Database },
        network: { label: 'Network', icon: Network },
        api: { label: 'API', icon: Zap }
      }
    },
    'workspace-ai': {
      label: 'Workspace AI',
      icon: Zap
    },
    auth: {
      label: 'Authentication',
      icon: Shield,
      children: {
        login: { label: 'Login', icon: Shield },
        register: { label: 'Register', icon: Users },
        'forgot-password': { label: 'Forgot Password', icon: Shield },
        'reset-password': { label: 'Reset Password', icon: Shield }
      }
    }
  };

  // Generate breadcrumbs from pathname or use custom items
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (customItems) return customItems;

    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];
    
    let currentPath = '';
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      const isLast = index === segments.length - 1;
      const parentSegment = segments[index - 1];
      
      // Get route configuration
      let config = routeConfig[segment];
      if (!config && parentSegment && routeConfig[parentSegment]?.children) {
        config = routeConfig[parentSegment].children[segment];
      }
      
      const label = config?.label || segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ');
      const icon = config?.icon;
      
      // Add children for dropdown if available
      const children: BreadcrumbItem[] = [];
      if (config?.children) {
        Object.entries(config.children).forEach(([key, childConfig]) => {
          children.push({
            label: childConfig.label,
            href: `${currentPath}/${key}`,
            icon: childConfig.icon
          });
        });
      }
      
      breadcrumbs.push({
        label,
        href: currentPath,
        icon,
        isLast,
        children: children.length > 0 ? children : undefined
      });
    });
    
    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Handle overflow by showing ellipsis
  const displayBreadcrumbs = breadcrumbs.length > maxItems 
    ? [
        breadcrumbs[0], // Always show first
        { label: '...', href: '#', isEllipsis: true } as BreadcrumbItem,
        ...breadcrumbs.slice(-maxItems + 2) // Show last few items
      ]
    : breadcrumbs;

  const renderBreadcrumbItem = (item: BreadcrumbItem, index: number) => {
    const isEllipsis = 'isEllipsis' in item && item.isEllipsis;
    
    if (isEllipsis) {
      return (
        <div key={index} className="flex items-center">
          <span className="text-gray-400 px-2">...</span>
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </div>
      );
    }

    const ItemContent = () => (
      <div className="flex items-center gap-2">
        {showIcons && item.icon && (
          <item.icon className="h-4 w-4" />
        )}
        <span className="truncate max-w-[150px]">{item.label}</span>
      </div>
    );

    // If item has children, render as dropdown
    if (item.children && item.children.length > 0) {
      return (
        <div key={index} className="flex items-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-auto p-1 font-normal ${
                  item.isLast 
                    ? 'text-gray-900 font-medium' 
                    : 'text-gray-600 hover:text-blue-600'
                }`}
              >
                <ItemContent />
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {item.children.map((child, childIndex) => (
                <DropdownMenuItem key={childIndex} asChild>
                  <Link href={child.href} className="flex items-center gap-2">
                    {child.icon && <child.icon className="h-4 w-4" />}
                    {child.label}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          {!item.isLast && <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />}
        </div>
      );
    }

    // Regular breadcrumb item
    return (
      <div key={index} className="flex items-center">
        {item.isLast ? (
          <span className="text-gray-900 font-medium flex items-center gap-2">
            <ItemContent />
          </span>
        ) : (
          <Link
            href={item.href}
            className="text-gray-600 hover:text-blue-600 transition-colors flex items-center gap-2"
          >
            <ItemContent />
          </Link>
        )}
        {!item.isLast && <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />}
      </div>
    );
  };

  if (breadcrumbs.length === 0) return null;

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {displayBreadcrumbs.map((item, index) => (
          <li key={index} className="flex items-center">
            {renderBreadcrumbItem(item, index)}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Utility component for custom breadcrumb usage
export const BreadcrumbItem: React.FC<{
  children: React.ReactNode;
  href?: string;
  isLast?: boolean;
  className?: string;
}> = ({ children, href, isLast, className = '' }) => {
  const content = (
    <span className={`flex items-center gap-2 ${className}`}>
      {children}
    </span>
  );

  return (
    <div className="flex items-center">
      {href && !isLast ? (
        <Link href={href} className="text-gray-600 hover:text-blue-600 transition-colors">
          {content}
        </Link>
      ) : (
        <span className={isLast ? 'text-gray-900 font-medium' : 'text-gray-600'}>
          {content}
        </span>
      )}
      {!isLast && <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />}
    </div>
  );
};

// Hook for programmatic breadcrumb management
export const useBreadcrumbs = () => {
  const pathname = usePathname();
  
  const setBreadcrumbs = (items: BreadcrumbItem[]) => {
    // This could be implemented with a context provider
    // For now, it's a placeholder for future enhancement
    console.log('Setting breadcrumbs:', items);
  };

  const addBreadcrumb = (item: BreadcrumbItem) => {
    // Add a single breadcrumb item
    console.log('Adding breadcrumb:', item);
  };

  const getCurrentPath = () => pathname;

  return {
    setBreadcrumbs,
    addBreadcrumb,
    getCurrentPath
  };
};
