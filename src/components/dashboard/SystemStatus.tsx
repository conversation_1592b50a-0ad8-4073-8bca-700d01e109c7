'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  Activity,
  Cpu,
  HardDrive,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  Monitor,
  Database,
  Server,
  Network,
  Zap,
  RefreshCw,
  ExternalLink,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature?: number;
    trend: 'up' | 'down' | 'stable';
  };
  memory: {
    usage: number;
    total: number;
    available: number;
    trend: 'up' | 'down' | 'stable';
  };
  disk: {
    usage: number;
    total: number;
    available: number;
    trend: 'up' | 'down' | 'stable';
  };
  network: {
    connected: boolean;
    latency?: number;
    throughput: {
      upload: number;
      download: number;
    };
  };
  services: {
    docker: 'healthy' | 'warning' | 'error';
    guacamole: 'healthy' | 'warning' | 'error';
    database: 'healthy' | 'warning' | 'error';
    nginx: 'healthy' | 'warning' | 'error';
  };
  workspaces: {
    running: number;
    total: number;
    healthy: number;
    warning: number;
    error: number;
  };
}

interface SystemStatusProps {
  className?: string;
  compact?: boolean;
  showDetails?: boolean;
  refreshInterval?: number;
}

export const SystemStatus: React.FC<SystemStatusProps> = ({
  className = '',
  compact = false,
  showDetails = true,
  refreshInterval = 30000
}) => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    cpu: {
      usage: 45,
      cores: 8,
      temperature: 65,
      trend: 'stable'
    },
    memory: {
      usage: 62,
      total: 16384,
      available: 6226,
      trend: 'up'
    },
    disk: {
      usage: 78,
      total: 512000,
      available: 112640,
      trend: 'stable'
    },
    network: {
      connected: true,
      latency: 12,
      throughput: {
        upload: 45.2,
        download: 123.8
      }
    },
    services: {
      docker: 'healthy',
      guacamole: 'healthy',
      database: 'healthy',
      nginx: 'warning'
    },
    workspaces: {
      running: 3,
      total: 8,
      healthy: 2,
      warning: 1,
      error: 0
    }
  });

  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Simulate real-time metrics updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        cpu: {
          ...prev.cpu,
          usage: Math.max(10, Math.min(95, prev.cpu.usage + (Math.random() - 0.5) * 10)),
          trend: Math.random() > 0.5 ? 'up' : 'down'
        },
        memory: {
          ...prev.memory,
          usage: Math.max(20, Math.min(90, prev.memory.usage + (Math.random() - 0.5) * 8)),
          trend: Math.random() > 0.5 ? 'up' : 'down'
        },
        network: {
          ...prev.network,
          latency: Math.max(5, Math.min(100, (prev.network.latency || 12) + (Math.random() - 0.5) * 10)),
          throughput: {
            upload: Math.max(0, prev.network.throughput.upload + (Math.random() - 0.5) * 20),
            download: Math.max(0, prev.network.throughput.download + (Math.random() - 0.5) * 30)
          }
        }
      }));
      setLastUpdated(new Date());
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getOverallHealth = (): 'healthy' | 'warning' | 'critical' => {
    const { cpu, memory, disk, services, workspaces } = metrics;
    
    // Check for critical conditions
    if (cpu.usage > 90 || memory.usage > 90 || disk.usage > 95) {
      return 'critical';
    }
    
    // Check for service errors
    const serviceStatuses = Object.values(services);
    if (serviceStatuses.includes('error') || workspaces.error > 0) {
      return 'critical';
    }
    
    // Check for warnings
    if (cpu.usage > 75 || memory.usage > 80 || disk.usage > 85 || 
        serviceStatuses.includes('warning') || workspaces.warning > 0) {
      return 'warning';
    }
    
    return 'healthy';
  };

  const overallHealth = getOverallHealth();

  const getHealthIcon = (status: 'healthy' | 'warning' | 'critical' | 'error') => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical':
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-3 w-3 text-red-500" />;
      case 'down': return <TrendingDown className="h-3 w-3 text-green-500" />;
      case 'stable': return <div className="w-3 h-3" />; // Empty space
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const refreshMetrics = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsRefreshing(false);
  };

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex items-center gap-1 text-sm">
          <Cpu className="h-4 w-4 text-gray-500" />
          <span className="text-gray-600">{metrics.cpu.usage}%</span>
        </div>
        <div className="flex items-center gap-1 text-sm">
          <HardDrive className="h-4 w-4 text-gray-500" />
          <span className="text-gray-600">{metrics.memory.usage}%</span>
        </div>
        <div className="flex items-center gap-1 text-sm">
          {metrics.network.connected ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-500" />
          )}
          <span className="text-gray-600">
            {metrics.workspaces.running}/{metrics.workspaces.total}
          </span>
        </div>
        <div className="flex items-center gap-1">
          {getHealthIcon(overallHealth)}
        </div>
      </div>
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className={`flex items-center gap-2 ${className}`}>
          {getHealthIcon(overallHealth)}
          <span className="hidden lg:inline text-sm">System Status</span>
          <Badge variant={overallHealth === 'healthy' ? 'default' : 'destructive'} className="text-xs">
            {overallHealth.toUpperCase()}
          </Badge>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96" align="end">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              <h3 className="font-semibold">System Status</h3>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshMetrics}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
              <Link href="/dashboard/monitoring">
                <Button variant="ghost" size="sm">
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Resource Usage */}
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Cpu className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">CPU Usage</span>
                  {getTrendIcon(metrics.cpu.trend)}
                </div>
                <span className="text-sm text-gray-600">
                  {metrics.cpu.usage}% ({metrics.cpu.cores} cores)
                </span>
              </div>
              <Progress value={metrics.cpu.usage} className="h-2" />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Memory</span>
                  {getTrendIcon(metrics.memory.trend)}
                </div>
                <span className="text-sm text-gray-600">
                  {metrics.memory.usage}% ({formatBytes(metrics.memory.available * 1024 * 1024)} free)
                </span>
              </div>
              <Progress value={metrics.memory.usage} className="h-2" />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">Disk Usage</span>
                  {getTrendIcon(metrics.disk.trend)}
                </div>
                <span className="text-sm text-gray-600">
                  {metrics.disk.usage}% ({formatBytes(metrics.disk.available * 1024 * 1024)} free)
                </span>
              </div>
              <Progress value={metrics.disk.usage} className="h-2" />
            </div>
          </div>

          {/* Network Status */}
          <div className="border-t pt-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Network className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Network</span>
              </div>
              <div className="flex items-center gap-2">
                {metrics.network.connected ? (
                  <Badge variant="default" className="text-xs">Connected</Badge>
                ) : (
                  <Badge variant="destructive" className="text-xs">Disconnected</Badge>
                )}
              </div>
            </div>
            {metrics.network.connected && (
              <div className="grid grid-cols-3 gap-2 text-xs text-gray-600">
                <div>Latency: {metrics.network.latency}ms</div>
                <div>↑ {metrics.network.throughput.upload.toFixed(1)} MB/s</div>
                <div>↓ {metrics.network.throughput.download.toFixed(1)} MB/s</div>
              </div>
            )}
          </div>

          {/* Services Status */}
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-2">Services</h4>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(metrics.services).map(([service, status]) => (
                <div key={service} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{service}</span>
                  {getHealthIcon(status)}
                </div>
              ))}
            </div>
          </div>

          {/* Workspaces Status */}
          <div className="border-t pt-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Monitor className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Workspaces</span>
              </div>
              <span className="text-sm text-gray-600">
                {metrics.workspaces.running}/{metrics.workspaces.total} running
              </span>
            </div>
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-green-500" />
                <span>{metrics.workspaces.healthy} healthy</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-yellow-500" />
                <span>{metrics.workspaces.warning} warning</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-red-500" />
                <span>{metrics.workspaces.error} error</span>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t pt-3 text-xs text-gray-500">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
