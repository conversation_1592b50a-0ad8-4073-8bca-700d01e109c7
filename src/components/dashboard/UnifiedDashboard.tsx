'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { GlassCard, AnimatedButton } from '@/components/ui/enhanced-card';
import {
  Container,
  Image as ImageIcon,
  Monitor,
  Activity,
  Cpu,
  HardDrive,
  Network,
  Zap,
  TrendingUp,
  Server,
  Database,
  Wifi,
  Shield,
  Clock,
  Users,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart
} from 'lucide-react';
import { ContainerManager } from '../containers/ContainerManager';
import { ImageManager } from '../images/ImageManager';
import { VMCard } from '../vms/VMCard';
import { MicroVM } from '@/types/vm';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    usage: number;
  };
  network: {
    rx: number;
    tx: number;
  };
}

interface DashboardStats {
  containers: {
    total: number;
    running: number;
    stopped: number;
  };
  images: {
    total: number;
    size: number;
  };
  vms: {
    total: number;
    running: number;
    stopped: number;
  };
}

export const UnifiedDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with real API calls
  const [systemMetrics] = useState<SystemMetrics>({
    cpu: { usage: 45, cores: 8 },
    memory: { used: 12.5, total: 32, usage: 39 },
    disk: { used: 250, total: 1000, usage: 25 },
    network: { rx: 1.2, tx: 0.8 }
  });

  const [dashboardStats] = useState<DashboardStats>({
    containers: { total: 12, running: 8, stopped: 4 },
    images: { total: 25, size: 15.6 },
    vms: { total: 5, running: 3, stopped: 2 }
  });

  // Mock VMs data
  const [vms] = useState<MicroVM[]>([
    {
      id: '1',
      name: 'Ubuntu Desktop',
      status: 'running',
      cpu: 2,
      memory: 2048,
      diskSize: 20,
      vncPort: 5901,
      createdAt: new Date('2024-01-15'),
      lastAccessed: new Date(),
      ipAddress: '*************',
      osType: 'ubuntu'
    },
    {
      id: '2',
      name: 'Development VM',
      status: 'stopped',
      cpu: 4,
      memory: 4096,
      diskSize: 40,
      vncPort: 5902,
      createdAt: new Date('2024-01-10'),
      ipAddress: '*************',
      osType: 'debian'
    }
  ]);

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const handleVMAction = (action: string, vmId: string) => {
    console.log(`VM ${action}:`, vmId);
  };

  const handleVMConnect = (vm: MicroVM) => {
    console.log('Connect to VM:', vm.name);
  };

  const handleVMSnapshot = (vm: MicroVM) => {
    console.log('Create snapshot for VM:', vm.name);
  };

  const handleVMConfigure = (vm: MicroVM) => {
    console.log('Configure VM:', vm.name);
  };

  const handleVMMetrics = (vm: MicroVM) => {
    console.log('View metrics for VM:', vm.name);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Infrastructure Dashboard</h1>
          <p className="text-muted-foreground">
            Unified management for containers, images, and virtual machines
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600 border-green-600">
            <Activity className="h-3 w-3 mr-1" />
            System Healthy
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="containers">Containers</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
          <TabsTrigger value="vms">Virtual Machines</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* System Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-500/10 rounded-lg">
                  <Cpu className="h-6 w-6 text-blue-600" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">CPU Usage</p>
                  <p className="text-2xl font-bold">{systemMetrics.cpu.usage}%</p>
                  <p className="text-xs text-muted-foreground">{systemMetrics.cpu.cores} cores</p>
                </div>
              </div>
              <Progress value={systemMetrics.cpu.usage} className="mt-4" />
            </GlassCard>

            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500/10 rounded-lg">
                  <HardDrive className="h-6 w-6 text-green-600" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Memory</p>
                  <p className="text-2xl font-bold">{systemMetrics.memory.usage}%</p>
                  <p className="text-xs text-muted-foreground">
                    {systemMetrics.memory.used}GB / {systemMetrics.memory.total}GB
                  </p>
                </div>
              </div>
              <Progress value={systemMetrics.memory.usage} className="mt-4" />
            </GlassCard>

            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-500/10 rounded-lg">
                  <Database className="h-6 w-6 text-purple-600" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Disk Usage</p>
                  <p className="text-2xl font-bold">{systemMetrics.disk.usage}%</p>
                  <p className="text-xs text-muted-foreground">
                    {systemMetrics.disk.used}GB / {systemMetrics.disk.total}GB
                  </p>
                </div>
              </div>
              <Progress value={systemMetrics.disk.usage} className="mt-4" />
            </GlassCard>

            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-orange-500/10 rounded-lg">
                  <Network className="h-6 w-6 text-orange-600" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Network</p>
                  <p className="text-lg font-bold">
                    ↓{systemMetrics.network.rx} ↑{systemMetrics.network.tx} MB/s
                  </p>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Resource Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <Container className="h-8 w-8 text-blue-600" />
                <div>
                  <h3 className="text-lg font-semibold">Containers</h3>
                  <p className="text-sm text-muted-foreground">Docker containers</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Total</span>
                  <span className="font-medium">{dashboardStats.containers.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-green-600">Running</span>
                  <span className="font-medium text-green-600">{dashboardStats.containers.running}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-red-600">Stopped</span>
                  <span className="font-medium text-red-600">{dashboardStats.containers.stopped}</span>
                </div>
              </div>
            </GlassCard>

            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <ImageIcon className="h-8 w-8 text-purple-600" />
                <div>
                  <h3 className="text-lg font-semibold">Images</h3>
                  <p className="text-sm text-muted-foreground">Docker images</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Total</span>
                  <span className="font-medium">{dashboardStats.images.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Size</span>
                  <span className="font-medium">{dashboardStats.images.size} GB</span>
                </div>
              </div>
            </GlassCard>

            <GlassCard variant="glass" animate className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <Monitor className="h-8 w-8 text-green-600" />
                <div>
                  <h3 className="text-lg font-semibold">Virtual Machines</h3>
                  <p className="text-sm text-muted-foreground">Firecracker VMs</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Total</span>
                  <span className="font-medium">{dashboardStats.vms.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-green-600">Running</span>
                  <span className="font-medium text-green-600">{dashboardStats.vms.running}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-red-600">Stopped</span>
                  <span className="font-medium text-red-600">{dashboardStats.vms.stopped}</span>
                </div>
              </div>
            </GlassCard>
          </div>
        </TabsContent>

        <TabsContent value="containers">
          <ContainerManager />
        </TabsContent>

        <TabsContent value="images">
          <ImageManager />
        </TabsContent>

        <TabsContent value="vms" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Virtual Machines</h2>
              <p className="text-muted-foreground">Manage your Firecracker microVMs</p>
            </div>
            <AnimatedButton>
              <Monitor className="h-4 w-4 mr-2" />
              Create VM
            </AnimatedButton>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {vms.map((vm, index) => (
              <VMCard
                key={vm.id}
                vm={vm}
                index={index}
                onStart={(id) => handleVMAction('start', id)}
                onStop={(id) => handleVMAction('stop', id)}
                onRestart={(id) => handleVMAction('restart', id)}
                onRemove={(id) => handleVMAction('remove', id)}
                onConnect={handleVMConnect}
                onSnapshot={handleVMSnapshot}
                onConfigure={handleVMConfigure}
                onViewMetrics={handleVMMetrics}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
