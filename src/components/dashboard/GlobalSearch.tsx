'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { 
  Search,
  Monitor,
  Container,
  Image,
  Settings,
  User,
  Activity,
  FileText,
  Database,
  Network,
  Shield,
  Plus,
  Clock,
  Star,
  Zap,
  Command as CommandIcon
} from 'lucide-react';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: 'workspaces' | 'containers' | 'images' | 'settings' | 'actions' | 'recent';
  url: string;
  icon: React.ComponentType<{ className?: string }>;
  metadata?: {
    status?: 'running' | 'stopped' | 'starting';
    type?: string;
    lastAccessed?: Date;
    tags?: string[];
  };
  priority?: number;
}

interface GlobalSearchProps {
  placeholder?: string;
  className?: string;
  onSelect?: (result: SearchResult) => void;
}

export const GlobalSearch: React.FC<GlobalSearchProps> = ({
  placeholder = "Search workspaces, containers, or press ⌘K",
  className = '',
  onSelect
}) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Mock data - in real app, this would come from APIs
  const allResults: SearchResult[] = useMemo(() => [
    // Workspaces
    {
      id: 'ws-1',
      title: 'Ubuntu Desktop',
      description: 'Primary development environment with GNOME desktop',
      category: 'workspaces',
      url: '/dashboard/workspaces',
      icon: Monitor,
      metadata: {
        status: 'running',
        type: 'ubuntu-desktop',
        lastAccessed: new Date(Date.now() - 2 * 60 * 1000),
        tags: ['development', 'ubuntu', 'desktop']
      },
      priority: 10
    },
    {
      id: 'ws-2',
      title: 'Development Environment',
      description: 'High-performance VM for heavy development tasks',
      category: 'workspaces',
      url: '/dashboard/workspaces',
      icon: Monitor,
      metadata: {
        status: 'stopped',
        type: 'development-env',
        lastAccessed: new Date(Date.now() - 60 * 60 * 1000),
        tags: ['development', 'coding', 'ide']
      },
      priority: 8
    },
    {
      id: 'ws-3',
      title: 'Test Environment',
      description: 'Lightweight testing environment for QA',
      category: 'workspaces',
      url: '/dashboard/workspaces',
      icon: Monitor,
      metadata: {
        status: 'running',
        type: 'minimal-desktop',
        lastAccessed: new Date(Date.now() - 30 * 60 * 1000),
        tags: ['testing', 'qa', 'minimal']
      },
      priority: 6
    },
    // Containers
    {
      id: 'container-1',
      title: 'nginx-proxy',
      description: 'Reverse proxy container for load balancing',
      category: 'containers',
      url: '/dashboard/containers',
      icon: Container,
      metadata: {
        status: 'running',
        type: 'nginx',
        tags: ['proxy', 'nginx', 'web']
      },
      priority: 5
    },
    {
      id: 'container-2',
      title: 'postgres-db',
      description: 'PostgreSQL database container',
      category: 'containers',
      url: '/dashboard/containers',
      icon: Database,
      metadata: {
        status: 'running',
        type: 'postgresql',
        tags: ['database', 'postgres', 'sql']
      },
      priority: 7
    },
    // Images
    {
      id: 'image-1',
      title: 'omnispace/ubuntu-desktop',
      description: 'Ubuntu desktop workspace image with VNC',
      category: 'images',
      url: '/dashboard/images',
      icon: Image,
      metadata: {
        type: 'workspace',
        tags: ['ubuntu', 'desktop', 'vnc']
      },
      priority: 4
    },
    // Settings
    {
      id: 'settings-1',
      title: 'User Preferences',
      description: 'Configure your account settings and preferences',
      category: 'settings',
      url: '/dashboard/settings',
      icon: User,
      priority: 3
    },
    {
      id: 'settings-2',
      title: 'Resource Limits',
      description: 'Manage CPU, memory, and storage limits',
      category: 'settings',
      url: '/dashboard/settings',
      icon: Activity,
      priority: 3
    },
    {
      id: 'settings-3',
      title: 'Security Settings',
      description: 'Configure authentication and security options',
      category: 'settings',
      url: '/dashboard/settings',
      icon: Shield,
      priority: 3
    },
    // Quick Actions
    {
      id: 'action-1',
      title: 'Create New Workspace',
      description: 'Launch a new workspace environment',
      category: 'actions',
      url: '/dashboard/workspaces',
      icon: Plus,
      priority: 9
    },
    {
      id: 'action-2',
      title: 'View System Monitoring',
      description: 'Check system performance and health',
      category: 'actions',
      url: '/dashboard/monitoring',
      icon: Activity,
      priority: 5
    },
    {
      id: 'action-3',
      title: 'Manage Docker Images',
      description: 'View and manage container images',
      category: 'actions',
      url: '/dashboard/images',
      icon: Image,
      priority: 4
    }
  ], []);

  // Filter results based on query
  const filteredResults = useMemo(() => {
    if (!query.trim()) {
      // Show recent searches and high-priority items when no query
      return allResults
        .filter(result => result.priority && result.priority >= 7)
        .sort((a, b) => (b.priority || 0) - (a.priority || 0));
    }

    const searchTerms = query.toLowerCase().split(' ').filter(Boolean);
    
    return allResults
      .filter(result => {
        const searchableText = [
          result.title,
          result.description,
          ...(result.metadata?.tags || [])
        ].join(' ').toLowerCase();

        return searchTerms.every(term => searchableText.includes(term));
      })
      .sort((a, b) => {
        // Prioritize exact title matches
        const aExactMatch = a.title.toLowerCase().includes(query.toLowerCase());
        const bExactMatch = b.title.toLowerCase().includes(query.toLowerCase());
        
        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;
        
        // Then sort by priority
        return (b.priority || 0) - (a.priority || 0);
      });
  }, [query, allResults]);

  // Group results by category
  const groupedResults = useMemo(() => {
    const groups: Record<string, SearchResult[]> = {};
    
    filteredResults.forEach(result => {
      if (!groups[result.category]) {
        groups[result.category] = [];
      }
      groups[result.category].push(result);
    });
    
    return groups;
  }, [filteredResults]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen(true);
      }
      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleSelect = useCallback((result: SearchResult) => {
    // Add to recent searches
    setRecentSearches(prev => {
      const updated = [result.title, ...prev.filter(s => s !== result.title)];
      return updated.slice(0, 5); // Keep only 5 recent searches
    });

    // Close dialog
    setOpen(false);
    setQuery('');

    // Navigate or call custom handler
    if (onSelect) {
      onSelect(result);
    } else {
      router.push(result.url);
    }
  }, [router, onSelect]);

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'stopped': return 'bg-red-500';
      case 'starting': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'workspaces': return 'Workspaces';
      case 'containers': return 'Containers';
      case 'images': return 'Images';
      case 'settings': return 'Settings';
      case 'actions': return 'Quick Actions';
      case 'recent': return 'Recent';
      default: return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  return (
    <>
      {/* Search Input Trigger */}
      <div className={`relative ${className}`}>
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder={placeholder}
          className="pl-10 pr-20"
          onClick={() => setOpen(true)}
          readOnly
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
            <CommandIcon className="h-3 w-3" />
            K
          </kbd>
        </div>
      </div>

      {/* Search Dialog */}
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput 
          placeholder="Search workspaces, containers, settings..." 
          value={query}
          onValueChange={setQuery}
        />
        <CommandList>
          <CommandEmpty>
            <div className="py-6 text-center text-sm">
              <Search className="mx-auto h-6 w-6 text-gray-400 mb-2" />
              <p>No results found for "{query}"</p>
              <p className="text-xs text-gray-500 mt-1">
                Try searching for workspaces, containers, or settings
              </p>
            </div>
          </CommandEmpty>

          {Object.entries(groupedResults).map(([category, results]) => (
            <React.Fragment key={category}>
              <CommandGroup heading={getCategoryLabel(category)}>
                {results.map((result) => (
                  <CommandItem
                    key={result.id}
                    value={`${result.title} ${result.description} ${result.metadata?.tags?.join(' ') || ''}`}
                    onSelect={() => handleSelect(result)}
                    className="flex items-center gap-3 p-3"
                  >
                    <result.icon className="h-4 w-4 text-gray-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-sm truncate">{result.title}</p>
                        {result.metadata?.status && (
                          <div className="flex items-center gap-1">
                            <div className={`w-2 h-2 rounded-full ${getStatusColor(result.metadata.status)}`} />
                            <span className="text-xs text-gray-500 capitalize">
                              {result.metadata.status}
                            </span>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 truncate">{result.description}</p>
                      {result.metadata?.tags && (
                        <div className="flex items-center gap-1 mt-1">
                          {result.metadata.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    {result.metadata?.lastAccessed && (
                      <div className="text-xs text-gray-400 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {new Date(result.metadata.lastAccessed).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
            </React.Fragment>
          ))}

          {/* Recent Searches */}
          {recentSearches.length > 0 && !query && (
            <>
              <CommandGroup heading="Recent Searches">
                {recentSearches.map((search, index) => (
                  <CommandItem
                    key={index}
                    value={search}
                    onSelect={() => setQuery(search)}
                    className="flex items-center gap-3 p-3"
                  >
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{search}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
            </>
          )}

          {/* Help Text */}
          <div className="px-3 py-2 text-xs text-gray-500 border-t">
            <div className="flex items-center justify-between">
              <span>Press Enter to select</span>
              <span>ESC to close</span>
            </div>
          </div>
        </CommandList>
      </CommandDialog>
    </>
  );
};
