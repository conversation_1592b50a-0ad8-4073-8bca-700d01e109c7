'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Switch } from '@/components/ui/switch';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  User,
  Settings,
  LogOut,
  HelpCircle,
  Monitor,
  Home,
  CreditCard,
  Shield,
  Bell,
  Palette,
  Moon,
  Sun,
  Laptop,
  Zap,
  Activity,
  FileText,
  Download,
  Upload,
  Key,
  Users,
  Building,
  Crown,
  Star,
  ChevronRight
} from 'lucide-react';

interface UserData {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  initials: string;
  role: 'admin' | 'user' | 'viewer';
  plan: 'free' | 'pro' | 'enterprise';
  organization?: string;
  lastLogin: Date;
  workspaceCount: number;
  storageUsed: number;
  storageLimit: number;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    notifications: boolean;
    autoSave: boolean;
    compactMode: boolean;
  };
}

interface UserProfileDropdownProps {
  className?: string;
  user?: Partial<UserData>;
  onLogout?: () => void;
  onThemeChange?: (theme: 'light' | 'dark' | 'system') => void;
}

export const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({
  className = '',
  user: userProp,
  onLogout,
  onThemeChange
}) => {
  const router = useRouter();
  
  // Mock user data - in real app, this would come from auth context
  const defaultUser: UserData = {
    id: 'user-1',
    name: 'Demo User',
    email: '<EMAIL>',
    avatar: '',
    initials: 'DU',
    role: 'admin',
    plan: 'pro',
    organization: 'Omnispace Inc.',
    lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
    workspaceCount: 8,
    storageUsed: 15.2,
    storageLimit: 50,
    preferences: {
      theme: 'system',
      notifications: true,
      autoSave: true,
      compactMode: false
    }
  };

  const user = { ...defaultUser, ...userProp };
  const [preferences, setPreferences] = useState(user.preferences);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'user': return <User className="h-3 w-3 text-blue-500" />;
      case 'viewer': return <Users className="h-3 w-3 text-gray-500" />;
      default: return <User className="h-3 w-3" />;
    }
  };

  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case 'free': return <Badge variant="secondary" className="text-xs">Free</Badge>;
      case 'pro': return <Badge variant="default" className="text-xs">Pro</Badge>;
      case 'enterprise': return <Badge className="text-xs bg-purple-600">Enterprise</Badge>;
      default: return null;
    }
  };

  const getStoragePercentage = () => {
    return Math.round((user.storageUsed / user.storageLimit) * 100);
  };

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    } else {
      // Default logout behavior
      router.push('/auth/login');
    }
  };

  const handleThemeChange = (theme: 'light' | 'dark' | 'system') => {
    setPreferences(prev => ({ ...prev, theme }));
    if (onThemeChange) {
      onThemeChange(theme);
    }
  };

  const togglePreference = (key: keyof typeof preferences) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={`relative h-8 w-8 rounded-full ${className}`}>
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback>{user.initials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end" forceMount>
        {/* User Info Header */}
        <DropdownMenuLabel className="font-normal">
          <div className="flex items-start gap-3 p-2">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="text-lg">{user.initials}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <p className="text-sm font-medium leading-none truncate">{user.name}</p>
                {getRoleIcon(user.role)}
              </div>
              <p className="text-xs leading-none text-muted-foreground truncate mb-2">
                {user.email}
              </p>
              <div className="flex items-center gap-2">
                {getPlanBadge(user.plan)}
                {user.organization && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Building className="h-3 w-3" />
                    <span className="truncate">{user.organization}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DropdownMenuLabel>

        {/* Quick Stats */}
        <div className="px-4 py-2 border-t border-b bg-muted/50">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold">{user.workspaceCount}</div>
              <div className="text-xs text-muted-foreground">Workspaces</div>
            </div>
            <div>
              <div className="text-lg font-semibold">{user.storageUsed}GB</div>
              <div className="text-xs text-muted-foreground">Storage Used</div>
            </div>
            <div>
              <div className="text-lg font-semibold">{getStoragePercentage()}%</div>
              <div className="text-xs text-muted-foreground">Capacity</div>
            </div>
          </div>
        </div>

        <DropdownMenuSeparator />

        {/* Navigation Items */}
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/dashboard">
              <Home className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/dashboard/workspaces">
              <Monitor className="mr-2 h-4 w-4" />
              My Workspaces
              <Badge variant="secondary" className="ml-auto text-xs">
                {user.workspaceCount}
              </Badge>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/dashboard/monitoring">
              <Activity className="mr-2 h-4 w-4" />
              Activity & Usage
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Settings & Preferences */}
        <DropdownMenuGroup>
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Palette className="mr-2 h-4 w-4" />
              Theme
              <span className="ml-auto text-xs text-muted-foreground capitalize">
                {preferences.theme}
              </span>
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem onClick={() => handleThemeChange('light')}>
                <Sun className="mr-2 h-4 w-4" />
                Light
                {preferences.theme === 'light' && <Star className="ml-auto h-3 w-3" />}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleThemeChange('dark')}>
                <Moon className="mr-2 h-4 w-4" />
                Dark
                {preferences.theme === 'dark' && <Star className="ml-auto h-3 w-3" />}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleThemeChange('system')}>
                <Laptop className="mr-2 h-4 w-4" />
                System
                {preferences.theme === 'system' && <Star className="ml-auto h-3 w-3" />}
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuItem onClick={() => togglePreference('notifications')}>
            <Bell className="mr-2 h-4 w-4" />
            Notifications
            <Switch
              checked={preferences.notifications}
              className="ml-auto"
              size="sm"
            />
          </DropdownMenuItem>

          <DropdownMenuItem onClick={() => togglePreference('autoSave')}>
            <Download className="mr-2 h-4 w-4" />
            Auto Save
            <Switch
              checked={preferences.autoSave}
              className="ml-auto"
              size="sm"
            />
          </DropdownMenuItem>

          <DropdownMenuItem onClick={() => togglePreference('compactMode')}>
            <Zap className="mr-2 h-4 w-4" />
            Compact Mode
            <Switch
              checked={preferences.compactMode}
              className="ml-auto"
              size="sm"
            />
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Account Management */}
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/dashboard/settings">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Link>
          </DropdownMenuItem>
          
          {user.plan !== 'enterprise' && (
            <DropdownMenuItem asChild>
              <Link href="/billing">
                <CreditCard className="mr-2 h-4 w-4" />
                Billing & Plans
                <Badge variant="outline" className="ml-auto text-xs">
                  Upgrade
                </Badge>
              </Link>
            </DropdownMenuItem>
          )}

          <DropdownMenuItem asChild>
            <Link href="/security">
              <Shield className="mr-2 h-4 w-4" />
              Security
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/api-keys">
              <Key className="mr-2 h-4 w-4" />
              API Keys
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Support & Resources */}
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/docs">
              <FileText className="mr-2 h-4 w-4" />
              Documentation
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/support">
              <HelpCircle className="mr-2 h-4 w-4" />
              Help & Support
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/export">
              <Upload className="mr-2 h-4 w-4" />
              Export Data
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Logout */}
        <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
          <LogOut className="mr-2 h-4 w-4" />
          Sign out
        </DropdownMenuItem>

        {/* Footer */}
        <div className="px-4 py-2 border-t text-xs text-muted-foreground">
          Last login: {user.lastLogin.toLocaleDateString()} at {user.lastLogin.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
