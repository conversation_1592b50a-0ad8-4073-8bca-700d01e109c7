'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Plus,
  Monitor,
  Container,
  Image,
  Settings,
  Activity,
  BarChart3,
  Zap,
  Download,
  Upload,
  RefreshCw,
  Search,
  Bell,
  HelpCircle,
  Bookmark,
  Star,
  ChevronDown,
  MoreHorizontal,
  Rocket,
  Database,
  Network,
  Shield,
  Users,
  FileText,
  Terminal,
  Code,
  Play,
  Square,
  Pause
} from 'lucide-react';

interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  badge?: string | number;
  tooltip?: string;
  shortcut?: string;
  category?: 'primary' | 'secondary' | 'workspace' | 'system' | 'tools';
  disabled?: boolean;
  loading?: boolean;
}

interface QuickActionsToolbarProps {
  className?: string;
  variant?: 'full' | 'compact' | 'minimal';
  showLabels?: boolean;
  maxPrimaryActions?: number;
  customActions?: QuickAction[];
}

export const QuickActionsToolbar: React.FC<QuickActionsToolbarProps> = ({
  className = '',
  variant = 'full',
  showLabels = true,
  maxPrimaryActions = 4,
  customActions
}) => {
  const pathname = usePathname();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Default quick actions based on current page context
  const getContextualActions = (): QuickAction[] => {
    const baseActions: QuickAction[] = [
      {
        id: 'new-workspace',
        label: 'New Workspace',
        icon: Plus,
        href: '/dashboard/workspaces',
        variant: 'default',
        tooltip: 'Create a new workspace environment',
        shortcut: '⌘+N',
        category: 'primary'
      },
      {
        id: 'monitoring',
        label: 'Monitoring',
        icon: BarChart3,
        href: '/dashboard/monitoring',
        variant: 'outline',
        tooltip: 'View system monitoring',
        shortcut: '⌘+M',
        category: 'primary'
      },
      {
        id: 'refresh',
        label: 'Refresh',
        icon: RefreshCw,
        onClick: handleRefresh,
        variant: 'ghost',
        tooltip: 'Refresh current data',
        shortcut: '⌘+R',
        category: 'system',
        loading: isRefreshing
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: Settings,
        href: '/dashboard/settings',
        variant: 'ghost',
        tooltip: 'Open settings',
        shortcut: '⌘+,',
        category: 'system'
      }
    ];

    // Add contextual actions based on current page
    if (pathname.includes('/workspaces')) {
      baseActions.splice(1, 0, {
        id: 'workspace-templates',
        label: 'Templates',
        icon: Rocket,
        href: '/dashboard/workspaces?tab=templates',
        variant: 'outline',
        tooltip: 'Browse workspace templates',
        category: 'workspace'
      });
    }

    if (pathname.includes('/containers')) {
      baseActions.splice(1, 0, {
        id: 'new-container',
        label: 'New Container',
        icon: Container,
        href: '/dashboard/containers?action=create',
        variant: 'outline',
        tooltip: 'Create a new container',
        category: 'workspace'
      });
    }

    if (pathname.includes('/monitoring')) {
      baseActions.splice(1, 0, {
        id: 'export-metrics',
        label: 'Export',
        icon: Download,
        onClick: () => console.log('Export metrics'),
        variant: 'outline',
        tooltip: 'Export monitoring data',
        category: 'tools'
      });
    }

    return baseActions;
  };

  const allActions = customActions || getContextualActions();
  
  // Separate primary and secondary actions
  const primaryActions = allActions
    .filter(action => action.category === 'primary')
    .slice(0, maxPrimaryActions);
  
  const secondaryActions = allActions.filter(action => 
    action.category !== 'primary' || !primaryActions.includes(action)
  );

  async function handleRefresh() {
    setIsRefreshing(true);
    // Simulate refresh operation
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    
    // Trigger page refresh or data reload
    window.location.reload();
  }

  const renderAction = (action: QuickAction, showLabel: boolean = true) => {
    const ActionButton = (
      <Button
        variant={action.variant || 'outline'}
        size={variant === 'minimal' ? 'sm' : 'default'}
        disabled={action.disabled || action.loading}
        onClick={action.onClick}
        asChild={!!action.href}
        className="flex items-center gap-2"
      >
        {action.href ? (
          <Link href={action.href}>
            <action.icon className={`h-4 w-4 ${action.loading ? 'animate-spin' : ''}`} />
            {showLabel && variant !== 'minimal' && (
              <span className="hidden sm:inline">{action.label}</span>
            )}
            {action.badge && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {action.badge}
              </Badge>
            )}
          </Link>
        ) : (
          <>
            <action.icon className={`h-4 w-4 ${action.loading ? 'animate-spin' : ''}`} />
            {showLabel && variant !== 'minimal' && (
              <span className="hidden sm:inline">{action.label}</span>
            )}
            {action.badge && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {action.badge}
              </Badge>
            )}
          </>
        )}
      </Button>
    );

    if (action.tooltip) {
      return (
        <TooltipProvider key={action.id}>
          <Tooltip>
            <TooltipTrigger asChild>
              {ActionButton}
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-center">
                <p>{action.tooltip}</p>
                {action.shortcut && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {action.shortcut}
                  </p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return <div key={action.id}>{ActionButton}</div>;
  };

  const groupSecondaryActions = () => {
    const groups: Record<string, QuickAction[]> = {};
    secondaryActions.forEach(action => {
      const category = action.category || 'other';
      if (!groups[category]) groups[category] = [];
      groups[category].push(action);
    });
    return groups;
  };

  const secondaryGroups = groupSecondaryActions();

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        {primaryActions.slice(0, 2).map(action => renderAction(action, false))}
        {secondaryActions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {Object.entries(secondaryGroups).map(([category, actions]) => (
                <DropdownMenuGroup key={category}>
                  <DropdownMenuLabel className="text-xs uppercase tracking-wider">
                    {category}
                  </DropdownMenuLabel>
                  {actions.map(action => (
                    <DropdownMenuItem
                      key={action.id}
                      disabled={action.disabled}
                      asChild={!!action.href}
                    >
                      {action.href ? (
                        <Link href={action.href} className="flex items-center gap-2">
                          <action.icon className="h-4 w-4" />
                          {action.label}
                          {action.badge && (
                            <Badge variant="secondary" className="ml-auto text-xs">
                              {action.badge}
                            </Badge>
                          )}
                        </Link>
                      ) : (
                        <div onClick={action.onClick} className="flex items-center gap-2">
                          <action.icon className="h-4 w-4" />
                          {action.label}
                          {action.badge && (
                            <Badge variant="secondary" className="ml-auto text-xs">
                              {action.badge}
                            </Badge>
                          )}
                        </div>
                      )}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                </DropdownMenuGroup>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {primaryActions.map(action => renderAction(action, false))}
        {secondaryActions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                More
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {Object.entries(secondaryGroups).map(([category, actions]) => (
                <DropdownMenuGroup key={category}>
                  <DropdownMenuLabel className="text-xs uppercase tracking-wider">
                    {category}
                  </DropdownMenuLabel>
                  {actions.map(action => (
                    <DropdownMenuItem
                      key={action.id}
                      disabled={action.disabled}
                      asChild={!!action.href}
                    >
                      {action.href ? (
                        <Link href={action.href} className="flex items-center gap-2">
                          <action.icon className="h-4 w-4" />
                          {action.label}
                          {action.shortcut && (
                            <span className="ml-auto text-xs text-muted-foreground">
                              {action.shortcut}
                            </span>
                          )}
                        </Link>
                      ) : (
                        <div onClick={action.onClick} className="flex items-center gap-2">
                          <action.icon className="h-4 w-4" />
                          {action.label}
                          {action.shortcut && (
                            <span className="ml-auto text-xs text-muted-foreground">
                              {action.shortcut}
                            </span>
                          )}
                        </div>
                      )}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                </DropdownMenuGroup>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  }

  // Full variant
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Primary Actions */}
      <div className="flex items-center gap-2">
        {primaryActions.map(action => renderAction(action, showLabels))}
      </div>

      {/* Secondary Actions Dropdown */}
      {secondaryActions.length > 0 && (
        <>
          <div className="h-6 w-px bg-gray-200" />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64">
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {Object.entries(secondaryGroups).map(([category, actions]) => (
                <DropdownMenuGroup key={category}>
                  <DropdownMenuLabel className="text-xs uppercase tracking-wider text-muted-foreground">
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </DropdownMenuLabel>
                  {actions.map(action => (
                    <DropdownMenuItem
                      key={action.id}
                      disabled={action.disabled}
                      asChild={!!action.href}
                    >
                      {action.href ? (
                        <Link href={action.href} className="flex items-center gap-2">
                          <action.icon className="h-4 w-4" />
                          <div className="flex-1">
                            <div className="font-medium">{action.label}</div>
                            {action.tooltip && (
                              <div className="text-xs text-muted-foreground">
                                {action.tooltip}
                              </div>
                            )}
                          </div>
                          {action.shortcut && (
                            <span className="text-xs text-muted-foreground">
                              {action.shortcut}
                            </span>
                          )}
                        </Link>
                      ) : (
                        <div onClick={action.onClick} className="flex items-center gap-2">
                          <action.icon className="h-4 w-4" />
                          <div className="flex-1">
                            <div className="font-medium">{action.label}</div>
                            {action.tooltip && (
                              <div className="text-xs text-muted-foreground">
                                {action.tooltip}
                              </div>
                            )}
                          </div>
                          {action.shortcut && (
                            <span className="text-xs text-muted-foreground">
                              {action.shortcut}
                            </span>
                          )}
                        </div>
                      )}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                </DropdownMenuGroup>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      )}
    </div>
  );
};
