'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface QuickAction {
  title: string;
  description: string;
  href: string;
  icon: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary';
  external?: boolean;
}

interface QuickActionsProps {
  title?: string;
  actions: QuickAction[];
  className?: string;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  title = 'Quick Actions',
  actions,
  className = ''
}) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {actions.map((action, index) => (
          <Link 
            key={index} 
            href={action.href} 
            className="block"
            {...(action.external && { target: '_blank', rel: 'noopener noreferrer' })}
          >
            <Button 
              className="w-full justify-start h-auto p-3" 
              variant={action.variant || 'outline'}
            >
              <action.icon className="h-4 w-4 mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">{action.title}</div>
                <div className="text-xs text-muted-foreground mt-0.5">
                  {action.description}
                </div>
              </div>
            </Button>
          </Link>
        ))}
      </CardContent>
    </Card>
  );
};
