'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  ArrowRight, 
  Rocket, 
  Clock, 
  Shield, 
  Zap,
  CheckCircle,
  Star,
  Sparkles,
  Terminal,
  Users,
  TrendingUp
} from 'lucide-react';

const benefits = [
  {
    icon: Clock,
    title: 'Instant Setup',
    description: 'Start coding in under 30 seconds with zero configuration',
    stat: '<30s'
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'VM-level isolation with AWS-grade security standards',
    stat: '100%'
  },
  {
    icon: Zap,
    title: 'Lightning Performance',
    description: 'Firecracker MicroVMs with minimal resource overhead',
    stat: '50MB'
  }
];

const guarantees = [
  'Free beta access',
  'No credit card required',
  'Cancel anytime',
  '99.9% uptime SLA'
];

const stats = [
  { label: 'Developers', value: '10K+', icon: Users },
  { label: 'Uptime', value: '99.9%', icon: TrendingUp },
  { label: 'Boot Time', value: '<1s', icon: Zap },
];

export function CTASection() {
  return (
    <section className="py-24 bg-gradient-surface relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]">
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, currentColor 1px, transparent 0)`,
            backgroundSize: '24px 24px',
          }}
        />
      </div>

      {/* Floating Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '3s' }} />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16 animate-fade-in-up">
            <Badge variant="outline" className="mb-6 px-4 py-2">
              <Sparkles className="h-4 w-4 mr-2" />
              Join the Beta
            </Badge>
            
            <h2 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
              Ready to
              <span className="block gradient-text-primary">
                Transform Your Workflow?
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Join thousands of developers who are already building the future with 
              AI-powered cloud workspaces. Start your journey today.
            </p>
          </div>

          {/* Benefits Cards */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {benefits.map((benefit, index) => (
              <Card 
                key={index} 
                className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300 hover-lift animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-primary/10 border border-primary/20 rounded-2xl flex items-center justify-center mb-6">
                    <benefit.icon className="h-8 w-8 text-primary" />
                  </div>
                  <div className="text-3xl font-bold text-primary mb-2">{benefit.stat}</div>
                  <h3 className="text-xl font-bold text-foreground mb-3">{benefit.title}</h3>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main CTA */}
          <div className="text-center mb-16">
            <div className="bg-gradient-primary rounded-3xl p-12 text-white mb-8">
              <h3 className="text-3xl font-bold mb-4">
                Start Building the Future Today
              </h3>
              <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
                Experience the next generation of development environments. 
                No setup required, just pure productivity.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <Link href="/dashboard">
                  <Button size="lg" variant="secondary" className="px-8 py-4 text-lg font-semibold">
                    <Terminal className="mr-2 h-5 w-5" />
                    Launch Workspace
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                
                <Link href="#demo">
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="px-8 py-4 text-lg font-semibold border-white/20 text-white hover:bg-white/10"
                  >
                    <Rocket className="mr-2 h-5 w-5" />
                    See Demo
                  </Button>
                </Link>
              </div>

              {/* Guarantees */}
              <div className="flex flex-wrap justify-center gap-6 text-sm opacity-90">
                {guarantees.map((guarantee, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span>{guarantee}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <stat.icon className="h-6 w-6 text-primary" />
                  <div className="text-3xl font-bold text-foreground">{stat.value}</div>
                </div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Social Proof */}
          <div className="text-center border-t border-border pt-12">
            <div className="flex flex-col md:flex-row items-center justify-center gap-8 text-muted-foreground">
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span className="font-semibold text-foreground">4.9/5</span>
                <span>from beta users</span>
              </div>
              
              <div className="hidden md:block w-px h-6 bg-border" />
              
              <div className="text-center md:text-left">
                <span className="font-semibold text-foreground">10,000+</span> developers in beta
              </div>
              
              <div className="hidden md:block w-px h-6 bg-border" />
              
              <div className="text-center md:text-left">
                <span className="font-semibold text-foreground">99.9%</span> uptime SLA
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
