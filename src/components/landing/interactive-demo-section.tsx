'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  Monitor, 
  Terminal, 
  Code, 
  Bot,
  Zap,
  ArrowRight,
  Clock,
  Cpu,
  HardDrive,
  Sparkles,
  CheckCircle,
  Circle,
  Pause
} from 'lucide-react';
import Link from 'next/link';

const demoSteps = [
  {
    id: 1,
    title: 'Create MicroVM',
    description: 'Spin up a lightweight Firecracker VM',
    command: 'omnispace create --template ubuntu-dev',
    duration: '0.8s',
    status: 'completed'
  },
  {
    id: 2,
    title: 'Start VNC Server',
    description: 'Initialize remote desktop access',
    command: 'Starting VNC server on port 5901...',
    duration: '0.3s',
    status: 'completed'
  },
  {
    id: 3,
    title: 'Connect Browser',
    description: 'Open desktop in your browser',
    command: 'omnispace connect vm-12345',
    duration: '0.1s',
    status: 'running'
  },
  {
    id: 4,
    title: 'AI Assistant Ready',
    description: 'Activate intelligent automation',
    command: 'AI assistant initialized',
    duration: 'instant',
    status: 'pending'
  }
];

export function InteractiveDemoSection() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const handlePlayDemo = () => {
    setIsPlaying(!isPlaying);
    if (!isPlaying) {
      // Simulate demo progression
      const interval = setInterval(() => {
        setCurrentStep((prev) => {
          if (prev >= demoSteps.length - 1) {
            clearInterval(interval);
            setIsPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, 2000);
    }
  };

  return (
    <section id="demo" className="py-24 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20 animate-fade-in-up">
            <Badge variant="outline" className="mb-6 px-4 py-2">
              <Play className="h-4 w-4 mr-2" />
              Interactive Demo
            </Badge>
            
            <h2 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
              See It
              <span className="block gradient-text-primary">
                In Action
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Watch how Omnispace transforms your development workflow with lightning-fast 
              MicroVMs, instant browser access, and intelligent AI assistance.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Demo Interface */}
            <div className="order-2 lg:order-1">
              <Card className="overflow-hidden border-0 shadow-2xl bg-card/50 backdrop-blur-sm">
                <div className="relative aspect-video bg-gradient-to-br from-gray-900 to-gray-800">
                  {/* Mock Terminal Interface */}
                  <div className="absolute inset-0 p-4">
                    <div className="h-full bg-black/90 rounded-lg overflow-hidden border border-gray-700">
                      {/* Terminal Header */}
                      <div className="flex items-center justify-between px-4 py-3 bg-gray-800/90 border-b border-gray-700">
                        <div className="flex items-center gap-3">
                          <div className="flex gap-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          </div>
                          <span className="text-sm text-gray-300 font-medium">Omnispace Terminal</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <Cpu className="h-3 w-3" />
                          <span>2 vCPU</span>
                          <span>•</span>
                          <span>2GB RAM</span>
                        </div>
                      </div>
                      
                      {/* Terminal Content */}
                      <div className="p-4 font-mono text-sm space-y-3 h-full overflow-hidden">
                        <div className="text-blue-400">$ omnispace --version</div>
                        <div className="text-gray-400">Omnispace v1.0.0-beta</div>
                        <div className="text-gray-400">Firecracker VMM ready ✓</div>
                        <div className="text-gray-400">AI SDK v5 loaded ✓</div>
                        <div className="border-t border-gray-700 pt-3 mt-3">
                          <div className="text-green-400">$ omnispace create --template ubuntu-dev</div>
                          <div className="text-gray-500 animate-pulse">Creating MicroVM...</div>
                          {isPlaying && (
                            <div className="space-y-1 mt-2">
                              <div className="text-blue-400">✓ VM created in 0.8s</div>
                              <div className="text-blue-400">✓ VNC server started</div>
                              <div className="text-blue-400">✓ Desktop environment ready</div>
                              <div className="text-green-400">🚀 Ready for connection!</div>
                            </div>
                          )}
                        </div>
                        <div className="absolute bottom-4 left-4 right-4">
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span>VM Status: {isPlaying ? 'Running' : 'Ready'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Play Button Overlay */}
                  {!isPlaying && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                      <Button 
                        size="lg" 
                        onClick={handlePlayDemo}
                        className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-full p-6 shadow-2xl hover:scale-110 transition-all duration-300"
                      >
                        <Play className="h-8 w-8 ml-1" />
                      </Button>
                    </div>
                  )}
                </div>
              </Card>
            </div>

            {/* Demo Steps */}
            <div className="order-1 lg:order-2 space-y-6">
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-foreground mb-6">
                  From Zero to Running in Seconds
                </h3>
                
                {demoSteps.map((step, index) => (
                  <div 
                    key={step.id}
                    className={`flex items-start gap-4 p-4 rounded-xl transition-all duration-300 ${
                      index <= currentStep && isPlaying 
                        ? 'bg-primary/10 border border-primary/20' 
                        : 'bg-card/30 border border-border/50'
                    }`}
                  >
                    <div className="flex-shrink-0 mt-1">
                      {index < currentStep && isPlaying ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : index === currentStep && isPlaying ? (
                        <div className="h-5 w-5 border-2 border-primary rounded-full animate-spin border-t-transparent" />
                      ) : (
                        <Circle className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-semibold text-foreground">{step.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {step.duration}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {step.description}
                      </p>
                      <code className="text-xs bg-muted/50 px-2 py-1 rounded font-mono text-muted-foreground">
                        {step.command}
                      </code>
                    </div>
                  </div>
                ))}
              </div>

              {/* Performance Stats */}
              <div className="grid grid-cols-3 gap-4 pt-6 border-t border-border">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">&lt;1s</div>
                  <div className="text-xs text-muted-foreground">Boot Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">50MB</div>
                  <div className="text-xs text-muted-foreground">Memory</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">0ms</div>
                  <div className="text-xs text-muted-foreground">Setup</div>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Highlights */}
          <div className="grid md:grid-cols-3 gap-8 mt-20 mb-16">
            <Card className="text-center border-0 bg-card/30 hover:bg-card/50 transition-all duration-300 hover-lift">
              <CardContent className="p-8">
                <div className="mx-auto w-16 h-16 bg-blue-500/10 border border-blue-500/20 rounded-2xl flex items-center justify-center mb-6">
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Lightning Fast</h3>
                <p className="text-muted-foreground">
                  Sub-second boot times with Firecracker's lightweight virtualization technology.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-0 bg-card/30 hover:bg-card/50 transition-all duration-300 hover-lift">
              <CardContent className="p-8">
                <div className="mx-auto w-16 h-16 bg-green-500/10 border border-green-500/20 rounded-2xl flex items-center justify-center mb-6">
                  <Monitor className="h-8 w-8 text-green-500" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Zero Setup</h3>
                <p className="text-muted-foreground">
                  Access full desktop environments directly from your browser with no downloads.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-0 bg-card/30 hover:bg-card/50 transition-all duration-300 hover-lift">
              <CardContent className="p-8">
                <div className="mx-auto w-16 h-16 bg-purple-500/10 border border-purple-500/20 rounded-2xl flex items-center justify-center mb-6">
                  <Bot className="h-8 w-8 text-purple-500" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">AI-Powered</h3>
                <p className="text-muted-foreground">
                  Intelligent automation that understands and assists with your development workflow.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* CTA Section */}
          <div className="text-center bg-gradient-primary rounded-3xl p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">
              Experience the Future Today
            </h3>
            <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
              Join the beta and be among the first to revolutionize your development workflow. 
              No credit card required.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <Button size="lg" variant="secondary" className="px-8 py-4 text-lg font-semibold">
                  <Sparkles className="mr-2 h-5 w-5" />
                  Start Free Beta
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              
              <Link href="#architecture">
                <Button size="lg" variant="outline" className="px-8 py-4 text-lg font-semibold border-white/20 text-white hover:bg-white/10">
                  <Code className="mr-2 h-5 w-5" />
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}