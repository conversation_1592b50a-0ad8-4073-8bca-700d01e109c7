'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Cloud, 
  Monitor, 
  Bot, 
  Shield, 
  Zap, 
  Cpu, 
  Globe, 
  Lock,
  Layers,
  Terminal,
  Code,
  Workflow,
  ArrowRight,
  Sparkles,
  Timer,
  Users,
  Database
} from 'lucide-react';
import Link from 'next/link';

const mainFeatures = [
  {
    title: 'Firecracker MicroVMs',
    description: 'Ultra-lightweight virtualization with sub-second boot times and minimal resource overhead.',
    icon: Cloud,
    color: 'blue',
    stats: { value: '<1s', label: 'Boot Time' },
    benefits: ['Sub-second startup', 'Minimal overhead', 'Strong isolation', 'AWS-grade security'],
  },
  {
    title: 'Browser-based VNC',
    description: 'Access full desktop environments directly from your browser with zero client installation.',
    icon: Monitor,
    color: 'green',
    stats: { value: '0ms', label: 'Setup Time' },
    benefits: ['No downloads', 'Cross-platform', 'Secure tunneling', 'Real-time interaction'],
  },
  {
    title: 'AI-Powered Automation',
    description: 'Intelligent AI agents that understand and control your development environment.',
    icon: Bot,
    color: 'purple',
    stats: { value: '10x', label: 'Productivity' },
    benefits: ['Natural language', 'Workflow automation', 'Smart assistance', 'Code generation'],
  },
];

const additionalFeatures = [
  {
    title: 'Enterprise Security',
    description: 'Multi-layered security with VM isolation and encrypted connections.',
    icon: Shield,
  },
  {
    title: 'Scalable Architecture',
    description: 'Auto-scaling infrastructure that grows with your team.',
    icon: Cpu,
  },
  {
    title: 'Team Collaboration',
    description: 'Real-time collaboration features for distributed teams.',
    icon: Users,
  },
  {
    title: 'Persistent Storage',
    description: 'Your work is automatically saved and synchronized.',
    icon: Database,
  },
  {
    title: 'Template Library',
    description: 'Pre-configured environments for popular frameworks.',
    icon: Layers,
  },
  {
    title: 'Performance Monitoring',
    description: 'Real-time insights into resource usage and performance.',
    icon: Timer,
  },
];

const getColorClasses = (color: string) => {
  const colors = {
    blue: {
      bg: 'bg-blue-500/10',
      border: 'border-blue-500/20',
      text: 'text-blue-600 dark:text-blue-400',
      icon: 'text-blue-500',
      gradient: 'from-blue-500/20 to-blue-600/20',
    },
    green: {
      bg: 'bg-green-500/10',
      border: 'border-green-500/20',
      text: 'text-green-600 dark:text-green-400',
      icon: 'text-green-500',
      gradient: 'from-green-500/20 to-green-600/20',
    },
    purple: {
      bg: 'bg-purple-500/10',
      border: 'border-purple-500/20',
      text: 'text-purple-600 dark:text-purple-400',
      icon: 'text-purple-500',
      gradient: 'from-purple-500/20 to-purple-600/20',
    },
  };
  return colors[color as keyof typeof colors] || colors.blue;
};

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gradient-surface">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20 animate-fade-in-up">
            <Badge variant="outline" className="mb-6 px-4 py-2">
              <Sparkles className="h-4 w-4 mr-2" />
              Core Features
            </Badge>
            
            <h2 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
              Built for
              <span className="block gradient-text-primary">
                Modern Development
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Experience the perfect blend of cutting-edge virtualization, seamless access, 
              and intelligent automation in one powerful platform.
            </p>
          </div>

          {/* Main Features */}
          <div className="grid lg:grid-cols-3 gap-8 mb-20">
            {mainFeatures.map((feature, index) => {
              const colorClasses = getColorClasses(feature.color);
              
              return (
                <Card 
                  key={feature.title}
                  className={`group relative overflow-hidden border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-500 hover:scale-105 animate-fade-in-up animate-stagger-${index + 1}`}
                >
                  {/* Background Gradient */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${colorClasses.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
                  
                  <CardContent className="relative p-8">
                    <div className="space-y-6">
                      {/* Icon and Stats */}
                      <div className="flex items-start justify-between">
                        <div className={`p-4 rounded-2xl ${colorClasses.bg} border ${colorClasses.border} group-hover:scale-110 transition-transform duration-300`}>
                          <feature.icon className={`h-8 w-8 ${colorClasses.icon}`} />
                        </div>
                        <div className="text-right">
                          <div className={`text-2xl font-bold ${colorClasses.text}`}>
                            {feature.stats.value}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {feature.stats.label}
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="space-y-4">
                        <h3 className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
                          {feature.title}
                        </h3>
                        <p className="text-muted-foreground leading-relaxed">
                          {feature.description}
                        </p>
                      </div>

                      {/* Benefits */}
                      <div className="space-y-3">
                        <h4 className="text-sm font-semibold text-foreground">
                          Key Benefits:
                        </h4>
                        <div className="grid grid-cols-2 gap-2">
                          {feature.benefits.map((benefit, benefitIndex) => (
                            <div 
                              key={benefitIndex}
                              className="flex items-center gap-2 text-sm text-muted-foreground"
                            >
                              <div className={`w-1.5 h-1.5 rounded-full ${colorClasses.bg} border ${colorClasses.border}`} />
                              {benefit}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Additional Features Grid */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-foreground text-center mb-12">
              Everything You Need
            </h3>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {additionalFeatures.map((feature, index) => (
                <Card 
                  key={feature.title}
                  className="group border-0 bg-card/30 hover:bg-card/60 transition-all duration-300 hover-lift"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-2 rounded-lg bg-primary/10 border border-primary/20 group-hover:bg-primary/20 transition-colors">
                        <feature.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-foreground mb-2">
                          {feature.title}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center bg-gradient-primary rounded-3xl p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">
              Ready to Transform Your Workflow?
            </h3>
            <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
              Join thousands of developers who have already revolutionized their development process with Omnispace.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <Button size="lg" variant="secondary" className="px-8 py-4 text-lg font-semibold">
                  <Terminal className="mr-2 h-5 w-5" />
                  Start Building Now
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              
              <Link href="#demo">
                <Button size="lg" variant="outline" className="px-8 py-4 text-lg font-semibold border-white/20 text-white hover:bg-white/10">
                  <Zap className="mr-2 h-5 w-5" />
                  See Live Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
