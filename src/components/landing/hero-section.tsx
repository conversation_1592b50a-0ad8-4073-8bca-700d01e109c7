'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  Play, 
  Cloud, 
  Zap, 
  Shield, 
  Bot,
  Monitor,
  Cpu,
  Globe,
  Sparkles,
  Terminal,
  Code2
} from 'lucide-react';

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-surface">
      {/* Modern Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-accent/5" />
        <div 
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, currentColor 1px, transparent 0)`,
            backgroundSize: '24px 24px',
          }}
        />
      </div>

      {/* Floating Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl animate-float" />
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-primary/3 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }} />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center space-y-12">
            {/* Announcement Badge */}
            <div className="flex justify-center animate-fade-in-up">
              <Badge variant="outline" className="px-6 py-3 text-sm bg-primary/5 border-primary/20 hover:bg-primary/10 transition-colors">
                <Sparkles className="h-4 w-4 mr-2 text-primary" />
                Introducing AI-Powered Computer Use
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-6 animate-fade-in-up animate-stagger-1">
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-foreground leading-[0.9] tracking-tight">
                Cloud Workspaces
                <span className="block gradient-text-primary">
                  Reimagined
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                Experience the next generation of development environments with{' '}
                <span className="font-semibold text-primary">Firecracker MicroVMs</span>,{' '}
                instant browser access, and{' '}
                <span className="font-semibold gradient-text-accent">intelligent AI assistance</span>.
              </p>
            </div>

            {/* Key Features Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto animate-fade-in-up animate-stagger-2">
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-card/50 border border-border/50 hover-lift">
                <div className="p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
                  <Cloud className="h-6 w-6 text-blue-500" />
                </div>
                <span className="text-sm font-medium text-foreground">Ultra-Light VMs</span>
              </div>
              
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-card/50 border border-border/50 hover-lift">
                <div className="p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                  <Monitor className="h-6 w-6 text-green-500" />
                </div>
                <span className="text-sm font-medium text-foreground">Browser VNC</span>
              </div>
              
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-card/50 border border-border/50 hover-lift">
                <div className="p-3 rounded-lg bg-purple-500/10 border border-purple-500/20">
                  <Bot className="h-6 w-6 text-purple-500" />
                </div>
                <span className="text-sm font-medium text-foreground">AI Automation</span>
              </div>
              
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-card/50 border border-border/50 hover-lift">
                <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                  <Shield className="h-6 w-6 text-red-500" />
                </div>
                <span className="text-sm font-medium text-foreground">Enterprise Security</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8 animate-fade-in-up animate-stagger-3">
              <Link href="/dashboard">
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 animate-glow">
                  <Terminal className="mr-2 h-5 w-5" />
                  Launch Workspace
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              
              <Link href="#demo">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg font-semibold border-2 hover:bg-accent/50 transition-all duration-300">
                  <Play className="mr-2 h-5 w-5" />
                  See It In Action
                </Button>
              </Link>
            </div>

            {/* Stats */}
            <div className="pt-16 animate-fade-in-up animate-stagger-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    &lt;1s
                  </div>
                  <p className="text-sm text-muted-foreground">Boot Time</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    99.9%
                  </div>
                  <p className="text-sm text-muted-foreground">Uptime</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    50MB
                  </div>
                  <p className="text-sm text-muted-foreground">Memory Overhead</p>
                </div>
              </div>
            </div>

            {/* Technology Indicators */}
            <div className="pt-12 animate-fade-in-up animate-stagger-4">
              <p className="text-sm text-muted-foreground mb-8">
                Built with cutting-edge technology
              </p>
              
              <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                <div className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
                  <Code2 className="h-5 w-5" />
                  <span className="font-medium">Next.js 15</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
                  <Cloud className="h-5 w-5" />
                  <span className="font-medium">Firecracker</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
                  <Bot className="h-5 w-5" />
                  <span className="font-medium">AI SDK v5</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
                  <Shield className="h-5 w-5" />
                  <span className="font-medium">TypeScript</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent" />
    </section>
  );
}
