'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Cpu, 
  HardDrive, 
  MemoryStick,
  TrendingUp,
  TrendingDown,
  Zap,
  AlertCircle,
  Info,
  XCircle,
  Play,
  Square,
  BarChart3,
  FileText,
  Settings
} from 'lucide-react';
import { PerformanceMetrics, Alert, LogEntry } from '@/services/monitoring/performance-monitor';

interface PerformanceDashboardProps {
  workspaceId: string;
  className?: string;
}

const alertSeverityColors = {
  low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const logLevelColors = {
  debug: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const logLevelIcons = {
  debug: <Info className="h-3 w-3" />,
  info: <Info className="h-3 w-3" />,
  warning: <AlertTriangle className="h-3 w-3" />,
  error: <XCircle className="h-3 w-3" />,
  critical: <AlertCircle className="h-3 w-3" />,
};

export function PerformanceDashboard({ 
  workspaceId, 
  className = '' 
}: PerformanceDashboardProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load monitoring data
  useEffect(() => {
    loadMonitoringData();
    
    // Set up polling for real-time updates
    const interval = setInterval(loadMonitoringData, 30000); // 30 seconds
    
    return () => clearInterval(interval);
  }, [workspaceId]);

  const loadMonitoringData = async () => {
    try {
      setError(null);
      
      // Load status
      const statusResponse = await fetch(`/api/monitoring?workspaceId=${workspaceId}&action=status`);
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        if (statusData.success) {
          setIsMonitoring(statusData.data.isMonitoring);
        }
      }

      // Load latest metrics
      const metricsResponse = await fetch(`/api/monitoring?workspaceId=${workspaceId}&action=metrics&limit=1`);
      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        if (metricsData.success && metricsData.data.latest) {
          setMetrics(metricsData.data.latest);
        }
      }

      // Load alerts
      const alertsResponse = await fetch(`/api/monitoring?workspaceId=${workspaceId}&action=alerts`);
      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        if (alertsData.success) {
          setAlerts(alertsData.data.alerts);
        }
      }

      // Load recent logs
      const logsResponse = await fetch(`/api/monitoring?workspaceId=${workspaceId}&action=logs&limit=20`);
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        if (logsData.success) {
          setLogs(logsData.data.logs);
        }
      }

    } catch (err) {
      console.error('Error loading monitoring data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load monitoring data');
    }
  };

  const startMonitoring = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          action: 'start',
          interval: 30000, // 30 seconds
          retentionPeriod: 24, // 24 hours
          alertThresholds: {
            cpuUsage: 80,
            memoryUsage: 85,
            diskUsage: 90,
            responseTime: 5000,
            errorRate: 5,
          },
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to start monitoring');
      }
      
      const data = await response.json();
      if (data.success) {
        setIsMonitoring(true);
        await loadMonitoringData();
      } else {
        throw new Error(data.message || 'Failed to start monitoring');
      }
    } catch (err) {
      console.error('Error starting monitoring:', err);
      setError(err instanceof Error ? err.message : 'Failed to start monitoring');
    } finally {
      setLoading(false);
    }
  };

  const stopMonitoring = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          action: 'stop',
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to stop monitoring');
      }
      
      const data = await response.json();
      if (data.success) {
        setIsMonitoring(false);
      } else {
        throw new Error(data.message || 'Failed to stop monitoring');
      }
    } catch (err) {
      console.error('Error stopping monitoring:', err);
      setError(err instanceof Error ? err.message : 'Failed to stop monitoring');
    } finally {
      setLoading(false);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          action: 'resolveAlert',
          alertId,
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Remove resolved alert from state
          setAlerts(prev => prev.filter(alert => alert.id !== alertId));
        }
      }
    } catch (err) {
      console.error('Error resolving alert:', err);
    }
  };

  const getHealthStatus = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return { status: 'critical', color: 'text-red-600' };
    if (value >= thresholds.warning) return { status: 'warning', color: 'text-yellow-600' };
    return { status: 'good', color: 'text-green-600' };
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Monitoring</h2>
          <p className="text-muted-foreground">
            Monitor system performance and application health
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge 
            variant={isMonitoring ? "default" : "secondary"}
            className="flex items-center gap-1"
          >
            <div className={`w-2 h-2 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-500'}`} />
            {isMonitoring ? 'Monitoring Active' : 'Monitoring Inactive'}
          </Badge>
          
          <Button
            onClick={isMonitoring ? stopMonitoring : startMonitoring}
            disabled={loading}
            variant={isMonitoring ? "outline" : "default"}
            className="flex items-center gap-2"
          >
            {isMonitoring ? (
              <>
                <Square className="h-4 w-4" />
                Stop
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                Start
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md"
        >
          <div className="flex items-center gap-2 text-red-800 dark:text-red-300">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Error</span>
          </div>
          <p className="text-sm text-red-700 dark:text-red-400 mt-1">{error}</p>
        </motion.div>
      )}

      {/* System Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* CPU Usage */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  CPU Usage
                </CardTitle>
                <div className={`text-sm font-medium ${getHealthStatus(metrics.cpu.usage, { warning: 70, critical: 85 }).color}`}>
                  {metrics.cpu.usage.toFixed(1)}%
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={metrics.cpu.usage} className="h-2" />
              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                <span>Load: {metrics.cpu.loadAverage[0]?.toFixed(2) || '0.00'}</span>
                <span>{metrics.cpu.cores} core{metrics.cpu.cores !== 1 ? 's' : ''}</span>
              </div>
            </CardContent>
          </Card>

          {/* Memory Usage */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <MemoryStick className="h-4 w-4" />
                  Memory Usage
                </CardTitle>
                <div className={`text-sm font-medium ${getHealthStatus(metrics.memory.usage, { warning: 75, critical: 90 }).color}`}>
                  {metrics.memory.usage.toFixed(1)}%
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={metrics.memory.usage} className="h-2" />
              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                <span>Used: {formatBytes(metrics.memory.used)}</span>
                <span>Total: {formatBytes(metrics.memory.total)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Disk Usage */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Disk Usage
                </CardTitle>
                <div className={`text-sm font-medium ${getHealthStatus(metrics.disk.usage, { warning: 80, critical: 95 }).color}`}>
                  {metrics.disk.usage.toFixed(1)}%
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={metrics.disk.usage} className="h-2" />
              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                <span>Used: {formatBytes(metrics.disk.used)}</span>
                <span>Available: {formatBytes(metrics.disk.available)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Monitoring Tabs */}
      <Tabs defaultValue="alerts" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="alerts" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Alerts ({alerts.length})
          </TabsTrigger>
          <TabsTrigger value="logs" className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            Logs ({logs.length})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-1">
            <BarChart3 className="h-3 w-3" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="alerts" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Active Alerts</CardTitle>
              <CardDescription>
                System alerts and performance warnings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {alerts.length > 0 ? (
                <div className="space-y-3">
                  <AnimatePresence>
                    {alerts.map((alert) => (
                      <motion.div
                        key={alert.id}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        className="flex items-start justify-between p-3 border rounded-lg"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge className={`text-xs ${alertSeverityColors[alert.severity]}`}>
                              {alert.severity}
                            </Badge>
                            <span className="font-medium text-sm">{alert.title}</span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {alert.description}
                          </p>
                          <div className="text-xs text-muted-foreground">
                            {alert.timestamp.toLocaleString()}
                          </div>
                        </div>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => resolveAlert(alert.id)}
                        >
                          <CheckCircle className="h-3 w-3" />
                          Resolve
                        </Button>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500 opacity-50" />
                  <p className="text-lg font-medium mb-2">No Active Alerts</p>
                  <p className="text-sm text-muted-foreground">
                    Your system is running smoothly
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Logs</CardTitle>
              <CardDescription>
                Application and system log entries
              </CardDescription>
            </CardHeader>
            <CardContent>
              {logs.length > 0 ? (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  <AnimatePresence>
                    {logs.map((log) => (
                      <motion.div
                        key={log.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-start gap-3 p-2 border rounded text-sm"
                      >
                        <div className="flex items-center gap-2 min-w-0">
                          <Badge className={`text-xs ${logLevelColors[log.level]}`}>
                            {logLevelIcons[log.level]}
                            {log.level}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-xs text-muted-foreground mb-1">
                            {log.source}
                          </div>
                          <div className="text-sm break-words">
                            {log.message}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <p className="text-lg font-medium mb-2">No Logs Available</p>
                  <p className="text-sm text-muted-foreground">
                    Start monitoring to see log entries
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Performance Analytics</CardTitle>
              <CardDescription>
                System performance trends and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                <p className="text-lg font-medium mb-2">Analytics Coming Soon</p>
                <p className="text-sm text-muted-foreground">
                  Detailed performance charts and trends will be available here
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
