'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { GlassCard } from '@/components/ui/enhanced-card';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import {
  Cpu,
  HardDrive,
  Network,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Zap,
  Database,
  Wifi,
  Server
} from 'lucide-react';

interface ResourceData {
  timestamp: string;
  cpu: number;
  memory: number;
  disk: number;
  networkRx: number;
  networkTx: number;
}

interface ResourceMetrics {
  cpu: {
    current: number;
    average: number;
    peak: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    available: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
    available: number;
  };
  network: {
    rxRate: number;
    txRate: number;
    rxTotal: number;
    txTotal: number;
  };
}

export const ResourceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<ResourceMetrics>({
    cpu: { current: 45, average: 38, peak: 78, cores: 8 },
    memory: { used: 12.5, total: 32, percentage: 39, available: 19.5 },
    disk: { used: 250, total: 1000, percentage: 25, available: 750 },
    network: { rxRate: 1.2, txRate: 0.8, rxTotal: 1024, txTotal: 512 }
  });

  const [historicalData, setHistoricalData] = useState<ResourceData[]>([]);

  // Generate mock historical data
  useEffect(() => {
    const generateData = () => {
      const data: ResourceData[] = [];
      const now = new Date();
      
      for (let i = 29; i >= 0; i--) {
        const timestamp = new Date(now.getTime() - i * 60000); // 1 minute intervals
        data.push({
          timestamp: timestamp.toLocaleTimeString('en-US', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
          }),
          cpu: Math.floor(Math.random() * 40) + 20,
          memory: Math.floor(Math.random() * 30) + 30,
          disk: Math.floor(Math.random() * 20) + 20,
          networkRx: Math.random() * 2,
          networkTx: Math.random() * 1.5
        });
      }
      
      setHistoricalData(data);
    };

    generateData();
    const interval = setInterval(generateData, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getStatusColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (percentage: number) => {
    if (percentage < 50) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (percentage < 80) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  return (
    <div className="space-y-6">
      {/* Real-time Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* CPU Metrics */}
        <GlassCard variant="glass" animate className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <Cpu className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold">CPU Usage</h3>
                <p className="text-xs text-muted-foreground">{metrics.cpu.cores} cores</p>
              </div>
            </div>
            {getStatusIcon(metrics.cpu.current)}
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">{metrics.cpu.current}%</span>
              <Badge variant="outline" className={getStatusColor(metrics.cpu.current)}>
                {metrics.cpu.current > metrics.cpu.average ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {metrics.cpu.current > metrics.cpu.average ? 'High' : 'Normal'}
              </Badge>
            </div>
            <Progress value={metrics.cpu.current} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Avg: {metrics.cpu.average}%</span>
              <span>Peak: {metrics.cpu.peak}%</span>
            </div>
          </div>
        </GlassCard>

        {/* Memory Metrics */}
        <GlassCard variant="glass" animate className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <HardDrive className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold">Memory</h3>
                <p className="text-xs text-muted-foreground">{metrics.memory.total}GB total</p>
              </div>
            </div>
            {getStatusIcon(metrics.memory.percentage)}
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">{metrics.memory.percentage}%</span>
              <Badge variant="outline" className={getStatusColor(metrics.memory.percentage)}>
                <Database className="h-3 w-3 mr-1" />
                {metrics.memory.used}GB used
              </Badge>
            </div>
            <Progress value={metrics.memory.percentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Used: {metrics.memory.used}GB</span>
              <span>Free: {metrics.memory.available}GB</span>
            </div>
          </div>
        </GlassCard>

        {/* Disk Metrics */}
        <GlassCard variant="glass" animate className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <Server className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold">Disk Usage</h3>
                <p className="text-xs text-muted-foreground">{metrics.disk.total}GB total</p>
              </div>
            </div>
            {getStatusIcon(metrics.disk.percentage)}
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">{metrics.disk.percentage}%</span>
              <Badge variant="outline" className={getStatusColor(metrics.disk.percentage)}>
                <HardDrive className="h-3 w-3 mr-1" />
                {metrics.disk.used}GB used
              </Badge>
            </div>
            <Progress value={metrics.disk.percentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Used: {metrics.disk.used}GB</span>
              <span>Free: {metrics.disk.available}GB</span>
            </div>
          </div>
        </GlassCard>

        {/* Network Metrics */}
        <GlassCard variant="glass" animate className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <Network className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="font-semibold">Network</h3>
                <p className="text-xs text-muted-foreground">Real-time traffic</p>
              </div>
            </div>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="text-center">
                <p className="text-lg font-bold text-green-600">↓ {metrics.network.rxRate} MB/s</p>
                <p className="text-xs text-muted-foreground">Download</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-blue-600">↑ {metrics.network.txRate} MB/s</p>
                <p className="text-xs text-muted-foreground">Upload</p>
              </div>
            </div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>RX: {formatBytes(metrics.network.rxTotal * 1024 * 1024)}</span>
              <span>TX: {formatBytes(metrics.network.txTotal * 1024 * 1024)}</span>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Historical Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* CPU & Memory Chart */}
        <GlassCard variant="glass" animate className="p-6">
          <CardHeader className="px-0 pt-0">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              CPU & Memory Usage
            </CardTitle>
          </CardHeader>
          <CardContent className="px-0 pb-0">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={historicalData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="timestamp" className="text-xs" />
                <YAxis className="text-xs" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(0, 0, 0, 0.8)', 
                    border: 'none', 
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="cpu" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name="CPU %"
                />
                <Line 
                  type="monotone" 
                  dataKey="memory" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  name="Memory %"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </GlassCard>

        {/* Network Traffic Chart */}
        <GlassCard variant="glass" animate className="p-6">
          <CardHeader className="px-0 pt-0">
            <CardTitle className="flex items-center gap-2">
              <Wifi className="h-5 w-5" />
              Network Traffic
            </CardTitle>
          </CardHeader>
          <CardContent className="px-0 pb-0">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={historicalData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="timestamp" className="text-xs" />
                <YAxis className="text-xs" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(0, 0, 0, 0.8)', 
                    border: 'none', 
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
                <Area 
                  type="monotone" 
                  dataKey="networkRx" 
                  stackId="1"
                  stroke="#f59e0b" 
                  fill="#f59e0b"
                  fillOpacity={0.6}
                  name="Download MB/s"
                />
                <Area 
                  type="monotone" 
                  dataKey="networkTx" 
                  stackId="1"
                  stroke="#ef4444" 
                  fill="#ef4444"
                  fillOpacity={0.6}
                  name="Upload MB/s"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </GlassCard>
      </div>
    </div>
  );
};
