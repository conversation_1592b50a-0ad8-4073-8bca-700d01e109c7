// Export all Appwrite-related components and hooks

// Hooks
export { useAppwriteAuth } from '@/hooks/useAppwriteAuth';
export { useAppwriteDatabase } from '@/hooks/useAppwriteDatabase';
export { useAppwriteStorage } from '@/hooks/useAppwriteStorage';

// Auth Components
export { EnhancedAuthForm } from '@/components/auth/EnhancedAuthForm';

// Database Components
export { DocumentManager } from '@/components/database/DocumentManager';

// Storage Components
export { FileUpload } from '@/components/storage/FileUpload';

// Dashboard Components
export { AppwriteDashboard } from '@/components/dashboard/AppwriteDashboard';

// Re-export existing auth components for compatibility
export { LoginForm } from '@/components/auth/login-form';
export { RegisterForm } from '@/components/auth/register-form';
export { useAuth } from '@/contexts/auth-context';

// Types
export type {
  UserProfile,
  UserSession,
  OAuthProvider,
} from '@/lib/appwrite';
