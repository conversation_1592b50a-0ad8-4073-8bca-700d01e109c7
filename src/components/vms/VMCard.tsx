'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { GlassCard, AnimatedButton } from '@/components/ui/enhanced-card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Play,
  Square,
  RotateCcw,
  Trash2,
  Monitor,
  Activity,
  Clock,
  Cpu,
  HardDrive,
  Network,
  MoreVertical,
  CheckCircle,
  XCircle,
  Pause,
  AlertTriangle,
  Loader2,
  Camera,
  Settings,
  Zap,
  Wifi,
  Database
} from 'lucide-react';
import { MicroVM } from '@/types/vm';

interface VMCardProps {
  vm: MicroVM;
  onStart: (id: string) => void;
  onStop: (id: string) => void;
  onRestart: (id: string) => void;
  onRemove: (id: string) => void;
  onConnect: (vm: MicroVM) => void;
  onSnapshot: (vm: MicroVM) => void;
  onConfigure: (vm: MicroVM) => void;
  onViewMetrics: (vm: MicroVM) => void;
  index?: number;
}

export const VMCard: React.FC<VMCardProps> = ({
  vm,
  onStart,
  onStop,
  onRestart,
  onRemove,
  onConnect,
  onSnapshot,
  onConfigure,
  onViewMetrics,
  index = 0
}) => {
  const getStatusColor = (status: MicroVM['status']) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'stopped': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'starting': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'stopping': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getStatusIcon = (status: MicroVM['status']) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />;
      case 'stopped': return <XCircle className="h-4 w-4" />;
      case 'starting': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'stopping': return <Loader2 className="h-4 w-4 animate-spin" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getOSIcon = (osType?: string) => {
    switch (osType) {
      case 'ubuntu': return '🐧';
      case 'debian': return '🌀';
      case 'alpine': return '🏔️';
      case 'windows': return '🪟';
      default: return '💻';
    }
  };

  // Mock resource usage data
  const cpuUsage = vm.status === 'running' ? Math.floor(Math.random() * 80) + 10 : 0;
  const memoryUsage = vm.status === 'running' ? Math.floor(Math.random() * 70) + 20 : 0;
  const diskUsage = Math.floor(Math.random() * 60) + 15;

  const isTransitioning = vm.status === 'starting' || vm.status === 'stopping';

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
    >
      <GlassCard variant="glass" animate className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1 min-w-0 flex-1">
              <div className="flex items-center gap-2">
                <span className="text-lg">{getOSIcon(vm.osType)}</span>
                <CardTitle className="text-lg font-semibold truncate" title={vm.name}>
                  {vm.name}
                </CardTitle>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Network className="h-3 w-3" />
                <span>{vm.ipAddress || 'No IP assigned'}</span>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 shrink-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {vm.status === 'stopped' && (
                  <DropdownMenuItem onClick={() => onStart(vm.id)}>
                    <Play className="h-4 w-4 mr-2" />
                    Start
                  </DropdownMenuItem>
                )}
                {vm.status === 'running' && (
                  <>
                    <DropdownMenuItem onClick={() => onStop(vm.id)}>
                      <Square className="h-4 w-4 mr-2" />
                      Stop
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onRestart(vm.id)}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Restart
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onSnapshot(vm)}>
                      <Camera className="h-4 w-4 mr-2" />
                      Create Snapshot
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onViewMetrics(vm)}>
                  <Activity className="h-4 w-4 mr-2" />
                  View Metrics
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onConfigure(vm)}>
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onRemove(vm.id)}
                  className="text-destructive focus:text-destructive"
                  disabled={vm.status === 'running'}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="flex items-center gap-2 mt-3">
            <Badge className={`${getStatusColor(vm.status)} border-0`}>
              {getStatusIcon(vm.status)}
              <span className="ml-1 capitalize">{vm.status}</span>
            </Badge>
            {vm.status === 'running' && (
              <Badge variant="outline" className="text-xs">
                <Zap className="h-3 w-3 mr-1" />
                Active
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Resource Specifications */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Cpu className="h-4 w-4 text-muted-foreground" />
              <span>{vm.cpu} vCPU</span>
            </div>
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-muted-foreground" />
              <span>{vm.memory}MB RAM</span>
            </div>
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-muted-foreground" />
              <span>{vm.diskSize}GB Disk</span>
            </div>
            <div className="flex items-center gap-2">
              <Monitor className="h-4 w-4 text-muted-foreground" />
              <span>VNC :{vm.vncPort}</span>
            </div>
          </div>

          {/* Resource Usage (only show when running) */}
          {vm.status === 'running' && (
            <div className="space-y-3">
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>CPU Usage</span>
                  <span>{cpuUsage}%</span>
                </div>
                <Progress value={cpuUsage} className="h-1.5" />
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Memory Usage</span>
                  <span>{memoryUsage}%</span>
                </div>
                <Progress value={memoryUsage} className="h-1.5" />
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Disk Usage</span>
                  <span>{diskUsage}%</span>
                </div>
                <Progress value={diskUsage} className="h-1.5" />
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3" />
              <span>Created {vm.createdAt.toLocaleDateString()}</span>
            </div>
            {vm.lastAccessed && (
              <div className="flex items-center gap-2">
                <Activity className="h-3 w-3" />
                <span>Last accessed {vm.lastAccessed.toLocaleDateString()}</span>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 pt-2">
            {vm.status === 'stopped' && (
              <AnimatedButton
                size="sm"
                onClick={() => onStart(vm.id)}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-1" />
                Start
              </AnimatedButton>
            )}
            
            {vm.status === 'running' && (
              <>
                <AnimatedButton
                  size="sm"
                  onClick={() => onConnect(vm)}
                  className="flex-1"
                >
                  <Monitor className="h-4 w-4 mr-1" />
                  Connect
                </AnimatedButton>
                <AnimatedButton
                  size="sm"
                  variant="outline"
                  onClick={() => onStop(vm.id)}
                >
                  <Square className="h-4 w-4" />
                </AnimatedButton>
              </>
            )}

            {isTransitioning && (
              <AnimatedButton size="sm" disabled className="flex-1">
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                {vm.status === 'starting' ? 'Starting...' : 'Stopping...'}
              </AnimatedButton>
            )}
          </div>
        </CardContent>
      </GlassCard>
    </motion.div>
  );
};
