'use client';

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

// Enhanced Card with glassmorphism effect
interface GlassCardProps extends React.ComponentProps<"div"> {
  variant?: "default" | "glass" | "gradient";
  animate?: boolean;
}

export function GlassCard({ 
  className, 
  variant = "default", 
  animate = false,
  children,
  ...props 
}: GlassCardProps) {
  const baseClasses = "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm";
  
  const variantClasses = {
    default: "",
    glass: "backdrop-blur-md bg-white/10 dark:bg-black/10 border-white/20 dark:border-white/10 shadow-xl",
    gradient: "bg-gradient-to-br from-primary/5 via-background to-secondary/5 border-primary/20"
  };

  const CardComponent = animate ? motion.div : "div";
  const motionProps = animate ? {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
    transition: { duration: 0.3, ease: "easeOut" },
    whileHover: { y: -2, transition: { duration: 0.2 } }
  } : {};

  return (
    <CardComponent
      data-slot="card"
      className={cn(
        baseClasses,
        variantClasses[variant],
        className
      )}
      {...(animate ? motionProps : {})}
      {...props}
    >
      {children}
    </CardComponent>
  );
}

// Enhanced Button with animations
interface AnimatedButtonProps extends React.ComponentProps<"button"> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  animate?: boolean;
  loading?: boolean;
}

export function AnimatedButton({
  className,
  variant = "default",
  size = "default",
  animate = true,
  loading = false,
  children,
  disabled,
  ...props
}: AnimatedButtonProps) {
  const baseClasses = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive";
  
  const variantClasses = {
    default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
    destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
    outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
    secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
    ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
    link: "text-primary underline-offset-4 hover:underline",
  };

  const sizeClasses = {
    default: "h-9 px-4 py-2 has-[>svg]:px-3",
    sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
    lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
    icon: "size-9",
  };

  const ButtonComponent = animate ? motion.button : "button";
  const motionProps = animate ? {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: { duration: 0.1 }
  } : {};

  return (
    <ButtonComponent
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      disabled={disabled || loading}
      {...(animate ? motionProps : {})}
      {...props}
    >
      {loading && (
        <motion.div
          className="mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
      )}
      {children}
    </ButtonComponent>
  );
}

// Gradient Background Component
interface GradientBackgroundProps {
  variant?: "primary" | "secondary" | "accent";
  animated?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export function GradientBackground({
  variant = "primary",
  animated = true,
  className,
  children
}: GradientBackgroundProps) {
  const gradients = {
    primary: "bg-gradient-to-br from-primary/20 via-primary/5 to-secondary/20",
    secondary: "bg-gradient-to-br from-secondary/20 via-accent/5 to-primary/20",
    accent: "bg-gradient-to-br from-accent/20 via-secondary/5 to-primary/20"
  };

  return (
    <div className={cn("relative overflow-hidden", className)}>
      <div className={cn(
        "absolute inset-0",
        gradients[variant],
        animated && "animate-gradient-x"
      )} />
      {animated && (
        <div className="absolute inset-0 opacity-30">
          <motion.div
            className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent"
            animate={{
              x: ["-100%", "200%"],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        </div>
      )}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}

// Floating Particles Component
export function FloatingParticles({ count = 20 }: { count?: number }) {
  const particles = Array.from({ length: count }, (_, i) => i);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle}
          className="absolute w-1 h-1 bg-primary/20 rounded-full"
          initial={{
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
          }}
          animate={{
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "linear",
          }}
        />
      ))}
    </div>
  );
}
