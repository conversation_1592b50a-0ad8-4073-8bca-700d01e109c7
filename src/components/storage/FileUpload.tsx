'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppwriteStorage } from '@/hooks/useAppwriteStorage';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  File, 
  Image, 
  Video, 
  Music, 
  FileText, 
  X, 
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface UploadResult {
  success: boolean;
  data?: {
    file: {
      $id: string;
    };
    url: string;
  };
  error?: {
    message: string;
  };
}

interface FileUploadProps {
  bucketId: string;
  onUploadComplete?: (files: Array<{ id: string; name: string; url: string }>) => void;
  onUploadError?: (error: string) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedFileTypes?: string[];
  className?: string;
}

interface UploadingFile {
  file: File;
  id: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  url?: string;
}

export function FileUpload({
  bucketId,
  onUploadComplete,
  onUploadError,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB default
  acceptedFileTypes = ['image/*', 'video/*', 'audio/*', 'application/pdf', 'text/*'],
  className = '',
}: FileUploadProps) {
  const { uploadFile, isLoading, error, clearError } = useAppwriteStorage();
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  const getFileIcon = (file: File) => {
    const type = file.type;
    if (type.startsWith('image/')) return <Image className="h-6 w-6" />;
    if (type.startsWith('video/')) return <Video className="h-6 w-6" />;
    if (type.startsWith('audio/')) return <Music className="h-6 w-6" />;
    if (type === 'application/pdf' || type.startsWith('text/')) return <FileText className="h-6 w-6" />;
    return <File className="h-6 w-6" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleUpload = useCallback(async (files: File[]) => {
    clearError();
    
    // Create uploading file entries
    const newUploadingFiles: UploadingFile[] = files.map(file => ({
      file,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      progress: 0,
      status: 'uploading' as const,
    }));

    setUploadingFiles(prev => [...prev, ...newUploadingFiles]);

    const completedFiles: Array<{ id: string; name: string; url: string }> = [];
    let hasErrors = false;

    // Upload files one by one
    for (const uploadingFile of newUploadingFiles) {
      try {
        const uploadResult = await uploadFile({
          bucketId,
          file: uploadingFile.file,
          onProgress: (progress) => {
            setUploadingFiles(prev => 
              prev.map(f => 
                f.id === uploadingFile.id 
                  ? { ...f, progress }
                  : f
              )
            );
          },
        });
        
        const result: UploadResult = {
          success: !!uploadResult && typeof uploadResult === 'object' && !('error' in (uploadResult as object)),
          data: uploadResult as { file: { $id: string }; url: string },
          error: (uploadResult as { error?: { message: string } }).error
        };

        if (result.success && result.data) {
          setUploadingFiles(prev => 
            prev.map(f => 
              f.id === uploadingFile.id 
                ? { ...f, status: 'completed', progress: 100, url: result.data!.url }
                : f
            )
          );

          completedFiles.push({
            id: result.data.file.$id,
            name: uploadingFile.file.name,
            url: result.data.url,
          });
        } else {
          hasErrors = true;
          setUploadingFiles(prev => 
            prev.map(f => 
              f.id === uploadingFile.id 
                ? { ...f, status: 'error', error: result.error?.message || 'Upload failed' }
                : f
            )
          );
        }
      } catch (error) {
        hasErrors = true;
        setUploadingFiles(prev => 
          prev.map(f => 
            f.id === uploadingFile.id 
              ? { ...f, status: 'error', error: 'Upload failed' }
              : f
          )
        );
      }
    }

    // Call callbacks
    if (completedFiles.length > 0) {
      onUploadComplete?.(completedFiles);
    }

    if (hasErrors) {
      onUploadError?.('Some files failed to upload');
    }
  }, [bucketId, uploadFile, clearError, onUploadComplete, onUploadError]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      );
      onUploadError?.(errors.join('\n'));
      return;
    }

    // Check file count limit
    if (uploadingFiles.length + acceptedFiles.length > maxFiles) {
      onUploadError?.(
        `Cannot upload more than ${maxFiles} files. Currently have ${uploadingFiles.length} files.`
      );
      return;
    }

    handleUpload(acceptedFiles);
  }, [uploadingFiles.length, maxFiles, handleUpload, onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxSize,
    multiple: maxFiles > 1,
  });

  const removeFile = (fileId: string) => {
    setUploadingFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearCompleted = () => {
    setUploadingFiles(prev => prev.filter(f => f.status !== 'completed'));
  };

  const hasCompletedFiles = uploadingFiles.some(f => f.status === 'completed');
  const hasErrors = uploadingFiles.some(f => f.status === 'error');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
              }
              ${isLoading ? 'pointer-events-none opacity-50' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            <motion.div
              animate={isDragActive ? { scale: 1.05 } : { scale: 1 }}
              transition={{ duration: 0.2 }}
              className="space-y-4"
            >
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              
              <div>
                <p className="text-lg font-medium">
                  {isDragActive ? 'Drop files here' : 'Upload files'}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Drag and drop files here, or click to select files
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Max {maxFiles} files, up to {formatFileSize(maxSize)} each
                </p>
              </div>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Uploading Files */}
      <AnimatePresence>
        {uploadingFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">
                Files ({uploadingFiles.length})
              </h3>
              {hasCompletedFiles && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearCompleted}
                  className="text-xs"
                >
                  Clear completed
                </Button>
              )}
            </div>

            {uploadingFiles.map((uploadingFile) => (
              <motion.div
                key={uploadingFile.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-muted/50 rounded-lg p-3"
              >
                <div className="flex items-center gap-3">
                  <div className="text-muted-foreground">
                    {getFileIcon(uploadingFile.file)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {uploadingFile.file.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(uploadingFile.file.size)}
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    {uploadingFile.status === 'uploading' && (
                      <>
                        <div className="w-20">
                          <Progress value={uploadingFile.progress} className="h-2" />
                        </div>
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </>
                    )}
                    
                    {uploadingFile.status === 'completed' && (
                      <Check className="h-4 w-4 text-green-500" />
                    )}
                    
                    {uploadingFile.status === 'error' && (
                      <AlertCircle className="h-4 w-4 text-destructive" />
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(uploadingFile.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {uploadingFile.status === 'uploading' && (
                  <div className="mt-2">
                    <Progress value={uploadingFile.progress} className="h-1" />
                  </div>
                )}

                {uploadingFile.status === 'error' && uploadingFile.error && (
                  <p className="text-xs text-destructive mt-1">
                    {uploadingFile.error}
                  </p>
                )}
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
