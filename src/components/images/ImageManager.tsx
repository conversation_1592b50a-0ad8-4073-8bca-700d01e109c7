'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AnimatedButton } from '@/components/ui/enhanced-card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Download,
  RefreshCw,
  Image as ImageIcon,
  AlertTriangle,
  Loader2,
  Search,
  Upload
} from 'lucide-react';
import { ImageCard } from './ImageCard';

interface DockerImage {
  id: string;
  repoTags: string[];
  size: number;
  created: Date;
  labels?: { [key: string]: string };
  architecture?: string;
  os?: string;
}

interface ImageManagerProps {
  onCreateContainer?: (image: DockerImage) => void;
}

export const ImageManager: React.FC<ImageManagerProps> = ({ onCreateContainer }) => {
  // Mock data - replace with actual Docker API calls
  const [images, setImages] = useState<DockerImage[]>([
    {
      id: 'sha256:abc123def456',
      repoTags: ['nginx:latest', 'nginx:1.21'],
      size: 142000000,
      created: new Date('2024-01-15'),
      architecture: 'amd64',
      os: 'linux',
      labels: {
        'maintainer': 'NGINX Docker Maintainers',
        'version': '1.21.6'
      }
    },
    {
      id: 'sha256:def456ghi789',
      repoTags: ['node:18-alpine'],
      size: 89000000,
      created: new Date('2024-01-10'),
      architecture: 'amd64',
      os: 'linux'
    },
    {
      id: 'sha256:ghi789jkl012',
      repoTags: ['postgres:15'],
      size: 374000000,
      created: new Date('2024-01-08'),
      architecture: 'amd64',
      os: 'linux',
      labels: {
        'maintainer': 'PostgreSQL Docker Maintainers',
        'version': '15.2'
      }
    }
  ]);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [pullDialogOpen, setPullDialogOpen] = useState(false);
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [inspectDialogOpen, setInspectDialogOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<DockerImage | null>(null);
  const [pullImageName, setPullImageName] = useState('');
  const [newTag, setNewTag] = useState('');
  const [pulling, setPulling] = useState(false);

  // Filter images based on search term
  const filteredImages = useMemo(() => {
    return images.filter(image => {
      const searchLower = searchTerm.toLowerCase();
      return image.repoTags.some(tag => tag.toLowerCase().includes(searchLower)) ||
             image.id.toLowerCase().includes(searchLower);
    });
  }, [images, searchTerm]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // In real implementation, call Docker API to list images
    } finally {
      setRefreshing(false);
    }
  };

  const handlePullImage = async () => {
    if (!pullImageName.trim()) return;
    
    setPulling(true);
    try {
      // Simulate image pull
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Add new image to list (mock)
      const newImage: DockerImage = {
        id: `sha256:${Math.random().toString(36).substring(2, 15)}`,
        repoTags: [pullImageName],
        size: Math.floor(Math.random() * 500000000),
        created: new Date(),
        architecture: 'amd64',
        os: 'linux'
      };
      
      setImages(prev => [newImage, ...prev]);
      setPullImageName('');
      setPullDialogOpen(false);
    } catch (error) {
      console.error('Failed to pull image:', error);
    } finally {
      setPulling(false);
    }
  };

  const handleRemoveImage = async (imageId: string) => {
    try {
      // Simulate image removal
      await new Promise(resolve => setTimeout(resolve, 1000));
      setImages(prev => prev.filter(img => img.id !== imageId));
      setImageToDelete(null);
    } catch (error) {
      console.error('Failed to remove image:', error);
    }
  };

  const handleTagImage = async () => {
    if (!selectedImage || !newTag.trim()) return;
    
    try {
      // Simulate tagging
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setImages(prev => prev.map(img => 
        img.id === selectedImage.id 
          ? { ...img, repoTags: [...img.repoTags, newTag] }
          : img
      ));
      
      setNewTag('');
      setTagDialogOpen(false);
      setSelectedImage(null);
    } catch (error) {
      console.error('Failed to tag image:', error);
    }
  };

  const handleCreateContainer = (image: DockerImage) => {
    if (onCreateContainer) {
      onCreateContainer(image);
    } else {
      // Default behavior - could open a create container dialog
      console.log('Create container from image:', image.repoTags[0]);
    }
  };

  const handleInspectImage = (image: DockerImage) => {
    setSelectedImage(image);
    setInspectDialogOpen(true);
  };

  const handleTagImageDialog = (image: DockerImage) => {
    setSelectedImage(image);
    setTagDialogOpen(true);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Image Management</h2>
          <p className="text-muted-foreground">
            Manage your Docker images, pull new ones, and create containers
          </p>
        </div>
        <div className="flex items-center gap-3">
          <AnimatedButton
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            loading={refreshing}
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </AnimatedButton>
          <AnimatedButton onClick={() => setPullDialogOpen(true)}>
            <Download className="h-4 w-4" />
            Pull Image
          </AnimatedButton>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search images by name or ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-lg">Loading images...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center"
        >
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-destructive mb-2">Error Loading Images</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <AnimatedButton onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </AnimatedButton>
        </motion.div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredImages.length === 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12"
        >
          <ImageIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">
            {images.length === 0 ? 'No Images Found' : 'No Matching Images'}
          </h3>
          <p className="text-muted-foreground mb-6">
            {images.length === 0 
              ? 'Pull your first Docker image to get started'
              : 'Try adjusting your search criteria'
            }
          </p>
          {images.length === 0 && (
            <AnimatedButton onClick={() => setPullDialogOpen(true)}>
              <Download className="h-4 w-4 mr-2" />
              Pull Your First Image
            </AnimatedButton>
          )}
        </motion.div>
      )}

      {/* Images Grid */}
      {!loading && !error && filteredImages.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <AnimatePresence mode="popLayout">
            {filteredImages.map((image, index) => (
              <ImageCard
                key={image.id}
                image={image}
                index={index}
                onCreateContainer={handleCreateContainer}
                onRemove={(id) => setImageToDelete(id)}
                onTag={handleTagImageDialog}
                onInspect={handleInspectImage}
              />
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Pull Image Dialog */}
      <Dialog open={pullDialogOpen} onOpenChange={setPullDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Pull Docker Image
            </DialogTitle>
            <DialogDescription>
              Enter the name and tag of the Docker image you want to pull
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Image Name</label>
              <Input
                placeholder="nginx:latest"
                value={pullImageName}
                onChange={(e) => setPullImageName(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Examples: nginx:latest, node:18-alpine, postgres:15
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPullDialogOpen(false)}>
              Cancel
            </Button>
            <AnimatedButton
              onClick={handlePullImage}
              disabled={!pullImageName.trim() || pulling}
              loading={pulling}
            >
              Pull Image
            </AnimatedButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Tag Image Dialog */}
      <Dialog open={tagDialogOpen} onOpenChange={setTagDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Add Tag to Image
            </DialogTitle>
            <DialogDescription>
              Add a new tag to {selectedImage?.repoTags[0]}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">New Tag</label>
              <Input
                placeholder="my-app:v1.0"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setTagDialogOpen(false)}>
              Cancel
            </Button>
            <AnimatedButton
              onClick={handleTagImage}
              disabled={!newTag.trim()}
            >
              Add Tag
            </AnimatedButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Inspect Image Dialog */}
      <Dialog open={inspectDialogOpen} onOpenChange={setInspectDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Image Details: {selectedImage?.repoTags[0]}
            </DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-4">
            {selectedImage && (
              <>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">ID:</span>
                    <p className="font-mono text-xs break-all">{selectedImage.id}</p>
                  </div>
                  <div>
                    <span className="font-medium">Size:</span>
                    <p>{(selectedImage.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                  <div>
                    <span className="font-medium">Created:</span>
                    <p>{selectedImage.created.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium">Architecture:</span>
                    <p>{selectedImage.architecture || 'N/A'}</p>
                  </div>
                </div>

                {selectedImage.repoTags.length > 0 && (
                  <div>
                    <span className="font-medium">Tags:</span>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedImage.repoTags.map((tag, idx) => (
                        <span key={idx} className="px-2 py-1 bg-secondary rounded text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {selectedImage.labels && Object.keys(selectedImage.labels).length > 0 && (
                  <div>
                    <span className="font-medium">Labels:</span>
                    <div className="mt-2 space-y-1 max-h-40 overflow-y-auto">
                      {Object.entries(selectedImage.labels).map(([key, value]) => (
                        <div key={key} className="text-xs">
                          <span className="font-mono text-muted-foreground">{key}:</span> {value}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setInspectDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!imageToDelete} onOpenChange={() => setImageToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Image</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this image? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => imageToDelete && handleRemoveImage(imageToDelete)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
