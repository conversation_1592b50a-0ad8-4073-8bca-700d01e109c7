'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GlassCard, AnimatedButton } from '@/components/ui/enhanced-card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Play,
  Trash2,
  Download,
  Tag,
  Clock,
  HardDrive,
  MoreVertical,
  Image as ImageIcon,
  Layers,
  Hash,
  Calendar,
  FileText,
  Copy
} from 'lucide-react';

interface DockerImage {
  id: string;
  repoTags: string[];
  size: number;
  created: Date;
  labels?: { [key: string]: string };
  architecture?: string;
  os?: string;
}

interface ImageCardProps {
  image: DockerImage;
  onCreateContainer: (image: DockerImage) => void;
  onRemove: (id: string) => void;
  onTag: (image: DockerImage) => void;
  onInspect: (image: DockerImage) => void;
  index?: number;
}

export const ImageCard: React.FC<ImageCardProps> = ({
  image,
  onCreateContainer,
  onRemove,
  onTag,
  onInspect,
  index = 0
}) => {
  const formatSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getMainTag = (): string => {
    if (!image.repoTags || image.repoTags.length === 0) {
      return '<none>';
    }
    return image.repoTags[0];
  };

  const getShortId = (): string => {
    return image.id.replace('sha256:', '').substring(0, 12);
  };

  const copyImageId = async () => {
    try {
      await navigator.clipboard.writeText(image.id);
    } catch (error) {
      console.error('Failed to copy image ID:', error);
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
    >
      <GlassCard variant="glass" animate className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1 min-w-0 flex-1">
              <CardTitle className="text-lg font-semibold truncate" title={getMainTag()}>
                {getMainTag()}
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Hash className="h-3 w-3" />
                <span className="font-mono text-xs">{getShortId()}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0"
                  onClick={copyImageId}
                  title="Copy full image ID"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 shrink-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onCreateContainer(image)}>
                  <Play className="h-4 w-4 mr-2" />
                  Create Container
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onTag(image)}>
                  <Tag className="h-4 w-4 mr-2" />
                  Add Tag
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onInspect(image)}>
                  <FileText className="h-4 w-4 mr-2" />
                  Inspect
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onRemove(image.id)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Image Information */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-muted-foreground" />
              <span>{formatSize(image.size)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{image.created.toLocaleDateString()}</span>
            </div>
            {image.architecture && (
              <div className="flex items-center gap-2">
                <Layers className="h-4 w-4 text-muted-foreground" />
                <span>{image.architecture}</span>
              </div>
            )}
            {image.os && (
              <div className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4 text-muted-foreground" />
                <span>{image.os}</span>
              </div>
            )}
          </div>

          {/* Additional Tags */}
          {image.repoTags && image.repoTags.length > 1 && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Additional Tags</p>
              <div className="flex flex-wrap gap-1">
                {image.repoTags.slice(1).map((tag, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Labels */}
          {image.labels && Object.keys(image.labels).length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Labels</p>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {Object.entries(image.labels).slice(0, 3).map(([key, value]) => (
                  <div key={key} className="text-xs text-muted-foreground">
                    <span className="font-mono">{key}:</span> {value}
                  </div>
                ))}
                {Object.keys(image.labels).length > 3 && (
                  <div className="text-xs text-muted-foreground">
                    +{Object.keys(image.labels).length - 3} more labels
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex gap-2 pt-2">
            <AnimatedButton
              size="sm"
              onClick={() => onCreateContainer(image)}
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-1" />
              Run
            </AnimatedButton>
            <AnimatedButton
              size="sm"
              variant="outline"
              onClick={() => onTag(image)}
            >
              <Tag className="h-4 w-4" />
            </AnimatedButton>
            <AnimatedButton
              size="sm"
              variant="outline"
              onClick={() => onInspect(image)}
            >
              <FileText className="h-4 w-4" />
            </AnimatedButton>
          </div>
        </CardContent>
      </GlassCard>
    </motion.div>
  );
};
