'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Package, 
  Download, 
  Trash2, 
  RefreshCw, 
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Loader2,
  Plus,
  Settings
} from 'lucide-react';
import { NodePackage, NodePackageManager } from '@/types/node-react-workspace';
import { nodeReactPackageManagerClientService, PackageSearchResult } from '@/services/client/node-react-package-manager-client';

interface NodeReactPackageManagerProps {
  workspaceId: string;
  className?: string;
  onPackageChange?: () => void;
}

const packageCategoryColors = {
  framework: 'bg-blue-500 text-white',
  ui: 'bg-purple-500 text-white',
  state: 'bg-green-500 text-white',
  database: 'bg-yellow-500 text-white',
  testing: 'bg-red-500 text-white',
  development: 'bg-gray-500 text-white',
  utility: 'bg-indigo-500 text-white',
  build: 'bg-orange-500 text-white',
};

export function NodeReactPackageManager({ 
  workspaceId, 
  className = '',
  onPackageChange 
}: NodeReactPackageManagerProps) {
  const [packages, setPackages] = useState<NodePackage[]>([]);
  const [searchResults, setSearchResults] = useState<PackageSearchResult[]>([]);
  const [packageManagers, setPackageManagers] = useState<NodePackageManager[]>([]);
  const [selectedManager, setSelectedManager] = useState<string>('npm');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [installing, setInstalling] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('installed');

  useEffect(() => {
    loadPackageManagers();
    loadInstalledPackages();
  }, [workspaceId]);

  const loadPackageManagers = () => {
    // Available package managers - hardcoded for client-side
    const managers: NodePackageManager[] = ['npm', 'yarn', 'pnpm'];
    setPackageManagers(managers);
  };

  const loadInstalledPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      const installedPackages = await nodeReactPackageManagerClientService.getInstalledPackages(workspaceId);
      setPackages(installedPackages);
    } catch (err) {
      console.error('Error loading packages:', err);
      setError('Failed to load installed packages');
    } finally {
      setLoading(false);
    }
  };

  const searchPackages = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const results = await nodeReactPackageManagerClientService.searchPackages(searchQuery, 20);
      
      // Mark installed packages
      const resultsWithInstallStatus = results.map(result => ({
        ...result,
        isInstalled: packages.some(pkg => pkg.name === result.name),
        installedVersion: packages.find(pkg => pkg.name === result.name)?.version,
      }));
      
      setSearchResults(resultsWithInstallStatus);
    } catch (err) {
      console.error('Error searching packages:', err);
      setError('Failed to search packages');
    } finally {
      setLoading(false);
    }
  };

  const installPackage = async (packageName: string, version?: string, isDev: boolean = false) => {
    try {
      setInstalling(prev => new Set(prev).add(packageName));
      setError(null);
      
      const result = await nodeReactPackageManagerClientService.installPackage(
        workspaceId,
        packageName,
        selectedManager as 'npm' | 'yarn' | 'pnpm',
        { version, isDev }
      );
      
      if (result.success) {
        await loadInstalledPackages();
        // Update search results to show installed status
        setSearchResults(prev => prev.map(pkg => 
          pkg.name === packageName 
            ? { ...pkg, isInstalled: true, installedVersion: version || pkg.version }
            : pkg
        ));
        onPackageChange?.();
      } else {
        throw new Error(result.error || 'Failed to install package');
      }
    } catch (err) {
      console.error('Error installing package:', err);
      setError(err instanceof Error ? err.message : 'Failed to install package');
    } finally {
      setInstalling(prev => {
        const newSet = new Set(prev);
        newSet.delete(packageName);
        return newSet;
      });
    }
  };

  const uninstallPackage = async (packageName: string) => {
    try {
      setInstalling(prev => new Set(prev).add(packageName));
      setError(null);
      
      const result = await nodeReactPackageManagerClientService.uninstallPackage(
        workspaceId,
        packageName,
        selectedManager as 'npm' | 'yarn' | 'pnpm'
      );
      
      if (result.success) {
        await loadInstalledPackages();
        // Update search results to show uninstalled status
        setSearchResults(prev => prev.map(pkg => 
          pkg.name === packageName 
            ? { ...pkg, isInstalled: false, installedVersion: undefined }
            : pkg
        ));
        onPackageChange?.();
      } else {
        throw new Error(result.error || 'Failed to uninstall package');
      }
    } catch (err) {
      console.error('Error uninstalling package:', err);
      setError(err instanceof Error ? err.message : 'Failed to uninstall package');
    } finally {
      setInstalling(prev => {
        const newSet = new Set(prev);
        newSet.delete(packageName);
        return newSet;
      });
    }
  };

  const filteredPackages = packages.filter(pkg => {
    const matchesCategory = selectedCategory === 'all' || pkg.category === selectedCategory;
    const matchesSearch = pkg.name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const categories = Array.from(new Set(packages.map(pkg => pkg.category)));

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">Package Manager</h3>
          <p className="text-muted-foreground">Manage your Node.js dependencies</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedManager} onValueChange={setSelectedManager}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {packageManagers.map(manager => (
                <SelectItem key={manager.name} value={manager.name}>
                  {manager.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={loadInstalledPackages}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="installed">Installed ({packages.length})</TabsTrigger>
          <TabsTrigger value="search">Search & Install</TabsTrigger>
        </TabsList>

        <TabsContent value="installed" className="space-y-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Filter installed packages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-3">
            {loading ? (
              <div className="text-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                <p className="mt-2 text-muted-foreground">Loading packages...</p>
              </div>
            ) : filteredPackages.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No packages found</p>
              </div>
            ) : (
              filteredPackages.map((pkg) => (
                <Card key={pkg.name} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Package className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{pkg.name}</span>
                          <Badge variant="outline">{pkg.version}</Badge>
                          <Badge className={packageCategoryColors[pkg.category]}>
                            {pkg.category}
                          </Badge>
                          {pkg.isDev && (
                            <Badge variant="secondary">dev</Badge>
                          )}
                        </div>
                        {pkg.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {pkg.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => uninstallPackage(pkg.name)}
                        disabled={installing.has(pkg.name)}
                      >
                        {installing.has(pkg.name) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="search" className="space-y-4">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search npm packages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && searchPackages()}
                className="pl-10"
              />
            </div>
            <Button onClick={searchPackages} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
            </Button>
          </div>

          <div className="grid gap-3">
            {searchResults.length === 0 && searchQuery && !loading && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No packages found for "{searchQuery}"</p>
              </div>
            )}
            
            {searchResults.map((pkg) => (
              <Card key={pkg.name} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Package className="h-5 w-5 text-muted-foreground" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{pkg.name}</span>
                        <Badge variant="outline">{pkg.version}</Badge>
                        {pkg.isInstalled && (
                          <Badge className="bg-green-500 text-white">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Installed
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {pkg.description}
                      </p>
                      {pkg.keywords.length > 0 && (
                        <div className="flex gap-1 mt-2">
                          {pkg.keywords.slice(0, 3).map(keyword => (
                            <Badge key={keyword} variant="secondary" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {pkg.homepage && (
                      <Button variant="ghost" size="sm" asChild>
                        <a href={pkg.homepage} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                    {pkg.isInstalled ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => uninstallPackage(pkg.name)}
                        disabled={installing.has(pkg.name)}
                      >
                        {installing.has(pkg.name) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    ) : (
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          onClick={() => installPackage(pkg.name, pkg.version, false)}
                          disabled={installing.has(pkg.name)}
                        >
                          {installing.has(pkg.name) ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Plus className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => installPackage(pkg.name, pkg.version, true)}
                          disabled={installing.has(pkg.name)}
                          title="Install as dev dependency"
                        >
                          Dev
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
