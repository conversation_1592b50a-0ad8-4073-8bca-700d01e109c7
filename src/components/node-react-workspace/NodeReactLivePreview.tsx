'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Square, 
  RefreshCw, 
  ExternalLink, 
  Terminal,
  AlertCircle,
  CheckCircle,
  Loader2,
  Settings,
  Monitor,
  Code,
  Globe
} from 'lucide-react';
import { NodeLivePreview, NodeFramework } from '@/types/node-react-workspace';
import { nodeReactWorkspaceClientService } from '@/services/client/node-react-workspace-client';

interface NodeReactLivePreviewProps {
  workspaceId: string;
  projectPath?: string;
  framework?: NodeFramework;
  className?: string;
}

const frameworkPorts: Record<NodeFramework, number> = {
  nextjs: 3000,
  react: 5173,
  express: 3001,
  nestjs: 3000,
  vue: 5173,
  angular: 4200,
  nuxt: 3000,
  svelte: 5173,
  remix: 3000,
  gatsby: 8000,
};

const frameworkCommands: Record<NodeFramework, string> = {
  nextjs: 'npm run dev',
  react: 'npm run dev',
  express: 'npm run dev',
  nestjs: 'npm run dev',
  vue: 'npm run dev',
  angular: 'ng serve --host 0.0.0.0',
  nuxt: 'npm run dev',
  svelte: 'npm run dev',
  remix: 'npm run dev',
  gatsby: 'npm run develop',
};

export function NodeReactLivePreview({ 
  workspaceId, 
  projectPath = '.',
  framework,
  className = '' 
}: NodeReactLivePreviewProps) {
  const [preview, setPreview] = useState<NodeLivePreview | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customPort, setCustomPort] = useState<string>('');
  const [customCommand, setCustomCommand] = useState<string>('');
  const [selectedFramework, setSelectedFramework] = useState<NodeFramework>(framework || 'react');
  const [logs, setLogs] = useState<string[]>([]);
  const [buildLogs, setBuildLogs] = useState<string[]>([]);
  const logsEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadPreviewStatus();
    const interval = setInterval(loadPreviewStatus, 5000); // Poll every 5 seconds
    return () => clearInterval(interval);
  }, [workspaceId]);

  useEffect(() => {
    if (framework) {
      setSelectedFramework(framework);
      setCustomCommand(frameworkCommands[framework]);
      setCustomPort(frameworkPorts[framework].toString());
    }
  }, [framework]);

  useEffect(() => {
    scrollToBottom();
  }, [logs, buildLogs]);

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadPreviewStatus = async () => {
    try {
      const previewStatus = await nodeReactWorkspaceClientService.getLivePreview(workspaceId);
      setPreview(previewStatus);

      if (previewStatus?.logs) {
        setLogs(previewStatus.logs);
      }

      if (previewStatus?.buildLogs) {
        setBuildLogs(previewStatus.buildLogs);
      }
    } catch (err) {
      console.error('Error loading preview status:', err);
    }
  };

  const startPreview = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const port = customPort ? parseInt(customPort) : frameworkPorts[selectedFramework];
      const command = customCommand || frameworkCommands[selectedFramework];
      
      const newPreview = await nodeReactWorkspaceClientService.startLivePreview(
        workspaceId,
        projectPath,
        selectedFramework,
        port,
        command
      );
      
      setPreview(newPreview);
    } catch (err) {
      console.error('Error starting preview:', err);
      setError(err instanceof Error ? err.message : 'Failed to start preview');
    } finally {
      setLoading(false);
    }
  };

  const stopPreview = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await nodeReactWorkspaceClientService.stopLivePreview(workspaceId);
      setPreview(null);
      setLogs([]);
      setBuildLogs([]);
    } catch (err) {
      console.error('Error stopping preview:', err);
      setError(err instanceof Error ? err.message : 'Failed to stop preview');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'starting': return 'bg-yellow-500';
      case 'building': return 'bg-blue-500';
      case 'stopped': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />;
      case 'starting': 
      case 'building': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'stopped': return <Square className="h-4 w-4" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      default: return <Square className="h-4 w-4" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">Live Preview</h3>
          <p className="text-muted-foreground">Run and preview your Node.js/React application</p>
        </div>
        {preview && (
          <div className="flex items-center gap-2">
            <Badge className={`${getStatusColor(preview.status)} text-white`}>
              {getStatusIcon(preview.status)}
              <span className="ml-1">{preview.status}</span>
            </Badge>
            {preview.status === 'running' && (
              <Button variant="outline" size="sm" asChild>
                <a href={preview.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Open
                </a>
              </Button>
            )}
          </div>
        )}
      </div>

      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configuration
          </CardTitle>
          <CardDescription>
            Configure your development server settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Framework</label>
              <Select value={selectedFramework} onValueChange={(value) => setSelectedFramework(value as NodeFramework)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nextjs">Next.js</SelectItem>
                  <SelectItem value="react">React</SelectItem>
                  <SelectItem value="vue">Vue.js</SelectItem>
                  <SelectItem value="angular">Angular</SelectItem>
                  <SelectItem value="svelte">Svelte</SelectItem>
                  <SelectItem value="express">Express</SelectItem>
                  <SelectItem value="nestjs">NestJS</SelectItem>
                  <SelectItem value="nuxt">Nuxt.js</SelectItem>
                  <SelectItem value="remix">Remix</SelectItem>
                  <SelectItem value="gatsby">Gatsby</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Port</label>
              <Input
                type="number"
                placeholder={frameworkPorts[selectedFramework].toString()}
                value={customPort}
                onChange={(e) => setCustomPort(e.target.value)}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Start Command</label>
            <Input
              placeholder={frameworkCommands[selectedFramework]}
              value={customCommand}
              onChange={(e) => setCustomCommand(e.target.value)}
            />
          </div>
          
          <div className="flex gap-2">
            {!preview || preview.status === 'stopped' || preview.status === 'error' ? (
              <Button onClick={startPreview} disabled={loading}>
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                Start Preview
              </Button>
            ) : (
              <Button variant="destructive" onClick={stopPreview} disabled={loading}>
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Square className="h-4 w-4 mr-2" />
                )}
                Stop Preview
              </Button>
            )}
            
            <Button variant="outline" onClick={loadPreviewStatus}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {preview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Preview Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Status</p>
                <div className="flex items-center gap-2">
                  {getStatusIcon(preview.status)}
                  <span className="capitalize">{preview.status}</span>
                </div>
              </div>
              
              <div className="space-y-1">
                <p className="text-sm font-medium">Port</p>
                <p className="text-sm text-muted-foreground">{preview.port}</p>
              </div>
              
              <div className="space-y-1">
                <p className="text-sm font-medium">URL</p>
                {preview.status === 'running' ? (
                  <a 
                    href={preview.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                  >
                    {preview.url}
                    <ExternalLink className="h-3 w-3" />
                  </a>
                ) : (
                  <p className="text-sm text-muted-foreground">{preview.url}</p>
                )}
              </div>
            </div>
            
            {preview.process && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Process ID</p>
                  <p className="text-sm text-muted-foreground">{preview.process.pid}</p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Started</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(preview.process.startTime).toLocaleString()}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {(logs.length > 0 || buildLogs.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="h-5 w-5" />
              Logs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="runtime">
              <TabsList>
                <TabsTrigger value="runtime">Runtime Logs ({logs.length})</TabsTrigger>
                {buildLogs.length > 0 && (
                  <TabsTrigger value="build">Build Logs ({buildLogs.length})</TabsTrigger>
                )}
              </TabsList>
              
              <TabsContent value="runtime">
                <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm max-h-96 overflow-y-auto">
                  {logs.length === 0 ? (
                    <p className="text-gray-500">No runtime logs yet...</p>
                  ) : (
                    logs.map((log, index) => (
                      <div key={index} className="mb-1">
                        {log}
                      </div>
                    ))
                  )}
                  <div ref={logsEndRef} />
                </div>
              </TabsContent>
              
              {buildLogs.length > 0 && (
                <TabsContent value="build">
                  <div className="bg-gray-900 text-blue-400 p-4 rounded-md font-mono text-sm max-h-96 overflow-y-auto">
                    {buildLogs.map((log, index) => (
                      <div key={index} className="mb-1">
                        {log}
                      </div>
                    ))}
                    <div ref={logsEndRef} />
                  </div>
                </TabsContent>
              )}
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
