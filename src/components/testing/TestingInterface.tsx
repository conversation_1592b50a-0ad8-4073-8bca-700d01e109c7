'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Play, 
  Square, 
  RefreshCw, 
  Bug,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  BarChart3,
  FileText,
  Settings,
  Zap,
  Target,
  Code,
  TestTube
} from 'lucide-react';
import { TestSuite, TestRun, TestFramework } from '@/services/testing/python-testing-service';
import { PythonFramework } from '@/types/python-workspace';

interface TestingInterfaceProps {
  workspaceId: string;
  framework: PythonFramework;
  className?: string;
}

const testStatusIcons = {
  pending: <Clock className="h-4 w-4 text-gray-500" />,
  running: <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />,
  passed: <CheckCircle className="h-4 w-4 text-green-500" />,
  failed: <XCircle className="h-4 w-4 text-red-500" />,
  skipped: <AlertTriangle className="h-4 w-4 text-yellow-500" />,
  error: <XCircle className="h-4 w-4 text-red-600" />,
};

const testStatusColors = {
  pending: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  running: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  passed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  skipped: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

export function TestingInterface({ 
  workspaceId, 
  framework, 
  className = '' 
}: TestingInterfaceProps) {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([]);
  const [selectedSuite, setSelectedSuite] = useState<TestSuite | null>(null);
  const [currentRun, setCurrentRun] = useState<TestRun | null>(null);
  const [selectedTestFramework, setSelectedTestFramework] = useState<TestFramework>('pytest');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTests, setSelectedTests] = useState<Set<string>>(new Set());

  // Load test suites on mount
  useEffect(() => {
    loadTestSuites();
  }, [workspaceId]);

  const loadTestSuites = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/testing/run');
      if (!response.ok) {
        throw new Error('Failed to load test suites');
      }
      
      const data = await response.json();
      if (data.success) {
        setTestSuites(data.data.testSuites || []);
      } else {
        throw new Error(data.message || 'Failed to load test suites');
      }
    } catch (err) {
      console.error('Error loading test suites:', err);
      setError(err instanceof Error ? err.message : 'Failed to load test suites');
    } finally {
      setLoading(false);
    }
  };

  const discoverTests = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/testing/discover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          framework: selectedTestFramework,
          pythonFramework: framework,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to discover tests');
      }
      
      const data = await response.json();
      if (data.success) {
        const newSuite = data.data.testSuite;
        setTestSuites(prev => [...prev.filter(s => s.id !== newSuite.id), newSuite]);
        setSelectedSuite(newSuite);
      } else {
        throw new Error(data.message || 'Failed to discover tests');
      }
    } catch (err) {
      console.error('Error discovering tests:', err);
      setError(err instanceof Error ? err.message : 'Failed to discover tests');
    } finally {
      setLoading(false);
    }
  };

  const runTests = async (testIds?: string[]) => {
    if (!selectedSuite) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/testing/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          suiteId: selectedSuite.id,
          testIds,
          options: {
            coverage: true,
            verbose: true,
          },
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to run tests');
      }
      
      const data = await response.json();
      if (data.success) {
        setCurrentRun(data.data.testRun);
        // Update suite with latest run
        const updatedSuite = { ...selectedSuite, lastRun: data.data.testRun };
        setSelectedSuite(updatedSuite);
        setTestSuites(prev => prev.map(s => s.id === updatedSuite.id ? updatedSuite : s));
      } else {
        throw new Error(data.message || 'Failed to run tests');
      }
    } catch (err) {
      console.error('Error running tests:', err);
      setError(err instanceof Error ? err.message : 'Failed to run tests');
    } finally {
      setLoading(false);
    }
  };

  const toggleTestSelection = (testId: string) => {
    setSelectedTests(prev => {
      const newSet = new Set(prev);
      if (newSet.has(testId)) {
        newSet.delete(testId);
      } else {
        newSet.add(testId);
      }
      return newSet;
    });
  };

  const selectAllTests = () => {
    if (!selectedSuite) return;
    setSelectedTests(new Set(selectedSuite.tests.map(t => t.id)));
  };

  const clearSelection = () => {
    setSelectedTests(new Set());
  };

  const getTestSummary = (suite: TestSuite) => {
    const total = suite.tests.length;
    const passed = suite.tests.filter(t => t.status === 'passed').length;
    const failed = suite.tests.filter(t => t.status === 'failed').length;
    const skipped = suite.tests.filter(t => t.status === 'skipped').length;
    
    return { total, passed, failed, skipped };
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Testing & Debugging</h2>
          <p className="text-muted-foreground">
            Run tests and debug your {framework} application
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select
            value={selectedTestFramework}
            onValueChange={(value: TestFramework) => setSelectedTestFramework(value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pytest">pytest</SelectItem>
              <SelectItem value="unittest">unittest</SelectItem>
              {framework === 'django' && (
                <SelectItem value="django-test">Django Test</SelectItem>
              )}
            </SelectContent>
          </Select>
          
          <Button
            onClick={discoverTests}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <TestTube className="h-4 w-4" />
            Discover Tests
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md"
        >
          <div className="flex items-center gap-2 text-red-800 dark:text-red-300">
            <XCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Error</span>
          </div>
          <p className="text-sm text-red-700 dark:text-red-400 mt-1">{error}</p>
        </motion.div>
      )}

      {/* Test Suites */}
      <div className="grid gap-4">
        <AnimatePresence>
          {testSuites.map((suite) => {
            const summary = getTestSummary(suite);
            const successRate = summary.total > 0 ? (summary.passed / summary.total) * 100 : 0;
            
            return (
              <motion.div
                key={suite.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <Card className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                  selectedSuite?.id === suite.id ? 'ring-2 ring-primary' : ''
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <TestTube className="h-5 w-5 text-primary" />
                        <div>
                          <h3 className="font-medium">{suite.name}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {suite.framework}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {summary.total} tests
                            </span>
                            {suite.lastRun && (
                              <Badge 
                                variant="secondary" 
                                className={`text-xs ${testStatusColors[suite.lastRun.status as keyof typeof testStatusColors] || ''}`}
                              >
                                Last run: {suite.lastRun.status}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        {/* Success Rate */}
                        <div className="text-right">
                          <div className="text-sm font-medium">
                            {successRate.toFixed(0)}% passed
                          </div>
                          <Progress value={successRate} className="w-20 h-2" />
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedSuite(suite)}
                          >
                            <Target className="h-3 w-3" />
                            Select
                          </Button>
                          
                          {selectedSuite?.id === suite.id && (
                            <Button
                              size="sm"
                              onClick={() => runTests()}
                              disabled={loading}
                            >
                              {loading ? (
                                <RefreshCw className="h-3 w-3 animate-spin" />
                              ) : (
                                <Play className="h-3 w-3" />
                              )}
                              Run All
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
        
        {testSuites.length === 0 && !loading && (
          <div className="text-center py-12">
            <TestTube className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <p className="text-lg font-medium mb-2">No test suites found</p>
            <p className="text-sm text-muted-foreground">
              Discover tests to get started with testing your application
            </p>
          </div>
        )}
      </div>

      {/* Selected Suite Details */}
      {selectedSuite && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TestTube className="h-5 w-5" />
                  {selectedSuite.name}
                </CardTitle>
                <CardDescription>
                  {selectedSuite.tests.length} tests • {selectedSuite.framework} framework
                </CardDescription>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectedTests.size === selectedSuite.tests.length ? clearSelection : selectAllTests}
                >
                  {selectedTests.size === selectedSuite.tests.length ? 'Clear All' : 'Select All'}
                </Button>
                
                <Button
                  size="sm"
                  onClick={() => runTests(selectedTests.size > 0 ? Array.from(selectedTests) : undefined)}
                  disabled={loading}
                >
                  {loading ? (
                    <RefreshCw className="h-3 w-3 animate-spin" />
                  ) : (
                    <Play className="h-3 w-3" />
                  )}
                  Run {selectedTests.size > 0 ? `Selected (${selectedTests.size})` : 'All'}
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <Tabs defaultValue="tests" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="tests">Tests</TabsTrigger>
                <TabsTrigger value="results">Results</TabsTrigger>
                <TabsTrigger value="coverage">Coverage</TabsTrigger>
              </TabsList>

              <TabsContent value="tests" className="mt-4">
                <div className="space-y-2">
                  <AnimatePresence>
                    {selectedSuite.tests.map((test) => (
                      <motion.div
                        key={test.id}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedTests.has(test.id) ? 'bg-primary/10 border-primary' : 'hover:bg-gray-50 dark:hover:bg-gray-900'
                        }`}
                        onClick={() => toggleTestSelection(test.id)}
                      >
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={selectedTests.has(test.id)}
                            onChange={() => toggleTestSelection(test.id)}
                            className="rounded"
                          />
                          
                          <div className="flex items-center gap-2">
                            {testStatusIcons[test.status]}
                            <div>
                              <div className="font-medium text-sm">{test.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {test.filePath}
                                {test.className && ` • ${test.className}`}
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Badge className={`text-xs ${testStatusColors[test.status]}`}>
                            {test.status}
                          </Badge>
                          
                          {test.duration && (
                            <span className="text-xs text-muted-foreground">
                              {test.duration}ms
                            </span>
                          )}
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Start debug session for this test
                            }}
                          >
                            <Bug className="h-3 w-3" />
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </TabsContent>

              <TabsContent value="results" className="mt-4">
                {currentRun ? (
                  <div className="space-y-4">
                    {/* Test Run Summary */}
                    <div className="grid grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {currentRun.summary.passed}
                        </div>
                        <div className="text-sm text-muted-foreground">Passed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                          {currentRun.summary.failed}
                        </div>
                        <div className="text-sm text-muted-foreground">Failed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">
                          {currentRun.summary.skipped}
                        </div>
                        <div className="text-sm text-muted-foreground">Skipped</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {currentRun.duration ? `${(currentRun.duration / 1000).toFixed(2)}s` : '-'}
                        </div>
                        <div className="text-sm text-muted-foreground">Duration</div>
                      </div>
                    </div>

                    {/* Test Results */}
                    <div className="space-y-2">
                      {currentRun.results.map((result, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {testStatusIcons[result.status]}
                              <span className="font-medium">{result.testId}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className={`text-xs ${testStatusColors[result.status]}`}>
                                {result.status}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {result.duration}ms
                              </span>
                            </div>
                          </div>
                          
                          {result.error && (
                            <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded text-sm">
                              <div className="font-medium text-red-800 dark:text-red-300">
                                {result.error.type}: {result.error.message}
                              </div>
                              {result.error.traceback && (
                                <pre className="mt-1 text-xs text-red-700 dark:text-red-400 overflow-x-auto">
                                  {result.error.traceback.join('\n')}
                                </pre>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-lg font-medium mb-2">No test results yet</p>
                    <p className="text-sm text-muted-foreground">
                      Run tests to see detailed results and performance metrics
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="coverage" className="mt-4">
                {currentRun?.coverage ? (
                  <div className="space-y-4">
                    {/* Overall Coverage */}
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">Overall Coverage</h4>
                      <div className="flex items-center gap-4">
                        <div className="flex-1">
                          <Progress value={currentRun.coverage.overall.percentage} className="h-3" />
                        </div>
                        <div className="text-sm font-medium">
                          {currentRun.coverage.overall.percentage.toFixed(1)}%
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {currentRun.coverage.overall.covered} of {currentRun.coverage.overall.lines} lines covered
                      </div>
                    </div>

                    {/* File Coverage */}
                    <div className="space-y-2">
                      <h4 className="font-medium">File Coverage</h4>
                      {currentRun.coverage.files.map((file, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-sm">{file.file}</span>
                            <span className="text-sm">{file.percentage.toFixed(1)}%</span>
                          </div>
                          <Progress value={file.percentage} className="h-2" />
                          <div className="text-xs text-muted-foreground mt-1">
                            {file.covered} of {file.lines} lines covered
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-lg font-medium mb-2">No coverage data</p>
                    <p className="text-sm text-muted-foreground">
                      Run tests with coverage enabled to see code coverage metrics
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
