'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search,
  Code,
  Database,
  Cloud,
  Smartphone,
  Palette,
  Server,
  Gamepad2,
  Brain,
  FileText,
  Minimize2,
  Zap,
  Settings,
  ChevronRight,
} from 'lucide-react';

import { WORKSPACE_LAYOUTS } from './components/WorkspaceLayout';

// Workspace metadata with icons and descriptions
const WORKSPACE_METADATA = {
  default: {
    icon: Code,
    category: 'General',
    description: 'Standard development environment with file explorer, editor, and terminal',
    tags: ['beginner', 'general', 'basic'],
    color: 'bg-blue-500',
  },
  aiWorkspace: {
    icon: Brain,
    category: 'AI-Powered',
    description: 'AI-enhanced workspace with intelligent assistance and chat',
    tags: ['ai', 'assistant', 'smart'],
    color: 'bg-purple-500',
  },
  aiCodeEditor: {
    icon: Zap,
    category: 'AI-Powered',
    description: 'Focused AI code editor with advanced completion and analysis',
    tags: ['ai', 'coding', 'completion'],
    color: 'bg-yellow-500',
  },
  fullStackDev: {
    icon: Code,
    category: 'Development',
    description: 'Complete full-stack development environment with all tools',
    tags: ['fullstack', 'web', 'complete'],
    color: 'bg-green-500',
  },
  dataScience: {
    icon: Database,
    category: 'Data & Analytics',
    description: 'Data science workspace with notebooks, visualizations, and analysis tools',
    tags: ['data', 'analytics', 'python', 'jupyter'],
    color: 'bg-cyan-500',
  },
  devOps: {
    icon: Server,
    category: 'Operations',
    description: 'DevOps and infrastructure management workspace',
    tags: ['devops', 'infrastructure', 'deployment'],
    color: 'bg-orange-500',
  },
  mobileApp: {
    icon: Smartphone,
    category: 'Mobile',
    description: 'Mobile app development with device preview and debugging',
    tags: ['mobile', 'app', 'react-native', 'flutter'],
    color: 'bg-pink-500',
  },
  webDesign: {
    icon: Palette,
    category: 'Design',
    description: 'Web design and frontend development with live preview',
    tags: ['design', 'frontend', 'css', 'ui'],
    color: 'bg-indigo-500',
  },
  apiDevelopment: {
    icon: Settings,
    category: 'Backend',
    description: 'API development and testing environment',
    tags: ['api', 'backend', 'testing', 'rest'],
    color: 'bg-teal-500',
  },
  gamedev: {
    icon: Gamepad2,
    category: 'Gaming',
    description: 'Game development workspace with preview and debugging tools',
    tags: ['game', 'unity', 'unreal', 'gamedev'],
    color: 'bg-red-500',
  },
  cloudNative: {
    icon: Cloud,
    category: 'Cloud',
    description: 'Cloud-native development with infrastructure as code',
    tags: ['cloud', 'kubernetes', 'docker', 'aws'],
    color: 'bg-sky-500',
  },
  mlEngineering: {
    icon: Brain,
    category: 'AI & ML',
    description: 'Machine learning engineering with training and metrics',
    tags: ['ml', 'ai', 'tensorflow', 'pytorch'],
    color: 'bg-violet-500',
  },
  documentation: {
    icon: FileText,
    category: 'Content',
    description: 'Documentation and technical writing workspace',
    tags: ['docs', 'writing', 'markdown', 'content'],
    color: 'bg-slate-500',
  },
  minimal: {
    icon: Minimize2,
    category: 'Minimal',
    description: 'Clean, distraction-free coding environment',
    tags: ['minimal', 'focus', 'simple'],
    color: 'bg-gray-500',
  },
} as const;

interface WorkspaceSelectorProps {
  onSelect: (workspaceType: keyof typeof WORKSPACE_LAYOUTS) => void;
  className?: string;
}

export function WorkspaceSelector({ onSelect, className }: WorkspaceSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Get unique categories
  const categories = Array.from(
    new Set(Object.values(WORKSPACE_METADATA).map(meta => meta.category))
  );

  // Filter workspaces based on search and category
  const filteredWorkspaces = Object.entries(WORKSPACE_LAYOUTS).filter(([key, layout]) => {
    const metadata = WORKSPACE_METADATA[key as keyof typeof WORKSPACE_METADATA];
    if (!metadata) return false;

    const matchesSearch = searchQuery === '' || 
      layout.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      metadata.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      metadata.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = selectedCategory === null || metadata.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const handleWorkspaceSelect = (workspaceType: keyof typeof WORKSPACE_LAYOUTS) => {
    onSelect(workspaceType);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Choose Your Workspace</h2>
        <p className="text-muted-foreground">
          Select a pre-configured workspace optimized for your development needs
        </p>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workspaces..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === null ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(null)}
          >
            All Categories
          </Button>
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Workspace Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {filteredWorkspaces.map(([key, layout], index) => {
            const metadata = WORKSPACE_METADATA[key as keyof typeof WORKSPACE_METADATA];
            if (!metadata) return null;

            const Icon = metadata.icon;

            return (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <Card 
                  className="h-full cursor-pointer transition-all hover:shadow-lg hover:scale-105 group"
                  onClick={() => handleWorkspaceSelect(key as keyof typeof WORKSPACE_LAYOUTS)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className={cn(
                        "p-2 rounded-lg text-white",
                        metadata.color
                      )}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                    </div>
                    <CardTitle className="text-lg">{layout.name}</CardTitle>
                    <Badge variant="secondary" className="w-fit">
                      {metadata.category}
                    </Badge>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      {metadata.description}
                    </p>
                    
                    {/* Panel Count */}
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Settings className="h-3 w-3" />
                      <span>{layout.panels.length} panels configured</span>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {metadata.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {metadata.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{metadata.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* No Results */}
      {filteredWorkspaces.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="text-muted-foreground">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No workspaces found</p>
            <p className="text-sm">
              Try adjusting your search terms or category filter
            </p>
          </div>
        </motion.div>
      )}

      {/* Quick Stats */}
      <div className="text-center text-sm text-muted-foreground">
        Showing {filteredWorkspaces.length} of {Object.keys(WORKSPACE_LAYOUTS).length} available workspaces
      </div>
    </div>
  );
}
