'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Monitor, 
  Activity, 
  Cpu, 
  HardDrive, 
  Play, 
  Square,
  RotateCcw,
  Settings,
  Terminal,
  FileText
} from 'lucide-react';
import { WorkspaceInfo } from '@/types/docker';
import { GuacamoleClient } from './GuacamoleClient';
import { WorkspaceMonitor } from './WorkspaceMonitor';
import { TerminalClient } from './TerminalClient';
import { FileManager } from './FileManager';

interface WorkspaceDashboardProps {
  userId?: string;
}

export const WorkspaceDashboard: React.FC<WorkspaceDashboardProps> = ({ 
  userId = 'default-user' 
}) => {
  const [activeTab, setActiveTab] = useState('workspaces');
  const [selectedWorkspace, setSelectedWorkspace] = useState<WorkspaceInfo | null>(null);
  const [workspaces, setWorkspaces] = useState<WorkspaceInfo[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch workspaces
  const fetchWorkspaces = async () => {
    try {
      const response = await fetch(`/api/workspaces?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setWorkspaces(data.data);
      }
    } catch (error) {
      console.error('Error fetching workspaces:', error);
    }
  };

  useEffect(() => {
    fetchWorkspaces();
  }, [userId, refreshTrigger]);

  const handleWorkspaceSelect = (workspace: WorkspaceInfo) => {
    setSelectedWorkspace(workspace);
    setActiveTab('connect');
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleDisconnect = () => {
    setSelectedWorkspace(null);
    setActiveTab('workspaces');
  };

  const runningWorkspaces = workspaces.filter(w => w.status === 'running');
  const stoppedWorkspaces = workspaces.filter(w => w.status === 'stopped');

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-none border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Workspace Dashboard</h1>
            <p className="text-gray-600">Manage your remote desktop environments</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm">
              <User className="h-4 w-4" />
              <span>{userId}</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-50 text-green-700">
                <Activity className="h-3 w-3 mr-1" />
                {runningWorkspaces.length} Running
              </Badge>
              <Badge variant="outline" className="bg-gray-50 text-gray-700">
                <Monitor className="h-3 w-3 mr-1" />
                {workspaces.length} Total
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="workspaces" className="flex items-center gap-2">
              <Grid3X3 className="h-4 w-4" />
              Workspaces
            </TabsTrigger>
            <TabsTrigger value="connect" disabled={!selectedWorkspace} className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              Connect
            </TabsTrigger>
            <TabsTrigger value="monitor" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Monitor
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="workspaces" className="mt-4 h-full">
            <WorkspaceManager
              onSelectWorkspace={handleWorkspaceSelect}
              userId={userId}
            />
          </TabsContent>

          <TabsContent value="connect" className="mt-4 h-full">
            {selectedWorkspace ? (
              <GuacamoleClient
                workspace={selectedWorkspace}
                onDisconnect={handleDisconnect}
              />
            ) : (
              <Card className="h-full">
                <CardContent className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Workspace Selected</h3>
                    <p className="text-gray-600 mb-4">
                      Select a running workspace from the Workspaces tab to connect
                    </p>
                    <Button onClick={() => setActiveTab('workspaces')}>
                      <List className="h-4 w-4 mr-2" />
                      View Workspaces
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="monitor" className="mt-4 h-full">
            <WorkspaceMonitor
              workspaces={workspaces}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="settings" className="mt-4 h-full">
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Default Display Resolution</label>
                      <select className="w-full mt-1 p-2 border rounded-md">
                        <option>1920x1080</option>
                        <option>1680x1050</option>
                        <option>1440x900</option>
                        <option>1280x720</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Default Workspace Type</label>
                      <select className="w-full mt-1 p-2 border rounded-md">
                        <option>Ubuntu Desktop</option>
                        <option>Development Environment</option>
                        <option>Minimal Desktop</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input type="checkbox" id="auto-connect" />
                    <label htmlFor="auto-connect" className="text-sm">
                      Automatically connect to workspace after creation
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <input type="checkbox" id="notifications" />
                    <label htmlFor="notifications" className="text-sm">
                      Enable desktop notifications for workspace events
                    </label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Resource Limits</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Maximum Concurrent Workspaces</label>
                      <input 
                        type="number" 
                        min="1" 
                        max="10" 
                        defaultValue="5"
                        className="w-full mt-1 p-2 border rounded-md"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Auto-stop Idle Workspaces (hours)</label>
                      <input 
                        type="number" 
                        min="1" 
                        max="24" 
                        defaultValue="4"
                        className="w-full mt-1 p-2 border rounded-md"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Total CPU Limit (cores)</label>
                    <input 
                      type="number" 
                      min="1" 
                      max="16" 
                      defaultValue="8"
                      className="w-full mt-1 p-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Total Memory Limit (GB)</label>
                    <input 
                      type="number" 
                      min="1" 
                      max="32" 
                      defaultValue="16"
                      className="w-full mt-1 p-2 border rounded-md"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Guacamole Integration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Guacamole Server URL</label>
                    <input 
                      type="url" 
                      defaultValue="http://localhost:8080"
                      className="w-full mt-1 p-2 border rounded-md"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <input type="checkbox" id="session-recording" />
                    <label htmlFor="session-recording" className="text-sm">
                      Enable session recording for audit purposes
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <input type="checkbox" id="clipboard-sync" />
                    <label htmlFor="clipboard-sync" className="text-sm">
                      Enable clipboard synchronization
                    </label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end">
                <Button>Save Settings</Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
