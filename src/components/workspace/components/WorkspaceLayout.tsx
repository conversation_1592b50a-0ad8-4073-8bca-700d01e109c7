'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { useWorkspace } from '../context/WorkspaceContext';
import { DragDropProvider } from '../context/DragDropContext';
import { LayoutNode } from '../types';
import { Panel } from './Panel';
import { TabGroup } from './TabGroup';

interface WorkspaceLayoutProps {
  className?: string;
  layoutId?: string;
}

export function WorkspaceLayout({ className, layoutId }: WorkspaceLayoutProps) {
  const { activeLayout, layouts } = useWorkspace();

  const layout = layoutId
    ? layouts.find(l => l.id === layoutId)
    : activeLayout;

  if (!layout) {
    return (
      <div className={cn("flex items-center justify-center h-full w-full", className)}>
        <div className="text-center">
          <h2 className="text-lg font-semibold mb-2">No Workspace Layout</h2>
          <p className="text-muted-foreground">Create a new layout to get started</p>
        </div>
      </div>
    );
  }

  return (
    <DragDropProvider>
      <div className={cn("h-full w-full bg-background", className)}>
        <LayoutRenderer node={layout.layout} layout={layout} />
      </div>
    </DragDropProvider>
  );
}

interface LayoutRendererProps {
  node: LayoutNode;
  layout: any;
}

function LayoutRenderer({ node, layout }: LayoutRendererProps) {
  if (node.type === 'split' && node.children) {
    return (
      <ResizablePanelGroup
        direction={node.direction || 'horizontal'}
        className="h-full w-full"
      >
        {node.children.map((child, index) => (
          <React.Fragment key={child.id}>
            <ResizablePanel
              defaultSize={child.size || 50}
              minSize={child.minSize || 10}
              maxSize={child.maxSize || 90}
            >
              <LayoutRenderer node={child} layout={layout} />
            </ResizablePanel>
            {index < (node.children?.length || 0) - 1 && (
              <ResizableHandle withHandle />
            )}
          </React.Fragment>
        ))}
      </ResizablePanelGroup>
    );
  }

  if (node.type === 'panel') {
    const panel = layout.panels.find((p: any) => p.id === node.id);
    if (!panel) return null;
    
    return <Panel config={panel} />;
  }

  if (node.type === 'tabGroup') {
    const tabGroup = layout.tabGroups.find((tg: any) => tg.id === node.id);
    if (!tabGroup) return null;
    
    return <TabGroup config={tabGroup} layoutId={layout.id} />;
  }

  return null;
}

// Default layout configurations
export const defaultLayouts = {
  codeEditor: {
    name: 'Code Editor',
    panels: [
      {
        id: 'explorer',
        title: 'Explorer',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 40,
        defaultSize: 20,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: true,
        minSize: 10,
        maxSize: 50,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'main-editor',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          size: 80,
          children: [
            {
              type: 'tabGroup' as const,
              id: 'main-editor',
              size: 75,
            },
            {
              type: 'panel' as const,
              id: 'terminal',
              size: 25,
            },
          ],
        },
      ],
    },
  },

  dashboard: {
    name: 'Dashboard',
    panels: [
      {
        id: 'sidebar',
        title: 'Navigation',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 30,
        defaultSize: 20,
      },
    ],
    tabGroups: [
      {
        id: 'main-content',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'sidebar',
          size: 20,
        },
        {
          type: 'tabGroup' as const,
          id: 'main-content',
          size: 80,
        },
      ],
    },
  },

  aiWorkspace: {
    name: 'AI Workspace',
    panels: [
      {
        id: 'ai-chat',
        title: 'AI Assistant',
        type: 'ai-chat' as const,
        closable: false,
        minSize: 20,
        maxSize: 50,
        defaultSize: 30,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'editor-group',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 30,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          size: 70,
          children: [
            {
              type: 'tabGroup' as const,
              id: 'editor-group',
              size: 70,
            },
            {
              type: 'panel' as const,
              id: 'terminal',
              size: 30,
            },
          ],
        },
      ],
    },
  },

  aiCodeEditor: {
    name: 'AI Code Editor',
    panels: [
      {
        id: 'ai-editor',
        title: 'AI Code Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 60,
        maxSize: 90,
        defaultSize: 75,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: true,
        minSize: 10,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'main-content',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'vertical' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'ai-editor',
          size: 75,
        },
        {
          type: 'panel' as const,
          id: 'terminal',
          size: 25,
        },
      ],
    },
  },

  fullStackDev: {
    name: 'Full Stack Development',
    panels: [
      {
        id: 'explorer',
        title: 'Explorer',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 40,
        defaultSize: 20,
      },
      {
        id: 'ai-editor',
        title: 'AI Code Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 40,
        maxSize: 70,
        defaultSize: 50,
      },
      {
        id: 'ai-chat',
        title: 'AI Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 30,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'output',
        title: 'Output',
        type: 'output' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 20,
      },
    ],
    tabGroups: [
      {
        id: 'main-editor',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'bottom-panel',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          children: [
            {
              type: 'split' as const,
              id: 'editor-area',
              direction: 'horizontal' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'ai-editor',
                  size: 60,
                },
                {
                  type: 'panel' as const,
                  id: 'ai-chat',
                  size: 40,
                },
              ],
              size: 75,
            },
            {
              type: 'split' as const,
              id: 'bottom-area',
              direction: 'horizontal' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'terminal',
                  size: 60,
                },
                {
                  type: 'panel' as const,
                  id: 'output',
                  size: 40,
                },
              ],
              size: 25,
            },
          ],
          size: 80,
        },
      ],
    },
  },

  dataScience: {
    name: 'Data Science & Analytics',
    panels: [
      {
        id: 'explorer',
        title: 'Data Explorer',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 25,
      },
      {
        id: 'ai-editor',
        title: 'Notebook Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 40,
        maxSize: 70,
        defaultSize: 50,
      },
      {
        id: 'ai-chat',
        title: 'Data Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'terminal',
        title: 'Python Console',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 30,
      },
      {
        id: 'output',
        title: 'Visualizations',
        type: 'output' as const,
        closable: true,
        minSize: 20,
        maxSize: 50,
        defaultSize: 35,
      },
    ],
    tabGroups: [
      {
        id: 'notebooks',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'analysis-tools',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 25,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'ai-editor',
              size: 60,
            },
            {
              type: 'split' as const,
              id: 'analysis-area',
              direction: 'horizontal' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'terminal',
                  size: 50,
                },
                {
                  type: 'panel' as const,
                  id: 'output',
                  size: 50,
                },
              ],
              size: 40,
            },
          ],
          size: 50,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 25,
        },
      ],
    },
  },

  devOps: {
    name: 'DevOps & Infrastructure',
    panels: [
      {
        id: 'explorer',
        title: 'Infrastructure',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 20,
      },
      {
        id: 'ai-editor',
        title: 'Config Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 30,
        maxSize: 60,
        defaultSize: 40,
      },
      {
        id: 'terminal',
        title: 'Terminal',
        type: 'terminal' as const,
        closable: false,
        minSize: 20,
        maxSize: 50,
        defaultSize: 40,
      },
      {
        id: 'output',
        title: 'Logs & Monitoring',
        type: 'output' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'ai-chat',
        title: 'DevOps Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 30,
      },
    ],
    tabGroups: [
      {
        id: 'config-files',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'monitoring',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'main-area',
          direction: 'vertical' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'ai-editor',
              size: 50,
            },
            {
              type: 'split' as const,
              id: 'ops-area',
              direction: 'horizontal' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'terminal',
                  size: 60,
                },
                {
                  type: 'panel' as const,
                  id: 'output',
                  size: 40,
                },
              ],
              size: 50,
            },
          ],
          size: 50,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 30,
        },
      ],
    },
  },

  mobileApp: {
    name: 'Mobile App Development',
    panels: [
      {
        id: 'explorer',
        title: 'Project Explorer',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 20,
      },
      {
        id: 'ai-editor',
        title: 'Code Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 40,
        maxSize: 70,
        defaultSize: 50,
      },
      {
        id: 'output',
        title: 'Device Preview',
        type: 'output' as const,
        closable: false,
        minSize: 20,
        maxSize: 40,
        defaultSize: 30,
      },
      {
        id: 'terminal',
        title: 'Build Console',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'debug',
        title: 'Debugger',
        type: 'debug' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 20,
      },
      {
        id: 'ai-chat',
        title: 'Mobile Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'source-code',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'development-tools',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'development-area',
          direction: 'horizontal' as const,
          children: [
            {
              type: 'split' as const,
              id: 'code-area',
              direction: 'vertical' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'ai-editor',
                  size: 70,
                },
                {
                  type: 'split' as const,
                  id: 'tools-area',
                  direction: 'horizontal' as const,
                  children: [
                    {
                      type: 'panel' as const,
                      id: 'terminal',
                      size: 60,
                    },
                    {
                      type: 'panel' as const,
                      id: 'debug',
                      size: 40,
                    },
                  ],
                  size: 30,
                },
              ],
              size: 50,
            },
            {
              type: 'split' as const,
              id: 'preview-area',
              direction: 'vertical' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'output',
                  size: 70,
                },
                {
                  type: 'panel' as const,
                  id: 'ai-chat',
                  size: 30,
                },
              ],
              size: 50,
            },
          ],
          size: 80,
        },
      ],
    },
  },

  webDesign: {
    name: 'Web Design & Frontend',
    panels: [
      {
        id: 'explorer',
        title: 'Assets',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 30,
        defaultSize: 20,
      },
      {
        id: 'ai-editor',
        title: 'Code Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 30,
        maxSize: 60,
        defaultSize: 40,
      },
      {
        id: 'output',
        title: 'Live Preview',
        type: 'output' as const,
        closable: false,
        minSize: 25,
        maxSize: 50,
        defaultSize: 40,
      },
      {
        id: 'ai-chat',
        title: 'Design Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'terminal',
        title: 'Build Tools',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 35,
        defaultSize: 20,
      },
    ],
    tabGroups: [
      {
        id: 'design-files',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'preview-tools',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'design-area',
          direction: 'horizontal' as const,
          children: [
            {
              type: 'split' as const,
              id: 'code-area',
              direction: 'vertical' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'ai-editor',
                  size: 80,
                },
                {
                  type: 'panel' as const,
                  id: 'terminal',
                  size: 20,
                },
              ],
              size: 50,
            },
            {
              type: 'panel' as const,
              id: 'output',
              size: 50,
            },
          ],
          size: 55,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 25,
        },
      ],
    },
  },

  apiDevelopment: {
    name: 'API Development & Testing',
    panels: [
      {
        id: 'explorer',
        title: 'API Explorer',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 25,
      },
      {
        id: 'ai-editor',
        title: 'Code Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 30,
        maxSize: 60,
        defaultSize: 45,
      },
      {
        id: 'output',
        title: 'API Testing',
        type: 'output' as const,
        closable: false,
        minSize: 20,
        maxSize: 40,
        defaultSize: 30,
      },
      {
        id: 'terminal',
        title: 'Server Console',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'ai-chat',
        title: 'API Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'api-code',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'testing-tools',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 25,
        },
        {
          type: 'split' as const,
          id: 'development-area',
          direction: 'vertical' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'ai-editor',
              size: 60,
            },
            {
              type: 'panel' as const,
              id: 'terminal',
              size: 40,
            },
          ],
          size: 45,
        },
        {
          type: 'split' as const,
          id: 'testing-area',
          direction: 'vertical' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'output',
              size: 60,
            },
            {
              type: 'panel' as const,
              id: 'ai-chat',
              size: 40,
            },
          ],
          size: 30,
        },
      ],
    },
  },

  gamedev: {
    name: 'Game Development',
    panels: [
      {
        id: 'explorer',
        title: 'Game Assets',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 30,
        defaultSize: 20,
      },
      {
        id: 'ai-editor',
        title: 'Script Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 30,
        maxSize: 60,
        defaultSize: 40,
      },
      {
        id: 'output',
        title: 'Game Preview',
        type: 'output' as const,
        closable: false,
        minSize: 25,
        maxSize: 50,
        defaultSize: 40,
      },
      {
        id: 'debug',
        title: 'Game Debugger',
        type: 'debug' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'terminal',
        title: 'Build Console',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 35,
        defaultSize: 20,
      },
      {
        id: 'ai-chat',
        title: 'GameDev Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'game-scripts',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'game-tools',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'development-area',
          direction: 'horizontal' as const,
          children: [
            {
              type: 'split' as const,
              id: 'code-area',
              direction: 'vertical' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'ai-editor',
                  size: 70,
                },
                {
                  type: 'split' as const,
                  id: 'tools-area',
                  direction: 'horizontal' as const,
                  children: [
                    {
                      type: 'panel' as const,
                      id: 'terminal',
                      size: 50,
                    },
                    {
                      type: 'panel' as const,
                      id: 'debug',
                      size: 50,
                    },
                  ],
                  size: 30,
                },
              ],
              size: 45,
            },
            {
              type: 'panel' as const,
              id: 'output',
              size: 55,
            },
          ],
          size: 55,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 25,
        },
      ],
    },
  },

  cloudNative: {
    name: 'Cloud Native Development',
    panels: [
      {
        id: 'explorer',
        title: 'Cloud Resources',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 22,
      },
      {
        id: 'ai-editor',
        title: 'Infrastructure Code',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 30,
        maxSize: 60,
        defaultSize: 38,
      },
      {
        id: 'terminal',
        title: 'Cloud CLI',
        type: 'terminal' as const,
        closable: false,
        minSize: 20,
        maxSize: 50,
        defaultSize: 40,
      },
      {
        id: 'output',
        title: 'Deployment Logs',
        type: 'output' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'ai-chat',
        title: 'Cloud Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 28,
      },
    ],
    tabGroups: [
      {
        id: 'infrastructure',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'monitoring',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 22,
        },
        {
          type: 'split' as const,
          id: 'development-area',
          direction: 'vertical' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'ai-editor',
              size: 55,
            },
            {
              type: 'split' as const,
              id: 'operations-area',
              direction: 'horizontal' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'terminal',
                  size: 65,
                },
                {
                  type: 'panel' as const,
                  id: 'output',
                  size: 35,
                },
              ],
              size: 45,
            },
          ],
          size: 50,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 28,
        },
      ],
    },
  },

  mlEngineering: {
    name: 'ML Engineering',
    panels: [
      {
        id: 'explorer',
        title: 'ML Pipeline',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 20,
      },
      {
        id: 'ai-editor',
        title: 'Model Code',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 30,
        maxSize: 60,
        defaultSize: 40,
      },
      {
        id: 'output',
        title: 'Training Metrics',
        type: 'output' as const,
        closable: false,
        minSize: 20,
        maxSize: 45,
        defaultSize: 30,
      },
      {
        id: 'terminal',
        title: 'ML Console',
        type: 'terminal' as const,
        closable: true,
        minSize: 15,
        maxSize: 40,
        defaultSize: 25,
      },
      {
        id: 'ai-chat',
        title: 'ML Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 25,
      },
    ],
    tabGroups: [
      {
        id: 'ml-notebooks',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'experiments',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'explorer',
          size: 20,
        },
        {
          type: 'split' as const,
          id: 'ml-workspace',
          direction: 'vertical' as const,
          children: [
            {
              type: 'split' as const,
              id: 'development-area',
              direction: 'horizontal' as const,
              children: [
                {
                  type: 'panel' as const,
                  id: 'ai-editor',
                  size: 60,
                },
                {
                  type: 'panel' as const,
                  id: 'output',
                  size: 40,
                },
              ],
              size: 70,
            },
            {
              type: 'panel' as const,
              id: 'terminal',
              size: 30,
            },
          ],
          size: 55,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 25,
        },
      ],
    },
  },

  documentation: {
    name: 'Documentation & Writing',
    panels: [
      {
        id: 'explorer',
        title: 'Docs Structure',
        type: 'explorer' as const,
        closable: false,
        minSize: 15,
        maxSize: 35,
        defaultSize: 25,
      },
      {
        id: 'ai-editor',
        title: 'Markdown Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 40,
        maxSize: 70,
        defaultSize: 50,
      },
      {
        id: 'output',
        title: 'Preview',
        type: 'output' as const,
        closable: false,
        minSize: 20,
        maxSize: 50,
        defaultSize: 35,
      },
      {
        id: 'ai-chat',
        title: 'Writing Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 20,
        maxSize: 40,
        defaultSize: 30,
      },
      {
        id: 'search',
        title: 'Content Search',
        type: 'search' as const,
        closable: true,
        minSize: 15,
        maxSize: 35,
        defaultSize: 20,
      },
    ],
    tabGroups: [
      {
        id: 'documentation',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: true,
      },
      {
        id: 'writing-tools',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'split' as const,
          id: 'sidebar',
          direction: 'vertical' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'explorer',
              size: 70,
            },
            {
              type: 'panel' as const,
              id: 'search',
              size: 30,
            },
          ],
          size: 25,
        },
        {
          type: 'split' as const,
          id: 'writing-area',
          direction: 'horizontal' as const,
          children: [
            {
              type: 'panel' as const,
              id: 'ai-editor',
              size: 60,
            },
            {
              type: 'panel' as const,
              id: 'output',
              size: 40,
            },
          ],
          size: 45,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 30,
        },
      ],
    },
  },

  minimal: {
    name: 'Minimal Workspace',
    panels: [
      {
        id: 'ai-editor',
        title: 'Editor',
        type: 'ai-editor' as const,
        closable: false,
        minSize: 70,
        maxSize: 90,
        defaultSize: 80,
      },
      {
        id: 'ai-chat',
        title: 'AI Assistant',
        type: 'ai-chat' as const,
        closable: true,
        minSize: 10,
        maxSize: 30,
        defaultSize: 20,
      },
    ],
    tabGroups: [
      {
        id: 'main',
        tabs: [],
        activeTabId: '',
        allowReorder: true,
        allowSplit: false,
      },
    ],
    layout: {
      type: 'split' as const,
      id: 'root',
      direction: 'horizontal' as const,
      children: [
        {
          type: 'panel' as const,
          id: 'ai-editor',
          size: 80,
        },
        {
          type: 'panel' as const,
          id: 'ai-chat',
          size: 20,
        },
      ],
    },
  },
};
