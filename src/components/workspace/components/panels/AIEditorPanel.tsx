'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  FileText, 
  Plus, 
  Settings,
  Zap,
  Code,
  Save,
  FolderOpen,
} from 'lucide-react';

import { AICodeEditor, AIAssistantPanel } from '@/components/ai-code-editor';
import { useAIEditorState } from '@/hooks/ai-code-editor';
import { CodeFile, AICodeEditorConfig } from '@/types/ai-code-editor';

interface AIEditorPanelProps {
  className?: string;
  userId?: string;
  workspaceId?: string;
}

// Sample files for demonstration
const SAMPLE_FILES: CodeFile[] = [
  {
    id: 'sample-1',
    name: 'example.ts',
    path: '/workspace/example.ts',
    content: `// TypeScript Example
interface User {
  id: number;
  name: string;
  email: string;
}

function createUser(userData: Partial<User>): User {
  return {
    id: Math.random(),
    name: userData.name || 'Anonymous',
    email: userData.email || '<EMAIL>',
  };
}

const users: User[] = [];

function addUser(user: User) {
  users.push(user);
  console.log('User added:', user);
}

// This function has some issues that AI can help fix
function findUser(id: number) {
  for (let i = 0; i < users.length; i++) {
    if (users[i].id == id) {
      return users[i];
    }
  }
  return null;
}`,
    language: 'typescript',
    isDirty: false,
    lastModified: new Date(),
    size: 0,
  },
  {
    id: 'sample-2',
    name: 'utils.js',
    path: '/workspace/utils.js',
    content: `// JavaScript Utility Functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// This function could be optimized
function processArray(arr) {
  var result = [];
  for (var i = 0; i < arr.length; i++) {
    if (arr[i] != null) {
      result.push(arr[i] * 2);
    }
  }
  return result;
}

const API_URL = 'https://api.example.com';

async function fetchData(endpoint) {
  try {
    const response = await fetch(API_URL + endpoint);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    return null;
  }
}`,
    language: 'javascript',
    isDirty: false,
    lastModified: new Date(),
    size: 0,
  },
];

export function AIEditorPanel({ className, userId, workspaceId }: AIEditorPanelProps) {
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  const { state, actions } = useAIEditorState({
    userId,
    workspaceId,
    persistState: true,
  });

  // Initialize with sample files if no files are open
  useEffect(() => {
    if (state.openFiles.length === 0) {
      SAMPLE_FILES.forEach(file => {
        actions.openFile({ ...file, size: file.content.length });
      });
      setSelectedFileId(SAMPLE_FILES[0].id);
    }
  }, [state.openFiles.length, actions]);

  // Set active file when selection changes
  useEffect(() => {
    if (selectedFileId) {
      const file = state.openFiles.find(f => f.id === selectedFileId);
      if (file && file.id !== state.activeFile?.id) {
        actions.openFile(file);
      }
    }
  }, [selectedFileId, state.openFiles, state.activeFile?.id, actions]);

  const handleFileSelect = (fileId: string) => {
    setSelectedFileId(fileId);
  };

  const handleFileChange = (file: CodeFile) => {
    // File changes are handled by the editor state
  };

  const handleFileSave = async (file: CodeFile) => {
    try {
      // In a real implementation, this would save to the backend
      console.log('Saving file:', file.name);
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate save
    } catch (error) {
      console.error('Failed to save file:', error);
    }
  };

  const handleCreateNewFile = () => {
    const newFile: CodeFile = {
      id: `file-${Date.now()}`,
      name: 'untitled.ts',
      path: '/workspace/untitled.ts',
      content: '// New TypeScript file\n',
      language: 'typescript',
      isDirty: true,
      lastModified: new Date(),
      size: 0,
    };
    
    actions.openFile(newFile);
    setSelectedFileId(newFile.id);
  };

  return (
    <div className={cn("h-full flex flex-col bg-background", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/50">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Brain className="h-5 w-5 text-primary" />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"
            />
          </div>
          <h2 className="font-semibold">AI Code Editor</h2>
          <Badge variant="secondary" className="h-5 px-2 text-xs">
            Beta
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCreateNewFile}
            className="h-7 px-2"
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="h-7 px-2"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* File Tabs */}
      {state.openFiles.length > 0 && (
        <div className="flex items-center gap-1 p-2 border-b bg-background/50 overflow-x-auto">
          {state.openFiles.map((file) => (
            <Button
              key={file.id}
              variant={selectedFileId === file.id ? "default" : "ghost"}
              size="sm"
              onClick={() => handleFileSelect(file.id)}
              className="h-7 px-3 text-xs shrink-0 relative"
            >
              <FileText className="h-3 w-3 mr-1" />
              {file.name}
              {file.isDirty && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full" />
              )}
            </Button>
          ))}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Editor */}
        <div className="flex-1 flex flex-col">
          {state.activeFile ? (
            <AICodeEditor
              file={state.activeFile}
              onFileChange={handleFileChange}
              onSave={handleFileSave}
              userId={userId}
              workspaceId={workspaceId}
              className="flex-1"
            />
          ) : (
            <div className="flex-1 flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No File Selected</p>
                <p className="text-sm mb-4">
                  Select a file from the tabs above or create a new one
                </p>
                <Button onClick={handleCreateNewFile} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Create New File
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* AI Assistant Panel */}
        {state.assistant.isOpen && (
          <div className="w-80 border-l">
            <AIAssistantPanel
              isOpen={state.assistant.isOpen}
              onClose={actions.toggleAssistant}
              currentFile={state.activeFile ? {
                name: state.activeFile.name,
                content: state.activeFile.content,
                language: state.activeFile.language,
              } : undefined}
              selection={state.selection}
              userId={userId}
              workspaceId={workspaceId}
              className="h-full"
            />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between p-2 border-t bg-muted/25 text-xs text-muted-foreground">
        <div className="flex items-center gap-4">
          {state.activeFile && (
            <>
              <span>{state.activeFile.language}</span>
              <Separator orientation="vertical" className="h-3" />
              <span>Line {state.cursor.line}, Col {state.cursor.column}</span>
              <Separator orientation="vertical" className="h-3" />
              <span>{state.activeFile.content.length} chars</span>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {state.completions.isLoading && (
            <Badge variant="outline" className="h-4 px-1 text-xs">
              <Zap className="h-3 w-3 mr-1" />
              AI
            </Badge>
          )}
          {state.analysis.errors.length > 0 && (
            <Badge variant="destructive" className="h-4 px-1 text-xs">
              {state.analysis.errors.length} issues
            </Badge>
          )}
          <span>AI Code Editor v1.0</span>
        </div>
      </div>
    </div>
  );
}
