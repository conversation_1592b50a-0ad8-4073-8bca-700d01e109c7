/**
 * Workspace Card Component
 * Displays workspace information in a card format with actions
 */

'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Square,
  MoreVertical,
  Users,
  Calendar,
  HardDrive,
  Cpu,
  MemoryStick,
  ExternalLink,
  Settings,
  Share,
  Trash2,
  Copy,
  Edit,
  Clock,
  Activity,
  AlertCircle,
  Archive
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Workspace } from '@/types/workspace';
import { formatDistanceToNow } from 'date-fns';

export interface WorkspaceCardProps {
  workspace: Workspace;
  onStart?: (workspace: Workspace) => void;
  onStop?: (workspace: Workspace) => void;
  onOpen?: (workspace: Workspace) => void;
  onEdit?: (workspace: Workspace) => void;
  onShare?: (workspace: Workspace) => void;
  onDelete?: (workspace: Workspace) => void;
  onDuplicate?: (workspace: Workspace) => void;
  className?: string;
  showActions?: boolean;
  showStats?: boolean;
  compact?: boolean;
}

const statusConfig = {
  creating: { color: 'bg-blue-500', label: 'Creating', icon: Clock },
  active: { color: 'bg-green-500', label: 'Active', icon: Activity },
  stopped: { color: 'bg-gray-500', label: 'Stopped', icon: Square },
  error: { color: 'bg-red-500', label: 'Error', icon: AlertCircle },
  archived: { color: 'bg-yellow-500', label: 'Archived', icon: Archive },
} as const;

const typeConfig = {
  python: { icon: '🐍', label: 'Python', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
  nodejs: { icon: '⚡', label: 'Node.js', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
  general: { icon: '🔧', label: 'General', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200' },
  collaborative: { icon: '👥', label: 'Collaborative', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' },
} as const;

export function WorkspaceCard({
  workspace,
  onStart,
  onStop,
  onOpen,
  onEdit,
  onShare,
  onDelete,
  onDuplicate,
  className,
  showActions = true,
  showStats = true,
  compact = false,
}: WorkspaceCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const status = statusConfig[workspace.status];
  const type = typeConfig[workspace.type];

  const handleAction = async (action: () => Promise<void> | void) => {
    setIsLoading(true);
    try {
      await action();
    } finally {
      setIsLoading(false);
    }
  };

  const canStart = workspace.status === 'stopped';
  const canStop = workspace.status === 'active';
  const canOpen = workspace.status === 'active' && workspace.accessUrl;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      <Card className={cn(
        "group relative overflow-hidden transition-all duration-200",
        "hover:shadow-lg hover:shadow-primary/5",
        "border-border/50 hover:border-border",
        compact && "p-3"
      )}>
        {/* Status indicator */}
        <div className={cn(
          "absolute top-0 left-0 w-full h-1",
          status.color
        )} />

        <CardHeader className={cn(
          "pb-3",
          compact && "pb-2 pt-3"
        )}>
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-lg">{type.icon}</span>
                <Badge variant="secondary" className={cn("text-xs", type.color)}>
                  {type.label}
                </Badge>
                <div className="flex items-center gap-1">
                  <div className={cn("w-2 h-2 rounded-full", status.color)} />
                  <span className="text-xs text-muted-foreground">{status.label}</span>
                </div>
              </div>
              
              <CardTitle className={cn(
                "text-lg font-semibold truncate",
                compact && "text-base"
              )}>
                {workspace.name}
              </CardTitle>
              
              {workspace.description && !compact && (
                <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                  {workspace.description}
                </p>
              )}
            </div>

            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {canOpen && (
                    <DropdownMenuItem onClick={() => onOpen?.(workspace)}>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open Workspace
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={() => onEdit?.(workspace)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onShare?.(workspace)}>
                    <Share className="h-4 w-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onDuplicate?.(workspace)}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete?.(workspace)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>

        <CardContent className={cn(
          "pt-0",
          compact && "pt-0 pb-3"
        )}>
          {/* Workspace metadata */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Created {formatDistanceToNow(workspace.createdAt, { addSuffix: true })}</span>
            </div>
            {workspace.collaborators.length > 0 && (
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>{workspace.collaborators.length} collaborator{workspace.collaborators.length !== 1 ? 's' : ''}</span>
              </div>
            )}
          </div>

          {/* Resource usage */}
          {showStats && !compact && (
            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="flex items-center gap-2 text-xs">
                <Cpu className="h-3 w-3 text-blue-500" />
                <span className="text-muted-foreground">CPU:</span>
                <span className="font-medium">{workspace.configuration.resources.cpu} cores</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <MemoryStick className="h-3 w-3 text-green-500" />
                <span className="text-muted-foreground">RAM:</span>
                <span className="font-medium">{workspace.configuration.resources.memory}MB</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <HardDrive className="h-3 w-3 text-purple-500" />
                <span className="text-muted-foreground">Storage:</span>
                <span className="font-medium">{workspace.configuration.resources.storage}GB</span>
              </div>
            </div>
          )}

          {/* Tags */}
          {workspace.tags.length > 0 && !compact && (
            <div className="flex flex-wrap gap-1 mb-4">
              {workspace.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {workspace.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{workspace.tags.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Action buttons */}
          {showActions && (
            <div className="flex items-center gap-2">
              {canStart && (
                <Button
                  size="sm"
                  onClick={() => handleAction(() => onStart?.(workspace))}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start
                </Button>
              )}
              
              {canStop && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleAction(() => onStop?.(workspace))}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
              )}

              {canOpen && (
                <Button
                  size="sm"
                  onClick={() => onOpen?.(workspace)}
                  className="flex-1"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open
                </Button>
              )}

              {!canStart && !canStop && !canOpen && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onEdit?.(workspace)}
                  className="flex-1"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
