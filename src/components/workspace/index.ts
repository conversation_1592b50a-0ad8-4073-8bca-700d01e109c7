// Main workspace components
export { WorkspaceLayout, defaultLayouts } from './components/WorkspaceLayout';
export { Panel } from './components/Panel';
export { TabGroup } from './components/TabGroup';

// Panel components
export * from './components/panels';

// New workspace management components
export { WorkspaceManager } from './WorkspaceManager';
export { WorkspaceCreationWizard } from './WorkspaceCreationWizard';
export { GuacamoleClient } from './GuacamoleClient';
export { WorkspaceMonitor } from './WorkspaceMonitor';
export { WorkspaceDashboard } from './WorkspaceDashboard';

// Core workspace UI components
export { WorkspaceCard } from './WorkspaceCard';
export { WorkspaceList } from './WorkspaceList';
export { WorkspaceCreateForm } from './WorkspaceCreateForm';
export { WorkspaceTemplateSelector } from './WorkspaceTemplateSelector';

// Export component prop types
export type { WorkspaceCardProps } from './WorkspaceCard';
export type { WorkspaceListProps } from './WorkspaceList';
export type { WorkspaceCreateFormProps } from './WorkspaceCreateForm';
export type { WorkspaceTemplateSelectorProps } from './WorkspaceTemplateSelector';

// Context and stores
export { useWorkspace } from './context/WorkspaceContext';
export { useDragDrop, DragDropProvider } from './context/DragDropContext';

// Types
export * from './types';
