'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Cpu, 
  HardDrive, 
  Monitor, 
  Clock,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { WorkspaceTemplate } from '@/app/api/workspace-templates/route';

interface WorkspaceCreationWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
  onWorkspaceCreated: () => void;
}

export const WorkspaceCreationWizard: React.FC<WorkspaceCreationWizardProps> = ({
  open,
  onOpenChange,
  userId,
  onWorkspaceCreated,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [templates, setTemplates] = useState<WorkspaceTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkspaceTemplate | null>(null);
  const [workspaceConfig, setWorkspaceConfig] = useState({
    name: '',
    displayWidth: 1920,
    displayHeight: 1080,
    resources: {
      cpu: 2,
      memory: 2048,
    },
  });
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  const steps = [
    'Select Template',
    'Configure Resources',
    'Review & Create'
  ];

  // Fetch templates
  useEffect(() => {
    if (open) {
      fetchTemplates();
    }
  }, [open]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/workspace-templates');
      const data = await response.json();
      
      if (data.success) {
        setTemplates(data.data);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template: WorkspaceTemplate) => {
    setSelectedTemplate(template);
    setWorkspaceConfig(prev => ({
      ...prev,
      name: `${template.name} - ${userId}`,
      resources: {
        cpu: template.defaultResources.cpu,
        memory: template.defaultResources.memory,
      },
      displayWidth: parseInt(template.environment.DISPLAY_WIDTH || '1920'),
      displayHeight: parseInt(template.environment.DISPLAY_HEIGHT || '1080'),
    }));
    setCurrentStep(1);
  };

  const createWorkspace = async () => {
    if (!selectedTemplate) return;

    try {
      setCreating(true);
      const response = await fetch('/api/workspaces', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceType: selectedTemplate.id,
          userId,
          name: workspaceConfig.name,
          displayWidth: workspaceConfig.displayWidth,
          displayHeight: workspaceConfig.displayHeight,
          resources: workspaceConfig.resources,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        onWorkspaceCreated();
        onOpenChange(false);
        resetWizard();
      } else {
        console.error('Failed to create workspace:', data.error);
        alert('Failed to create workspace: ' + data.message);
      }
    } catch (error) {
      console.error('Error creating workspace:', error);
      alert('Error creating workspace. Please try again.');
    } finally {
      setCreating(false);
    }
  };

  const resetWizard = () => {
    setCurrentStep(0);
    setSelectedTemplate(null);
    setWorkspaceConfig({
      name: '',
      displayWidth: 1920,
      displayHeight: 1080,
      resources: { cpu: 2, memory: 2048 },
    });
  };

  const handleClose = () => {
    onOpenChange(false);
    resetWizard();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold mb-2">Choose a Workspace Template</h3>
              <p className="text-gray-600">Select the environment that best fits your needs</p>
            </div>
            
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading templates...</span>
              </div>
            ) : (
              <div className="grid gap-4">
                {templates.map((template) => (
                  <Card 
                    key={template.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">{template.icon}</span>
                          <div>
                            <CardTitle className="text-lg">{template.name}</CardTitle>
                            <p className="text-sm text-gray-600">{template.description}</p>
                          </div>
                        </div>
                        <Badge variant="outline">{template.category}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                        <div className="flex items-center gap-2">
                          <Cpu className="h-4 w-4 text-gray-500" />
                          <span>{template.defaultResources.cpu} CPU</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <HardDrive className="h-4 w-4 text-gray-500" />
                          <span>{template.defaultResources.memory}MB RAM</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span>~{template.estimatedStartTime}s</span>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {template.features.slice(0, 3).map((feature, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                        {template.features.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{template.features.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold mb-2">Configure Your Workspace</h3>
              <p className="text-gray-600">Customize the resources and settings</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="workspace-name">Workspace Name</Label>
                <Input
                  id="workspace-name"
                  value={workspaceConfig.name}
                  onChange={(e) => setWorkspaceConfig(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="My Workspace"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Display Width</Label>
                  <Input
                    type="number"
                    value={workspaceConfig.displayWidth}
                    onChange={(e) => setWorkspaceConfig(prev => ({ 
                      ...prev, 
                      displayWidth: parseInt(e.target.value) || 1920 
                    }))}
                  />
                </div>
                <div>
                  <Label>Display Height</Label>
                  <Input
                    type="number"
                    value={workspaceConfig.displayHeight}
                    onChange={(e) => setWorkspaceConfig(prev => ({ 
                      ...prev, 
                      displayHeight: parseInt(e.target.value) || 1080 
                    }))}
                  />
                </div>
              </div>

              <div>
                <Label>CPU Cores: {workspaceConfig.resources.cpu}</Label>
                <Slider
                  value={[workspaceConfig.resources.cpu]}
                  onValueChange={(value) => setWorkspaceConfig(prev => ({
                    ...prev,
                    resources: { ...prev.resources, cpu: value[0] }
                  }))}
                  max={8}
                  min={1}
                  step={1}
                  className="mt-2"
                />
              </div>

              <div>
                <Label>Memory: {workspaceConfig.resources.memory}MB</Label>
                <Slider
                  value={[workspaceConfig.resources.memory]}
                  onValueChange={(value) => setWorkspaceConfig(prev => ({
                    ...prev,
                    resources: { ...prev.resources, memory: value[0] }
                  }))}
                  max={8192}
                  min={512}
                  step={512}
                  className="mt-2"
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
              <p className="text-gray-600">Confirm the details before creating your workspace</p>
            </div>

            {selectedTemplate && (
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{selectedTemplate.icon}</span>
                    <div>
                      <CardTitle>{workspaceConfig.name}</CardTitle>
                      <p className="text-sm text-gray-600">{selectedTemplate.name}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Cpu className="h-4 w-4 text-gray-500" />
                      <span>{workspaceConfig.resources.cpu} CPU Cores</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <HardDrive className="h-4 w-4 text-gray-500" />
                      <span>{workspaceConfig.resources.memory}MB RAM</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Monitor className="h-4 w-4 text-gray-500" />
                      <span>{workspaceConfig.displayWidth}x{workspaceConfig.displayHeight}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span>~{selectedTemplate.estimatedStartTime}s startup</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Workspace</DialogTitle>
          <DialogDescription>
            Step {currentStep + 1} of {steps.length}: {steps[currentStep]}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {renderStepContent()}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            {currentStep > 0 && (
              <Button
                variant="outline"
                onClick={() => setCurrentStep(prev => prev - 1)}
                disabled={creating}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose} disabled={creating}>
              Cancel
            </Button>
            
            {currentStep < steps.length - 1 ? (
              <Button
                onClick={() => setCurrentStep(prev => prev + 1)}
                disabled={!selectedTemplate || creating}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={createWorkspace}
                disabled={!workspaceConfig.name.trim() || creating}
              >
                {creating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Create Workspace
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
