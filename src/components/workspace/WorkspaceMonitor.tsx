'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Activity, 
  Cpu, 
  HardDrive,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { WorkspaceInfo } from '@/types/docker';
import { useDocker } from '@/hooks/useDocker';

interface WorkspaceStats {
  cpu: {
    usage: number;
    limit: number;
  };
  memory: {
    usage: number;
    limit: number;
  };
  network: {
    rx: number;
    tx: number;
  };
  uptime: number;
}

interface WorkspaceMonitorProps {
  workspaces: WorkspaceInfo[];
  onRefresh: () => void;
}

export const WorkspaceMonitor: React.FC<WorkspaceMonitorProps> = ({
  workspaces,
  onRefresh
}) => {
  const [stats, setStats] = useState<{ [key: string]: WorkspaceStats }>({});
  const [loading, setLoading] = useState(false);

  // Mock stats generation (in real app, this would come from API)
  const generateMockStats = (workspace: WorkspaceInfo): WorkspaceStats => {
    const cpuUsage = workspace.status === 'running' 
      ? Math.random() * workspace.resources.cpuLimit * 100
      : 0;
    
    const memoryUsage = workspace.status === 'running'
      ? (0.3 + Math.random() * 0.4) * workspace.resources.memoryLimit
      : 0;

    return {
      cpu: {
        usage: cpuUsage,
        limit: workspace.resources.cpuLimit * 100,
      },
      memory: {
        usage: memoryUsage,
        limit: workspace.resources.memoryLimit,
      },
      network: {
        rx: Math.random() * 1000,
        tx: Math.random() * 500,
      },
      uptime: workspace.status === 'running' 
        ? Math.floor((Date.now() - workspace.created.getTime()) / 1000)
        : 0,
    };
  };

  // Fetch workspace statistics
  const fetchStats = async () => {
    setLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newStats: { [key: string]: WorkspaceStats } = {};
    workspaces.forEach(workspace => {
      newStats[workspace.id] = generateMockStats(workspace);
    });
    
    setStats(newStats);
    setLoading(false);
  };

  useEffect(() => {
    fetchStats();
    
    // Set up periodic refresh
    const interval = setInterval(fetchStats, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [workspaces]);

  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-red-100 text-red-800';
      case 'starting': return 'bg-yellow-100 text-yellow-800';
      case 'stopping': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthStatus = (workspace: WorkspaceInfo, workspaceStats?: WorkspaceStats) => {
    if (workspace.status !== 'running') {
      return { status: 'stopped', color: 'text-gray-500', icon: Monitor };
    }

    if (!workspaceStats) {
      return { status: 'unknown', color: 'text-gray-500', icon: AlertTriangle };
    }

    const cpuPercent = (workspaceStats.cpu.usage / workspaceStats.cpu.limit) * 100;
    const memoryPercent = (workspaceStats.memory.usage / workspaceStats.memory.limit) * 100;

    if (cpuPercent > 90 || memoryPercent > 90) {
      return { status: 'critical', color: 'text-red-500', icon: AlertTriangle };
    }

    if (cpuPercent > 70 || memoryPercent > 70) {
      return { status: 'warning', color: 'text-yellow-500', icon: TrendingUp };
    }

    return { status: 'healthy', color: 'text-green-500', icon: CheckCircle };
  };

  const runningWorkspaces = workspaces.filter(w => w.status === 'running');
  const totalCpuUsage = Object.values(stats).reduce((sum, stat) => sum + stat.cpu.usage, 0);
  const totalMemoryUsage = Object.values(stats).reduce((sum, stat) => sum + stat.memory.usage, 0);

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Workspaces</p>
                <p className="text-2xl font-bold">{workspaces.length}</p>
              </div>
              <Monitor className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Running</p>
                <p className="text-2xl font-bold text-green-600">{runningWorkspaces.length}</p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">CPU Usage</p>
                <p className="text-2xl font-bold">{totalCpuUsage.toFixed(1)}%</p>
              </div>
              <Cpu className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Memory Usage</p>
                <p className="text-2xl font-bold">{formatBytes(totalMemoryUsage * 1024 * 1024)}</p>
              </div>
              <HardDrive className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Workspace Stats */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Workspace Details</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              onRefresh();
              fetchStats();
            }}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          {workspaces.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Monitor className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No workspaces to monitor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {workspaces.map((workspace) => {
                const workspaceStats = stats[workspace.id];
                const health = getHealthStatus(workspace, workspaceStats);
                const HealthIcon = health.icon;

                return (
                  <div key={workspace.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <HealthIcon className={`h-5 w-5 ${health.color}`} />
                        <div>
                          <h4 className="font-semibold">{workspace.name}</h4>
                          <p className="text-sm text-gray-600">{workspace.userId}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(workspace.status)}>
                          {workspace.status}
                        </Badge>
                        {workspaceStats && workspace.status === 'running' && (
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatUptime(workspaceStats.uptime)}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {workspaceStats && workspace.status === 'running' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium">CPU Usage</span>
                            <span className="text-sm text-gray-600">
                              {workspaceStats.cpu.usage.toFixed(1)}%
                            </span>
                          </div>
                          <Progress 
                            value={(workspaceStats.cpu.usage / workspaceStats.cpu.limit) * 100} 
                            className="h-2"
                          />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium">Memory Usage</span>
                            <span className="text-sm text-gray-600">
                              {formatBytes(workspaceStats.memory.usage * 1024 * 1024)} / {formatBytes(workspaceStats.memory.limit * 1024 * 1024)}
                            </span>
                          </div>
                          <Progress 
                            value={(workspaceStats.memory.usage / workspaceStats.memory.limit) * 100} 
                            className="h-2"
                          />
                        </div>
                      </div>
                    )}

                    {workspace.status !== 'running' && (
                      <div className="text-center py-4 text-gray-500">
                        <p className="text-sm">Workspace is {workspace.status}</p>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
