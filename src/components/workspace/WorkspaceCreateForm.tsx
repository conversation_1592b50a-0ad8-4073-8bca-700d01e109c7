/**
 * Workspace Creation Form Component
 * Multi-step form for creating new workspaces
 */

'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { 
  ChevronLeft,
  ChevronRight,
  Check,
  Cpu,
  Memory,
  HardDrive,
  Users,
  Lock,
  Globe,
  Eye,
  Zap,
  Code,
  Database,
  Palette
} from 'lucide-react';
import { CreateWorkspaceRequest, WorkspaceType, WorkspaceVisibility } from '@/types/workspace';
import { useWorkspaceTemplates } from '@/hooks/useWorkspaceTemplates';

// Form validation schema
const workspaceSchema = z.object({
  name: z.string().min(1, 'Workspace name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  type: z.enum(['python', 'nodejs', 'general', 'collaborative'] as const),
  templateId: z.string().optional(),
  visibility: z.enum(['private', 'team', 'public'] as const).default('private'),
  tags: z.array(z.string()).max(10, 'Too many tags').default([]),
  configuration: z.object({
    resources: z.object({
      cpu: z.number().min(1).max(16).default(2),
      memory: z.number().min(512).max(32768).default(2048),
      storage: z.number().min(1).max(1000).default(10),
    }),
    collaboration: z.object({
      enabled: z.boolean().default(false),
      maxCollaborators: z.number().min(1).max(50).default(5),
    }),
  }),
});

type WorkspaceFormData = z.infer<typeof workspaceSchema>;

export interface WorkspaceCreateFormProps {
  onSubmit: (data: CreateWorkspaceRequest) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

const steps = [
  { id: 'basic', title: 'Basic Information', description: 'Name and type' },
  { id: 'template', title: 'Template', description: 'Choose a template' },
  { id: 'resources', title: 'Resources', description: 'Configure resources' },
  { id: 'settings', title: 'Settings', description: 'Advanced settings' },
];

const workspaceTypes = [
  { 
    value: 'python', 
    label: 'Python', 
    icon: '🐍', 
    description: 'Python development environment with Jupyter and data science tools',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
  },
  { 
    value: 'nodejs', 
    label: 'Node.js', 
    icon: '⚡', 
    description: 'Node.js development environment with modern JavaScript tools',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  },
  { 
    value: 'general', 
    label: 'General', 
    icon: '🔧', 
    description: 'General purpose development environment',
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  },
  { 
    value: 'collaborative', 
    label: 'Collaborative', 
    icon: '👥', 
    description: 'Multi-user collaborative workspace with real-time features',
    color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
  },
] as const;

const visibilityOptions = [
  { value: 'private', label: 'Private', icon: Lock, description: 'Only you can access' },
  { value: 'team', label: 'Team', icon: Users, description: 'Team members can access' },
  { value: 'public', label: 'Public', icon: Globe, description: 'Anyone can view' },
] as const;

export function WorkspaceCreateForm({
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: WorkspaceCreateFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  const { templates, isLoading: templatesLoading } = useWorkspaceTemplates({
    pagination: { limit: 20, offset: 0 }
  });

  const form = useForm<WorkspaceFormData>({
    resolver: zodResolver(workspaceSchema),
    defaultValues: {
      name: '',
      description: '',
      type: 'python',
      visibility: 'private',
      tags: [],
      configuration: {
        resources: {
          cpu: 2,
          memory: 2048,
          storage: 10,
        },
        collaboration: {
          enabled: false,
          maxCollaborators: 5,
        },
      },
    },
  });

  const watchedType = form.watch('type');
  const watchedResources = form.watch('configuration.resources');

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await form.trigger(fieldsToValidate);
    
    if (isValid && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (data: WorkspaceFormData) => {
    const submitData: CreateWorkspaceRequest = {
      ...data,
      tags: selectedTags,
    };
    await onSubmit(submitData);
  };

  const addTag = () => {
    if (newTag.trim() && !selectedTags.includes(newTag.trim()) && selectedTags.length < 10) {
      setSelectedTags([...selectedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setSelectedTags(selectedTags.filter(t => t !== tag));
  };

  const getFieldsForStep = (step: number): (keyof WorkspaceFormData)[] => {
    switch (step) {
      case 0: return ['name', 'type'];
      case 1: return ['templateId'];
      case 2: return ['configuration'];
      case 3: return ['visibility'];
      default: return [];
    }
  };

  const filteredTemplates = templates.filter(template => template.type === watchedType);

  return (
    <div className={cn("max-w-2xl mx-auto", className)}>
      <Card>
        <CardHeader>
          <CardTitle>Create New Workspace</CardTitle>
          
          {/* Progress indicator */}
          <div className="flex items-center justify-between mt-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors",
                  index <= currentStep 
                    ? "bg-primary text-primary-foreground" 
                    : "bg-muted text-muted-foreground"
                )}>
                  {index < currentStep ? <Check className="h-4 w-4" /> : index + 1}
                </div>
                {index < steps.length - 1 && (
                  <div className={cn(
                    "w-12 h-0.5 mx-2 transition-colors",
                    index < currentStep ? "bg-primary" : "bg-muted"
                  )} />
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center mt-2">
            <h3 className="font-medium">{steps[currentStep].title}</h3>
            <p className="text-sm text-muted-foreground">{steps[currentStep].description}</p>
          </div>
        </CardHeader>

        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Step 0: Basic Information */}
                  {currentStep === 0 && (
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Workspace Name</FormLabel>
                            <FormControl>
                              <Input placeholder="My awesome workspace" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description (Optional)</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Describe what this workspace is for..."
                                className="resize-none"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Workspace Type</FormLabel>
                            <div className="grid grid-cols-2 gap-3">
                              {workspaceTypes.map((type) => (
                                <div
                                  key={type.value}
                                  className={cn(
                                    "relative cursor-pointer rounded-lg border p-4 transition-all",
                                    field.value === type.value
                                      ? "border-primary bg-primary/5"
                                      : "border-border hover:border-border/80"
                                  )}
                                  onClick={() => field.onChange(type.value)}
                                >
                                  <div className="flex items-start space-x-3">
                                    <span className="text-2xl">{type.icon}</span>
                                    <div className="flex-1">
                                      <h4 className="font-medium">{type.label}</h4>
                                      <p className="text-sm text-muted-foreground mt-1">
                                        {type.description}
                                      </p>
                                    </div>
                                  </div>
                                  {field.value === type.value && (
                                    <div className="absolute top-2 right-2">
                                      <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                                        <Check className="h-3 w-3 text-primary-foreground" />
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {/* Step 1: Template Selection */}
                  {currentStep === 1 && (
                    <div className="space-y-4">
                      <div>
                        <Label>Choose a Template (Optional)</Label>
                        <p className="text-sm text-muted-foreground mt-1">
                          Start with a pre-configured template or create from scratch
                        </p>
                      </div>

                      <FormField
                        control={form.control}
                        name="templateId"
                        render={({ field }) => (
                          <FormItem>
                            <div className="grid gap-3">
                              <div
                                className={cn(
                                  "relative cursor-pointer rounded-lg border p-4 transition-all",
                                  !field.value
                                    ? "border-primary bg-primary/5"
                                    : "border-border hover:border-border/80"
                                )}
                                onClick={() => field.onChange(undefined)}
                              >
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                                    <Code className="h-5 w-5" />
                                  </div>
                                  <div>
                                    <h4 className="font-medium">Start from Scratch</h4>
                                    <p className="text-sm text-muted-foreground">
                                      Create a blank {workspaceTypes.find(t => t.value === watchedType)?.label} workspace
                                    </p>
                                  </div>
                                </div>
                                {!field.value && (
                                  <div className="absolute top-2 right-2">
                                    <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                                      <Check className="h-3 w-3 text-primary-foreground" />
                                    </div>
                                  </div>
                                )}
                              </div>

                              {templatesLoading ? (
                                <div className="text-center py-8">
                                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                                  <p className="text-sm text-muted-foreground mt-2">Loading templates...</p>
                                </div>
                              ) : (
                                filteredTemplates.map((template) => (
                                  <div
                                    key={template.id}
                                    className={cn(
                                      "relative cursor-pointer rounded-lg border p-4 transition-all",
                                      field.value === template.id
                                        ? "border-primary bg-primary/5"
                                        : "border-border hover:border-border/80"
                                    )}
                                    onClick={() => field.onChange(template.id)}
                                  >
                                    <div className="flex items-start space-x-3">
                                      <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                                        <span className="text-lg">
                                          {workspaceTypes.find(t => t.value === template.type)?.icon}
                                        </span>
                                      </div>
                                      <div className="flex-1">
                                        <h4 className="font-medium">{template.name}</h4>
                                        <p className="text-sm text-muted-foreground mt-1">
                                          {template.description}
                                        </p>
                                        <div className="flex items-center gap-2 mt-2">
                                          <Badge variant="outline" className="text-xs">
                                            {template.category}
                                          </Badge>
                                          <span className="text-xs text-muted-foreground">
                                            ⭐ {template.rating.toFixed(1)} • {template.usageCount} uses
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                    {field.value === template.id && (
                                      <div className="absolute top-2 right-2">
                                        <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                                          <Check className="h-3 w-3 text-primary-foreground" />
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                ))
                              )}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {/* Additional steps would continue here... */}
                </motion.div>
              </AnimatePresence>

              {/* Navigation buttons */}
              <div className="flex items-center justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={currentStep === 0 ? onCancel : handlePrevious}
                  disabled={isLoading}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  {currentStep === 0 ? 'Cancel' : 'Previous'}
                </Button>

                {currentStep === steps.length - 1 ? (
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        Create Workspace
                        <Check className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                ) : (
                  <Button type="button" onClick={handleNext} disabled={isLoading}>
                    Next
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
