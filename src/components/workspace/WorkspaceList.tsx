/**
 * Workspace List Component
 * Displays a list of workspaces with filtering and pagination
 */

'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Grid3X3,
  List,
  Plus,
  RefreshCw,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { WorkspaceCard } from './WorkspaceCard';
import { Workspace, WorkspaceType, WorkspaceStatus, WorkspaceVisibility } from '@/types/workspace';
import { useWorkspace } from '@/hooks/useWorkspace';

export interface WorkspaceListProps {
  onCreateWorkspace?: () => void;
  onOpenWorkspace?: (workspace: Workspace) => void;
  onEditWorkspace?: (workspace: Workspace) => void;
  onShareWorkspace?: (workspace: Workspace) => void;
  onDeleteWorkspace?: (workspace: Workspace) => void;
  onDuplicateWorkspace?: (workspace: Workspace) => void;
  className?: string;
  showCreateButton?: boolean;
  showFilters?: boolean;
  defaultView?: 'grid' | 'list';
  pageSize?: number;
}

const workspaceTypes: { value: WorkspaceType; label: string }[] = [
  { value: 'python', label: 'Python' },
  { value: 'nodejs', label: 'Node.js' },
  { value: 'general', label: 'General' },
  { value: 'collaborative', label: 'Collaborative' },
];

const workspaceStatuses: { value: WorkspaceStatus; label: string }[] = [
  { value: 'creating', label: 'Creating' },
  { value: 'active', label: 'Active' },
  { value: 'stopped', label: 'Stopped' },
  { value: 'error', label: 'Error' },
  { value: 'archived', label: 'Archived' },
];

const visibilityOptions: { value: WorkspaceVisibility; label: string }[] = [
  { value: 'private', label: 'Private' },
  { value: 'team', label: 'Team' },
  { value: 'public', label: 'Public' },
];

const sortOptions = [
  { value: 'name', label: 'Name' },
  { value: 'createdAt', label: 'Created Date' },
  { value: 'updatedAt', label: 'Updated Date' },
  { value: 'lastAccessedAt', label: 'Last Accessed' },
];

export function WorkspaceList({
  onCreateWorkspace,
  onOpenWorkspace,
  onEditWorkspace,
  onShareWorkspace,
  onDeleteWorkspace,
  onDuplicateWorkspace,
  className,
  showCreateButton = true,
  showFilters = true,
  defaultView = 'grid',
  pageSize = 12,
}: WorkspaceListProps) {
  const [view, setView] = useState<'grid' | 'list'>(defaultView);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<WorkspaceType | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<WorkspaceStatus | 'all'>('all');
  const [selectedVisibility, setSelectedVisibility] = useState<WorkspaceVisibility | 'all'>('all');
  const [sortBy, setSortBy] = useState('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);

  // Build filters for the hook
  const filters = useMemo(() => {
    const result: any = {};
    
    if (searchQuery) result.search = searchQuery;
    if (selectedType !== 'all') result.type = [selectedType];
    if (selectedStatus !== 'all') result.status = [selectedStatus];
    if (selectedVisibility !== 'all') result.visibility = [selectedVisibility];
    
    return result;
  }, [searchQuery, selectedType, selectedStatus, selectedVisibility]);

  const pagination = useMemo(() => ({
    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
    orderBy: sortBy,
    orderDirection: sortOrder,
  }), [currentPage, pageSize, sortBy, sortOrder]);

  const {
    workspaces,
    total,
    isLoading,
    error,
    refetch,
    hasNextPage,
    hasPreviousPage,
    updateStatus,
  } = useWorkspace({
    filters,
    pagination,
    autoRefresh: true,
    refreshInterval: 30000,
  });

  const handleStartWorkspace = async (workspace: Workspace) => {
    await updateStatus(workspace.id, 'active');
  };

  const handleStopWorkspace = async (workspace: Workspace) => {
    await updateStatus(workspace.id, 'stopped');
  };

  const handleNextPage = () => {
    if (hasNextPage) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (hasPreviousPage) {
      setCurrentPage(currentPage - 1);
    }
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedType('all');
    setSelectedStatus('all');
    setSelectedVisibility('all');
    setCurrentPage(1);
  };

  const activeFiltersCount = [
    searchQuery,
    selectedType !== 'all' ? selectedType : null,
    selectedStatus !== 'all' ? selectedStatus : null,
    selectedVisibility !== 'all' ? selectedVisibility : null,
  ].filter(Boolean).length;

  if (error) {
    return (
      <div className={cn("text-center py-12", className)}>
        <div className="text-destructive mb-4">
          <p className="text-lg font-medium">Failed to load workspaces</p>
          <p className="text-sm text-muted-foreground">{error.message}</p>
        </div>
        <Button onClick={() => refetch()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Workspaces</h2>
          <p className="text-muted-foreground">
            {total} workspace{total !== 1 ? 's' : ''} found
          </p>
        </div>
        
        {showCreateButton && (
          <Button onClick={onCreateWorkspace}>
            <Plus className="h-4 w-4 mr-2" />
            Create Workspace
          </Button>
        )}
      </div>

      {/* Filters and Controls */}
      {showFilters && (
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search workspaces..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center gap-2">
            <Select value={selectedType} onValueChange={(value) => setSelectedType(value as WorkspaceType | 'all')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {workspaceTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as WorkspaceStatus | 'all')}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                {workspaceStatuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {sortOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => setSortBy(option.value)}
                    className={sortBy === option.value ? 'bg-accent' : ''}
                  >
                    {option.label}
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}>
                  {sortOrder === 'asc' ? 'Descending' : 'Ascending'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex items-center border rounded-md">
              <Button
                variant={view === 'grid' ? 'default' : 'ghost'}
                size="icon"
                onClick={() => setView('grid')}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={view === 'list' ? 'default' : 'ghost'}
                size="icon"
                onClick={() => setView('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <Button onClick={() => refetch()} variant="outline" size="icon">
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
      )}

      {/* Active filters */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {searchQuery && (
            <Badge variant="secondary">
              Search: {searchQuery}
            </Badge>
          )}
          {selectedType !== 'all' && (
            <Badge variant="secondary">
              Type: {workspaceTypes.find(t => t.value === selectedType)?.label}
            </Badge>
          )}
          {selectedStatus !== 'all' && (
            <Badge variant="secondary">
              Status: {workspaceStatuses.find(s => s.value === selectedStatus)?.label}
            </Badge>
          )}
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            Clear all
          </Button>
        </div>
      )}

      {/* Loading state */}
      {isLoading && workspaces.length === 0 && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading workspaces...</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && workspaces.length === 0 && (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            <p className="text-lg font-medium">No workspaces found</p>
            <p className="text-sm">
              {activeFiltersCount > 0 
                ? 'Try adjusting your filters or search terms'
                : 'Create your first workspace to get started'
              }
            </p>
          </div>
          {activeFiltersCount > 0 ? (
            <Button onClick={clearFilters} variant="outline">
              Clear Filters
            </Button>
          ) : showCreateButton ? (
            <Button onClick={onCreateWorkspace}>
              <Plus className="h-4 w-4 mr-2" />
              Create Workspace
            </Button>
          ) : null}
        </div>
      )}

      {/* Workspace grid/list */}
      {workspaces.length > 0 && (
        <AnimatePresence mode="wait">
          <motion.div
            key={view}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className={cn(
              view === 'grid' 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            )}
          >
            {workspaces.map((workspace) => (
              <WorkspaceCard
                key={workspace.id}
                workspace={workspace}
                onStart={handleStartWorkspace}
                onStop={handleStopWorkspace}
                onOpen={onOpenWorkspace}
                onEdit={onEditWorkspace}
                onShare={onShareWorkspace}
                onDelete={onDeleteWorkspace}
                onDuplicate={onDuplicateWorkspace}
                compact={view === 'list'}
                className={view === 'list' ? 'max-w-none' : ''}
              />
            ))}
          </motion.div>
        </AnimatePresence>
      )}

      {/* Pagination */}
      {workspaces.length > 0 && (hasNextPage || hasPreviousPage) && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, total)} of {total} workspaces
          </p>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousPage}
              disabled={!hasPreviousPage}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <span className="text-sm text-muted-foreground">
              Page {currentPage}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={!hasNextPage}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
