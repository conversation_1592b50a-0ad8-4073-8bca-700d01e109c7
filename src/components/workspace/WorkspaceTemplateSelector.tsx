/**
 * Workspace Template Selector Component
 * Allows users to browse and select workspace templates
 */

'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search,
  Star,
  Download,
  Eye,
  Code,
  Users,
  Calendar,
  Filter,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Heart,
  Bookmark
} from 'lucide-react';
import { WorkspaceTemplate, WorkspaceType } from '@/types/workspace';
import { useWorkspaceTemplates } from '@/hooks/useWorkspaceTemplates';
import { formatDistanceToNow } from 'date-fns';

export interface WorkspaceTemplateSelectorProps {
  onSelectTemplate?: (template: WorkspaceTemplate) => void;
  onPreviewTemplate?: (template: WorkspaceTemplate) => void;
  selectedTemplateId?: string;
  workspaceType?: WorkspaceType;
  className?: string;
  showCreateFromScratch?: boolean;
  compact?: boolean;
}

const workspaceTypes: { value: WorkspaceType; label: string; icon: string }[] = [
  { value: 'python', label: 'Python', icon: '🐍' },
  { value: 'nodejs', label: 'Node.js', icon: '⚡' },
  { value: 'general', label: 'General', icon: '🔧' },
  { value: 'collaborative', label: 'Collaborative', icon: '👥' },
];

const categories = [
  'Development',
  'Data Science',
  'Web Development',
  'Machine Learning',
  'DevOps',
  'Education',
  'Research',
  'Collaboration',
];

const sortOptions = [
  { value: 'rating', label: 'Rating' },
  { value: 'usageCount', label: 'Popularity' },
  { value: 'updatedAt', label: 'Recently Updated' },
  { value: 'createdAt', label: 'Newest' },
];

export function WorkspaceTemplateSelector({
  onSelectTemplate,
  onPreviewTemplate,
  selectedTemplateId,
  workspaceType,
  className,
  showCreateFromScratch = true,
  compact = false,
}: WorkspaceTemplateSelectorProps) {
  const [view, setView] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<WorkspaceType | 'all'>(workspaceType || 'all');
  const [selectedCategory, setSelectedCategory] = useState<string | 'all'>('all');
  const [sortBy, setSortBy] = useState('rating');
  const [currentPage, setCurrentPage] = useState(1);

  const pageSize = compact ? 6 : 12;

  const {
    templates,
    total,
    isLoading,
    error,
    popularTemplates,
    recentTemplates,
    hasNextPage,
    hasPreviousPage,
    incrementUsage,
  } = useWorkspaceTemplates({
    type: selectedType !== 'all' ? [selectedType] : undefined,
    category: selectedCategory !== 'all' ? [selectedCategory] : undefined,
    search: searchQuery || undefined,
    pagination: {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      orderBy: sortBy,
      orderDirection: 'desc',
    },
    autoRefresh: false,
  });

  const handleSelectTemplate = async (template: WorkspaceTemplate) => {
    await incrementUsage(template.id);
    onSelectTemplate?.(template);
  };

  const handleCreateFromScratch = () => {
    onSelectTemplate?.(undefined as any); // Signal to create from scratch
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedType(workspaceType || 'all');
    setSelectedCategory('all');
    setCurrentPage(1);
  };

  const activeFiltersCount = [
    searchQuery,
    selectedType !== 'all' && selectedType !== workspaceType ? selectedType : null,
    selectedCategory !== 'all' ? selectedCategory : null,
  ].filter(Boolean).length;

  if (error) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-destructive mb-2">Failed to load templates</p>
        <p className="text-sm text-muted-foreground">{error.message}</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      {!compact && (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Choose a Template</h3>
            <p className="text-sm text-muted-foreground">
              Start with a pre-configured template or create from scratch
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center border rounded-md">
              <Button
                variant={view === 'grid' ? 'default' : 'ghost'}
                size="icon"
                onClick={() => setView('grid')}
                className="rounded-r-none h-8 w-8"
              >
                <Grid3X3 className="h-3 w-3" />
              </Button>
              <Button
                variant={view === 'list' ? 'default' : 'ghost'}
                size="icon"
                onClick={() => setView('list')}
                className="rounded-l-none h-8 w-8"
              >
                <List className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
            size={compact ? 'sm' : 'default'}
          />
        </div>

        <div className="flex items-center gap-2">
          {!workspaceType && (
            <Select value={selectedType} onValueChange={(value) => setSelectedType(value as WorkspaceType | 'all')}>
              <SelectTrigger className={cn("w-32", compact && "h-8 text-xs")}>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {workspaceTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <span className="flex items-center gap-2">
                      <span>{type.icon}</span>
                      {type.label}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value)}>
            <SelectTrigger className={cn("w-36", compact && "h-8 text-xs")}>
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className={cn("w-32", compact && "h-8 text-xs")}>
              <SelectValue placeholder="Sort" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Active filters */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">Filters:</span>
          {searchQuery && <Badge variant="secondary" className="text-xs">Search: {searchQuery}</Badge>}
          {selectedType !== 'all' && selectedType !== workspaceType && (
            <Badge variant="secondary" className="text-xs">
              {workspaceTypes.find(t => t.value === selectedType)?.label}
            </Badge>
          )}
          {selectedCategory !== 'all' && (
            <Badge variant="secondary" className="text-xs">Category: {selectedCategory}</Badge>
          )}
          <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6 px-2 text-xs">
            Clear
          </Button>
        </div>
      )}

      {/* Create from scratch option */}
      {showCreateFromScratch && (
        <Card 
          className={cn(
            "cursor-pointer transition-all hover:shadow-md border-2",
            !selectedTemplateId ? "border-primary bg-primary/5" : "border-dashed"
          )}
          onClick={handleCreateFromScratch}
        >
          <CardContent className={cn("p-4", compact && "p-3")}>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                <Code className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Start from Scratch</h4>
                <p className="text-sm text-muted-foreground">
                  Create a blank {workspaceType ? workspaceTypes.find(t => t.value === workspaceType)?.label : 'workspace'}
                </p>
              </div>
              {!selectedTemplateId && (
                <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading state */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading templates...</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && templates.length === 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground mb-2">No templates found</p>
          <p className="text-sm text-muted-foreground">
            {activeFiltersCount > 0 ? 'Try adjusting your filters' : 'No templates available'}
          </p>
          {activeFiltersCount > 0 && (
            <Button variant="outline" size="sm" onClick={clearFilters} className="mt-2">
              Clear Filters
            </Button>
          )}
        </div>
      )}

      {/* Templates grid/list */}
      {templates.length > 0 && (
        <AnimatePresence mode="wait">
          <motion.div
            key={view}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className={cn(
              view === 'grid' 
                ? compact 
                  ? "grid grid-cols-1 sm:grid-cols-2 gap-3"
                  : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                : "space-y-3"
            )}
          >
            {templates.map((template) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Card 
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md border-2",
                    selectedTemplateId === template.id 
                      ? "border-primary bg-primary/5" 
                      : "border-transparent hover:border-border"
                  )}
                  onClick={() => handleSelectTemplate(template)}
                >
                  <CardContent className={cn("p-4", compact && "p-3")}>
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-lg">
                          {workspaceTypes.find(t => t.value === template.type)?.icon}
                        </span>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <h4 className="font-medium truncate">{template.name}</h4>
                          {selectedTemplateId === template.id && (
                            <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                              <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                            </div>
                          )}
                        </div>
                        
                        <p className={cn(
                          "text-sm text-muted-foreground mt-1",
                          compact ? "line-clamp-1" : "line-clamp-2"
                        )}>
                          {template.description}
                        </p>
                        
                        <div className="flex items-center gap-3 mt-2">
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            <span className="text-xs text-muted-foreground">
                              {template.rating.toFixed(1)}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Download className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">
                              {template.usageCount}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {template.category}
                          </Badge>
                        </div>
                        
                        {!compact && template.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {template.tags.slice(0, 3).map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {template.tags.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{template.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {onPreviewTemplate && (
                      <div className="flex items-center justify-end mt-3 pt-3 border-t">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onPreviewTemplate(template);
                          }}
                          className="h-7 px-2 text-xs"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Preview
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      )}

      {/* Pagination */}
      {templates.length > 0 && (hasNextPage || hasPreviousPage) && (
        <div className="flex items-center justify-between">
          <p className="text-xs text-muted-foreground">
            {total} template{total !== 1 ? 's' : ''} found
          </p>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={!hasPreviousPage}
              className="h-7 px-2 text-xs"
            >
              <ChevronLeft className="h-3 w-3 mr-1" />
              Previous
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={!hasNextPage}
              className="h-7 px-2 text-xs"
            >
              Next
              <ChevronRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
