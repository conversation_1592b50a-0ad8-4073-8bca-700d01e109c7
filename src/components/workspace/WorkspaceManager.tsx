'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Play, 
  Square, 
  RefreshCw, 
  Monitor, 
  HardDrive,
  Cpu,
  MoreHorizontal,
  Plus,
  Search,
  Filter
} from 'lucide-react';
import { WorkspaceInfo } from '@/types/docker';
import { CreateWorkspaceDialog } from './CreateWorkspaceDialog';
import { WorkspaceCard } from './WorkspaceCard';
import { useDocker } from '@/hooks/useDocker';

interface WorkspaceManagerProps {
  onSelectWorkspace: (workspace: WorkspaceInfo) => void;
  userId?: string;
}

export const WorkspaceManager: React.FC<WorkspaceManagerProps> = ({ 
  onSelectWorkspace, 
  userId = 'default-user' 
}) => {
  const [workspaces, setWorkspaces] = useState<WorkspaceInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateWizardOpen, setIsCreateWizardOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Fetch workspaces
  const fetchWorkspaces = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workspaces?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setWorkspaces(data.data);
      } else {
        console.error('Failed to fetch workspaces:', data.error);
      }
    } catch (error) {
      console.error('Error fetching workspaces:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWorkspaces();
  }, [userId]);

  // Workspace actions
  const startWorkspace = async (id: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/workspaces/${id}?action=start`, {
        method: 'POST',
      });
      
      if (response.ok) {
        await fetchWorkspaces(); // Refresh the list
      }
    } catch (error) {
      console.error('Error starting workspace:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const stopWorkspace = async (id: string) => {
    try {
      setActionLoading(id);
      const response = await fetch(`/api/workspaces/${id}?action=stop`, {
        method: 'POST',
      });
      
      if (response.ok) {
        await fetchWorkspaces(); // Refresh the list
      }
    } catch (error) {
      console.error('Error stopping workspace:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const deleteWorkspace = async (id: string) => {
    if (!confirm('Are you sure you want to delete this workspace? This action cannot be undone.')) {
      return;
    }

    try {
      setActionLoading(id);
      const response = await fetch(`/api/workspaces/${id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        await fetchWorkspaces(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting workspace:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const connectToWorkspace = (workspace: WorkspaceInfo) => {
    if (workspace.status === 'running') {
      onSelectWorkspace(workspace);
    }
  };

  const getStatusColor = (status: WorkspaceInfo['status']) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-red-100 text-red-800';
      case 'starting': return 'bg-yellow-100 text-yellow-800';
      case 'stopping': return 'bg-orange-100 text-orange-800';
      case 'paused': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getWorkspaceTypeIcon = (type: string) => {
    switch (type) {
      case 'ubuntu-desktop': return '🖥️';
      case 'development-env': return '💻';
      case 'minimal-desktop': return '⚡';
      default: return '🖥️';
    }
  };

  const getWorkspaceTypeName = (type: string) => {
    switch (type) {
      case 'ubuntu-desktop': return 'Ubuntu Desktop';
      case 'development-env': return 'Development Environment';
      case 'minimal-desktop': return 'Minimal Desktop';
      default: return type;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading workspaces...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Workspace Management</h2>
          <p className="text-gray-600">Manage your ephemeral desktop environments</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchWorkspaces}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => setIsCreateWizardOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Workspace
          </Button>
        </div>
      </div>

      {workspaces.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Monitor className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No workspaces found</h3>
            <p className="text-gray-600 text-center mb-4">
              Create your first workspace to get started with remote desktop environments.
            </p>
            <Button onClick={() => setIsCreateWizardOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Workspace
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {workspaces.map((workspace) => (
            <Card key={workspace.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-xl">
                      {getWorkspaceTypeIcon(workspace.workspaceType)}
                    </span>
                    <CardTitle className="text-lg">{workspace.name}</CardTitle>
                  </div>
                  <Badge className={getStatusColor(workspace.status)}>
                    {workspace.status}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  {getWorkspaceTypeName(workspace.workspaceType)}
                </p>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-gray-500" />
                    <span>{workspace.resources.cpuLimit} CPU</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-gray-500" />
                    <span>{workspace.resources.memoryLimit}MB RAM</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-gray-500" />
                    <span>:{workspace.vncPort}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span>{workspace.userId}</span>
                  </div>
                </div>

                <div className="text-xs text-gray-500 flex items-center gap-2">
                  <Clock className="h-3 w-3" />
                  Created {workspace.created.toLocaleDateString()}
                </div>

                <div className="flex gap-2">
                  {workspace.status === 'stopped' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => startWorkspace(workspace.id)}
                      disabled={actionLoading === workspace.id}
                      className="flex-1"
                    >
                      <Play className="h-4 w-4 mr-1" />
                      {actionLoading === workspace.id ? 'Starting...' : 'Start'}
                    </Button>
                  )}
                  
                  {workspace.status === 'running' && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => connectToWorkspace(workspace)}
                        className="flex-1"
                      >
                        <Monitor className="h-4 w-4 mr-1" />
                        Connect
                      </Button>
                      {workspace.guacamoleConnectionId && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(`/guacamole/#/client/${workspace.guacamoleConnectionId}`, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => stopWorkspace(workspace.id)}
                        disabled={actionLoading === workspace.id}
                      >
                        <Square className="h-4 w-4" />
                      </Button>
                    </>
                  )}

                  {(workspace.status === 'starting' || workspace.status === 'stopping') && (
                    <Button size="sm" disabled className="flex-1">
                      <Activity className="h-4 w-4 mr-1 animate-spin" />
                      {workspace.status === 'starting' ? 'Starting...' : 'Stopping...'}
                    </Button>
                  )}

                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => deleteWorkspace(workspace.id)}
                    disabled={workspace.status === 'running' || actionLoading === workspace.id}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <WorkspaceCreationWizard
        open={isCreateWizardOpen}
        onOpenChange={setIsCreateWizardOpen}
        userId={userId}
        onWorkspaceCreated={fetchWorkspaces}
      />
    </div>
  );
};
