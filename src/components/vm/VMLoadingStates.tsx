/**
 * VM Loading States Components
 * Loading indicators and skeleton states for VM components
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Loader2, 
  Server, 
  Activity, 
  RefreshCw,
  Wifi,
  Database,
  Shield,
  Monitor
} from 'lucide-react';
import { BaseVMComponentProps } from '@/types/vm-frontend';

interface VMLoadingProps extends BaseVMComponentProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  type?: 'spinner' | 'pulse' | 'dots' | 'bars';
  color?: string;
}

interface VMSkeletonProps extends BaseVMComponentProps {
  lines?: number;
  height?: string;
  width?: string;
}

const sizeConfig = {
  sm: {
    spinner: 'h-4 w-4',
    text: 'text-sm',
    container: 'p-2'
  },
  md: {
    spinner: 'h-6 w-6',
    text: 'text-base',
    container: 'p-4'
  },
  lg: {
    spinner: 'h-8 w-8',
    text: 'text-lg',
    container: 'p-6'
  }
};

// Basic loading spinner
export function VMLoading({
  message = 'Loading...',
  size = 'md',
  type = 'spinner',
  color = 'text-blue-500',
  className,
  animate = true,
  ...props
}: VMLoadingProps) {
  const sizeStyles = sizeConfig[size];

  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  };

  const dotsVariants = {
    animate: {
      transition: {
        staggerChildren: 0.2,
        repeat: Infinity,
        repeatType: 'loop' as const
      }
    }
  };

  const dotVariants = {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 0.6,
        ease: 'easeInOut'
      }
    }
  };

  const renderLoader = () => {
    switch (type) {
      case 'pulse':
        return (
          <motion.div
            className={cn('rounded-full bg-current', sizeStyles.spinner)}
            variants={animate ? pulseVariants : undefined}
            animate={animate ? 'animate' : undefined}
          />
        );
      
      case 'dots':
        return (
          <motion.div
            className="flex gap-1"
            variants={animate ? dotsVariants : undefined}
            animate={animate ? 'animate' : undefined}
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-current rounded-full"
                variants={animate ? dotVariants : undefined}
              />
            ))}
          </motion.div>
        );
      
      case 'bars':
        return (
          <div className="flex gap-1">
            {[0, 1, 2, 3].map((i) => (
              <motion.div
                key={i}
                className="w-1 bg-current rounded-full"
                style={{ height: '16px' }}
                animate={animate ? {
                  scaleY: [1, 2, 1],
                  transition: {
                    duration: 1,
                    repeat: Infinity,
                    delay: i * 0.1
                  }
                } : undefined}
              />
            ))}
          </div>
        );
      
      default:
        return (
          <motion.div
            variants={animate ? spinnerVariants : undefined}
            animate={animate ? 'animate' : undefined}
          >
            <Loader2 className={cn(sizeStyles.spinner)} />
          </motion.div>
        );
    }
  };

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center gap-3',
        sizeStyles.container,
        color,
        className
      )}
      {...props}
    >
      {renderLoader()}
      {message && (
        <span className={cn('text-muted-foreground font-medium', sizeStyles.text)}>
          {message}
        </span>
      )}
    </div>
  );
}

// Skeleton loading component
export function VMSkeleton({
  lines = 3,
  height = 'h-4',
  width = 'w-full',
  className,
  animate = true,
  ...props
}: VMSkeletonProps) {
  const skeletonVariants = {
    animate: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  };

  return (
    <div className={cn('space-y-3', className)} {...props}>
      {Array.from({ length: lines }).map((_, i) => (
        <motion.div
          key={i}
          className={cn(
            'bg-muted rounded',
            height,
            i === lines - 1 ? 'w-3/4' : width
          )}
          variants={animate ? skeletonVariants : undefined}
          animate={animate ? 'animate' : undefined}
          style={animate ? undefined : { opacity: 0.5 }}
        />
      ))}
    </div>
  );
}

// Specialized loading states for VM operations
export function VMConnectionLoading({
  message = 'Establishing connection...',
  ...props
}: Omit<VMLoadingProps, 'type'>) {
  return (
    <div className="flex items-center gap-3">
      <motion.div
        animate={{
          scale: [1, 1.2, 1],
          transition: {
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }
        }}
      >
        <Wifi className="h-5 w-5 text-blue-500" />
      </motion.div>
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
}

export function VMDockerLoading({
  message = 'Loading containers...',
  ...props
}: Omit<VMLoadingProps, 'type'>) {
  return (
    <div className="flex items-center gap-3">
      <motion.div
        animate={{
          rotate: 360,
          transition: {
            duration: 2,
            repeat: Infinity,
            ease: 'linear'
          }
        }}
      >
        <Database className="h-5 w-5 text-blue-500" />
      </motion.div>
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
}

export function VMMonitoringLoading({
  message = 'Collecting metrics...',
  ...props
}: Omit<VMLoadingProps, 'type'>) {
  return (
    <div className="flex items-center gap-3">
      <motion.div
        animate={{
          scale: [1, 1.1, 1],
          transition: {
            duration: 1,
            repeat: Infinity,
            ease: 'easeInOut'
          }
        }}
      >
        <Activity className="h-5 w-5 text-green-500" />
      </motion.div>
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
}

export function VMSecurityLoading({
  message = 'Checking permissions...',
  ...props
}: Omit<VMLoadingProps, 'type'>) {
  return (
    <div className="flex items-center gap-3">
      <motion.div
        animate={{
          opacity: [0.5, 1, 0.5],
          transition: {
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut'
          }
        }}
      >
        <Shield className="h-5 w-5 text-orange-500" />
      </motion.div>
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
}

// Card skeleton for VM components
export function VMCardSkeleton({
  className,
  ...props
}: VMSkeletonProps) {
  return (
    <div className={cn('bg-card border border-border rounded-lg p-4', className)} {...props}>
      <div className="flex items-center gap-3 mb-4">
        <VMSkeleton lines={1} height="h-5" width="w-5" />
        <VMSkeleton lines={1} height="h-4" width="w-24" />
      </div>
      <VMSkeleton lines={2} height="h-3" />
      <div className="mt-4">
        <VMSkeleton lines={1} height="h-2" width="w-full" />
      </div>
    </div>
  );
}

// Metrics skeleton
export function VMMetricsSkeleton({
  count = 4,
  className,
  ...props
}: VMSkeletonProps & { count?: number }) {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4', className)} {...props}>
      {Array.from({ length: count }).map((_, i) => (
        <VMCardSkeleton key={i} />
      ))}
    </div>
  );
}

// Table skeleton for VM lists
export function VMTableSkeleton({
  rows = 5,
  columns = 4,
  className,
  ...props
}: VMSkeletonProps & { rows?: number; columns?: number }) {
  return (
    <div className={cn('space-y-3', className)} {...props}>
      {/* Header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, i) => (
          <VMSkeleton key={i} lines={1} height="h-4" width="w-20" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <VMSkeleton key={colIndex} lines={1} height="h-3" />
          ))}
        </div>
      ))}
    </div>
  );
}

// Full page loading state
export function VMPageLoading({
  title = 'Loading VM Dashboard',
  subtitle = 'Please wait while we load your virtual machine data...',
  className,
  ...props
}: {
  title?: string;
  subtitle?: string;
  className?: string;
} & BaseVMComponentProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center min-h-[400px] gap-6', className)} {...props}>
      <motion.div
        className="relative"
        animate={{
          rotate: 360,
          transition: {
            duration: 3,
            repeat: Infinity,
            ease: 'linear'
          }
        }}
      >
        <Server className="h-12 w-12 text-blue-500" />
        <motion.div
          className="absolute inset-0 border-2 border-blue-500/20 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5],
            transition: {
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }
          }}
        />
      </motion.div>
      
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold">{title}</h3>
        <p className="text-sm text-muted-foreground max-w-md">{subtitle}</p>
      </div>
      
      <div className="flex gap-2">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-blue-500 rounded-full"
            animate={{
              y: [0, -8, 0],
              transition: {
                duration: 0.6,
                repeat: Infinity,
                delay: i * 0.1
              }
            }}
          />
        ))}
      </div>
    </div>
  );
}
