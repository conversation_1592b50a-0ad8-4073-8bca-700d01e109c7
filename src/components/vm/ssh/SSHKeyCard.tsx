/**
 * SSH Key Card Component
 * Displays SSH key information with management controls
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Key, 
  Copy, 
  Download, 
  Edit, 
  Trash2, 
  TestTube, 
  Users, 
  Server, 
  Eye, 
  EyeOff,
  Calendar,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  MoreVertical
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { SSHKeyCardProps } from '@/types/vm-frontend';

export function SSHKeyCard({
  sshKey,
  onEdit,
  onDelete,
  onAssign,
  onExport,
  onTest,
  showPrivateKey = false,
  showUsage = true,
  className,
  animate = true,
  variant = 'default',
  ...props
}: SSHKeyCardProps) {
  const [showKey, setShowKey] = React.useState(false);
  const [copied, setCopied] = React.useState(false);

  const getKeyTypeColor = (keyType: string) => {
    switch (keyType) {
      case 'rsa': return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'ed25519': return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'ecdsa': return 'bg-purple-500/10 text-purple-500 border-purple-500/20';
      default: return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  const getStatusColor = (isActive: boolean, expiresAt?: Date) => {
    if (!isActive) return 'text-gray-500';
    if (expiresAt && expiresAt < new Date()) return 'text-red-500';
    return 'text-green-500';
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'gradient':
        return 'bg-gradient-to-br from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10';
      default:
        return 'bg-card border border-border';
    }
  };

  const formatFingerprint = (fingerprint: string) => {
    return fingerprint.replace(/(.{2})/g, '$1:').slice(0, -1);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const truncateKey = (key: string, length: number = 50) => {
    if (key.length <= length) return key;
    return `${key.substring(0, length)}...`;
  };

  const isExpired = sshKey.expiresAt && sshKey.expiresAt < new Date();
  const isExpiringSoon = sshKey.expiresAt && 
    sshKey.expiresAt > new Date() && 
    sshKey.expiresAt < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  const cardVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut" as const
      }
    },
    exit: { 
      opacity: 0, 
      y: -20, 
      scale: 0.95,
      transition: {
        duration: 0.2
      }
    },
    hover: {
      y: -2,
      transition: {
        duration: 0.2,
        ease: "easeOut" as const
      }
    }
  };

  const Component = animate ? motion.div : Card;
  const componentProps = animate ? {
    variants: cardVariants,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit',
    whileHover: 'hover'
  } : {};

  return (
    <TooltipProvider>
      <Component
        className={cn(
          'transition-all duration-200 cursor-pointer',
          getVariantStyles(),
          'hover:shadow-lg',
          isExpired && 'ring-2 ring-red-500/20',
          isExpiringSoon && 'ring-2 ring-orange-500/20',
          className
        )}
        {...componentProps}
        {...props}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Key className="h-8 w-8 text-blue-500" />
                {sshKey.isEncrypted && (
                  <Shield className="absolute -top-1 -right-1 h-4 w-4 text-green-500" />
                )}
              </div>
              <div>
                <h3 className="font-semibold text-lg">{sshKey.name}</h3>
                {sshKey.description && (
                  <p className="text-sm text-muted-foreground">
                    {sshKey.description}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {(isExpired || isExpiringSoon) && (
                <Tooltip>
                  <TooltipTrigger>
                    <AlertTriangle className={cn(
                      'h-5 w-5',
                      isExpired ? 'text-red-500' : 'text-orange-500'
                    )} />
                  </TooltipTrigger>
                  <TooltipContent>
                    {isExpired ? 'Key has expired' : 'Key expires soon'}
                  </TooltipContent>
                </Tooltip>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(sshKey)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Key
                    </DropdownMenuItem>
                  )}
                  {onAssign && (
                    <DropdownMenuItem onClick={() => onAssign(sshKey.id)}>
                      <Users className="h-4 w-4 mr-2" />
                      Manage Assignments
                    </DropdownMenuItem>
                  )}
                  {onTest && (
                    <DropdownMenuItem onClick={() => onTest(sshKey.id)}>
                      <TestTube className="h-4 w-4 mr-2" />
                      Test Connection
                    </DropdownMenuItem>
                  )}
                  {onExport && (
                    <DropdownMenuItem onClick={() => onExport(sshKey.id)}>
                      <Download className="h-4 w-4 mr-2" />
                      Export Key
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  {onDelete && (
                    <DropdownMenuItem 
                      onClick={() => onDelete(sshKey.id)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Key
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Key Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Type:</span>
                <Badge variant="outline" className={getKeyTypeColor(sshKey.keyType)}>
                  {sshKey.keyType.toUpperCase()} {sshKey.keySize}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Status:</span>
                <div className="flex items-center gap-1">
                  {sshKey.isActive ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-gray-500" />
                  )}
                  <span className={cn('text-xs font-medium', getStatusColor(sshKey.isActive, sshKey.expiresAt))}>
                    {isExpired ? 'Expired' : sshKey.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">VMs:</span>
                <span className="text-xs font-medium">
                  {sshKey.vmAssignments.length}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Users:</span>
                <span className="text-xs font-medium">
                  {sshKey.userAssignments.length}
                </span>
              </div>
            </div>
          </div>

          {/* Fingerprint */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Fingerprint:</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(sshKey.fingerprint)}
                className="h-6 px-2"
              >
                <Copy className="h-3 w-3 mr-1" />
                {copied ? 'Copied!' : 'Copy'}
              </Button>
            </div>
            <code className="text-xs font-mono bg-muted px-2 py-1 rounded block">
              {formatFingerprint(sshKey.fingerprint)}
            </code>
          </div>

          {/* Public Key */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Public Key:</span>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowKey(!showKey)}
                  className="h-6 px-2"
                >
                  {showKey ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(sshKey.publicKey)}
                  className="h-6 px-2"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <code className="text-xs font-mono bg-muted px-2 py-1 rounded block break-all">
              {showKey ? sshKey.publicKey : truncateKey(sshKey.publicKey)}
            </code>
          </div>

          {/* Usage Stats */}
          {showUsage && (
            <div className="flex items-center justify-between text-sm pt-2 border-t">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  Used {sshKey.usageCount} times
                </span>
              </div>
              {sshKey.lastUsed && (
                <span className="text-muted-foreground">
                  Last: {formatDate(sshKey.lastUsed)}
                </span>
              )}
            </div>
          )}

          {/* Tags */}
          {sshKey.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 pt-2 border-t">
              {sshKey.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Expiration Warning */}
          {(isExpired || isExpiringSoon) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className={cn(
                'p-3 rounded-lg border',
                isExpired 
                  ? 'bg-red-500/10 border-red-500/20 text-red-700 dark:text-red-300'
                  : 'bg-orange-500/10 border-orange-500/20 text-orange-700 dark:text-orange-300'
              )}
            >
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">
                    {isExpired ? 'Key Expired' : 'Key Expiring Soon'}
                  </p>
                  <p className="text-xs mt-1">
                    {isExpired 
                      ? `Expired on ${formatDate(sshKey.expiresAt!)}`
                      : `Expires on ${formatDate(sshKey.expiresAt!)}`
                    }
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Component>
    </TooltipProvider>
  );
}

// Compact version for lists
export function SSHKeyCardCompact({
  sshKey,
  onEdit,
  onDelete,
  className,
  ...props
}: Omit<SSHKeyCardProps, 'showPrivateKey' | 'showUsage'>) {
  const isExpired = sshKey.expiresAt && sshKey.expiresAt < new Date();

  return (
    <div
      className={cn(
        'flex items-center justify-between p-3 bg-card border border-border rounded-lg',
        'hover:shadow-sm transition-all duration-200',
        isExpired && 'ring-1 ring-red-500/20',
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3">
        <Key className="h-5 w-5 text-blue-500" />
        <div>
          <p className="font-medium">{sshKey.name}</p>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{sshKey.keyType.toUpperCase()}</span>
            <span>•</span>
            <span>{sshKey.vmAssignments.length} VMs</span>
            <span>•</span>
            <span>{sshKey.usageCount} uses</span>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Badge variant={sshKey.isActive ? 'default' : 'secondary'}>
          {isExpired ? 'Expired' : sshKey.isActive ? 'Active' : 'Inactive'}
        </Badge>
        
        {onEdit && (
          <Button variant="ghost" size="sm" onClick={() => onEdit(sshKey)}>
            <Edit className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
