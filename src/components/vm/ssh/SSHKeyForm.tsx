/**
 * SSH Key Form Component
 * Form for creating, editing, and importing SSH keys
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { cn } from '@/lib/utils';
import { 
  Key, 
  Upload, 
  Download, 
  Eye, 
  EyeOff, 
  Save, 
  X,
  AlertCircle,
  CheckCircle,
  Loader2,
  Calendar,
  Tag,
  Shield
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SSHKeyFormProps } from '@/types/vm-frontend';

const sshKeyGenerationSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional(),
  keyType: z.enum(['rsa', 'ed25519', 'ecdsa'], {
    required_error: 'Key type is required',
  }),
  keySize: z.number().int().min(1024).max(8192).optional(),
  passphrase: z.string().optional(),
  tags: z.array(z.string()).optional(),
  expiresAt: z.date().optional(),
});

const sshKeyImportSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional(),
  publicKey: z.string().min(1, 'Public key is required'),
  privateKey: z.string().optional(),
  passphrase: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

type GenerationFormData = z.infer<typeof sshKeyGenerationSchema>;
type ImportFormData = z.infer<typeof sshKeyImportSchema>;

export function SSHKeyForm({
  initialData,
  mode,
  onSubmit,
  onCancel,
  loading = false,
  className,
  animate = true,
  variant = 'default',
  ...props
}: SSHKeyFormProps) {
  const [showPassphrase, setShowPassphrase] = React.useState(false);
  const [showPrivateKey, setShowPrivateKey] = React.useState(false);
  const [tagInput, setTagInput] = React.useState('');
  const [tags, setTags] = React.useState<string[]>(initialData?.tags || []);
  const [hasExpiration, setHasExpiration] = React.useState(!!initialData?.expiresAt);

  const isGeneration = mode === 'create';
  const isImport = mode === 'import';
  const isEdit = mode === 'edit';

  const schema = isImport ? sshKeyImportSchema : sshKeyGenerationSchema;

  const form = useForm<GenerationFormData | ImportFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      keyType: (initialData as any)?.keyType || 'ed25519',
      keySize: (initialData as any)?.keySize || 2048,
      publicKey: (initialData as any)?.publicKey || '',
      privateKey: (initialData as any)?.privateKey || '',
      passphrase: '',
      tags: initialData?.tags || [],
    },
  });

  const keyType = form.watch('keyType' as any);

  const handleSubmit = async (data: GenerationFormData | ImportFormData) => {
    try {
      const submitData = {
        ...data,
        tags,
        ...(hasExpiration && { expiresAt: form.getValues('expiresAt' as any) })
      };
      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const getKeySizeOptions = (keyType: string) => {
    switch (keyType) {
      case 'rsa':
        return [2048, 3072, 4096];
      case 'ecdsa':
        return [256, 384, 521];
      default:
        return [];
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'gradient':
        return 'bg-gradient-to-br from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10';
      default:
        return 'bg-card border border-border';
    }
  };

  const formVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut" as const
      }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: {
        duration: 0.2
      }
    }
  };

  const Component = animate ? motion.div : Card;
  const componentProps = animate ? {
    variants: formVariants,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit'
  } : {};

  return (
    <Component
      className={cn(getVariantStyles(), className)}
      {...componentProps}
      {...props}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          {mode === 'create' && 'Generate SSH Key'}
          {mode === 'import' && 'Import SSH Key'}
          {mode === 'edit' && 'Edit SSH Key'}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Key Name</FormLabel>
                    <FormControl>
                      <Input placeholder="my-ssh-key" {...field} />
                    </FormControl>
                    <FormDescription>
                      Unique name for this SSH key
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Key for production servers" {...field} />
                    </FormControl>
                    <FormDescription>
                      Brief description of key usage
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Key Configuration */}
            {(isGeneration || isEdit) && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="keyType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Key Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select key type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="ed25519">Ed25519 (Recommended)</SelectItem>
                            <SelectItem value="rsa">RSA</SelectItem>
                            <SelectItem value="ecdsa">ECDSA</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Ed25519 is recommended for new keys
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {(keyType === 'rsa' || keyType === 'ecdsa') && (
                    <FormField
                      control={form.control}
                      name="keySize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Key Size</FormLabel>
                          <Select 
                            onValueChange={(value) => field.onChange(parseInt(value))} 
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select key size" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {getKeySizeOptions(keyType).map((size) => (
                                <SelectItem key={size} value={size.toString()}>
                                  {size} bits
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>
            )}

            {/* Import Fields */}
            {isImport && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="publicKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Public Key</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI..."
                          className="min-h-[120px] font-mono text-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Paste your SSH public key here
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="privateKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Private Key (Optional)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea 
                            type={showPrivateKey ? 'text' : 'password'}
                            placeholder="-----BEGIN PRIVATE KEY-----&#10;...&#10;-----END PRIVATE KEY-----"
                            className="min-h-[120px] font-mono text-sm pr-10"
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-2 top-2"
                            onClick={() => setShowPrivateKey(!showPrivateKey)}
                          >
                            {showPrivateKey ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Private key for authentication (stored encrypted)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Passphrase */}
            <FormField
              control={form.control}
              name="passphrase"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Passphrase (Optional)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input 
                        type={showPassphrase ? 'text' : 'password'}
                        placeholder="Enter passphrase for key encryption"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassphrase(!showPassphrase)}
                      >
                        {showPassphrase ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    {isGeneration 
                      ? 'Passphrase to encrypt the generated private key'
                      : 'Passphrase if the private key is encrypted'
                    }
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags */}
            <div className="space-y-3">
              <Label>Tags (Optional)</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add tag"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" variant="outline" onClick={addTag}>
                  <Tag className="h-4 w-4 mr-2" />
                  Add
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <div
                      key={tag}
                      className="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md text-sm"
                    >
                      <span>{tag}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => removeTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Expiration */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Key Expiration</Label>
                <Switch
                  checked={hasExpiration}
                  onCheckedChange={setHasExpiration}
                />
              </div>
              {hasExpiration && (
                <FormField
                  control={form.control}
                  name="expiresAt"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                          value={field.value ? new Date(field.value).toISOString().slice(0, 16) : ''}
                          onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormDescription>
                        When this key should expire and become inactive
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Shield className="h-4 w-4" />
                <span>Keys are stored encrypted and secure</span>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={onCancel}
                  disabled={loading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {mode === 'create' && 'Generate Key'}
                  {mode === 'import' && 'Import Key'}
                  {mode === 'edit' && 'Update Key'}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Component>
  );
}
