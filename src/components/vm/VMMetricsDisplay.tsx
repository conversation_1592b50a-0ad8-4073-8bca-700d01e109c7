/**
 * VM Metrics Display Component
 * Displays VM resource metrics with progress bars and visual indicators
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Cpu, 
  MemoryStick, 
  HardDrive, 
  Network,
  Activity,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { BaseVMComponentProps } from '@/types/vm-frontend';

interface VMMetricProps extends BaseVMComponentProps {
  label: string;
  value: number;
  unit: string;
  max?: number;
  threshold?: number;
  icon?: React.ComponentType<any>;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  showTrend?: boolean;
  showIcon?: boolean;
}

interface VMMetricsDisplayProps extends BaseVMComponentProps {
  metrics: {
    cpu: {
      usage: number;
      loadAverage?: number[];
      processes?: number;
    };
    memory: {
      total: number;
      used: number;
      free: number;
      usagePercent: number;
    };
    disk: {
      total: number;
      used: number;
      free: number;
      usagePercent: number;
    };
    network?: {
      bytesIn: number;
      bytesOut: number;
      packetsIn: number;
      packetsOut: number;
    };
  };
  showTrends?: boolean;
  compact?: boolean;
  thresholds?: {
    cpu: number;
    memory: number;
    disk: number;
  };
}

const defaultThresholds = {
  cpu: 80,
  memory: 85,
  disk: 90
};

const sizeConfig = {
  sm: {
    container: 'p-3',
    title: 'text-sm',
    value: 'text-lg',
    unit: 'text-xs',
    icon: 'h-4 w-4',
    progress: 'h-1.5'
  },
  md: {
    container: 'p-4',
    title: 'text-sm',
    value: 'text-xl',
    unit: 'text-sm',
    icon: 'h-5 w-5',
    progress: 'h-2'
  },
  lg: {
    container: 'p-6',
    title: 'text-base',
    value: 'text-2xl',
    unit: 'text-base',
    icon: 'h-6 w-6',
    progress: 'h-3'
  }
};

function VMMetric({
  label,
  value,
  unit,
  max = 100,
  threshold = 80,
  icon: Icon,
  trend,
  trendValue,
  color,
  size = 'md',
  showPercentage = true,
  showTrend = true,
  showIcon = true,
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMMetricProps) {
  const sizeStyles = sizeConfig[size];
  const percentage = max > 0 ? (value / max) * 100 : 0;
  
  const getColor = () => {
    if (color) return color;
    if (percentage >= threshold) return 'text-red-500';
    if (percentage >= threshold * 0.8) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getProgressColor = () => {
    if (color) return color.replace('text-', 'bg-');
    if (percentage >= threshold) return 'bg-red-500';
    if (percentage >= threshold * 0.8) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return TrendingUp;
      case 'down': return TrendingDown;
      default: return Minus;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-red-500';
      case 'down': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'gradient':
        return 'bg-gradient-to-br from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10';
      default:
        return 'bg-card border border-border';
    }
  };

  const progressVariants = {
    initial: { width: 0 },
    animate: { 
      width: `${percentage}%`,
      transition: {
        duration: 1,
        ease: 'easeOut'
      }
    }
  };

  const valueVariants = {
    initial: { opacity: 0, y: 10 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        delay: 0.2
      }
    }
  };

  const TrendIcon = getTrendIcon();

  return (
    <motion.div
      className={cn(
        'rounded-lg transition-all duration-200',
        sizeStyles.container,
        getVariantStyles(),
        'hover:shadow-md',
        className
      )}
      initial={animate ? { opacity: 0, scale: 0.95 } : undefined}
      animate={animate ? { opacity: 1, scale: 1 } : undefined}
      transition={animate ? { duration: 0.3 } : undefined}
      {...props}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {showIcon && Icon && (
            <Icon className={cn(sizeStyles.icon, getColor())} />
          )}
          <span className={cn('font-medium text-muted-foreground', sizeStyles.title)}>
            {label}
          </span>
        </div>
        {showTrend && trend && trendValue !== undefined && (
          <div className={cn('flex items-center gap-1', getTrendColor())}>
            <TrendIcon className="h-3 w-3" />
            <span className="text-xs font-medium">
              {trendValue > 0 ? '+' : ''}{trendValue.toFixed(1)}%
            </span>
          </div>
        )}
      </div>

      <div className="flex items-baseline gap-2 mb-3">
        <motion.span
          className={cn('font-bold', getColor(), sizeStyles.value)}
          variants={animate ? valueVariants : undefined}
          initial={animate ? 'initial' : undefined}
          animate={animate ? 'animate' : undefined}
        >
          {value.toFixed(1)}
        </motion.span>
        <span className={cn('text-muted-foreground', sizeStyles.unit)}>
          {unit}
        </span>
        {showPercentage && max > 0 && (
          <span className={cn('text-muted-foreground text-xs')}>
            ({percentage.toFixed(1)}%)
          </span>
        )}
      </div>

      <div className={cn('bg-muted rounded-full overflow-hidden', sizeStyles.progress)}>
        <motion.div
          className={cn('h-full rounded-full', getProgressColor())}
          variants={animate ? progressVariants : undefined}
          initial={animate ? 'initial' : undefined}
          animate={animate ? 'animate' : undefined}
          style={!animate ? { width: `${percentage}%` } : undefined}
        />
      </div>

      {threshold && percentage >= threshold && (
        <div className="mt-2 text-xs text-red-500 font-medium">
          Above threshold ({threshold}%)
        </div>
      )}
    </motion.div>
  );
}

export function VMMetricsDisplay({
  metrics,
  showTrends = true,
  compact = false,
  thresholds = defaultThresholds,
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMMetricsDisplayProps) {
  const formatBytes = (bytes: number): { value: number; unit: string } => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let value = bytes;
    let unitIndex = 0;

    while (value >= 1024 && unitIndex < units.length - 1) {
      value /= 1024;
      unitIndex++;
    }

    return { value, unit: units[unitIndex] };
  };

  const formatNetworkBytes = (bytes: number): { value: number; unit: string } => {
    const formatted = formatBytes(bytes);
    return { ...formatted, unit: `${formatted.unit}/s` };
  };

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const size = compact ? 'sm' : 'md';

  return (
    <motion.div
      className={cn(
        'grid gap-4',
        compact ? 'grid-cols-2 lg:grid-cols-4' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
        className
      )}
      variants={animate ? containerVariants : undefined}
      initial={animate ? 'initial' : undefined}
      animate={animate ? 'animate' : undefined}
      {...props}
    >
      {/* CPU Metric */}
      <VMMetric
        label="CPU Usage"
        value={metrics.cpu.usage}
        unit="%"
        max={100}
        threshold={thresholds.cpu}
        icon={Cpu}
        size={size}
        showTrend={showTrends}
        animate={animate}
        variant={variant}
      />

      {/* Memory Metric */}
      <VMMetric
        label="Memory Usage"
        value={metrics.memory.usagePercent}
        unit="%"
        max={100}
        threshold={thresholds.memory}
        icon={MemoryStick}
        size={size}
        showTrend={showTrends}
        animate={animate}
        variant={variant}
      />

      {/* Disk Metric */}
      <VMMetric
        label="Disk Usage"
        value={metrics.disk.usagePercent}
        unit="%"
        max={100}
        threshold={thresholds.disk}
        icon={HardDrive}
        size={size}
        showTrend={showTrends}
        animate={animate}
        variant={variant}
      />

      {/* Network Metric (if available) */}
      {metrics.network && (
        <VMMetric
          label="Network I/O"
          value={metrics.network.bytesIn + metrics.network.bytesOut}
          unit="B/s"
          max={0} // No max for network
          icon={Network}
          size={size}
          showPercentage={false}
          showTrend={showTrends}
          animate={animate}
          variant={variant}
        />
      )}
    </motion.div>
  );
}

// Compact metrics display for smaller spaces
export function VMMetricsCompact({
  metrics,
  className,
  ...props
}: Omit<VMMetricsDisplayProps, 'compact'>) {
  return (
    <div className={cn('flex items-center gap-4', className)} {...props}>
      <div className="flex items-center gap-2">
        <Cpu className="h-4 w-4 text-blue-500" />
        <span className="text-sm font-medium">
          {metrics.cpu.usage.toFixed(1)}%
        </span>
      </div>
      <div className="flex items-center gap-2">
        <MemoryStick className="h-4 w-4 text-green-500" />
        <span className="text-sm font-medium">
          {metrics.memory.usagePercent.toFixed(1)}%
        </span>
      </div>
      <div className="flex items-center gap-2">
        <HardDrive className="h-4 w-4 text-orange-500" />
        <span className="text-sm font-medium">
          {metrics.disk.usagePercent.toFixed(1)}%
        </span>
      </div>
    </div>
  );
}

// Individual metric card
export function VMMetricCard({
  title,
  value,
  unit,
  icon: Icon,
  color = 'text-blue-500',
  trend,
  trendValue,
  className,
  ...props
}: {
  title: string;
  value: number;
  unit: string;
  icon?: React.ComponentType<any>;
  color?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  className?: string;
} & BaseVMComponentProps) {
  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus;
  const trendColor = trend === 'up' ? 'text-red-500' : trend === 'down' ? 'text-green-500' : 'text-gray-500';

  return (
    <motion.div
      className={cn(
        'bg-card border border-border rounded-lg p-4',
        'hover:shadow-md transition-all duration-200',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {Icon && <Icon className={cn('h-4 w-4', color)} />}
          <span className="text-sm font-medium text-muted-foreground">
            {title}
          </span>
        </div>
        {trend && trendValue !== undefined && (
          <div className={cn('flex items-center gap-1', trendColor)}>
            <TrendIcon className="h-3 w-3" />
            <span className="text-xs font-medium">
              {trendValue > 0 ? '+' : ''}{trendValue.toFixed(1)}%
            </span>
          </div>
        )}
      </div>
      <div className="flex items-baseline gap-2">
        <span className={cn('text-2xl font-bold', color)}>
          {value.toFixed(1)}
        </span>
        <span className="text-sm text-muted-foreground">
          {unit}
        </span>
      </div>
    </motion.div>
  );
}
