/**
 * VM Error Display Component
 * Error states and error handling components for VM operations
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  Info,
  AlertCircle,
  WifiOff,
  Server,
  Shield,
  Database,
  Activity,
  ChevronDown,
  ChevronUp,
  Copy,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BaseVMComponentProps } from '@/types/vm-frontend';

interface VMErrorProps extends BaseVMComponentProps {
  title?: string;
  message: string;
  details?: string;
  errorCode?: string;
  type?: 'error' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  showRetry?: boolean;
  showDetails?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  retryLabel?: string;
  dismissLabel?: string;
}

interface VMConnectionErrorProps extends Omit<VMErrorProps, 'type'> {
  connectionId?: string;
  vmId?: string;
}

const errorTypeConfig = {
  error: {
    icon: XCircle,
    color: 'text-red-500',
    bgColor: 'bg-red-500/10',
    borderColor: 'border-red-500/20',
    title: 'Error'
  },
  warning: {
    icon: AlertTriangle,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10',
    borderColor: 'border-yellow-500/20',
    title: 'Warning'
  },
  info: {
    icon: Info,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
    title: 'Information'
  }
};

const sizeConfig = {
  sm: {
    container: 'p-3',
    title: 'text-sm',
    message: 'text-xs',
    icon: 'h-4 w-4',
    button: 'h-7 px-2 text-xs'
  },
  md: {
    container: 'p-4',
    title: 'text-base',
    message: 'text-sm',
    icon: 'h-5 w-5',
    button: 'h-8 px-3 text-sm'
  },
  lg: {
    container: 'p-6',
    title: 'text-lg',
    message: 'text-base',
    icon: 'h-6 w-6',
    button: 'h-9 px-4 text-sm'
  }
};

export function VMError({
  title,
  message,
  details,
  errorCode,
  type = 'error',
  size = 'md',
  showRetry = false,
  showDetails = false,
  onRetry,
  onDismiss,
  retryLabel = 'Retry',
  dismissLabel = 'Dismiss',
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMErrorProps) {
  const [showDetailsExpanded, setShowDetailsExpanded] = React.useState(false);
  const [copied, setCopied] = React.useState(false);

  const config = errorTypeConfig[type];
  const sizeStyles = sizeConfig[size];
  const Icon = config.icon;

  const displayTitle = title || config.title;

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return cn(
          'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10',
          config.bgColor
        );
      case 'gradient':
        return cn(
          'bg-gradient-to-r from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10',
          config.bgColor
        );
      default:
        return cn(config.bgColor, config.borderColor, 'border');
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const containerVariants = {
    initial: { opacity: 0, scale: 0.95, y: 10 },
    animate: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.95, 
      y: -10,
      transition: {
        duration: 0.2
      }
    }
  };

  const detailsVariants = {
    initial: { height: 0, opacity: 0 },
    animate: { 
      height: 'auto', 
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      height: 0, 
      opacity: 0,
      transition: {
        duration: 0.2
      }
    }
  };

  const Component = animate ? motion.div : 'div';
  const componentProps = animate ? {
    variants: containerVariants,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit'
  } : {};

  return (
    <Component
      className={cn(
        'rounded-lg transition-all duration-200',
        sizeStyles.container,
        getVariantStyles(),
        className
      )}
      {...componentProps}
      {...props}
    >
      <div className="flex items-start gap-3">
        <Icon className={cn(sizeStyles.icon, config.color, 'flex-shrink-0 mt-0.5')} />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className={cn('font-semibold', config.color, sizeStyles.title)}>
              {displayTitle}
            </h4>
            {errorCode && (
              <span className={cn('text-xs font-mono px-2 py-1 rounded', config.bgColor, config.color)}>
                {errorCode}
              </span>
            )}
          </div>
          
          <p className={cn('text-muted-foreground', sizeStyles.message)}>
            {message}
          </p>

          {(details || showDetails) && (
            <div className="mt-3">
              <button
                onClick={() => setShowDetailsExpanded(!showDetailsExpanded)}
                className={cn(
                  'flex items-center gap-1 text-xs text-muted-foreground hover:text-foreground transition-colors',
                  config.color
                )}
              >
                {showDetailsExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                {showDetailsExpanded ? 'Hide Details' : 'Show Details'}
              </button>

              {showDetailsExpanded && (
                <motion.div
                  variants={animate ? detailsVariants : undefined}
                  initial={animate ? 'initial' : undefined}
                  animate={animate ? 'animate' : undefined}
                  exit={animate ? 'exit' : undefined}
                  className="mt-2 overflow-hidden"
                >
                  <div className={cn(
                    'p-3 rounded border text-xs font-mono whitespace-pre-wrap',
                    'bg-muted/50 border-border'
                  )}>
                    {details || 'No additional details available.'}
                  </div>
                  
                  {details && (
                    <div className="flex gap-2 mt-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(details)}
                        className="h-6 px-2 text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        {copied ? 'Copied!' : 'Copy'}
                      </Button>
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          )}

          <div className="flex items-center gap-2 mt-4">
            {showRetry && onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className={cn(sizeStyles.button)}
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                {retryLabel}
              </Button>
            )}
            
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className={cn(sizeStyles.button)}
              >
                {dismissLabel}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Component>
  );
}

// Specialized error components
export function VMConnectionError({
  connectionId,
  vmId,
  title = 'Connection Failed',
  message = 'Unable to establish connection to the virtual machine.',
  ...props
}: VMConnectionErrorProps) {
  return (
    <VMError
      title={title}
      message={message}
      type="error"
      showRetry
      showDetails
      details={`VM ID: ${vmId}\nConnection ID: ${connectionId}\nTimestamp: ${new Date().toISOString()}`}
      {...props}
    />
  );
}

export function VMDockerError({
  containerId,
  operation,
  title = 'Docker Operation Failed',
  message = 'The Docker operation could not be completed.',
  ...props
}: Omit<VMErrorProps, 'type'> & {
  containerId?: string;
  operation?: string;
}) {
  return (
    <VMError
      title={title}
      message={message}
      type="error"
      showRetry
      showDetails
      details={`Container ID: ${containerId}\nOperation: ${operation}\nTimestamp: ${new Date().toISOString()}`}
      {...props}
    />
  );
}

export function VMMonitoringError({
  metricType,
  title = 'Monitoring Error',
  message = 'Unable to collect system metrics.',
  ...props
}: Omit<VMErrorProps, 'type'> & {
  metricType?: string;
}) {
  return (
    <VMError
      title={title}
      message={message}
      type="error"
      showRetry
      showDetails
      details={`Metric Type: ${metricType}\nTimestamp: ${new Date().toISOString()}`}
      {...props}
    />
  );
}

export function VMSecurityError({
  userId,
  action,
  title = 'Security Error',
  message = 'Access denied or security check failed.',
  ...props
}: Omit<VMErrorProps, 'type'> & {
  userId?: string;
  action?: string;
}) {
  return (
    <VMError
      title={title}
      message={message}
      type="error"
      showDetails
      details={`User ID: ${userId}\nAction: ${action}\nTimestamp: ${new Date().toISOString()}`}
      {...props}
    />
  );
}

// Inline error component for smaller spaces
export function VMInlineError({
  message,
  type = 'error',
  onRetry,
  className,
  ...props
}: {
  message: string;
  type?: 'error' | 'warning' | 'info';
  onRetry?: () => void;
  className?: string;
} & BaseVMComponentProps) {
  const config = errorTypeConfig[type];
  const Icon = config.icon;

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)} {...props}>
      <Icon className={cn('h-4 w-4 flex-shrink-0', config.color)} />
      <span className="text-muted-foreground flex-1">{message}</span>
      {onRetry && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onRetry}
          className="h-6 px-2 text-xs"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}

// Empty state component
export function VMEmptyState({
  icon: Icon = Server,
  title = 'No Data Available',
  message = 'There is no data to display at this time.',
  actionLabel,
  onAction,
  className,
  ...props
}: {
  icon?: React.ComponentType<any>;
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
} & BaseVMComponentProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-12 text-center', className)} {...props}>
      <div className="mb-4 p-3 rounded-full bg-muted">
        <Icon className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-sm text-muted-foreground mb-6 max-w-md">{message}</p>
      {actionLabel && onAction && (
        <Button onClick={onAction} variant="outline">
          {actionLabel}
        </Button>
      )}
    </div>
  );
}

// Error boundary fallback
export function VMErrorFallback({
  error,
  resetError,
  className,
  ...props
}: {
  error: Error;
  resetError: () => void;
  className?: string;
} & BaseVMComponentProps) {
  return (
    <div className={cn('p-6 text-center', className)} {...props}>
      <div className="mb-4 p-3 rounded-full bg-red-500/10 inline-block">
        <AlertTriangle className="h-8 w-8 text-red-500" />
      </div>
      <h3 className="text-lg font-semibold mb-2 text-red-500">Something went wrong</h3>
      <p className="text-sm text-muted-foreground mb-4">
        An unexpected error occurred while loading this component.
      </p>
      <details className="mb-4 text-left">
        <summary className="cursor-pointer text-sm font-medium mb-2">Error Details</summary>
        <pre className="text-xs bg-muted p-3 rounded border overflow-auto">
          {error.message}
          {error.stack && `\n\n${error.stack}`}
        </pre>
      </details>
      <Button onClick={resetError} variant="outline">
        <RefreshCw className="h-4 w-4 mr-2" />
        Try Again
      </Button>
    </div>
  );
}
