/**
 * VM Connection Dashboard Component
 * Main dashboard for managing VM connections
 */

'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Plus, 
  Search, 
  Filter, 
  RefreshCw, 
  Grid, 
  List,
  Server,
  Wifi,
  WifiOff,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useVMConnection } from '@/hooks/vm';
import { VMConnectionConfig } from '@/types/vm';
import { BaseVMComponentProps, VMConnectionFormData } from '@/types/vm-frontend';
import { VMConnectionCard, VMConnectionCardCompact } from './VMConnectionCard';
import { VMConnectionForm } from './VMConnectionForm';
import { VMLoading, VMError, VMEmptyState } from '@/components/vm';

interface VMConnectionDashboardProps extends BaseVMComponentProps {
  onConnectionSelect?: (vmId: string, connectionId: string) => void;
}

type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'connected' | 'disconnected' | 'error';

export function VMConnectionDashboard({
  onConnectionSelect,
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMConnectionDashboardProps) {
  const [viewMode, setViewMode] = React.useState<ViewMode>('grid');
  const [filter, setFilter] = React.useState<FilterType>('all');
  const [searchQuery, setSearchQuery] = React.useState('');
  const [showCreateDialog, setShowCreateDialog] = React.useState(false);
  const [editingConnection, setEditingConnection] = React.useState<{
    vmId: string;
    config: VMConnectionConfig;
  } | null>(null);

  // Mock connections data - in real app this would come from a store or API
  const [connections, setConnections] = React.useState<Array<{
    vmId: string;
    config: VMConnectionConfig;
  }>>([
    {
      vmId: 'vm-001',
      config: {
        host: 'vm1.example.com',
        port: 22,
        username: 'admin',
        privateKey: '-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----',
        timeout: 30000,
        keepAlive: true,
        maxRetries: 3,
        retryDelay: 1000,
      }
    },
    {
      vmId: 'vm-002',
      config: {
        host: 'vm2.example.com',
        port: 22,
        username: 'root',
        password: 'password123',
        timeout: 30000,
        keepAlive: true,
        maxRetries: 3,
        retryDelay: 1000,
      }
    }
  ]);

  const vmConnection = useVMConnection({
    autoReconnect: true,
    maxRetryAttempts: 3,
    onConnectionEvent: (vmId, event, data) => {
      console.log(`VM ${vmId} event:`, event, data);
      if (event === 'connected' && onConnectionSelect) {
        onConnectionSelect(vmId, data.connectionId);
      }
    }
  });

  const handleCreateConnection = async (data: VMConnectionFormData) => {
    const config: VMConnectionConfig = {
      host: data.host,
      port: data.port,
      username: data.username,
      password: data.authType === 'password' ? data.password : undefined,
      privateKey: data.authType === 'key' ? data.privateKey : undefined,
      passphrase: data.passphrase,
      timeout: 30000,
      keepAlive: true,
      maxRetries: 3,
      retryDelay: 1000,
    };

    setConnections(prev => [...prev, { vmId: data.vmId, config }]);
    setShowCreateDialog(false);
  };

  const handleEditConnection = async (data: VMConnectionFormData) => {
    if (!editingConnection) return;

    const config: VMConnectionConfig = {
      host: data.host,
      port: data.port,
      username: data.username,
      password: data.authType === 'password' ? data.password : undefined,
      privateKey: data.authType === 'key' ? data.privateKey : undefined,
      passphrase: data.passphrase,
      timeout: 30000,
      keepAlive: true,
      maxRetries: 3,
      retryDelay: 1000,
    };

    setConnections(prev => 
      prev.map(conn => 
        conn.vmId === editingConnection.vmId 
          ? { ...conn, config }
          : conn
      )
    );
    setEditingConnection(null);
  };

  const handleDeleteConnection = (vmId: string) => {
    setConnections(prev => prev.filter(conn => conn.vmId !== vmId));
  };

  const handleConnect = async (vmId: string, config: VMConnectionConfig) => {
    await vmConnection.connect(vmId, config);
  };

  const handleDisconnect = async (vmId: string) => {
    const connectionState = vmConnection.connections.get(vmId);
    if (connectionState?.connectionId) {
      await vmConnection.disconnect(vmId, connectionState.connectionId);
    }
  };

  const filteredConnections = connections.filter(conn => {
    const connectionState = vmConnection.connections.get(conn.vmId);
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!conn.vmId.toLowerCase().includes(query) && 
          !conn.config.host.toLowerCase().includes(query) &&
          !conn.config.username.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply status filter
    switch (filter) {
      case 'connected':
        return connectionState?.isConnected === true;
      case 'disconnected':
        return !connectionState?.isConnected && !connectionState?.error;
      case 'error':
        return !!connectionState?.error;
      default:
        return true;
    }
  });

  const getConnectionStats = () => {
    const total = connections.length;
    const connected = connections.filter(conn => 
      vmConnection.connections.get(conn.vmId)?.isConnected
    ).length;
    const errors = connections.filter(conn => 
      vmConnection.connections.get(conn.vmId)?.error
    ).length;

    return { total, connected, errors };
  };

  const stats = getConnectionStats();

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <motion.div
      className={cn('space-y-6', className)}
      variants={animate ? containerVariants : undefined}
      initial={animate ? 'initial' : undefined}
      animate={animate ? 'animate' : undefined}
      {...props}
    >
      {/* Header */}
      <motion.div
        className="flex items-center justify-between"
        variants={animate ? itemVariants : undefined}
      >
        <div>
          <h1 className="text-3xl font-bold">VM Connections</h1>
          <p className="text-muted-foreground">
            Manage your virtual machine connections and SSH sessions
          </p>
        </div>

        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Connection
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create VM Connection</DialogTitle>
            </DialogHeader>
            <VMConnectionForm
              onSubmit={handleCreateConnection}
              onCancel={() => setShowCreateDialog(false)}
              animate={false}
            />
          </DialogContent>
        </Dialog>
      </motion.div>

      {/* Stats */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-4 gap-4"
        variants={animate ? itemVariants : undefined}
      >
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Server className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-sm text-muted-foreground">Total VMs</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Wifi className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{stats.connected}</p>
                <p className="text-sm text-muted-foreground">Connected</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <WifiOff className="h-8 w-8 text-gray-500" />
              <div>
                <p className="text-2xl font-bold">{stats.total - stats.connected}</p>
                <p className="text-sm text-muted-foreground">Disconnected</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-8 w-8 text-red-500" />
              <div>
                <p className="text-2xl font-bold">{stats.errors}</p>
                <p className="text-sm text-muted-foreground">Errors</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Controls */}
      <motion.div
        className="flex items-center justify-between gap-4"
        variants={animate ? itemVariants : undefined}
      >
        <div className="flex items-center gap-4 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search connections..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
                {filter !== 'all' && (
                  <Badge variant="secondary" className="ml-2">
                    {filter}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilter('all')}>
                All Connections
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter('connected')}>
                Connected Only
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter('disconnected')}>
                Disconnected Only
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter('error')}>
                Errors Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>

          <div className="flex items-center border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Connections List */}
      <motion.div variants={animate ? itemVariants : undefined}>
        {vmConnection.loading && (
          <VMLoading message="Loading connections..." />
        )}

        {vmConnection.error && (
          <VMError
            message={vmConnection.error}
            onRetry={() => window.location.reload()}
            showRetry
          />
        )}

        {!vmConnection.loading && !vmConnection.error && filteredConnections.length === 0 && (
          <VMEmptyState
            title="No connections found"
            message={searchQuery || filter !== 'all' 
              ? "No connections match your current filters."
              : "Get started by creating your first VM connection."
            }
            actionLabel={searchQuery || filter !== 'all' ? undefined : "Create Connection"}
            onAction={searchQuery || filter !== 'all' ? undefined : () => setShowCreateDialog(true)}
          />
        )}

        {!vmConnection.loading && !vmConnection.error && filteredConnections.length > 0 && (
          <AnimatePresence mode="popLayout">
            {viewMode === 'grid' ? (
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                layout
              >
                {filteredConnections.map((conn) => (
                  <motion.div
                    key={conn.vmId}
                    layout
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.2 }}
                  >
                    <VMConnectionCard
                      vmId={conn.vmId}
                      config={conn.config}
                      status={vmConnection.connections.get(conn.vmId) || {
                        isConnected: false,
                        isConnecting: false,
                        retryCount: 0
                      }}
                      onConnect={(config) => handleConnect(conn.vmId, config)}
                      onDisconnect={() => handleDisconnect(conn.vmId)}
                      onEdit={() => setEditingConnection(conn)}
                      onDelete={() => handleDeleteConnection(conn.vmId)}
                      variant={variant}
                    />
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <motion.div className="space-y-3" layout>
                {filteredConnections.map((conn) => (
                  <motion.div
                    key={conn.vmId}
                    layout
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <VMConnectionCardCompact
                      vmId={conn.vmId}
                      config={conn.config}
                      status={vmConnection.connections.get(conn.vmId) || {
                        isConnected: false,
                        isConnecting: false,
                        retryCount: 0
                      }}
                      onConnect={(config) => handleConnect(conn.vmId, config)}
                      onDisconnect={() => handleDisconnect(conn.vmId)}
                    />
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </motion.div>

      {/* Edit Dialog */}
      <Dialog 
        open={!!editingConnection} 
        onOpenChange={(open) => !open && setEditingConnection(null)}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit VM Connection</DialogTitle>
          </DialogHeader>
          {editingConnection && (
            <VMConnectionForm
              initialData={{
                vmId: editingConnection.vmId,
                host: editingConnection.config.host,
                port: editingConnection.config.port,
                username: editingConnection.config.username,
                authType: editingConnection.config.privateKey ? 'key' : 'password',
                password: editingConnection.config.password,
                privateKey: editingConnection.config.privateKey,
                passphrase: editingConnection.config.passphrase,
                saveCredentials: false
              }}
              onSubmit={handleEditConnection}
              onCancel={() => setEditingConnection(null)}
              animate={false}
            />
          )}
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}
