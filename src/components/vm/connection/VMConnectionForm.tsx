/**
 * VM Connection Form Component
 * Form for creating and editing VM connections
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { cn } from '@/lib/utils';
import { 
  Server, 
  Key, 
  Eye, 
  EyeOff, 
  TestTube, 
  Save, 
  X,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useSSHKeyManagement } from '@/hooks/vm/useSSHKeyManagement';
import { VMConnectionFormProps, VMConnectionFormData } from '@/types/vm-frontend';

const connectionFormSchema = z.object({
  vmId: z.string().min(1, 'VM ID is required').max(50, 'VM ID must be less than 50 characters'),
  host: z.string().min(1, 'Host is required').max(255, 'Host must be less than 255 characters'),
  port: z.number().int().min(1, 'Port must be at least 1').max(65535, 'Port must be less than 65536'),
  username: z.string().min(1, 'Username is required').max(100, 'Username must be less than 100 characters'),
  authType: z.enum(['password', 'key', 'stored-key'], {
    required_error: 'Authentication type is required',
  }),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  storedKeyId: z.string().optional(),
  passphrase: z.string().optional(),
  saveCredentials: z.boolean().default(false),
}).refine((data) => {
  if (data.authType === 'password' && !data.password) {
    return false;
  }
  if (data.authType === 'key' && !data.privateKey) {
    return false;
  }
  if (data.authType === 'stored-key' && !data.storedKeyId) {
    return false;
  }
  return true;
}, {
  message: 'Password is required for password authentication, private key is required for key authentication, stored key is required for stored key authentication',
  path: ['password'],
});

type ConnectionFormData = z.infer<typeof connectionFormSchema>;

export function VMConnectionForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMConnectionFormProps) {
  const [showPassword, setShowPassword] = React.useState(false);
  const [showPrivateKey, setShowPrivateKey] = React.useState(false);
  const [testingConnection, setTestingConnection] = React.useState(false);
  const [testResult, setTestResult] = React.useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // SSH Key Management
  const sshKeyManagement = useSSHKeyManagement();
  const availableKeys = sshKeyManagement.keys.filter(key => key.isActive);

  const form = useForm<ConnectionFormData>({
    resolver: zodResolver(connectionFormSchema),
    defaultValues: {
      vmId: initialData?.vmId || '',
      host: initialData?.host || '',
      port: initialData?.port || 22,
      username: initialData?.username || '',
      authType: initialData?.authType || 'password',
      password: initialData?.password || '',
      privateKey: initialData?.privateKey || '',
      passphrase: initialData?.passphrase || '',
      saveCredentials: initialData?.saveCredentials || false,
    },
  });

  const authType = form.watch('authType');

  const handleSubmit = async (data: ConnectionFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleTestConnection = async () => {
    const formData = form.getValues();
    const validation = connectionFormSchema.safeParse(formData);
    
    if (!validation.success) {
      setTestResult({
        success: false,
        message: 'Please fill in all required fields correctly'
      });
      return;
    }

    setTestingConnection(true);
    setTestResult(null);

    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would test the actual connection
      const success = Math.random() > 0.3; // 70% success rate for demo
      
      setTestResult({
        success,
        message: success 
          ? 'Connection test successful!' 
          : 'Connection test failed. Please check your credentials and network connectivity.'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Connection test failed with an unexpected error.'
      });
    } finally {
      setTestingConnection(false);
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'gradient':
        return 'bg-gradient-to-br from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10';
      default:
        return 'bg-card border border-border';
    }
  };

  const formVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: {
        duration: 0.2
      }
    }
  };

  const Component = animate ? motion.div : Card;
  const componentProps = animate ? {
    variants: formVariants,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit'
  } : {};

  return (
    <Component
      className={cn(getVariantStyles(), className)}
      {...componentProps}
      {...props}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          {initialData ? 'Edit VM Connection' : 'New VM Connection'}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="vmId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>VM ID</FormLabel>
                    <FormControl>
                      <Input placeholder="vm-001" {...field} />
                    </FormControl>
                    <FormDescription>
                      Unique identifier for this VM connection
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="host"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Host</FormLabel>
                    <FormControl>
                      <Input placeholder="vm.example.com" {...field} />
                    </FormControl>
                    <FormDescription>
                      Hostname or IP address of the VM
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="port"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Port</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="22" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 22)}
                      />
                    </FormControl>
                    <FormDescription>
                      SSH port (usually 22)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="admin" {...field} />
                    </FormControl>
                    <FormDescription>
                      SSH username for authentication
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Authentication */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="authType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Authentication Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select authentication method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="password">Password</SelectItem>
                        <SelectItem value="key">SSH Key (Manual)</SelectItem>
                        <SelectItem value="stored-key">Stored SSH Key</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {authType === 'password' && (
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input 
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter password"
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {authType === 'stored-key' && (
                <FormField
                  control={form.control}
                  name="storedKeyId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Select SSH Key</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose a stored SSH key" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableKeys.map((key) => (
                            <SelectItem key={key.id} value={key.id}>
                              <div className="flex items-center gap-2">
                                <Key className="h-4 w-4" />
                                <span>{key.name}</span>
                                <span className="text-xs text-muted-foreground">
                                  ({key.keyType.toUpperCase()})
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select from your stored SSH keys
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {authType === 'key' && (
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="privateKey"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Private Key</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Textarea
                              placeholder="-----BEGIN PRIVATE KEY-----&#10;...&#10;-----END PRIVATE KEY-----"
                              className="min-h-[120px] font-mono text-sm"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-2 top-2"
                              onClick={() => setShowPrivateKey(!showPrivateKey)}
                            >
                              {showPrivateKey ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Paste your SSH private key here
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="passphrase"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passphrase (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Enter passphrase if key is encrypted"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Options */}
            <FormField
              control={form.control}
              name="saveCredentials"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Save Credentials</FormLabel>
                    <FormDescription>
                      Store credentials securely for future connections
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Test Connection Result */}
            {testResult && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className={cn(
                  'p-3 rounded-lg border',
                  testResult.success 
                    ? 'bg-green-500/10 border-green-500/20' 
                    : 'bg-red-500/10 border-red-500/20'
                )}
              >
                <div className="flex items-start gap-2">
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0 mt-0.5" />
                  )}
                  <p className={cn(
                    'text-sm',
                    testResult.success ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
                  )}>
                    {testResult.message}
                  </p>
                </div>
              </motion.div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleTestConnection}
                disabled={loading || testingConnection}
              >
                {testingConnection ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Test Connection
              </Button>

              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={onCancel}
                  disabled={loading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                
                <Button
                  type="submit"
                  disabled={loading || testingConnection}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {initialData ? 'Update' : 'Create'} Connection
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Component>
  );
}
