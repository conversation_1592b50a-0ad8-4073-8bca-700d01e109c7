/**
 * VM Connection Card Component
 * Displays VM connection information with status and controls
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Server, 
  Wifi, 
  WifiOff, 
  Settings, 
  Trash2, 
  Play, 
  Square, 
  MoreVertical,
  Clock,
  User,
  Key,
  Shield,
  Activity,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { VMConnectionCardProps } from '@/types/vm-frontend';
import { VMConnectionStatus, VMHealthStatus } from '@/components/vm';

export function VMConnectionCard({
  vmId,
  config,
  status,
  onConnect,
  onDisconnect,
  onEdit,
  onDelete,
  className,
  animate = true,
  variant = 'default',
  ...props
}: VMConnectionCardProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleConnect = async () => {
    if (status.isConnecting || status.isConnected) return;
    
    setIsLoading(true);
    try {
      await onConnect(config);
    } catch (error) {
      console.error('Connection failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    if (!status.isConnected || status.isConnecting) return;
    
    setIsLoading(true);
    try {
      await onDisconnect();
    } catch (error) {
      console.error('Disconnect failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'gradient':
        return 'bg-gradient-to-br from-white/5 to-white/10 dark:from-black/5 dark:to-black/10 border border-white/10';
      default:
        return 'bg-card border border-border';
    }
  };

  const formatUptime = (lastConnected?: Date) => {
    if (!lastConnected) return 'Never connected';
    
    const now = new Date();
    const diff = now.getTime() - lastConnected.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just connected';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const cardVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      opacity: 0, 
      y: -20, 
      scale: 0.95,
      transition: {
        duration: 0.2
      }
    },
    hover: {
      y: -2,
      transition: {
        duration: 0.2,
        ease: 'easeOut'
      }
    }
  };

  const Component = animate ? motion.div : Card;
  const componentProps = animate ? {
    variants: cardVariants,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit',
    whileHover: 'hover'
  } : {};

  return (
    <Component
      className={cn(
        'transition-all duration-200 cursor-pointer',
        getVariantStyles(),
        'hover:shadow-lg',
        status.isConnected && 'ring-2 ring-green-500/20',
        status.error && 'ring-2 ring-red-500/20',
        className
      )}
      {...componentProps}
      {...props}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Server className="h-8 w-8 text-blue-500" />
              {status.isConnected && (
                <motion.div
                  className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    transition: {
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }
                  }}
                />
              )}
            </div>
            <div>
              <h3 className="font-semibold text-lg">{vmId}</h3>
              <p className="text-sm text-muted-foreground">
                {config.username}@{config.host}:{config.port}
              </p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onEdit}>
                <Settings className="h-4 w-4 mr-2" />
                Edit Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={onDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Connection
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <VMConnectionStatus
            isConnected={status.isConnected}
            isConnecting={status.isConnecting || isLoading}
            error={status.error}
            size="sm"
          />
          
          <div className="flex items-center gap-2">
            {status.isConnected ? (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDisconnect}
                disabled={isLoading || status.isConnecting}
                className="h-7"
              >
                <Square className="h-3 w-3 mr-1" />
                Disconnect
              </Button>
            ) : (
              <Button
                variant="default"
                size="sm"
                onClick={handleConnect}
                disabled={isLoading || status.isConnecting}
                className="h-7"
              >
                <Play className="h-3 w-3 mr-1" />
                Connect
              </Button>
            )}
          </div>
        </div>

        {/* Connection Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Auth:</span>
              <Badge variant="secondary" className="text-xs">
                {config.privateKey ? 'SSH Key' : 'Password'}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Last:</span>
              <span className="text-xs">
                {formatUptime(status.lastConnected)}
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Shield className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Timeout:</span>
              <span className="text-xs">
                {config.timeout ? `${config.timeout / 1000}s` : '30s'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Activity className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Retries:</span>
              <span className="text-xs">
                {status.retryCount}/{config.maxRetries || 3}
              </span>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {status.error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
          >
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-500">Connection Error</p>
                <p className="text-xs text-red-500/80 mt-1">{status.error}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Connection ID (when connected) */}
        {status.isConnected && status.connectionId && (
          <div className="p-2 bg-muted/50 rounded border">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Connection ID:</span>
              <code className="text-xs font-mono bg-background px-2 py-1 rounded">
                {status.connectionId.substring(0, 8)}...
              </code>
            </div>
          </div>
        )}
      </CardContent>
    </Component>
  );
}

// Compact version for lists
export function VMConnectionCardCompact({
  vmId,
  config,
  status,
  onConnect,
  onDisconnect,
  className,
  ...props
}: Omit<VMConnectionCardProps, 'onEdit' | 'onDelete'>) {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleToggleConnection = async () => {
    setIsLoading(true);
    try {
      if (status.isConnected) {
        await onDisconnect();
      } else {
        await onConnect(config);
      }
    } catch (error) {
      console.error('Connection toggle failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className={cn(
        'flex items-center justify-between p-3 bg-card border border-border rounded-lg',
        'hover:shadow-sm transition-all duration-200',
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3">
        <Server className="h-5 w-5 text-blue-500" />
        <div>
          <p className="font-medium">{vmId}</p>
          <p className="text-xs text-muted-foreground">
            {config.host}:{config.port}
          </p>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <VMConnectionStatus
          isConnected={status.isConnected}
          isConnecting={status.isConnecting || isLoading}
          error={status.error}
          size="sm"
          showLabel={false}
        />
        
        <Button
          variant={status.isConnected ? "outline" : "default"}
          size="sm"
          onClick={handleToggleConnection}
          disabled={isLoading || status.isConnecting}
          className="h-7 px-3"
        >
          {status.isConnected ? (
            <>
              <Square className="h-3 w-3 mr-1" />
              Disconnect
            </>
          ) : (
            <>
              <Play className="h-3 w-3 mr-1" />
              Connect
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
