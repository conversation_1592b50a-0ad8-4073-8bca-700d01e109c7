'use client';

import React, { useRef, useEffect, useCallback, useState } from 'react';
import { Editor, OnMount, OnChange } from '@monaco-editor/react';
import { motion, AnimatePresence } from 'framer-motion';
import { editor } from 'monaco-editor';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Zap,
  Brain,
  Bug,
  Wand2,
  Save,
  FileText,
  Loader2,
} from 'lucide-react';

// Accessibility imports
import {
  ARIA_LABELS,
  KEYBOARD_SHORTCUTS,
  announceToScreenReader,
  focusManager,
} from '@/lib/ai-code-editor-accessibility';

// Hooks
import {
  useAICodeCompletion,
  useCodeAnalysis,
  useAIAssistant,
  useCodeRefactoring,
  useAIEditorState,
} from '@/hooks/ai-code-editor';

// Types
import {
  CodeFile,
  CodePosition,
  AICodeEditorConfig,
} from '@/types/ai-code-editor';

// Components
import { CodeCompletionPopup } from './CodeCompletionPopup';
import { AIErrorAnalyzer } from './AIErrorAnalyzer';
import { CodeRefactoringSuggestions } from './CodeRefactoringSuggestions';

interface AICodeEditorProps {
  file?: CodeFile;
  config?: Partial<AICodeEditorConfig>;
  className?: string;
  onFileChange?: (file: CodeFile) => void;
  onSave?: (file: CodeFile) => void;
  userId?: string;
  workspaceId?: string;
}

export function AICodeEditor({
  file,
  config = {},
  className,
  onFileChange,
  onSave,
  userId,
  workspaceId,
}: AICodeEditorProps) {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [showCompletions, setShowCompletions] = useState(false);
  const [completionPosition, setCompletionPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Initialize hooks
  const { state, actions, updateCompletions, updateAnalysis } = useAIEditorState({
    userId,
    workspaceId,
  });

  const completion = useAICodeCompletion({
    userId,
    workspaceId,
    autoTrigger: state.settings.autoCompletion,
  });

  const analysis = useCodeAnalysis({
    userId,
    workspaceId,
    autoAnalyze: state.settings.errorAnalysis,
  });

  const assistant = useAIAssistant({
    userId,
    workspaceId,
  });

  const refactoring = useCodeRefactoring({
    userId,
    workspaceId,
    autoSuggest: state.settings.refactoringSuggestions,
  });

  // Update editor state when file changes
  useEffect(() => {
    if (file && file.id !== state.activeFile?.id) {
      actions.openFile(file);
    }
  }, [file, state.activeFile?.id, actions]);

  // Update completions state
  useEffect(() => {
    updateCompletions({
      suggestions: completion.suggestions,
      isLoading: completion.isLoading,
      error: completion.error,
    });

    // Announce completion status to screen readers
    if (completion.isLoading) {
      announceToScreenReader(ARIA_LABELS.completionLoading);
    } else if (completion.suggestions.length > 0) {
      announceToScreenReader(`${completion.suggestions.length} code completions available`);
    } else if (completion.error) {
      announceToScreenReader(`Error loading completions: ${completion.error}`, 'assertive');
    }
  }, [completion.suggestions, completion.isLoading, completion.error, updateCompletions]);

  // Update analysis state
  useEffect(() => {
    updateAnalysis({
      errors: analysis.errors,
      suggestions: analysis.suggestions,
      metrics: analysis.metrics,
      isLoading: analysis.isLoading,
      error: analysis.error,
    });
  }, [
    analysis.errors,
    analysis.suggestions,
    analysis.metrics,
    analysis.isLoading,
    analysis.error,
    updateAnalysis,
  ]);

  // Handle editor mount
  const handleEditorDidMount: OnMount = useCallback((editor, monaco) => {
    editorRef.current = editor;
    setIsEditorReady(true);

    // Configure editor
    editor.updateOptions({
      fontSize: state.settings.fontSize,
      tabSize: state.settings.tabSize,
      wordWrap: state.settings.wordWrap ? 'on' : 'off',
      lineNumbers: state.settings.lineNumbers ? 'on' : 'off',
      minimap: { enabled: state.settings.minimap },
      automaticLayout: true,
      suggestOnTriggerCharacters: false, // We handle this with AI
      quickSuggestions: false,
      parameterHints: { enabled: false },
      hover: { enabled: true },
      contextmenu: true,
    });

    // Handle cursor position changes
    editor.onDidChangeCursorPosition((e) => {
      actions.setCursor({
        line: e.position.lineNumber,
        column: e.position.column,
      });
    });

    // Handle selection changes
    editor.onDidChangeCursorSelection((e) => {
      if (!e.selection.isEmpty()) {
        const model = editor.getModel();
        if (model) {
          const selectedText = model.getValueInRange(e.selection);
          actions.setSelection({
            range: {
              start: {
                line: e.selection.startLineNumber,
                column: e.selection.startColumn,
              },
              end: {
                line: e.selection.endLineNumber,
                column: e.selection.endColumn,
              },
            },
            text: selectedText,
          });
        }
      }
    });

    // Handle key events for completions
    editor.onKeyDown((e) => {
      if (e.keyCode === monaco.KeyCode.Escape) {
        setShowCompletions(false);
        completion.dismissSuggestions();
      }
    });

    // Handle content changes for AI features
    let changeTimeout: NodeJS.Timeout;
    editor.onDidChangeModelContent((e) => {
      const model = editor.getModel();
      if (!model || !state.activeFile) return;

      const content = model.getValue();
      actions.updateFileContent(state.activeFile.id, content);

      // Trigger AI features with debouncing
      clearTimeout(changeTimeout);
      changeTimeout = setTimeout(() => {
        const position = editor.getPosition();
        if (position) {
          // Trigger completion
          if (state.settings.autoCompletion) {
            completion.requestCompletion({
              code: content,
              position: { line: position.lineNumber, column: position.column },
              language: state.activeFile?.language || 'typescript',
              context: {
                fileName: state.activeFile?.name,
              },
            });
          }

          // Trigger analysis
          if (state.settings.errorAnalysis) {
            analysis.analyzeCode({
              code: content,
              language: state.activeFile?.language || 'typescript',
              fileName: state.activeFile?.name,
            });
          }

          // Trigger refactoring suggestions
          if (state.settings.refactoringSuggestions) {
            refactoring.getSuggestions(content, state.activeFile?.language || 'typescript');
          }
        }
      }, 500);
    });
  }, [state.settings, state.activeFile, actions, completion, analysis, refactoring]);

  // Handle editor content change
  const handleEditorChange: OnChange = useCallback((value) => {
    if (value !== undefined && state.activeFile) {
      actions.updateFileContent(state.activeFile.id, value);
      onFileChange?.(state.activeFile);
    }
  }, [state.activeFile, actions, onFileChange]);

  // Handle save
  const handleSave = useCallback(async () => {
    if (state.activeFile) {
      try {
        await actions.saveFile(state.activeFile.id);
        onSave?.(state.activeFile);
      } catch (error) {
        console.error('Failed to save file:', error);
      }
    }
  }, [state.activeFile, actions, onSave]);

  // Show completions when available
  useEffect(() => {
    if (completion.suggestions.length > 0 && editorRef.current) {
      const position = editorRef.current.getPosition();
      if (position) {
        const coords = editorRef.current.getScrolledVisiblePosition(position);
        if (coords) {
          setCompletionPosition({ x: coords.left, y: coords.top + coords.height });
          setShowCompletions(true);
        }
      }
    } else {
      setShowCompletions(false);
    }
  }, [completion.suggestions]);

  // Apply completion
  const handleApplyCompletion = useCallback((suggestionId: string) => {
    const suggestion = completion.suggestions.find(s => s.id === suggestionId);
    if (suggestion && editorRef.current && state.activeFile) {
      const model = editorRef.current.getModel();
      if (model) {
        const range = {
          startLineNumber: suggestion.range.start.line,
          startColumn: suggestion.range.start.column,
          endLineNumber: suggestion.range.end.line,
          endColumn: suggestion.range.end.column,
        };
        
        model.pushEditOperations([], [{
          range,
          text: suggestion.insertText,
        }], () => null);
        
        completion.applySuggestion(suggestion);
        setShowCompletions(false);
      }
    }
  }, [completion, state.activeFile]);

  if (!state.activeFile) {
    return (
      <Card className={cn("h-full flex items-center justify-center", className)}>
        <div className="text-center text-muted-foreground">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No file selected</p>
          <p className="text-sm">Open a file to start coding with AI assistance</p>
        </div>
      </Card>
    );
  }

  return (
    <div
      className={cn("h-full flex flex-col", className)}
      role="application"
      aria-label={ARIA_LABELS.aiCodeEditor}
      aria-describedby="ai-editor-description"
    >
      {/* Hidden description for screen readers */}
      <div id="ai-editor-description" className="sr-only">
        AI-powered code editor with intelligent completion, error analysis, and refactoring suggestions.
        Use {KEYBOARD_SHORTCUTS.triggerCompletion} to trigger completions, {KEYBOARD_SHORTCUTS.toggleAssistant} to open AI assistant.
      </div>

      {/* Editor Header */}
      <div className="flex items-center justify-between p-3 border-b bg-background/50 backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          <span className="font-medium">{state.activeFile.name}</span>
          {state.activeFile.isDirty && (
            <Badge variant="secondary" className="h-5 px-1.5 text-xs">
              Modified
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* AI Status Indicators */}
          <div className="flex items-center gap-1">
            {completion.isLoading && (
              <Badge variant="outline" className="h-6 px-2 text-xs">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                AI
              </Badge>
            )}
            {analysis.errors.length > 0 && (
              <Badge variant="destructive" className="h-6 px-2 text-xs">
                <Bug className="h-3 w-3 mr-1" />
                {analysis.errors.length}
              </Badge>
            )}
            {refactoring.suggestions.length > 0 && (
              <Badge variant="secondary" className="h-6 px-2 text-xs">
                <Wand2 className="h-3 w-3 mr-1" />
                {refactoring.suggestions.length}
              </Badge>
            )}
          </div>
          
          <Separator orientation="vertical" className="h-4" />
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSave}
            disabled={!state.activeFile.isDirty}
            aria-label={ARIA_LABELS.saveFile}
            title={`${KEYBOARD_SHORTCUTS.saveFile} - Save current file`}
          >
            <Save className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={actions.toggleAssistant}
            aria-label={ARIA_LABELS.aiAssistant}
            aria-pressed={state.assistant.isOpen}
            title={`${KEYBOARD_SHORTCUTS.toggleAssistant} - Toggle AI assistant`}
          >
            <Brain className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 relative">
        <Editor
          height="100%"
          language={state.activeFile.language}
          value={state.activeFile.content}
          theme={state.settings.theme === 'dark' ? 'vs-dark' : 'light'}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          options={{
            automaticLayout: true,
            scrollBeyondLastLine: false,
            renderWhitespace: 'selection',
            smoothScrolling: true,
            cursorBlinking: 'smooth',
            fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
          }}
        />

        {/* Code Completion Popup */}
        <AnimatePresence>
          {showCompletions && completion.suggestions.length > 0 && (
            <CodeCompletionPopup
              suggestions={completion.suggestions}
              position={completionPosition}
              onApply={handleApplyCompletion}
              onDismiss={() => setShowCompletions(false)}
            />
          )}
        </AnimatePresence>

        {/* Error Analysis Panel */}
        {analysis.errors.length > 0 && (
          <AIErrorAnalyzer
            errors={analysis.errors}
            onApplyFix={analysis.applyFix}
            onDismissError={analysis.dismissError}
            className="absolute bottom-4 right-4 max-w-md"
          />
        )}

        {/* Refactoring Suggestions */}
        {refactoring.suggestions.length > 0 && (
          <CodeRefactoringSuggestions
            suggestions={refactoring.suggestions}
            onApplySuggestion={refactoring.applySuggestion}
            onPreview={refactoring.previewSuggestion}
            className="absolute top-4 right-4 max-w-md"
          />
        )}
      </div>
    </div>
  );
}
