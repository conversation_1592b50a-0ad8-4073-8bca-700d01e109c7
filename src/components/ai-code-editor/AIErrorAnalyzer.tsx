'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  Bug, 
  Info, 
  Lightbulb,
  X,
  ChevronDown,
  ChevronRight,
  Wand2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

import { CodeError, CodeFix } from '@/types/ai-code-editor';

interface AIErrorAnalyzerProps {
  errors: CodeError[];
  onApplyFix: (fix: CodeFix) => void;
  onDismissError: (errorId: string) => void;
  className?: string;
}

const SEVERITY_ICONS = {
  error: Bug,
  warning: AlertTriangle,
  info: Info,
  hint: Lightbulb,
};

const SEVERITY_COLORS = {
  error: 'text-red-600 bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800',
  warning: 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800',
  info: 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800',
  hint: 'text-green-600 bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800',
};

const SEVERITY_BADGE_COLORS = {
  error: 'destructive',
  warning: 'secondary',
  info: 'outline',
  hint: 'default',
} as const;

export function AIErrorAnalyzer({
  errors,
  onApplyFix,
  onDismissError,
  className,
}: AIErrorAnalyzerProps) {
  const [expandedErrors, setExpandedErrors] = useState<Set<string>>(new Set());
  const [isMinimized, setIsMinimized] = useState(false);

  if (errors.length === 0) {
    return null;
  }

  const toggleErrorExpansion = (errorId: string) => {
    setExpandedErrors(prev => {
      const newSet = new Set(prev);
      if (newSet.has(errorId)) {
        newSet.delete(errorId);
      } else {
        newSet.add(errorId);
      }
      return newSet;
    });
  };

  const errorsByType = errors.reduce((acc, error) => {
    acc[error.severity] = (acc[error.severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <motion.div
      initial={{ opacity: 0, x: 20, scale: 0.95 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 20, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className={cn("w-full max-w-md", className)}
    >
      <Card className="shadow-lg backdrop-blur-md bg-background/95 border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-500" />
              Code Issues
              <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                {errors.length}
              </Badge>
            </CardTitle>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="h-6 w-6 p-0"
              >
                {isMinimized ? (
                  <ChevronRight className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
            </div>
          </div>
          
          {!isMinimized && (
            <div className="flex items-center gap-2 mt-2">
              {Object.entries(errorsByType).map(([severity, count]) => {
                const Icon = SEVERITY_ICONS[severity as keyof typeof SEVERITY_ICONS];
                return (
                  <Badge
                    key={severity}
                    variant={SEVERITY_BADGE_COLORS[severity as keyof typeof SEVERITY_BADGE_COLORS]}
                    className="h-5 px-2 text-xs capitalize"
                  >
                    <Icon className="h-3 w-3 mr-1" />
                    {count}
                  </Badge>
                );
              })}
            </div>
          )}
        </CardHeader>

        <AnimatePresence>
          {!isMinimized && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <CardContent className="pt-0">
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {errors.map((error, index) => {
                      const Icon = SEVERITY_ICONS[error.severity];
                      const isExpanded = expandedErrors.has(error.id);
                      
                      return (
                        <motion.div
                          key={error.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className={cn(
                            "border rounded-lg p-3 transition-all",
                            SEVERITY_COLORS[error.severity]
                          )}
                        >
                          <div className="flex items-start gap-3">
                            <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <div className="flex-1">
                                  <p className="text-sm font-medium leading-tight">
                                    {error.message}
                                  </p>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant="outline" className="h-4 px-1 text-xs">
                                      Line {error.range.start.line}
                                    </Badge>
                                    {error.code && (
                                      <Badge variant="outline" className="h-4 px-1 text-xs">
                                        {error.code}
                                      </Badge>
                                    )}
                                    <span className="text-xs text-muted-foreground">
                                      {error.source}
                                    </span>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-1">
                                  {error.fixes && error.fixes.length > 0 && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => toggleErrorExpansion(error.id)}
                                      className="h-6 w-6 p-0"
                                    >
                                      {isExpanded ? (
                                        <ChevronDown className="h-3 w-3" />
                                      ) : (
                                        <ChevronRight className="h-3 w-3" />
                                      )}
                                    </Button>
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onDismissError(error.id)}
                                    className="h-6 w-6 p-0"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Fixes */}
                          <AnimatePresence>
                            {isExpanded && error.fixes && error.fixes.length > 0 && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                                className="mt-3 pt-3 border-t border-current/20"
                              >
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2 mb-2">
                                    <Wand2 className="h-3 w-3" />
                                    <span className="text-xs font-medium">
                                      AI Suggested Fixes:
                                    </span>
                                  </div>
                                  
                                  {error.fixes.map((fix, fixIndex) => (
                                    <motion.div
                                      key={fix.id}
                                      initial={{ opacity: 0, x: -10 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ delay: fixIndex * 0.1 }}
                                      className="bg-background/50 rounded-md p-2 border border-current/20"
                                    >
                                      <div className="flex items-start justify-between gap-2">
                                        <div className="flex-1">
                                          <p className="text-xs font-medium">
                                            {fix.title}
                                          </p>
                                          <p className="text-xs text-muted-foreground mt-1">
                                            {fix.description}
                                          </p>
                                          <div className="flex items-center gap-2 mt-1">
                                            <div className="flex items-center gap-1">
                                              <span className="text-xs text-muted-foreground">
                                                Confidence:
                                              </span>
                                              <div className="flex items-center gap-1">
                                                {Array.from({ length: 5 }).map((_, i) => (
                                                  <div
                                                    key={i}
                                                    className={cn(
                                                      "w-1 h-1 rounded-full",
                                                      i < Math.round(fix.confidence * 5)
                                                        ? "bg-current"
                                                        : "bg-current/30"
                                                    )}
                                                  />
                                                ))}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => onApplyFix(fix)}
                                          className="h-6 px-2 text-xs"
                                        >
                                          <CheckCircle className="h-3 w-3 mr-1" />
                                          Apply
                                        </Button>
                                      </div>
                                    </motion.div>
                                  ))}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </motion.div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
}
