'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Wand2, 
  Eye, 
  Check, 
  X,
  ChevronDown,
  ChevronRight,
  ArrowRight,
  Zap,
  TrendingUp,
  Shuffle,
  Move,
  Minimize,
  Palette,
} from 'lucide-react';

import { RefactoringSuggestion } from '@/types/ai-code-editor';

interface CodeRefactoringSuggestionsProps {
  suggestions: RefactoringSuggestion[];
  onApplySuggestion: (suggestion: RefactoringSuggestion) => Promise<void>;
  onPreview: (suggestion: RefactoringSuggestion) => string;
  className?: string;
}

const REFACTORING_TYPE_ICONS = {
  'extract-method': Shuffle,
  'rename': Palette,
  'inline': Minimize,
  'move': Move,
  'optimize': TrendingUp,
  'style': Pa<PERSON>,
};

const REFACTORING_TYPE_COLORS = {
  'extract-method': 'bg-purple-500/10 text-purple-600 border-purple-200',
  'rename': 'bg-blue-500/10 text-blue-600 border-blue-200',
  'inline': 'bg-green-500/10 text-green-600 border-green-200',
  'move': 'bg-orange-500/10 text-orange-600 border-orange-200',
  'optimize': 'bg-red-500/10 text-red-600 border-red-200',
  'style': 'bg-cyan-500/10 text-cyan-600 border-cyan-200',
};

const IMPACT_COLORS = {
  low: 'text-green-600 bg-green-50 border-green-200',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  high: 'text-red-600 bg-red-50 border-red-200',
};

export function CodeRefactoringSuggestions({
  suggestions,
  onApplySuggestion,
  onPreview,
  className,
}: CodeRefactoringSuggestionsProps) {
  const [expandedSuggestions, setExpandedSuggestions] = useState<Set<string>>(new Set());
  const [previewingSuggestion, setPreviewingSuggestion] = useState<string | null>(null);
  const [isMinimized, setIsMinimized] = useState(false);
  const [applyingId, setApplyingId] = useState<string | null>(null);

  if (suggestions.length === 0) {
    return null;
  }

  const toggleSuggestionExpansion = (suggestionId: string) => {
    setExpandedSuggestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(suggestionId)) {
        newSet.delete(suggestionId);
      } else {
        newSet.add(suggestionId);
      }
      return newSet;
    });
  };

  const handlePreview = (suggestion: RefactoringSuggestion) => {
    if (previewingSuggestion === suggestion.id) {
      setPreviewingSuggestion(null);
    } else {
      setPreviewingSuggestion(suggestion.id);
    }
  };

  const handleApply = async (suggestion: RefactoringSuggestion) => {
    try {
      setApplyingId(suggestion.id);
      await onApplySuggestion(suggestion);
    } catch (error) {
      console.error('Failed to apply refactoring:', error);
    } finally {
      setApplyingId(null);
    }
  };

  const suggestionsByType = suggestions.reduce((acc, suggestion) => {
    acc[suggestion.type] = (acc[suggestion.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <motion.div
      initial={{ opacity: 0, x: 20, scale: 0.95 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 20, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className={cn("w-full max-w-md", className)}
    >
      <Card className="shadow-lg backdrop-blur-md bg-background/95 border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wand2 className="h-4 w-4 text-purple-500" />
              Refactoring Suggestions
              <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                {suggestions.length}
              </Badge>
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-6 w-6 p-0"
            >
              {isMinimized ? (
                <ChevronRight className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          </div>
          
          {!isMinimized && (
            <div className="flex flex-wrap gap-1 mt-2">
              {Object.entries(suggestionsByType).map(([type, count]) => {
                const Icon = REFACTORING_TYPE_ICONS[type as keyof typeof REFACTORING_TYPE_ICONS];
                return (
                  <Badge
                    key={type}
                    variant="outline"
                    className="h-5 px-2 text-xs capitalize"
                  >
                    <Icon className="h-3 w-3 mr-1" />
                    {type.replace('-', ' ')} ({count})
                  </Badge>
                );
              })}
            </div>
          )}
        </CardHeader>

        <AnimatePresence>
          {!isMinimized && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <CardContent className="pt-0">
                <ScrollArea className="h-80">
                  <div className="space-y-3">
                    {suggestions.map((suggestion, index) => {
                      const Icon = REFACTORING_TYPE_ICONS[suggestion.type];
                      const isExpanded = expandedSuggestions.has(suggestion.id);
                      const isPreviewing = previewingSuggestion === suggestion.id;
                      const isApplying = applyingId === suggestion.id;
                      
                      return (
                        <motion.div
                          key={suggestion.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="border rounded-lg p-3 bg-background/50"
                        >
                          <div className="flex items-start gap-3">
                            <div className={cn(
                              "flex-shrink-0 p-1.5 rounded-md border",
                              REFACTORING_TYPE_COLORS[suggestion.type]
                            )}>
                              <Icon className="h-3 w-3" />
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <div className="flex-1">
                                  <h4 className="text-sm font-medium leading-tight">
                                    {suggestion.title}
                                  </h4>
                                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                    {suggestion.description}
                                  </p>
                                  
                                  <div className="flex items-center gap-2 mt-2">
                                    <Badge variant="outline" className="h-4 px-1 text-xs">
                                      Line {suggestion.range.start.line}
                                    </Badge>
                                    <Badge
                                      variant="outline"
                                      className={cn(
                                        "h-4 px-1 text-xs capitalize",
                                        IMPACT_COLORS[suggestion.impact]
                                      )}
                                    >
                                      {suggestion.impact} impact
                                    </Badge>
                                    <div className="flex items-center gap-1">
                                      <span className="text-xs text-muted-foreground">
                                        Confidence:
                                      </span>
                                      <div className="flex items-center gap-1">
                                        {Array.from({ length: 5 }).map((_, i) => (
                                          <div
                                            key={i}
                                            className={cn(
                                              "w-1 h-1 rounded-full",
                                              i < Math.round(suggestion.confidence * 5)
                                                ? "bg-primary"
                                                : "bg-muted"
                                            )}
                                          />
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleSuggestionExpansion(suggestion.id)}
                                  className="h-6 w-6 p-0"
                                >
                                  {isExpanded ? (
                                    <ChevronDown className="h-3 w-3" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>

                          {/* Expanded Content */}
                          <AnimatePresence>
                            {isExpanded && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                                className="mt-3 pt-3 border-t"
                              >
                                <div className="space-y-3">
                                  {/* Preview */}
                                  <div>
                                    <div className="flex items-center justify-between mb-2">
                                      <span className="text-xs font-medium text-muted-foreground">
                                        Code Preview:
                                      </span>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePreview(suggestion)}
                                        className="h-6 px-2 text-xs"
                                      >
                                        <Eye className="h-3 w-3 mr-1" />
                                        {isPreviewing ? 'Hide' : 'Show'}
                                      </Button>
                                    </div>
                                    
                                    <AnimatePresence>
                                      {isPreviewing && (
                                        <motion.div
                                          initial={{ height: 0, opacity: 0 }}
                                          animate={{ height: 'auto', opacity: 1 }}
                                          exit={{ height: 0, opacity: 0 }}
                                          transition={{ duration: 0.2 }}
                                          className="bg-muted rounded-md p-2 border"
                                        >
                                          <code className="text-xs font-mono whitespace-pre-wrap">
                                            {onPreview(suggestion)}
                                          </code>
                                        </motion.div>
                                      )}
                                    </AnimatePresence>
                                  </div>

                                  {/* Actions */}
                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="default"
                                      size="sm"
                                      onClick={() => handleApply(suggestion)}
                                      disabled={isApplying}
                                      className="h-7 px-3 text-xs"
                                    >
                                      {isApplying ? (
                                        <>
                                          <motion.div
                                            animate={{ rotate: 360 }}
                                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                            className="mr-1"
                                          >
                                            <Zap className="h-3 w-3" />
                                          </motion.div>
                                          Applying...
                                        </>
                                      ) : (
                                        <>
                                          <Check className="h-3 w-3 mr-1" />
                                          Apply Refactoring
                                        </>
                                      )}
                                    </Button>
                                    
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handlePreview(suggestion)}
                                      className="h-7 px-3 text-xs"
                                    >
                                      <ArrowRight className="h-3 w-3 mr-1" />
                                      Preview Changes
                                    </Button>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </motion.div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
}
