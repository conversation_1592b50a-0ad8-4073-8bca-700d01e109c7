'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  Send, 
  User, 
  Bot, 
  Code, 
  Lightbulb,
  Bug,
  Zap,
  Trash2,
  Copy,
  Check,
  Loader2,
  MessageSquare,
  Sparkles,
} from 'lucide-react';

import { useAIAssistant } from '@/hooks/ai-code-editor';
import { AIAssistantMessage, CodeSelection } from '@/types/ai-code-editor';

interface AIAssistantPanelProps {
  isOpen: boolean;
  onClose: () => void;
  currentFile?: {
    name: string;
    content: string;
    language: string;
  };
  selection?: CodeSelection;
  userId?: string;
  workspaceId?: string;
  className?: string;
}

const MESSAGE_TYPE_ICONS = {
  user: User,
  assistant: Bot,
  system: MessageSquare,
};

const QUICK_ACTIONS = [
  {
    id: 'explain',
    label: 'Explain Code',
    icon: Lightbulb,
    prompt: 'Please explain this code',
    requiresSelection: true,
  },
  {
    id: 'debug',
    label: 'Find Bugs',
    icon: Bug,
    prompt: 'Are there any bugs or issues in this code?',
    requiresSelection: true,
  },
  {
    id: 'optimize',
    label: 'Optimize',
    icon: Zap,
    prompt: 'How can I optimize this code for better performance?',
    requiresSelection: true,
  },
  {
    id: 'generate-tests',
    label: 'Generate Tests',
    icon: Code,
    prompt: 'Generate unit tests for this code',
    requiresSelection: true,
  },
];

export function AIAssistantPanel({
  isOpen,
  onClose,
  currentFile,
  selection,
  userId,
  workspaceId,
  className,
}: AIAssistantPanelProps) {
  const [message, setMessage] = useState('');
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const assistant = useAIAssistant({
    userId,
    workspaceId,
    persistConversation: true,
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [assistant.messages]);

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const handleSendMessage = async () => {
    if (!message.trim() || assistant.isLoading) return;

    const messageText = message.trim();
    setMessage('');

    await assistant.sendMessage({
      message: messageText,
      codeContext: currentFile && selection ? {
        file: currentFile.name,
        selection,
        fullCode: currentFile.content,
      } : currentFile ? {
        file: currentFile.name,
        fullCode: currentFile.content,
      } : undefined,
    });
  };

  const handleQuickAction = async (action: typeof QUICK_ACTIONS[0]) => {
    if (action.requiresSelection && !selection) {
      // Show a message that selection is required
      return;
    }

    await assistant.sendMessage({
      message: action.prompt,
      codeContext: currentFile ? {
        file: currentFile.name,
        selection: action.requiresSelection ? selection : undefined,
        fullCode: currentFile.content,
      } : undefined,
    });
  };

  const handleCopyMessage = async (messageId: string, content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 300 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={cn("h-full flex flex-col", className)}
    >
      <Card className="h-full flex flex-col backdrop-blur-md bg-background/95 border-2">
        {/* Header */}
        <CardHeader className="pb-3 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <div className="relative">
                <Brain className="h-4 w-4 text-purple-500" />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"
                />
              </div>
              AI Assistant
              {assistant.messages.length > 0 && (
                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                  {assistant.messages.length}
                </Badge>
              )}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-2 mt-3">
            {QUICK_ACTIONS.map((action) => {
              const Icon = action.icon;
              const isDisabled = action.requiresSelection && !selection;
              
              return (
                <Button
                  key={action.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction(action)}
                  disabled={isDisabled || assistant.isLoading}
                  className="h-8 px-2 text-xs justify-start"
                >
                  <Icon className="h-3 w-3 mr-1" />
                  {action.label}
                </Button>
              );
            })}
          </div>

          {/* Context Info */}
          {currentFile && (
            <div className="mt-2 p-2 bg-muted/50 rounded-md">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Code className="h-3 w-3" />
                <span>Context: {currentFile.name}</span>
                {selection && (
                  <Badge variant="outline" className="h-4 px-1 text-xs">
                    {selection.text.split('\n').length} lines selected
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardHeader>

        {/* Messages */}
        <CardContent className="flex-1 flex flex-col p-0">
          <ScrollArea className="flex-1 p-4">
            {assistant.messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground">
                <Sparkles className="h-12 w-12 mb-4 opacity-50" />
                <p className="text-sm font-medium mb-2">AI Assistant Ready</p>
                <p className="text-xs">
                  Ask me anything about your code, or use the quick actions above.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {assistant.messages.map((msg, index) => {
                  const Icon = MESSAGE_TYPE_ICONS[msg.type];
                  const isUser = msg.type === 'user';
                  const isSystem = msg.type === 'system';
                  
                  return (
                    <motion.div
                      key={msg.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={cn(
                        "flex gap-3",
                        isUser && "flex-row-reverse"
                      )}
                    >
                      <div className={cn(
                        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                        isUser && "bg-primary text-primary-foreground",
                        !isUser && !isSystem && "bg-purple-500 text-white",
                        isSystem && "bg-muted text-muted-foreground"
                      )}>
                        <Icon className="h-4 w-4" />
                      </div>
                      
                      <div className={cn(
                        "flex-1 min-w-0",
                        isUser && "text-right"
                      )}>
                        <div className={cn(
                          "inline-block max-w-[85%] p-3 rounded-lg text-sm",
                          isUser && "bg-primary text-primary-foreground ml-auto",
                          !isUser && !isSystem && "bg-muted",
                          isSystem && "bg-muted/50 text-muted-foreground italic"
                        )}>
                          <div className="whitespace-pre-wrap break-words">
                            {msg.content}
                          </div>
                          
                          {msg.codeContext && (
                            <div className="mt-2 pt-2 border-t border-current/20">
                              <div className="text-xs opacity-75">
                                📁 {msg.codeContext.file}
                                {msg.codeContext.range && (
                                  <span className="ml-2">
                                    Line {msg.codeContext.range.start.line}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <div className={cn(
                          "flex items-center gap-2 mt-1 text-xs text-muted-foreground",
                          isUser && "justify-end"
                        )}>
                          <span>
                            {new Date(msg.timestamp).toLocaleTimeString()}
                          </span>
                          {!isSystem && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyMessage(msg.id, msg.content)}
                              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100"
                            >
                              {copiedMessageId === msg.id ? (
                                <Check className="h-3 w-3" />
                              ) : (
                                <Copy className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
                
                {assistant.isLoading && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex gap-3"
                  >
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-500 text-white flex items-center justify-center">
                      <Bot className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <div className="inline-block bg-muted p-3 rounded-lg">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          AI is thinking...
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>

          {/* Input */}
          <div className="p-4 border-t">
            {assistant.error && (
              <div className="mb-2 p-2 bg-destructive/10 text-destructive text-xs rounded-md">
                {assistant.error}
              </div>
            )}
            
            <div className="flex gap-2">
              <Input
                ref={inputRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me about your code..."
                disabled={assistant.isLoading}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!message.trim() || assistant.isLoading}
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            
            {assistant.messages.length > 0 && (
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-muted-foreground">
                  {assistant.messages.length} messages
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={assistant.clearConversation}
                  className="h-6 px-2 text-xs"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
