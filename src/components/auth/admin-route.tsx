'use client';

import React from 'react';
import { ProtectedRoute } from './protected-route';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Shield, Crown, Settings, Users, BarChart3 } from 'lucide-react';

interface AdminRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loadingComponent?: React.ReactNode;
}

/**
 * AdminRoute Component
 * 
 * Wraps content that should only be accessible to admin users.
 * Provides admin-specific UI elements and role verification.
 */
export function AdminRoute({ 
  children, 
  fallback,
  loadingComponent 
}: AdminRouteProps) {
  const adminFallback = fallback || (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="w-full max-w-lg mx-auto">
        <CardContent className="flex flex-col items-center justify-center p-8 space-y-6">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-red-100 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20">
            <Crown className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          
          <div className="text-center space-y-3">
            <div className="flex items-center justify-center gap-2">
              <h3 className="font-semibold text-lg">Admin Access Required</h3>
              <Badge variant="destructive" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                Admin Only
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground max-w-md">
              This area is restricted to system administrators only. You need admin privileges to access this content.
            </p>
          </div>

          <Alert variant="destructive" className="w-full">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Access Denied:</strong> Administrator role required.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-2 gap-4 w-full text-center">
            <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/50">
              <Users className="h-5 w-5 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">User Management</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/50">
              <Settings className="h-5 w-5 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">System Settings</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/50">
              <BarChart3 className="h-5 w-5 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Analytics</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/50">
              <Shield className="h-5 w-5 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Security</span>
            </div>
          </div>

          <div className="text-center space-y-2">
            <p className="text-xs text-muted-foreground">
              Contact your system administrator if you believe you should have access.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <ProtectedRoute
      adminOnly={true}
      fallback={adminFallback}
      loadingComponent={loadingComponent}
      requireEmailVerification={false}
    >
      {/* Admin Header Banner */}
      <div className="bg-gradient-to-r from-red-500/10 to-orange-500/10 border-b border-red-200 dark:border-red-800">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <Crown className="h-4 w-4 text-red-600 dark:text-red-400" />
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-red-900 dark:text-red-100">
                  Administrator Mode
                </span>
                <Badge variant="destructive" className="text-xs">
                  <Shield className="h-3 w-3 mr-1" />
                  Admin
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-2 text-xs text-red-700 dark:text-red-300">
              <Shield className="h-3 w-3" />
              <span>Full System Access</span>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Content */}
      <div className="admin-content">
        {children}
      </div>
    </ProtectedRoute>
  );
}

/**
 * AdminSection Component
 * 
 * Wraps individual sections within admin pages for consistent styling
 */
interface AdminSectionProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  className?: string;
}

export function AdminSection({ 
  children, 
  title, 
  description, 
  icon: Icon,
  className = '' 
}: AdminSectionProps) {
  return (
    <div className={`admin-section ${className}`}>
      {(title || description) && (
        <div className="mb-6">
          {title && (
            <div className="flex items-center gap-3 mb-2">
              {Icon && (
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
                  <Icon className="h-4 w-4 text-red-600 dark:text-red-400" />
                </div>
              )}
              <h2 className="text-xl font-semibold text-foreground">{title}</h2>
              <Badge variant="outline" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                Admin
              </Badge>
            </div>
          )}
          {description && (
            <p className="text-sm text-muted-foreground max-w-2xl">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
}

/**
 * AdminCard Component
 * 
 * Styled card component for admin content with admin-specific styling
 */
interface AdminCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  variant?: 'default' | 'warning' | 'danger';
}

export function AdminCard({ 
  children, 
  title, 
  description, 
  className = '',
  variant = 'default'
}: AdminCardProps) {
  const variantStyles = {
    default: 'border-border',
    warning: 'border-yellow-200 dark:border-yellow-800 bg-yellow-50/50 dark:bg-yellow-900/10',
    danger: 'border-red-200 dark:border-red-800 bg-red-50/50 dark:bg-red-900/10'
  };

  return (
    <Card className={`${variantStyles[variant]} ${className}`}>
      <CardContent className="p-6">
        {(title || description) && (
          <div className="mb-4">
            {title && (
              <h3 className="font-medium text-foreground mb-1">{title}</h3>
            )}
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
        )}
        {children}
      </CardContent>
    </Card>
  );
}
