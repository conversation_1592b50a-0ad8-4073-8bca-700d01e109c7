/**
 * Python Workspace Service
 * Handles Python project creation, management, and framework-specific operations
 */

import { 
  PythonProjectTemplate, 
  PythonWorkspaceConfig, 
  PythonEnvironment,
  PythonLivePreview,
  CreatePythonProjectRequest,
  CreatePythonProjectResponse,
  PythonWorkspaceStatus,
  PythonFramework
} from '@/types/python-workspace';
import { PYTHON_PROJECT_TEMPLATES, getPythonTemplate } from '@/data/python-templates';

export class PythonWorkspaceService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/python-workspace') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get all available Python project templates
   */
  async getTemplates(): Promise<PythonProjectTemplate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/templates`);
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching Python templates:', error);
      // Fallback to local templates
      return PYTHON_PROJECT_TEMPLATES;
    }
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(id: string): Promise<PythonProjectTemplate | null> {
    try {
      const response = await fetch(`${this.baseUrl}/templates/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch template: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching Python template ${id}:`, error);
      // Fallback to local template
      return getPythonTemplate(id) || null;
    }
  }

  /**
   * Create a new Python project from template
   */
  async createProject(request: CreatePythonProjectRequest): Promise<CreatePythonProjectResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/projects`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to create project: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error creating Python project:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get workspace status and available services
   */
  async getWorkspaceStatus(workspaceId: string): Promise<PythonWorkspaceStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/status`);
      if (!response.ok) {
        throw new Error(`Failed to fetch workspace status: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching workspace status:', error);
      throw error;
    }
  }

  /**
   * Start live preview for a Python project
   */
  async startLivePreview(
    workspaceId: string, 
    projectPath: string, 
    framework: PythonFramework,
    port?: number
  ): Promise<PythonLivePreview> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectPath,
          framework,
          port,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start live preview: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error starting live preview:', error);
      throw error;
    }
  }

  /**
   * Stop live preview
   */
  async stopLivePreview(workspaceId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/stop`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Failed to stop live preview: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error stopping live preview:', error);
      throw error;
    }
  }

  /**
   * Get live preview status
   */
  async getLivePreviewStatus(workspaceId: string): Promise<PythonLivePreview | null> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/status`);
      if (response.status === 404) {
        return null;
      }
      if (!response.ok) {
        throw new Error(`Failed to get preview status: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting live preview status:', error);
      return null;
    }
  }

  /**
   * Install Python package
   */
  async installPackage(
    workspaceId: string, 
    packageName: string, 
    version?: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages/install`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageName,
          version,
          packageManager,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to install package: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error installing package:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Create Python virtual environment
   */
  async createEnvironment(
    workspaceId: string,
    environmentName: string,
    pythonVersion?: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<PythonEnvironment> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/environments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: environmentName,
          pythonVersion,
          packageManager,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create environment: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating environment:', error);
      throw error;
    }
  }

  /**
   * List Python environments
   */
  async listEnvironments(workspaceId: string): Promise<PythonEnvironment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/environments`);
      if (!response.ok) {
        throw new Error(`Failed to list environments: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error listing environments:', error);
      return [];
    }
  }

  /**
   * Activate Python environment
   */
  async activateEnvironment(workspaceId: string, environmentName: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/environments/${environmentName}/activate`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Failed to activate environment: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error activating environment:', error);
      throw error;
    }
  }

  /**
   * Execute Python command in workspace
   */
  async executeCommand(
    workspaceId: string,
    command: string,
    workingDirectory?: string
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command,
          workingDirectory,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to execute command: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error executing command:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get project files and structure
   */
  async getProjectStructure(workspaceId: string, projectPath: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/projects/structure?path=${encodeURIComponent(projectPath)}`);
      if (!response.ok) {
        throw new Error(`Failed to get project structure: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting project structure:', error);
      throw error;
    }
  }

  /**
   * Generate framework-specific code using AI
   */
  async generateCode(
    workspaceId: string,
    framework: PythonFramework,
    prompt: string,
    context?: string
  ): Promise<{ success: boolean; code?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/generate-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          framework,
          prompt,
          context,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to generate code: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error generating code:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }
}

// Export singleton instance
export const pythonWorkspaceService = new PythonWorkspaceService();
