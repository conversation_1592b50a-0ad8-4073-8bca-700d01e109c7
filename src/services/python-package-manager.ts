/**
 * Python Package Manager Service
 * Handles Python package installation, virtual environments, and dependency management
 */

import { 
  PythonPackageManager, 
  PythonEnvironment, 
  PythonPackage,
} from '@/types/python-workspace';
import { PythonProjectStructure } from '@/types/python-ai-editor';
import { dockerService } from './docker';

export interface PackageInstallOptions {
  version?: string;
  dev?: boolean;
  upgrade?: boolean;
  force?: boolean;
  extraIndex?: string;
}

export interface EnvironmentCreateOptions {
  pythonVersion?: string;
  packages?: string[];
  requirements?: string;
  copyPackages?: boolean;
}

export interface PackageSearchResult {
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  keywords: string[];
  lastUpdated: string;
}

class PythonPackageManagerService {
  private readonly packageManagers: PythonPackageManager[] = [
    {
      name: 'pip',
      displayName: 'pip',
      installCommand: 'pip install',
      createEnvCommand: 'python -m venv',
      activateCommand: 'source venv/bin/activate',
      requirementsFile: 'requirements.txt',
    },
    {
      name: 'conda',
      displayName: 'Conda',
      installCommand: 'conda install',
      createEnvCommand: 'conda create -n',
      activateCommand: 'conda activate',
      requirementsFile: 'environment.yml',
      lockFile: 'conda-lock.yml',
    },
    {
      name: 'poetry',
      displayName: 'Poetry',
      installCommand: 'poetry add',
      createEnvCommand: 'poetry init',
      activateCommand: 'poetry shell',
      requirementsFile: 'pyproject.toml',
      lockFile: 'poetry.lock',
    },
  ];

  /**
   * Get available package managers
   */
  getPackageManagers(): PythonPackageManager[] {
    return this.packageManagers;
  }

  /**
   * Get package manager by name
   */
  getPackageManager(name: string): PythonPackageManager | undefined {
    return this.packageManagers.find(pm => pm.name === name);
  }

  /**
   * Install a Python package
   */
  async installPackage(
    workspaceId: string,
    packageName: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    options: PackageInstallOptions = {}
  ): Promise<{ success: boolean; message?: string; output?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      // Build install command
      let command = pm.installCommand;
      let packageSpec = packageName;

      if (options.version) {
        packageSpec = packageManager === 'conda' 
          ? `${packageName}=${options.version}`
          : `${packageName}==${options.version}`;
      }

      // Add package manager specific options
      switch (packageManager) {
        case 'pip':
          if (options.dev) command += ' --dev';
          if (options.upgrade) command += ' --upgrade';
          if (options.force) command += ' --force-reinstall';
          if (options.extraIndex) command += ` --extra-index-url ${options.extraIndex}`;
          break;
        case 'conda':
          if (options.dev) command += ' --dev';
          if (options.upgrade) command += ' --update-all';
          if (options.force) command += ' --force-reinstall';
          break;
        case 'poetry':
          if (options.dev) command += ' --group dev';
          if (options.upgrade) command += ' --upgrade';
          break;
      }

      const fullCommand = `${command} ${packageSpec}`;

      // Execute the install command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', fullCommand]);

      return {
        success: !!result,
        message: result ? `Successfully installed ${packageName}` : `Failed to install ${packageName}`,
      };

    } catch (error) {
      console.error('Error installing package:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to install package'
      };
    }
  }

  /**
   * Uninstall a Python package
   */
  async uninstallPackage(
    workspaceId: string,
    packageName: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      switch (packageManager) {
        case 'pip':
          command = `pip uninstall -y ${packageName}`;
          break;
        case 'conda':
          command = `conda remove -y ${packageName}`;
          break;
        case 'poetry':
          command = `poetry remove ${packageName}`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the uninstall command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      return {
        success: !!result,
        message: result ? `Successfully uninstalled ${packageName}` : `Failed to uninstall ${packageName}`,
      };

    } catch (error) {
      console.error('Error uninstalling package:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to uninstall package'
      };
    }
  }

  /**
   * List installed packages
   */
  async listPackages(
    workspaceId: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<PythonPackage[]> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      switch (packageManager) {
        case 'pip':
          command = 'pip list --format=json';
          break;
        case 'conda':
          command = 'conda list --json';
          break;
        case 'poetry':
          command = 'poetry show --format=json';
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the list command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      if (!result) {
        return [];
      }

      // Parse the package list based on package manager
      return this.parsePackageList(result, packageManager);

    } catch (error) {
      console.error('Error listing packages:', error);
      return [];
    }
  }

  /**
   * Search for packages
   */
  async searchPackages(
    workspaceId: string,
    query: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<PackageSearchResult[]> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      switch (packageManager) {
        case 'pip':
          // Use a Python script to search PyPI
          command = `python -c "
import json
import urllib.request
try:
    response = urllib.request.urlopen('https://pypi.org/pypi/${query}/json')
    data = json.loads(response.read())
    results = [{
        'name': data['info']['name'],
        'version': data['info']['version'],
        'description': data['info']['summary'],
        'author': data['info']['author'],
        'keywords': data['info']['keywords'].split(',') if data['info']['keywords'] else [],
        'lastUpdated': data['info']['release_url']
    }]
    print(json.dumps(results))
except:
    print('[]')
"`;
          break;
        case 'conda':
          command = `conda search ${query} --json`;
          break;
        case 'poetry':
          // Use a Python script to search PyPI for Poetry
          command = `python -c "
import json
import urllib.request
try:
    response = urllib.request.urlopen('https://pypi.org/pypi/${query}/json')
    data = json.loads(response.read())
    results = [{
        'name': data['info']['name'],
        'version': data['info']['version'],
        'description': data['info']['summary'],
        'author': data['info']['author'],
        'keywords': data['info']['keywords'].split(',') if data['info']['keywords'] else [],
        'lastUpdated': data['info']['release_url']
    }]
    print(json.dumps(results))
except:
    print('[]')
"`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the search command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      if (!result) {
        return [];
      }

      // Parse search results
      return this.parseSearchResults(result, packageManager);

    } catch (error) {
      console.error('Error searching packages:', error);
      return [];
    }
  }

  /**
   * Create virtual environment
   */
  async createEnvironment(
    workspaceId: string,
    environmentName: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    options: EnvironmentCreateOptions = {}
  ): Promise<PythonEnvironment> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      let environmentPath: string;

      switch (packageManager) {
        case 'pip':
          environmentPath = `/home/<USER>/envs/${environmentName}`;
          command = `${pm.createEnvCommand} ${environmentPath}`;
          if (options.pythonVersion) {
            command = `python${options.pythonVersion} -m venv ${environmentPath}`;
          }
          break;
        case 'conda':
          environmentPath = environmentName;
          command = `${pm.createEnvCommand} ${environmentName}`;
          if (options.pythonVersion) {
            command += ` python=${options.pythonVersion}`;
          }
          if (options.packages && options.packages.length > 0) {
            command += ` ${options.packages.join(' ')}`;
          }
          break;
        case 'poetry':
          environmentPath = `/home/<USER>/projects/${environmentName}`;
          command = `cd ${environmentPath} && ${pm.createEnvCommand}`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the environment creation command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      // Activate the environment
      const activateCommand = pm.activateCommand.replace('{env}', environmentPath);
      await dockerService.execInContainer(workspaceId, ['sh', '-c', activateCommand]);

      // Install packages if specified
      if (options.packages && options.packages.length > 0) {
        for (const pkg of options.packages) {
          const installCmd = `${pm.installCommand} ${pkg}`;
          await dockerService.execInContainer(workspaceId, installCmd.split(' '));
        }
      }

      if (options.requirements) {
        const installCmd = `${pm.installCommand} -r ${options.requirements}`;
        await dockerService.execInContainer(workspaceId, installCmd.split(' '));
      }

      return {
        name: environmentName,
        pythonVersion: options.pythonVersion || '3.x',
        packageManager: pm,
        packages: [],
        isActive: false,
        path: environmentPath,
      };

    } catch (error) {
      console.error('Error creating environment:', error);
      throw error;
    }
  }

  /**
   * List environments
   */
  async listEnvironments(
    workspaceId: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<PythonEnvironment[]> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      switch (packageManager) {
        case 'pip':
          command = 'ls -1 /home/<USER>/envs';
          break;
        case 'conda':
          command = 'conda env list --json';
          break;
        case 'poetry':
          command = 'ls -1 /home/<USER>/projects';
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the list command
      const result = await dockerService.execInContainer(workspaceId, command.split(' '));

      if (!result) {
        return [];
      }

      // Parse environment list
      return this.parseEnvironmentList(result, packageManager);

    } catch (error) {
      console.error('Error listing environments:', error);
      return [];
    }
  }

  /**
   * Generate requirements file
   */
  async generateRequirements(
    workspaceId: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    outputPath?: string
  ): Promise<{ success: boolean; content?: string; message?: string }> {
    try {
      let command: string;
      const defaultPath = outputPath || `/home/<USER>/${this.getPackageManager(packageManager)?.requirementsFile}`;

      switch (packageManager) {
        case 'pip':
          command = `pip freeze > ${defaultPath}`;
          break;
        case 'conda':
          command = `conda env export > ${defaultPath}`;
          break;
        case 'poetry':
          command = `poetry export -f requirements.txt --output ${defaultPath}`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the freeze command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      if (!result) {
        return {
          success: false,
          message: 'Failed to generate requirements'
        };
      }

      return {
        success: true,
        content: result.split('\n').filter(line => line.trim() && !line.startsWith('#')).join('\n'),
        message: 'Successfully generated requirements'
      };

    } catch (error) {
      console.error('Error generating requirements:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to generate requirements'
      };
    }
  }

  /**
   * Get package information
   */
  async getPackageInfo(
    workspaceId: string,
    packageName: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip'
  ): Promise<PythonPackage> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      switch (packageManager) {
        case 'pip':
          command = `pip show ${packageName} --verbose`;
          break;
        case 'conda':
          command = `conda search ${packageName} --json`;
          break;
        case 'poetry':
          command = `poetry show ${packageName} --verbose`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the show command
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      if (!result) {
        throw new Error(`Failed to get info for package ${packageName}`);
      }

      return this.parsePackageInfo(result, packageManager);

    } catch (error) {
      console.error('Error getting package info:', error);
      throw error;
    }
  }

  /**
   * Update package
   */
  async updatePackage(
    workspaceId: string,
    packageName: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    options: PackageInstallOptions = {}
  ): Promise<{ success: boolean; message?: string; output?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      // Build update command
      let command = pm.installCommand;
      let packageSpec = packageName;

      if (options.version) {
        packageSpec = packageManager === 'conda' 
          ? `${packageName}=${options.version}`
          : `${packageName}==${options.version}`;
      }

      // Add package manager specific options
      switch (packageManager) {
        case 'pip':
          command += ' --upgrade';
          break;
        case 'conda':
          command += ' --update-all';
          break;
        case 'poetry':
          command += ' --group dev';
          break;
      }

      const fullCommand = `${command} ${packageSpec}`;

      // Execute the update command
      const result = await dockerService.execInContainer(workspaceId, fullCommand.split(' '));

      return {
        success: !!result,
        message: result ? `Successfully updated ${packageName}` : `Failed to update ${packageName}`,
      };

    } catch (error) {
      console.error('Error updating package:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Execute arbitrary command
   */
  async executeCommand(
    workspaceId: string,
    command: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    environment?: string
  ): Promise<string> {
    try {
      let fullCommand: string;

      switch (packageManager) {
        case 'pip':
          fullCommand = environment 
            ? `source ${environment}/bin/activate && ${command}`
            : command;
          break;
        case 'conda':
          fullCommand = environment 
            ? `conda activate ${environment} && ${command}`
            : command;
          break;
        case 'poetry':
          fullCommand = `cd /home/<USER>/projects/${environment} && ${command}`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      // Execute the command
      const result = await dockerService.execInContainer(workspaceId, fullCommand.split(' '));

      if (!result) {
        throw new Error(`Failed to execute command: ${command}`);
      }

      return result;

    } catch (error) {
      console.error('Error executing command:', error);
      throw error;
    }
  }

  /**
   * Install from requirements file
   */
  async installFromRequirements(
    workspaceId: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    requirementsPath?: string
  ): Promise<PythonPackage[]> {
    try {
      const defaultPath = requirementsPath || `/home/<USER>/${this.getPackageManager(packageManager)?.requirementsFile}`;

      let installCmd: string;

      switch (packageManager) {
        case 'pip':
          installCmd = `pip install -r ${defaultPath}`;
          break;
        case 'conda':
          installCmd = `conda env update -f ${defaultPath}`;
          break;
        case 'poetry':
          installCmd = `poetry install`;
          break;
        default:
          throw new Error(`Unsupported package manager: ${packageManager}`);
      }

      await dockerService.execInContainer(workspaceId, installCmd.split(' '));

      // Get installed packages
      const packages = await this.listPackages(workspaceId, packageManager);

      return packages;

    } catch (error) {
      console.error('Error installing from requirements:', error);
      throw error;
    }
  }

  /**
   * Read requirements file
   */
  async readRequirements(
    workspaceId: string,
    packageManager: 'pip' | 'conda' | 'poetry' = 'pip',
    requirementsPath?: string
  ): Promise<PythonPackage[]> {
    try {
      const defaultPath = requirementsPath || `/home/<USER>/${this.getPackageManager(packageManager)?.requirementsFile}`;

      const readResult = await dockerService.execInContainer(workspaceId, [`cat`, defaultPath]);

      if (!readResult) {
        throw new Error(`Failed to read requirements file: ${defaultPath}`);
      }

      return readResult.split('\n')
        .filter(line => line.trim() && !line.startsWith('#'))
        .map(line => {
          // Parse package name from line (simple approach)
          const parts = line.split('==');
          return {
            name: parts[0].trim(),
            version: parts[1] || 'latest',
            category: 'utility', // Default category
            required: false,
          } as PythonPackage;
        });

    } catch (error) {
      console.error('Error reading requirements:', error);
      throw error;
    }
  }

  private parsePackageList(output: string, packageManager: string): PythonPackage[] {
    try {
      const packages: PythonPackage[] = [];

      if (packageManager === 'pip') {
        const data = JSON.parse(output);
        if (Array.isArray(data)) {
          for (const pkg of data) {
            packages.push({
              name: pkg.name || '',
              version: pkg.version || 'latest',
              category: this.categorizePackage(pkg.name),
              required: false,
            });
          }
        }
      } else if (packageManager === 'conda') {
        const data = JSON.parse(output);
        if (Array.isArray(data)) {
          for (const pkg of data) {
            packages.push({
              name: pkg.name || '',
              version: pkg.version || 'latest',
              category: this.categorizePackage(pkg.name),
              required: false,
            });
          }
        }
      }

      return packages;
    } catch (error) {
      console.error('Error parsing package list:', error);
      return [];
    }
  }

  private parseSearchResults(output: string, packageManager: string): PackageSearchResult[] {
    try {
      const results: PackageSearchResult[] = [];
      const data = JSON.parse(output);

      if (Array.isArray(data)) {
        for (const item of data) {
          results.push({
            name: item.name || '',
            version: item.version || 'latest',
            description: item.description || item.summary || '',
            author: item.author || '',
            homepage: item.homepage || item.home_page || '',
            keywords: item.keywords || [],
            lastUpdated: item.lastUpdated || item.release_url || '',
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Error parsing search results:', error);
      return [];
    }
  }

  private parseEnvironmentList(output: string, packageManager: string): PythonEnvironment[] {
    try {
      const environments: PythonEnvironment[] = [];
      const lines = output.split('\n').filter(line => line.trim());

      for (const line of lines) {
        environments.push({
          name: line.trim(),
          pythonVersion: '3.x', // Default
          packageManager: this.getPackageManager(packageManager)!,
          packages: [],
          isActive: false,
          path: line.trim(),
        });
      }

      return environments;
    } catch (error) {
      console.error('Error parsing environment list:', error);
      return [];
    }
  }

  private parsePackageInfo(output: string, packageManager: string): PythonPackage {
    try {
      const data = JSON.parse(output);
      
      return {
        name: data.name || '',
        version: data.version || 'latest',
        description: data.summary || data.description || '',
        category: this.categorizePackage(data.name),
        required: false,
      };
    } catch (error) {
      console.error('Error parsing package info:', error);
      // Return a default package object
      return {
        name: 'unknown',
        version: 'unknown',
        category: 'utility',
        required: false,
      };
    }
  }

  private categorizePackage(packageName: string): PythonPackage['category'] {
    const frameworks = ['django', 'flask', 'fastapi', 'streamlit', 'gradio'];
    const databases = ['sqlalchemy', 'psycopg2', 'mysql', 'sqlite', 'redis'];
    const testing = ['pytest', 'unittest2', 'nose', 'coverage'];
    const development = ['black', 'flake8', 'mypy', 'isort', 'pre-commit'];
    const ml = ['numpy', 'pandas', 'scikit-learn', 'tensorflow', 'torch'];
    const web = ['requests', 'httpx', 'aiohttp', 'beautifulsoup4'];

    const name = packageName.toLowerCase();

    if (frameworks.some(f => name.includes(f))) return 'framework';
    if (databases.some(d => name.includes(d))) return 'database';
    if (testing.some(t => name.includes(t))) return 'testing';
    if (development.some(d => name.includes(d))) return 'development';
    if (ml.some(m => name.includes(m))) return 'ml';
    if (web.some(w => name.includes(w))) return 'web';

    return 'utility';
  }
}

// Export singleton instance
export const pythonPackageManagerService = new PythonPackageManagerService();