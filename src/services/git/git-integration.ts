/**
 * Git Integration Service
 * Handles Git operations, version control, and repository management
 */

import { dockerService } from '../docker';

export interface GitRepository {
  id: string;
  name: string;
  path: string;
  remoteUrl?: string;
  branch: string;
  status: GitStatus;
  lastCommit?: GitCommit;
  isInitialized: boolean;
  hasRemote: boolean;
}

export interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  staged: GitFileStatus[];
  unstaged: GitFileStatus[];
  untracked: string[];
  conflicted: string[];
  clean: boolean;
}

export interface GitFileStatus {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  oldPath?: string; // For renamed files
}

export interface GitCommit {
  hash: string;
  shortHash: string;
  author: string;
  email: string;
  date: Date;
  message: string;
  files: Array<{
    path: string;
    status: 'added' | 'modified' | 'deleted';
    additions: number;
    deletions: number;
  }>;
}

export interface GitBranch {
  name: string;
  current: boolean;
  remote?: string;
  lastCommit: {
    hash: string;
    message: string;
    date: Date;
  };
}

export interface GitRemote {
  name: string;
  url: string;
  type: 'fetch' | 'push';
}

export interface GitDiff {
  file: string;
  oldFile?: string;
  hunks: GitDiffHunk[];
  isBinary: boolean;
  isNew: boolean;
  isDeleted: boolean;
  isRenamed: boolean;
}

export interface GitDiffHunk {
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  lines: GitDiffLine[];
}

export interface GitDiffLine {
  type: 'context' | 'addition' | 'deletion';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

export interface GitLog {
  commits: GitCommit[];
  hasMore: boolean;
  totalCount: number;
}

class GitIntegrationService {
  // In-memory storage for demo - replace with persistent storage in production
  private repositories: Map<string, GitRepository> = new Map();

  /**
   * Initialize Git repository
   */
  async initRepository(
    workspaceId: string,
    path: string = '/home/<USER>',
    options: {
      initialBranch?: string;
      remoteUrl?: string;
      userName?: string;
      userEmail?: string;
    } = {}
  ): Promise<GitRepository> {
    try {
      const { initialBranch = 'main', remoteUrl, userName, userEmail } = options;

      // Initialize Git repository
      let initCommand = `cd "${path}" && git init`;
      if (initialBranch) {
        initCommand += ` --initial-branch=${initialBranch}`;
      }

      const initResult = await dockerService.execInContainer(workspaceId, ['sh', '-c', initCommand]);
      if (!initResult) {
        throw new Error(`Failed to initialize Git repository`);
      }

      // Configure user if provided
      if (userName) {
        await dockerService.execInContainer(
          workspaceId,
          ['sh', '-c', `cd "${path}" && git config user.name "${userName}"`]
        );
      }

      if (userEmail) {
        await dockerService.execInContainer(
          workspaceId,
          ['sh', '-c', `cd "${path}" && git config user.email "${userEmail}"`]
        );
      }

      // Add remote if provided
      if (remoteUrl) {
        await dockerService.execInContainer(
          workspaceId,
          ['sh', '-c', `cd "${path}" && git remote add origin "${remoteUrl}"`]
        );
      }

      // Create initial commit
      await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${path}" && echo "# ${path.split('/').pop()}" > README.md`]
      );

      await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${path}" && git add README.md && git commit -m "Initial commit"`]
      );

      // Get repository status
      const status = await this.getStatus(workspaceId, path);
      const lastCommit = await this.getLastCommit(workspaceId, path);

      const repository: GitRepository = {
        id: this.generateId(),
        name: path.split('/').pop() || 'repository',
        path,
        remoteUrl,
        branch: initialBranch,
        status,
        lastCommit,
        isInitialized: true,
        hasRemote: !!remoteUrl,
      };

      this.repositories.set(repository.id, repository);
      return repository;

    } catch (error) {
      console.error('Error initializing Git repository:', error);
      throw error;
    }
  }

  /**
   * Clone repository
   */
  async cloneRepository(
    workspaceId: string,
    remoteUrl: string,
    destinationPath: string,
    options: {
      branch?: string;
      depth?: number;
      recursive?: boolean;
    } = {}
  ): Promise<GitRepository> {
    try {
      const { branch, depth, recursive = false } = options;

      // Build clone command
      let cloneCommand = `git clone "${remoteUrl}" "${destinationPath}"`;
      
      if (branch) {
        cloneCommand += ` --branch ${branch}`;
      }
      
      if (depth) {
        cloneCommand += ` --depth ${depth}`;
      }
      
      if (recursive) {
        cloneCommand += ' --recursive';
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', cloneCommand]);

      if (!result) {
        throw new Error(`Failed to clone repository`);
      }

      // Get repository information
      const status = await this.getStatus(workspaceId, destinationPath);
      const lastCommit = await this.getLastCommit(workspaceId, destinationPath);

      const repository: GitRepository = {
        id: this.generateId(),
        name: destinationPath.split('/').pop() || 'repository',
        path: destinationPath,
        remoteUrl,
        branch: status.branch,
        status,
        lastCommit,
        isInitialized: true,
        hasRemote: true,
      };

      this.repositories.set(repository.id, repository);
      return repository;

    } catch (error) {
      console.error('Error cloning repository:', error);
      throw error;
    }
  }

  /**
   * Get repository status
   */
  async getStatus(workspaceId: string, repoPath: string): Promise<GitStatus> {
    try {
      // Get status information
      const statusResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${repoPath}" && git status --porcelain -b`]
      );

      if (!statusResult) {
        throw new Error(`Failed to get Git status`);
      }

      return this.parseGitStatus(statusResult);

    } catch (error) {
      console.error('Error getting Git status:', error);
      throw error;
    }
  }

  /**
   * Stage files
   */
  async stageFiles(
    workspaceId: string,
    repoPath: string,
    files: string[]
  ): Promise<void> {
    try {
      const filesStr = files.map(f => `"${f}"`).join(' ');
      const result = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${repoPath}" && git add ${filesStr}`]
      );

      if (!result) {
        throw new Error(`Failed to stage files`);
      }
    } catch (error) {
      console.error('Error staging files:', error);
      throw error;
    }
  }

  /**
   * Unstage files
   */
  async unstageFiles(
    workspaceId: string,
    repoPath: string,
    files: string[]
  ): Promise<void> {
    try {
      const filesStr = files.map(f => `"${f}"`).join(' ');
      const result = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${repoPath}" && git reset HEAD ${filesStr}`]
      );

      if (!result) {
        throw new Error(`Failed to unstage files`);
      }
    } catch (error) {
      console.error('Error unstaging files:', error);
      throw error;
    }
  }

  /**
   * Commit changes
   */
  async commit(
    workspaceId: string,
    repoPath: string,
    message: string,
    options: {
      amend?: boolean;
      signOff?: boolean;
      author?: string;
    } = {}
  ): Promise<GitCommit> {
    try {
      const { amend = false, signOff = false, author } = options;

      let commitCommand = `cd "${repoPath}" && git commit -m "${message}"`;
      
      if (amend) {
        commitCommand += ' --amend';
      }
      
      if (signOff) {
        commitCommand += ' --signoff';
      }
      
      if (author) {
        commitCommand += ` --author="${author}"`;
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', commitCommand]);

      if (!result) {
        throw new Error(`Failed to commit`);
      }

      // Get the new commit information
      const lastCommit = await this.getLastCommit(workspaceId, repoPath);
      if (!lastCommit) {
        throw new Error(`Failed to retrieve commit information`);
      }
      return lastCommit;

    } catch (error) {
      console.error('Error committing changes:', error);
      throw error;
    }
  }

  /**
   * Get commit history
   */
  async getLog(
    workspaceId: string,
    repoPath: string,
    options: {
      limit?: number;
      skip?: number;
      branch?: string;
      author?: string;
      since?: Date;
      until?: Date;
    } = {}
  ): Promise<GitLog> {
    try {
      const { limit = 50, skip = 0, branch, author, since, until } = options;

      let logCommand = `cd "${repoPath}" && git log --pretty=format:"%H|%h|%an|%ae|%ad|%s" --date=iso`;
      
      if (limit) {
        logCommand += ` -${limit}`;
      }
      
      if (skip) {
        logCommand += ` --skip=${skip}`;
      }
      
      if (branch) {
        logCommand += ` ${branch}`;
      }
      
      if (author) {
        logCommand += ` --author="${author}"`;
      }
      
      if (since) {
        logCommand += ` --since="${since.toISOString()}"`;
      }
      
      if (until) {
        logCommand += ` --until="${until.toISOString()}"`;
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', logCommand]);

      if (!result) {
        throw new Error(`Failed to get Git log`);
      }

      const commits = this.parseGitLog(result);

      // Get total count
      const countResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${repoPath}" && git rev-list --count HEAD`]
      );

      const totalCount = parseInt(countResult?.trim() || '0');

      return {
        commits,
        hasMore: commits.length === limit,
        totalCount,
      };

    } catch (error) {
      console.error('Error getting Git log:', error);
      throw error;
    }
  }

  /**
   * Get file diff
   */
  async getDiff(
    workspaceId: string,
    repoPath: string,
    options: {
      staged?: boolean;
      file?: string;
      commit1?: string;
      commit2?: string;
    } = {}
  ): Promise<GitDiff[]> {
    try {
      const { staged = false, file, commit1, commit2 } = options;

      let diffCommand = `cd "${repoPath}" && git diff`;
      
      if (staged) {
        diffCommand += ' --staged';
      }
      
      if (commit1 && commit2) {
        diffCommand += ` ${commit1}..${commit2}`;
      } else if (commit1) {
        diffCommand += ` ${commit1}`;
      }
      
      if (file) {
        diffCommand += ` -- "${file}"`;
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', diffCommand]);

      if (!result) {
        throw new Error(`Failed to get diff`);
      }

      return this.parseGitDiff(result);

    } catch (error) {
      console.error('Error getting Git diff:', error);
      throw error;
    }
  }

  /**
   * Create branch
   */
  async createBranch(
    workspaceId: string,
    repoPath: string,
    branchName: string,
    options: {
      checkout?: boolean;
      startPoint?: string;
    } = {}
  ): Promise<void> {
    try {
      const { checkout = true, startPoint } = options;

      let branchCommand = `cd "${repoPath}" && git branch "${branchName}"`;
      
      if (startPoint) {
        branchCommand += ` "${startPoint}"`;
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', branchCommand]);

      if (!result) {
        throw new Error(`Failed to create branch`);
      }

      // Checkout the new branch if requested
      if (checkout) {
        await this.checkoutBranch(workspaceId, repoPath, branchName);
      }

    } catch (error) {
      console.error('Error creating branch:', error);
      throw error;
    }
  }

  /**
   * Checkout branch
   */
  async checkoutBranch(
    workspaceId: string,
    repoPath: string,
    branchName: string
  ): Promise<void> {
    try {
      const result = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${repoPath}" && git checkout "${branchName}"`]
      );

      if (!result) {
        throw new Error(`Failed to checkout branch`);
      }
    } catch (error) {
      console.error('Error checking out branch:', error);
      throw error;
    }
  }

  /**
   * List branches
   */
  async listBranches(
    workspaceId: string,
    repoPath: string,
    options: { includeRemote?: boolean } = {}
  ): Promise<GitBranch[]> {
    try {
      const { includeRemote = false } = options;

      let branchCommand = `cd "${repoPath}" && git branch -v`;
      if (includeRemote) {
        branchCommand += ' -a';
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', branchCommand]);

      if (!result) {
        throw new Error(`Failed to list branches`);
      }

      return this.parseGitBranches(result);

    } catch (error) {
      console.error('Error listing branches:', error);
      throw error;
    }
  }

  /**
   * Delete a branch
   */
  async deleteBranch(
    workspaceId: string,
    repoPath: string,
    branchName: string,
    force: boolean = false
  ): Promise<void> {
    try {
      const deleteCommand = `cd "${repoPath}" && git branch ${force ? '-D' : '-d'} "${branchName}"`;
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', deleteCommand]);

      if (!result) {
        throw new Error(`Failed to delete branch`);
      }

    } catch (error) {
      console.error('Error deleting branch:', error);
      throw error;
    }
  }

  /**
   * Push changes
   */
  async push(
    workspaceId: string,
    repoPath: string,
    options: {
      remote?: string;
      branch?: string;
      force?: boolean;
      setUpstream?: boolean;
    } = {}
  ): Promise<void> {
    try {
      const { remote = 'origin', branch, force = false, setUpstream = false } = options;

      let pushCommand = `cd "${repoPath}" && git push ${remote}`;
      
      if (branch) {
        pushCommand += ` ${branch}`;
      }
      
      if (force) {
        pushCommand += ' --force';
      }
      
      if (setUpstream) {
        pushCommand += ' --set-upstream';
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', pushCommand]);

      if (!result) {
        throw new Error(`Failed to push`);
      }

    } catch (error) {
      console.error('Error pushing changes:', error);
      throw error;
    }
  }

  /**
   * Pull changes
   */
  async pull(
    workspaceId: string,
    repoPath: string,
    options: {
      remote?: string;
      branch?: string;
      rebase?: boolean;
    } = {}
  ): Promise<void> {
    try {
      const { remote = 'origin', branch, rebase = false } = options;

      let pullCommand = `cd "${repoPath}" && git pull`;
      
      if (rebase) {
        pullCommand += ' --rebase';
      }
      
      pullCommand += ` ${remote}`;
      
      if (branch) {
        pullCommand += ` ${branch}`;
      }

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', pullCommand]);

      if (!result) {
        throw new Error(`Failed to pull`);
      }

    } catch (error) {
      console.error('Error pulling changes:', error);
      throw error;
    }
  }

  // Private helper methods

  private parseGitStatus(output: string): GitStatus {
    const lines = output.split('\n');
    const branchLine = lines[0];
    
    // Parse branch information
    let branch = 'main';
    let ahead = 0;
    let behind = 0;
    
    if (branchLine.startsWith('## ')) {
      const branchInfo = branchLine.substring(3);
      const parts = branchInfo.split('...');
      branch = parts[0];
      
      if (parts[1]) {
        const trackingInfo = parts[1];
        const aheadMatch = trackingInfo.match(/ahead (\d+)/);
        const behindMatch = trackingInfo.match(/behind (\d+)/);
        
        if (aheadMatch) ahead = parseInt(aheadMatch[1]);
        if (behindMatch) behind = parseInt(behindMatch[1]);
      }
    }

    // Parse file statuses
    const staged: GitFileStatus[] = [];
    const unstaged: GitFileStatus[] = [];
    const untracked: string[] = [];
    const conflicted: string[] = [];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const statusCode = line.substring(0, 2);
      const filePath = line.substring(3);

      // Handle different status codes
      if (statusCode === '??') {
        untracked.push(filePath);
      } else if (statusCode.includes('U') || statusCode === 'AA' || statusCode === 'DD') {
        conflicted.push(filePath);
      } else {
        // Parse staged and unstaged changes
        const stagedStatus = statusCode[0];
        const unstagedStatus = statusCode[1];

        if (stagedStatus !== ' ') {
          staged.push({
            path: filePath,
            status: this.mapStatusCode(stagedStatus),
          });
        }

        if (unstagedStatus !== ' ') {
          unstaged.push({
            path: filePath,
            status: this.mapStatusCode(unstagedStatus),
          });
        }
      }
    }

    return {
      branch,
      ahead,
      behind,
      staged,
      unstaged,
      untracked,
      conflicted,
      clean: staged.length === 0 && unstaged.length === 0 && untracked.length === 0,
    };
  }

  private parseGitLog(output: string): GitCommit[] {
    const lines = output.split('\n').filter(line => line.trim());
    const commits: GitCommit[] = [];

    for (const line of lines) {
      const parts = line.split('|');
      if (parts.length >= 6) {
        commits.push({
          hash: parts[0],
          shortHash: parts[1],
          author: parts[2],
          email: parts[3],
          date: new Date(parts[4]),
          message: parts[5],
          files: [], // Would be populated with additional git command
        });
      }
    }

    return commits;
  }

  private parseGitBranches(output: string): GitBranch[] {
    const lines = output.split('\n').filter(line => line.trim());
    const branches: GitBranch[] = [];

    for (const line of lines) {
      const trimmed = line.trim();
      const current = trimmed.startsWith('*');
      const parts = trimmed.replace(/^\*?\s+/, '').split(/\s+/);
      
      if (parts.length >= 2) {
        const name = parts[0];
        const hash = parts[1];
        const message = parts.slice(2).join(' ');

        branches.push({
          name,
          current,
          lastCommit: {
            hash,
            message,
            date: new Date(), // Would get actual date with additional command
          },
        });
      }
    }

    return branches;
  }

  private parseGitDiff(output: string): GitDiff[] {
    const diffs: GitDiff[] = [];
    const lines = output.split('\n');
    let currentDiff: GitDiff | null = null;
    let currentHunk: GitDiffHunk | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Parse file header (diff --git a/file b/file)
      if (line.startsWith('diff --git ')) {
        if (currentDiff) {
          diffs.push(currentDiff);
        }

        const match = line.match(/diff --git a\/(.+) b\/(.+)/);
        if (match) {
          currentDiff = {
            file: match[2],
            oldFile: match[1],
            hunks: [],
            isBinary: false,
            isNew: false,
            isDeleted: false,
            isRenamed: match[1] !== match[2],
          };
        }
        continue;
      }

      if (!currentDiff) continue;

      // Parse binary file indicator
      if (line.includes('Binary files') && line.includes('differ')) {
        currentDiff.isBinary = true;
        continue;
      }

      // Parse hunk header (@@ -start,count +start,count @@)
      if (line.startsWith('@@')) {
        if (currentHunk) {
          currentDiff.hunks.push(currentHunk);
        }

        const hunkMatch = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@(.*)/);
        if (hunkMatch) {
          currentHunk = {
            oldStart: parseInt(hunkMatch[1]),
            oldLines: parseInt(hunkMatch[2] || '1'),
            newStart: parseInt(hunkMatch[3]),
            newLines: parseInt(hunkMatch[4] || '1'),
            lines: [],
          };
        }
        continue;
      }

      if (!currentHunk) continue;

      // Parse diff lines
      if (line.startsWith('+') && !line.startsWith('+++')) {
        const newLineNumber = currentHunk.newStart + currentHunk.lines.filter(l => l.type !== 'deletion').length;
        currentHunk.lines.push({
          type: 'addition',
          content: line.substring(1),
          newLineNumber,
        });
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        const oldLineNumber = currentHunk.oldStart + currentHunk.lines.filter(l => l.type !== 'addition').length;
        currentHunk.lines.push({
          type: 'deletion',
          content: line.substring(1),
          oldLineNumber,
        });
      } else if (line.startsWith(' ')) {
        const oldLineNumber = currentHunk.oldStart + currentHunk.lines.filter(l => l.type !== 'addition').length;
        const newLineNumber = currentHunk.newStart + currentHunk.lines.filter(l => l.type !== 'deletion').length;
        currentHunk.lines.push({
          type: 'context',
          content: line.substring(1),
          oldLineNumber,
          newLineNumber,
        });
      }
    }

    // Add the last diff and hunk
    if (currentHunk && currentDiff) {
      currentDiff.hunks.push(currentHunk);
    }
    if (currentDiff) {
      diffs.push(currentDiff);
    }

    return diffs;
  }

  private mapStatusCode(code: string): 'added' | 'modified' | 'deleted' | 'renamed' | 'copied' {
    switch (code) {
      case 'A': return 'added';
      case 'M': return 'modified';
      case 'D': return 'deleted';
      case 'R': return 'renamed';
      case 'C': return 'copied';
      default: return 'modified';
    }
  }

  private async getLastCommit(workspaceId: string, repoPath: string): Promise<GitCommit | undefined> {
    try {
      const result = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `cd "${repoPath}" && git log -1 --pretty=format:"%H|%h|%an|%ae|%ad|%s" --date=iso`]
      );

      if (!result) {
        return undefined;
      }

      const commits = this.parseGitLog(result);
      return commits[0];

    } catch (error) {
      console.error('Error getting last commit:', error);
      return undefined;
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // Public getter methods
  getRepositories(): GitRepository[] {
    return Array.from(this.repositories.values());
  }

  getRepository(id: string): GitRepository | undefined {
    return this.repositories.get(id);
  }
}

// Export singleton instance
export const gitIntegrationService = new GitIntegrationService();
