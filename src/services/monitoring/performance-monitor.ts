/**
 * Performance Monitoring Service
 * Tracks application performance, resource usage, and provides analytics
 */

import { dockerService } from '../docker';
import { PythonFramework } from '@/types/python-workspace';

export interface PerformanceMetrics {
  timestamp: Date;
  workspaceId: string;
  cpu: {
    usage: number; // percentage
    cores: number;
    loadAverage: number[];
  };
  memory: {
    used: number; // bytes
    total: number; // bytes
    usage: number; // percentage
    available: number; // bytes
  };
  disk: {
    used: number; // bytes
    total: number; // bytes
    usage: number; // percentage
    available: number; // bytes
    iops: {
      read: number;
      write: number;
    };
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
    zombie: number;
  };
}

export interface ApplicationMetrics {
  timestamp: Date;
  workspaceId: string;
  framework: PythonFramework;
  application: {
    name: string;
    version: string;
    uptime: number; // seconds
    status: 'running' | 'stopped' | 'error' | 'starting';
    pid?: number;
  };
  performance: {
    responseTime: number; // milliseconds
    throughput: number; // requests per second
    errorRate: number; // percentage
    activeConnections: number;
  };
  resources: {
    cpuUsage: number; // percentage
    memoryUsage: number; // bytes
    fileDescriptors: number;
    threads: number;
  };
  python: {
    version: string;
    garbageCollector: {
      collections: number;
      collected: number;
      uncollectable: number;
    };
    modules: number;
    exceptions: number;
  };
}

export interface LogEntry {
  id: string;
  timestamp: Date;
  workspaceId: string;
  level: 'debug' | 'info' | 'warning' | 'error' | 'critical';
  source: string;
  message: string;
  context?: Record<string, any>;
  stackTrace?: string;
  tags: string[];
}

export interface Alert {
  id: string;
  workspaceId: string;
  type: 'performance' | 'error' | 'resource' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  metric: string;
  threshold: number;
  currentValue: number;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  actions: string[];
}

export interface PerformanceReport {
  workspaceId: string;
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    avgCpuUsage: number;
    avgMemoryUsage: number;
    avgResponseTime: number;
    totalRequests: number;
    errorCount: number;
    uptime: number;
  };
  trends: {
    cpu: Array<{ timestamp: Date; value: number }>;
    memory: Array<{ timestamp: Date; value: number }>;
    responseTime: Array<{ timestamp: Date; value: number }>;
    throughput: Array<{ timestamp: Date; value: number }>;
  };
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurrence: Date;
  }>;
  recommendations: string[];
}

class PerformanceMonitorService {
  // In-memory storage for demo - replace with time-series database in production
  private metrics: Map<string, PerformanceMetrics[]> = new Map();
  private appMetrics: Map<string, ApplicationMetrics[]> = new Map();
  private logs: Map<string, LogEntry[]> = new Map();
  private alerts: Map<string, Alert[]> = new Map();
  private monitoringIntervals: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Start monitoring workspace
   */
  startMonitoring(
    workspaceId: string,
    options: {
      interval?: number; // milliseconds
      retentionPeriod?: number; // hours
      alertThresholds?: {
        cpuUsage?: number;
        memoryUsage?: number;
        diskUsage?: number;
        responseTime?: number;
        errorRate?: number;
      };
    } = {}
  ): void {
    const {
      interval = 30000, // 30 seconds
      retentionPeriod = 24, // 24 hours
      alertThresholds = {
        cpuUsage: 80,
        memoryUsage: 85,
        diskUsage: 90,
        responseTime: 5000,
        errorRate: 5,
      },
    } = options;

    // Stop existing monitoring if running
    this.stopMonitoring(workspaceId);

    // Start monitoring interval
    const intervalId = setInterval(async () => {
      try {
        await this.collectMetrics(workspaceId);
        await this.checkAlerts(workspaceId, alertThresholds);
        this.cleanupOldData(workspaceId, retentionPeriod);
      } catch (error) {
        console.error(`Error collecting metrics for workspace ${workspaceId}:`, error);
      }
    }, interval);

    this.monitoringIntervals.set(workspaceId, intervalId);
  }

  /**
   * Stop monitoring workspace
   */
  stopMonitoring(workspaceId: string): void {
    const intervalId = this.monitoringIntervals.get(workspaceId);
    if (intervalId) {
      clearInterval(intervalId);
      this.monitoringIntervals.delete(workspaceId);
    }
  }

  /**
   * Collect system metrics
   */
  async collectMetrics(workspaceId: string): Promise<PerformanceMetrics> {
    try {
      // Get CPU usage
      const cpuResult = await dockerService.execInContainer(
        workspaceId,
        ["top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1"]
    );

      // Get memory usage
      const memResult = await dockerService.execInContainer(
        workspaceId,
        ["free -b | grep '^Mem:' | awk '{print $2,$3,$7}'"]
    );

      // Get disk usage
      const diskResult = await dockerService.execInContainer(
        workspaceId,
        ["df -B1 /home/<USER>'{print $2,$3,$4}'"]
    );

      // Get load average
      const loadResult = await dockerService.execInContainer(
        workspaceId,
        ["uptime | awk -F'load average:' '{print $2}'"]
    );

      // Get process count
      const procResult = await dockerService.execInContainer(
        workspaceId,
        ["ps aux | wc -l"]
    );

      // Parse results
      const cpuUsage = parseFloat(cpuResult.output?.trim() || '0');
      const [memTotal, memUsed, memAvailable] = (memResult.output?.trim() || '0 0 0').split(' ').map(Number);
      const [diskTotal, diskUsed, diskAvailable] = (diskResult.output?.trim() || '0 0 0').split(' ').map(Number);
      const loadAverage = (loadResult.output?.trim() || '0,0,0').split(',').map(s => parseFloat(s.trim()));
      const totalProcesses = parseInt(procResult.output?.trim() || '0');

      const metrics: PerformanceMetrics = {
        timestamp: new Date(),
        workspaceId,
        cpu: {
          usage: cpuUsage,
          cores: 1, // Simplified for demo
          loadAverage,
        },
        memory: {
          used: memUsed,
          total: memTotal,
          usage: memTotal > 0 ? (memUsed / memTotal) * 100 : 0,
          available: memAvailable,
        },
        disk: {
          used: diskUsed,
          total: diskTotal,
          usage: diskTotal > 0 ? (diskUsed / diskTotal) * 100 : 0,
          available: diskAvailable,
          iops: {
            read: 0, // Would require more complex monitoring
            write: 0,
          },
        },
        network: {
          bytesIn: 0, // Would require network monitoring
          bytesOut: 0,
          packetsIn: 0,
          packetsOut: 0,
        },
        processes: {
          total: totalProcesses,
          running: 0, // Would parse from ps output
          sleeping: 0,
          zombie: 0,
        },
      };

      // Store metrics
      const workspaceMetrics = this.metrics.get(workspaceId) || [];
      workspaceMetrics.push(metrics);
      this.metrics.set(workspaceId, workspaceMetrics);

      return metrics;

    } catch (error) {
      console.error('Error collecting metrics:', error);
      throw error;
    }
  }

  /**
   * Collect application-specific metrics
   */
  async collectApplicationMetrics(
    workspaceId: string,
    framework: PythonFramework,
    appName: string
  ): Promise<ApplicationMetrics> {
    try {
      // Get Python process info
      const pythonPsResult = await dockerService.execInContainer(
        workspaceId,
        ["ps aux | grep python | grep -v grep | head -1"]
    );

      // Get Python version
      const pythonVersionResult = await dockerService.execInContainer(
        workspaceId,
        ["python --version 2>&1"]
    );

      // Get application-specific metrics based on framework
      let appMetrics = {};
      
      switch (framework) {
        case 'django':
          appMetrics = await this.collectDjangoMetrics(workspaceId);
          break;
        case 'flask':
          appMetrics = await this.collectFlaskMetrics(workspaceId);
          break;
        case 'fastapi':
          appMetrics = await this.collectFastAPIMetrics(workspaceId);
          break;
        case 'streamlit':
          appMetrics = await this.collectStreamlitMetrics(workspaceId);
          break;
        case 'gradio':
          appMetrics = await this.collectGradioMetrics(workspaceId);
          break;
      }

      const pythonVersion = pythonVersionResult.output?.trim().replace('Python ', '') || 'unknown';
      const processInfo = pythonPsResult.output?.trim().split(/\s+/) || [];
      const pid = processInfo.length > 1 ? parseInt(processInfo[1]) : undefined;

      const metrics: ApplicationMetrics = {
        timestamp: new Date(),
        workspaceId,
        framework,
        application: {
          name: appName,
          version: '1.0.0', // Would get from app config
          uptime: 0, // Would calculate from process start time
          status: pid ? 'running' : 'stopped',
          pid,
        },
        performance: {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          activeConnections: 0,
          ...appMetrics,
        },
        resources: {
          cpuUsage: 0, // Would get from process-specific monitoring
          memoryUsage: 0,
          fileDescriptors: 0,
          threads: 1,
        },
        python: {
          version: pythonVersion,
          garbageCollector: {
            collections: 0,
            collected: 0,
            uncollectable: 0,
          },
          modules: 0,
          exceptions: 0,
        },
      };

      // Store application metrics
      const workspaceAppMetrics = this.appMetrics.get(workspaceId) || [];
      workspaceAppMetrics.push(metrics);
      this.appMetrics.set(workspaceId, workspaceAppMetrics);

      return metrics;

    } catch (error) {
      console.error('Error collecting application metrics:', error);
      throw error;
    }
  }

  /**
   * Add log entry
   */
  addLogEntry(
    workspaceId: string,
    level: LogEntry['level'],
    source: string,
    message: string,
    context?: Record<string, any>,
    stackTrace?: string
  ): LogEntry {
    const logEntry: LogEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      workspaceId,
      level,
      source,
      message,
      context,
      stackTrace,
      tags: [],
    };

    const workspaceLogs = this.logs.get(workspaceId) || [];
    workspaceLogs.push(logEntry);
    this.logs.set(workspaceId, workspaceLogs);

    // Check if this log entry should trigger an alert
    if (level === 'error' || level === 'critical') {
      this.createAlert(workspaceId, {
        type: 'error',
        severity: level === 'critical' ? 'critical' : 'high',
        title: `${level.toUpperCase()}: ${source}`,
        description: message,
        metric: 'error_count',
        threshold: 1,
        currentValue: 1,
      });
    }

    return logEntry;
  }

  /**
   * Generate performance report
   */
  generateReport(
    workspaceId: string,
    period: { start: Date; end: Date }
  ): PerformanceReport {
    const workspaceMetrics = this.metrics.get(workspaceId) || [];
    const workspaceLogs = this.logs.get(workspaceId) || [];

    // Filter metrics by period
    const periodMetrics = workspaceMetrics.filter(
      m => m.timestamp >= period.start && m.timestamp <= period.end
    );

    // Filter logs by period
    const periodLogs = workspaceLogs.filter(
      l => l.timestamp >= period.start && l.timestamp <= period.end
    );

    // Calculate summary statistics
    const summary = {
      avgCpuUsage: this.calculateAverage(periodMetrics.map(m => m.cpu.usage)),
      avgMemoryUsage: this.calculateAverage(periodMetrics.map(m => m.memory.usage)),
      avgResponseTime: 0, // Would calculate from app metrics
      totalRequests: 0, // Would calculate from app metrics
      errorCount: periodLogs.filter(l => l.level === 'error' || l.level === 'critical').length,
      uptime: periodMetrics.length > 0 ? 
        (period.end.getTime() - period.start.getTime()) / 1000 : 0,
    };

    // Generate trends
    const trends = {
      cpu: periodMetrics.map(m => ({ timestamp: m.timestamp, value: m.cpu.usage })),
      memory: periodMetrics.map(m => ({ timestamp: m.timestamp, value: m.memory.usage })),
      responseTime: [], // Would populate from app metrics
      throughput: [], // Would populate from app metrics
    };

    // Get top errors
    const errorCounts = new Map<string, { count: number; lastOccurrence: Date }>();
    periodLogs
      .filter(l => l.level === 'error' || l.level === 'critical')
      .forEach(log => {
        const existing = errorCounts.get(log.message);
        if (existing) {
          existing.count++;
          if (log.timestamp > existing.lastOccurrence) {
            existing.lastOccurrence = log.timestamp;
          }
        } else {
          errorCounts.set(log.message, { count: 1, lastOccurrence: log.timestamp });
        }
      });

    const topErrors = Array.from(errorCounts.entries())
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Generate recommendations
    const recommendations = this.generateRecommendations(summary, periodMetrics);

    return {
      workspaceId,
      period,
      summary,
      trends,
      topErrors,
      recommendations,
    };
  }

  // Private helper methods

  private async collectDjangoMetrics(workspaceId: string): Promise<any> {
    try {
      // Check if Django is running
      const processCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'pgrep -f "python.*manage.py.*runserver" | wc -l']
    );

      const isRunning = parseInt(processCheck?.trim() || '0') > 0;

      if (!isRunning) {
        return {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          activeConnections: 0,
        };
      }

      // Get Django process stats
      const statsResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'ps aux | grep "python.*manage.py.*runserver" | grep -v grep | awk \'{print $3, $4}\'']
    );

      const [cpuUsage = '0', memUsage = '0'] = statsResult?.trim().split(' ') || [];

      // Check for Django logs to estimate metrics
      const logCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'find /home/<USER>"*.log" -type f -exec tail -100 {} \\; 2>/dev/null | grep -E "(GET|POST|PUT|DELETE)" | wc -l']
    );

      const recentRequests = parseInt(logCheck?.trim() || '0');

      return {
        responseTime: parseFloat(cpuUsage) * 10, // Estimate based on CPU usage
        throughput: recentRequests / 60, // Requests per minute converted to per second
        errorRate: parseFloat(memUsage) > 80 ? 2 : 0.5, // Higher error rate if memory is high
        activeConnections: Math.min(recentRequests, 50),
      };
    } catch (error) {
      console.error('Error collecting Django metrics:', error);
      return {
        responseTime: 0,
        throughput: 0,
        errorRate: 0,
        activeConnections: 0,
      };
    }
  }

  private async collectFlaskMetrics(workspaceId: string): Promise<any> {
    try {
      // Check if Flask is running
      const processCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'pgrep -f "python.*flask.*run\\|python.*app.py" | wc -l']
    );

      const isRunning = parseInt(processCheck?.trim() || '0') > 0;

      if (!isRunning) {
        return {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          activeConnections: 0,
        };
      }

      // Get Flask process stats
      const statsResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'ps aux | grep -E "python.*(flask|app.py)" | grep -v grep | awk \'{print $3, $4}\'']
    );

      const [cpuUsage = '0', memUsage = '0'] = statsResult?.trim().split(' ') || [];

      // Check Flask access logs
      const logCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'find /home/<USER>"*.log" -type f -exec grep -l "GET\\|POST" {} \\; 2>/dev/null | xargs tail -50 2>/dev/null | grep -E "\\[.*\\]" | wc -l']
    );

      const recentRequests = parseInt(logCheck?.trim() || '0');

      return {
        responseTime: parseFloat(cpuUsage) * 8, // Flask typically faster than Django
        throughput: recentRequests / 60,
        errorRate: parseFloat(memUsage) > 75 ? 1.5 : 0.3,
        activeConnections: Math.min(recentRequests, 30),
      };
    } catch (error) {
      console.error('Error collecting Flask metrics:', error);
      return {
        responseTime: 0,
        throughput: 0,
        errorRate: 0,
        activeConnections: 0,
      };
    }
  }

  private async collectFastAPIMetrics(workspaceId: string): Promise<any> {
    try {
      // Check if FastAPI/Uvicorn is running
      const processCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'pgrep -f "uvicorn\\|fastapi" | wc -l']
    );

      const isRunning = parseInt(processCheck?.trim() || '0') > 0;

      if (!isRunning) {
        return {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          activeConnections: 0,
        };
      }

      // Get FastAPI process stats
      const statsResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'ps aux | grep -E "uvicorn\\|fastapi" | grep -v grep | awk \'{print $3, $4}\'']
    );

      const [cpuUsage = '0', memUsage = '0'] = statsResult?.trim().split(' ') || [];

      // Check for access logs
      const logCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'find /home/<USER>"*.log" -type f -exec grep -l "INFO.*HTTP" {} \\; 2>/dev/null | xargs tail -50 2>/dev/null | grep "HTTP" | wc -l']
    );

      const recentRequests = parseInt(logCheck?.trim() || '0');

      return {
        responseTime: parseFloat(cpuUsage) * 5, // FastAPI is typically very fast
        throughput: recentRequests / 60,
        errorRate: parseFloat(memUsage) > 70 ? 1 : 0.2,
        activeConnections: Math.min(recentRequests, 100),
      };
    } catch (error) {
      console.error('Error collecting FastAPI metrics:', error);
      return {
        responseTime: 0,
        throughput: 0,
        errorRate: 0,
        activeConnections: 0,
      };
    }
  }

  private async collectStreamlitMetrics(workspaceId: string): Promise<any> {
    try {
      // Check if Streamlit is running
      const processCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'pgrep -f "streamlit.*run" | wc -l']
    );

      const isRunning = parseInt(processCheck?.trim() || '0') > 0;

      if (!isRunning) {
        return {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          activeConnections: 0,
        };
      }

      // Get Streamlit process stats
      const statsResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'ps aux | grep "streamlit.*run" | grep -v grep | awk \'{print $3, $4}\'']
    );

      const [cpuUsage = '0', memUsage = '0'] = statsResult?.trim().split(' ') || [];

      // Check for Streamlit sessions (typically fewer concurrent users)
      const sessionCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'netstat -an 2>/dev/null | grep ":8501" | grep ESTABLISHED | wc -l']
    );

      const activeSessions = parseInt(sessionCheck?.trim() || '0');

      return {
        responseTime: parseFloat(cpuUsage) * 20, // Streamlit can be slower due to recomputation
        throughput: activeSessions / 60, // Sessions per minute
        errorRate: parseFloat(memUsage) > 85 ? 0.8 : 0.1,
        activeConnections: activeSessions,
      };
    } catch (error) {
      console.error('Error collecting Streamlit metrics:', error);
      return {
        responseTime: 0,
        throughput: 0,
        errorRate: 0,
        activeConnections: 0,
      };
    }
  }

  private async collectGradioMetrics(workspaceId: string): Promise<any> {
    try {
      // Check if Gradio is running
      const processCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'pgrep -f "python.*gradio" | wc -l']
    );

      const isRunning = parseInt(processCheck?.trim() || '0') > 0;

      if (!isRunning) {
        return {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          activeConnections: 0,
        };
      }

      // Get Gradio process stats
      const statsResult = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'ps aux | grep "python.*gradio" | grep -v grep | awk \'{print $3, $4}\'']
    );

      const [cpuUsage = '0', memUsage = '0'] = statsResult?.trim().split(' ') || [];

      // Check for Gradio connections (typically on port 7860)
      const connectionCheck = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', 'netstat -an 2>/dev/null | grep ":7860" | grep ESTABLISHED | wc -l']
    );

      const activeConnections = parseInt(connectionCheck?.trim() || '0');

      return {
        responseTime: parseFloat(cpuUsage) * 30, // Gradio can be slow for ML inference
        throughput: activeConnections / 60,
        errorRate: parseFloat(memUsage) > 90 ? 1 : 0.1,
        activeConnections,
      };
    } catch (error) {
      console.error('Error collecting Gradio metrics:', error);
      return {
        responseTime: 0,
        throughput: 0,
        errorRate: 0,
        activeConnections: 0,
      };
    }
  }

  private async checkAlerts(
    workspaceId: string,
    thresholds: Record<string, number>
  ): Promise<void> {
    const latestMetrics = this.getLatestMetrics(workspaceId);
    if (!latestMetrics) return;

    // Check CPU usage
    if (latestMetrics.cpu.usage > thresholds.cpuUsage!) {
      this.createAlert(workspaceId, {
        type: 'performance',
        severity: 'high',
        title: 'High CPU Usage',
        description: `CPU usage is ${latestMetrics.cpu.usage.toFixed(1)}%`,
        metric: 'cpu_usage',
        threshold: thresholds.cpuUsage!,
        currentValue: latestMetrics.cpu.usage,
      });
    }

    // Check memory usage
    if (latestMetrics.memory.usage > thresholds.memoryUsage!) {
      this.createAlert(workspaceId, {
        type: 'resource',
        severity: 'high',
        title: 'High Memory Usage',
        description: `Memory usage is ${latestMetrics.memory.usage.toFixed(1)}%`,
        metric: 'memory_usage',
        threshold: thresholds.memoryUsage!,
        currentValue: latestMetrics.memory.usage,
      });
    }

    // Check disk usage
    if (latestMetrics.disk.usage > thresholds.diskUsage!) {
      this.createAlert(workspaceId, {
        type: 'resource',
        severity: 'medium',
        title: 'High Disk Usage',
        description: `Disk usage is ${latestMetrics.disk.usage.toFixed(1)}%`,
        metric: 'disk_usage',
        threshold: thresholds.diskUsage!,
        currentValue: latestMetrics.disk.usage,
      });
    }
  }

  private createAlert(
    workspaceId: string,
    alertData: Omit<Alert, 'id' | 'workspaceId' | 'timestamp' | 'resolved' | 'actions'>
  ): Alert {
    const alert: Alert = {
      id: this.generateId(),
      workspaceId,
      timestamp: new Date(),
      resolved: false,
      actions: this.generateAlertActions(alertData.type, alertData.metric),
      ...alertData,
    };

    const workspaceAlerts = this.alerts.get(workspaceId) || [];
    workspaceAlerts.push(alert);
    this.alerts.set(workspaceId, workspaceAlerts);

    return alert;
  }

  private generateAlertActions(type: Alert['type'], metric: string): string[] {
    const actions: Record<string, string[]> = {
      performance: [
        'Check for resource-intensive processes',
        'Optimize application code',
        'Consider scaling resources',
      ],
      resource: [
        'Free up disk space',
        'Increase memory allocation',
        'Monitor resource usage trends',
      ],
      error: [
        'Check application logs',
        'Review recent code changes',
        'Verify external dependencies',
      ],
      security: [
        'Review security logs',
        'Check for unauthorized access',
        'Update security configurations',
      ],
    };

    return actions[type] || ['Investigate the issue', 'Monitor the situation'];
  }

  private generateRecommendations(
    summary: PerformanceReport['summary'],
    metrics: PerformanceMetrics[]
  ): string[] {
    const recommendations: string[] = [];

    if (summary.avgCpuUsage > 70) {
      recommendations.push('Consider optimizing CPU-intensive operations or scaling resources');
    }

    if (summary.avgMemoryUsage > 80) {
      recommendations.push('Monitor memory usage and consider increasing available memory');
    }

    if (summary.errorCount > 10) {
      recommendations.push('High error rate detected - review application logs and fix issues');
    }

    if (summary.avgResponseTime > 2000) {
      recommendations.push('Response times are high - optimize database queries and caching');
    }

    if (recommendations.length === 0) {
      recommendations.push('System performance is within normal parameters');
    }

    return recommendations;
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private cleanupOldData(workspaceId: string, retentionHours: number): void {
    const cutoffTime = new Date(Date.now() - retentionHours * 60 * 60 * 1000);

    // Clean up metrics
    const workspaceMetrics = this.metrics.get(workspaceId) || [];
    const filteredMetrics = workspaceMetrics.filter(m => m.timestamp > cutoffTime);
    this.metrics.set(workspaceId, filteredMetrics);

    // Clean up logs
    const workspaceLogs = this.logs.get(workspaceId) || [];
    const filteredLogs = workspaceLogs.filter(l => l.timestamp > cutoffTime);
    this.logs.set(workspaceId, filteredLogs);

    // Clean up app metrics
    const workspaceAppMetrics = this.appMetrics.get(workspaceId) || [];
    const filteredAppMetrics = workspaceAppMetrics.filter(m => m.timestamp > cutoffTime);
    this.appMetrics.set(workspaceId, filteredAppMetrics);
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // Public getter methods
  getLatestMetrics(workspaceId: string): PerformanceMetrics | undefined {
    const workspaceMetrics = this.metrics.get(workspaceId) || [];
    return workspaceMetrics[workspaceMetrics.length - 1];
  }

  getMetrics(workspaceId: string, limit?: number): PerformanceMetrics[] {
    const workspaceMetrics = this.metrics.get(workspaceId) || [];
    return limit ? workspaceMetrics.slice(-limit) : workspaceMetrics;
  }

  getLogs(workspaceId: string, limit?: number): LogEntry[] {
    const workspaceLogs = this.logs.get(workspaceId) || [];
    return limit ? workspaceLogs.slice(-limit) : workspaceLogs;
  }

  getAlerts(workspaceId: string, includeResolved: boolean = false): Alert[] {
    const workspaceAlerts = this.alerts.get(workspaceId) || [];
    return includeResolved ? workspaceAlerts : workspaceAlerts.filter(a => !a.resolved);
  }

  resolveAlert(workspaceId: string, alertId: string): boolean {
    const workspaceAlerts = this.alerts.get(workspaceId) || [];
    const alert = workspaceAlerts.find(a => a.id === alertId);
    
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      return true;
    }
    
    return false;
  }
}

// Export singleton instance
export const performanceMonitorService = new PerformanceMonitorService();
