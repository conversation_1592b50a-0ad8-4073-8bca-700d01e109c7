/**
 * Node/React Live Preview Service
 * Handles live preview functionality for Node.js and React applications
 */

import { NodeLivePreview, NodeFramework } from '@/types/node-react-workspace';
import { dockerService } from './docker';

export interface StartPreviewOptions {
  projectPath: string;
  framework: NodeFramework;
  port?: number;
  command?: string;
  environment?: Record<string, string>;
  hotReload?: boolean;
  buildMode?: 'development' | 'production' | 'preview';
}

export class NodeReactLivePreviewService {
  private previews: Map<string, NodeLivePreview> = new Map();
  private processes: Map<string, any> = new Map();

  /**
   * Get default port for framework
   */
  private getDefaultPort(framework: NodeFramework): number {
    const ports: Record<NodeFramework, number> = {
      nextjs: 3000,
      react: 5173,
      express: 3001,
      nestjs: 3000,
      vue: 5173,
      angular: 4200,
      nuxt: 3000,
      svelte: 5173,
      remix: 3000,
      gatsby: 8000,
    };
    return ports[framework];
  }

  /**
   * Get default command for framework
   */
  private getDefaultCommand(framework: NodeFramework): string {
    const commands: Record<NodeFramework, string> = {
      nextjs: 'npm run dev',
      react: 'npm run dev',
      express: 'npm run dev',
      nestjs: 'npm run dev',
      vue: 'npm run dev',
      angular: 'ng serve --host 0.0.0.0',
      nuxt: 'npm run dev',
      svelte: 'npm run dev',
      remix: 'npm run dev',
      gatsby: 'npm run develop',
    };
    return commands[framework];
  }

  /**
   * Check if port is available
   */
  private async isPortAvailable(workspaceId: string, port: number): Promise<boolean> {
    try {
      const result = await dockerService.executeCommand(
        workspaceId,
        `netstat -tuln | grep :${port} || echo "available"`
      );
      return result.stdout.includes('available');
    } catch (error) {
      console.warn(`Error checking port ${port}:`, error);
      return true; // Assume available if check fails
    }
  }

  /**
   * Find available port starting from preferred port
   */
  private async findAvailablePort(workspaceId: string, preferredPort: number): Promise<number> {
    let port = preferredPort;
    while (port < preferredPort + 100) {
      if (await this.isPortAvailable(workspaceId, port)) {
        return port;
      }
      port++;
    }
    throw new Error(`No available ports found starting from ${preferredPort}`);
  }

  /**
   * Start live preview
   */
  async startPreview(workspaceId: string, options: StartPreviewOptions): Promise<NodeLivePreview> {
    try {
      // Stop existing preview if any
      await this.stopPreview(workspaceId);

      const {
        projectPath,
        framework,
        port: preferredPort,
        command,
        environment = {},
        hotReload = true,
        buildMode = 'development',
      } = options;

      // Find available port
      const defaultPort = this.getDefaultPort(framework);
      const port = await this.findAvailablePort(
        workspaceId,
        preferredPort || defaultPort
      );

      // Prepare command
      const defaultCommand = this.getDefaultCommand(framework);
      let finalCommand = command || defaultCommand;

      // Modify command to use specific port and host
      if (framework === 'nextjs') {
        finalCommand = finalCommand.replace('npm run dev', `npm run dev -- --port ${port} --hostname 0.0.0.0`);
      } else if (framework === 'react' || framework === 'vue' || framework === 'svelte') {
        finalCommand = finalCommand.replace('npm run dev', `npm run dev -- --port ${port} --host 0.0.0.0`);
      } else if (framework === 'angular') {
        finalCommand = `ng serve --host 0.0.0.0 --port ${port}`;
      } else if (framework === 'gatsby') {
        finalCommand = finalCommand.replace('npm run develop', `npm run develop -- --port ${port} --host 0.0.0.0`);
      } else if (framework === 'nuxt') {
        finalCommand = finalCommand.replace('npm run dev', `npm run dev -- --port ${port} --host 0.0.0.0`);
      } else if (framework === 'remix') {
        finalCommand = finalCommand.replace('npm run dev', `npm run dev -- --port ${port}`);
      }

      // Prepare environment variables
      const env = {
        NODE_ENV: buildMode === 'production' ? 'production' : 'development',
        PORT: port.toString(),
        HOST: '0.0.0.0',
        ...environment,
      };

      // Create preview object
      const preview: NodeLivePreview = {
        framework,
        port,
        url: `http://localhost:${port}`,
        status: 'starting',
        logs: [],
        buildLogs: [],
        hotReload,
        buildMode,
      };

      this.previews.set(workspaceId, preview);

      // Start the development server
      try {
        // Set environment variables
        const envString = Object.entries(env)
          .map(([key, value]) => `export ${key}="${value}"`)
          .join(' && ');

        // Execute command
        const fullCommand = `cd ${projectPath} && ${envString} && ${finalCommand}`;
        
        // Start process (non-blocking)
        const processPromise = dockerService.executeCommand(workspaceId, fullCommand);
        
        // Store process reference
        this.processes.set(workspaceId, {
          command: finalCommand,
          startTime: new Date(),
          promise: processPromise,
        });

        // Update preview with process info
        preview.process = {
          pid: Date.now(), // Mock PID for now
          command: finalCommand,
          startTime: new Date(),
        };

        // Wait a bit and check if server started
        setTimeout(async () => {
          try {
            const isRunning = await this.checkServerStatus(workspaceId, port);
            if (isRunning) {
              preview.status = 'running';
              preview.logs.push(`✅ Server started successfully on port ${port}`);
            } else {
              preview.status = 'error';
              preview.logs.push(`❌ Server failed to start on port ${port}`);
            }
            this.previews.set(workspaceId, preview);
          } catch (error) {
            console.error('Error checking server status:', error);
          }
        }, 5000);

        // Handle process completion
        processPromise.then((result) => {
          if (result.exitCode !== 0) {
            preview.status = 'error';
            preview.logs.push(`❌ Process exited with code ${result.exitCode}`);
            if (result.stderr) {
              preview.logs.push(`Error: ${result.stderr}`);
            }
          } else {
            preview.status = 'stopped';
            preview.logs.push('✅ Process completed successfully');
          }
          this.previews.set(workspaceId, preview);
        }).catch((error) => {
          preview.status = 'error';
          preview.logs.push(`❌ Process error: ${error.message}`);
          this.previews.set(workspaceId, preview);
        });

        return preview;
      } catch (error) {
        preview.status = 'error';
        preview.logs.push(`❌ Failed to start: ${error instanceof Error ? error.message : 'Unknown error'}`);
        this.previews.set(workspaceId, preview);
        throw error;
      }
    } catch (error) {
      console.error('Error starting preview:', error);
      throw error;
    }
  }

  /**
   * Stop live preview
   */
  async stopPreview(workspaceId: string): Promise<{ success: boolean }> {
    try {
      const preview = this.previews.get(workspaceId);
      const process = this.processes.get(workspaceId);

      if (preview) {
        preview.status = 'stopped';
        preview.logs.push('🛑 Stopping preview server...');
        this.previews.set(workspaceId, preview);
      }

      if (process) {
        // Kill the process
        try {
          await dockerService.executeCommand(
            workspaceId,
            `pkill -f "${process.command}" || true`
          );
          
          // Also kill by port
          if (preview) {
            await dockerService.executeCommand(
              workspaceId,
              `lsof -ti:${preview.port} | xargs kill -9 || true`
            );
          }
        } catch (error) {
          console.warn('Error killing process:', error);
        }

        this.processes.delete(workspaceId);
      }

      if (preview) {
        preview.logs.push('✅ Preview server stopped');
        this.previews.set(workspaceId, preview);
      }

      return { success: true };
    } catch (error) {
      console.error('Error stopping preview:', error);
      return { success: false };
    }
  }

  /**
   * Get preview status
   */
  getPreview(workspaceId: string): NodeLivePreview | null {
    return this.previews.get(workspaceId) || null;
  }

  /**
   * Get all workspace previews
   */
  getWorkspacePreviews(workspaceId: string): NodeLivePreview[] {
    const preview = this.previews.get(workspaceId);
    return preview ? [preview] : [];
  }

  /**
   * Check if server is running on port
   */
  private async checkServerStatus(workspaceId: string, port: number): Promise<boolean> {
    try {
      const result = await dockerService.executeCommand(
        workspaceId,
        `curl -s -o /dev/null -w "%{http_code}" http://localhost:${port} || echo "000"`
      );
      
      const statusCode = result.stdout.trim();
      return statusCode !== '000' && statusCode !== ''; // Any HTTP response means server is running
    } catch (error) {
      return false;
    }
  }

  /**
   * Add log entry to preview
   */
  addLog(workspaceId: string, message: string, isBuildLog: boolean = false): void {
    const preview = this.previews.get(workspaceId);
    if (preview) {
      const timestamp = new Date().toISOString();
      const logEntry = `[${timestamp}] ${message}`;
      
      if (isBuildLog) {
        preview.buildLogs = preview.buildLogs || [];
        preview.buildLogs.push(logEntry);
      } else {
        preview.logs.push(logEntry);
      }
      
      // Keep only last 100 log entries
      if (preview.logs.length > 100) {
        preview.logs = preview.logs.slice(-100);
      }
      if (preview.buildLogs && preview.buildLogs.length > 100) {
        preview.buildLogs = preview.buildLogs.slice(-100);
      }
      
      this.previews.set(workspaceId, preview);
    }
  }

  /**
   * Update preview status
   */
  updateStatus(workspaceId: string, status: NodeLivePreview['status']): void {
    const preview = this.previews.get(workspaceId);
    if (preview) {
      preview.status = status;
      this.previews.set(workspaceId, preview);
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    const workspaceIds = Array.from(this.previews.keys());
    for (const workspaceId of workspaceIds) {
      await this.stopPreview(workspaceId);
    }
    this.previews.clear();
    this.processes.clear();
  }
}

// Export singleton instance
export const nodeReactLivePreviewService = new NodeReactLivePreviewService();
