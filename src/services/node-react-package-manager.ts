/**
 * Node/React Package Manager Service
 * Handles Node.js package installation, dependency management, and environment setup
 */

import { 
  NodePackageManager, 
  NodeEnvironment, 
  NodePackage,
  NODE_PACKAGE_MANAGERS
} from '@/types/node-react-workspace';
import { dockerService } from './docker';

export interface PackageInstallOptions {
  version?: string;
  isDev?: boolean;
  global?: boolean;
  exact?: boolean;
  save?: boolean;
  peer?: boolean;
}

export interface EnvironmentCreateOptions {
  nodeVersion?: string;
  packages?: string[];
  packageJson?: string;
  copyNodeModules?: boolean;
}

export interface PackageSearchResult {
  name: string;
  version: string;
  description: string;
  keywords: string[];
  author: string;
  homepage?: string;
  repository?: string;
  downloads: number;
  isInstalled?: boolean;
  installedVersion?: string;
}

export class NodeReactPackageManagerService {
  private packageManagers: Map<string, NodePackageManager>;

  constructor() {
    this.packageManagers = new Map();
    NODE_PACKAGE_MANAGERS.forEach(pm => {
      this.packageManagers.set(pm.name, pm);
    });
  }

  /**
   * Get package manager by name
   */
  getPackageManager(name: string): NodePackageManager | undefined {
    return this.packageManagers.get(name);
  }

  /**
   * Get all available package managers
   */
  getAvailablePackageManagers(): NodePackageManager[] {
    return Array.from(this.packageManagers.values());
  }

  /**
   * Install package in workspace
   */
  async installPackage(
    workspaceId: string,
    packageName: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm',
    options: PackageInstallOptions = {}
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command = pm.addCommand;
      
      // Build command with options
      if (options.isDev) {
        command += packageManager === 'npm' ? ' --save-dev' : ' -D';
      }
      
      if (options.global) {
        command += packageManager === 'npm' ? ' -g' : ' --global';
      }
      
      if (options.exact) {
        command += packageManager === 'npm' ? ' --save-exact' : ' --exact';
      }
      
      if (options.peer) {
        command += packageManager === 'npm' ? ' --save-peer' : ' --peer';
      }

      // Add package name and version
      const packageSpec = options.version ? `${packageName}@${options.version}` : packageName;
      command += ` ${packageSpec}`;

      // Execute command in workspace
      const result = await dockerService.executeCommand(workspaceId, command);
      
      return {
        success: result.exitCode === 0,
        output: result.stdout,
        error: result.stderr,
      };
    } catch (error) {
      console.error('Error installing package:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Uninstall package from workspace
   */
  async uninstallPackage(
    workspaceId: string,
    packageName: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm'
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      const command = `${pm.removeCommand} ${packageName}`;

      // Execute command in workspace
      const result = await dockerService.executeCommand(workspaceId, command);
      
      return {
        success: result.exitCode === 0,
        output: result.stdout,
        error: result.stderr,
      };
    } catch (error) {
      console.error('Error uninstalling package:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Update package in workspace
   */
  async updatePackage(
    workspaceId: string,
    packageName: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm',
    version?: string
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      let command: string;
      
      if (packageManager === 'npm') {
        command = version ? `npm install ${packageName}@${version}` : `npm update ${packageName}`;
      } else if (packageManager === 'yarn') {
        command = version ? `yarn add ${packageName}@${version}` : `yarn upgrade ${packageName}`;
      } else { // pnpm
        command = version ? `pnpm add ${packageName}@${version}` : `pnpm update ${packageName}`;
      }

      // Execute command in workspace
      const result = await dockerService.executeCommand(workspaceId, command);
      
      return {
        success: result.exitCode === 0,
        output: result.stdout,
        error: result.stderr,
      };
    } catch (error) {
      console.error('Error updating package:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get installed packages from package.json
   */
  async getInstalledPackages(workspaceId: string, projectPath?: string): Promise<NodePackage[]> {
    try {
      const packageJsonPath = projectPath ? `${projectPath}/package.json` : 'package.json';
      
      // Read package.json
      const result = await dockerService.executeCommand(workspaceId, `cat ${packageJsonPath}`);
      
      if (result.exitCode !== 0) {
        throw new Error('package.json not found');
      }

      const packageJson = JSON.parse(result.stdout);
      const packages: NodePackage[] = [];

      // Process dependencies
      if (packageJson.dependencies) {
        Object.entries(packageJson.dependencies).forEach(([name, version]) => {
          packages.push({
            name,
            version: version as string,
            category: this.categorizePackage(name),
            required: true,
            isDev: false,
          });
        });
      }

      // Process devDependencies
      if (packageJson.devDependencies) {
        Object.entries(packageJson.devDependencies).forEach(([name, version]) => {
          packages.push({
            name,
            version: version as string,
            category: this.categorizePackage(name),
            required: false,
            isDev: true,
          });
        });
      }

      return packages;
    } catch (error) {
      console.error('Error getting installed packages:', error);
      return [];
    }
  }

  /**
   * Search for packages on npm registry
   */
  async searchPackages(query: string, limit: number = 20): Promise<PackageSearchResult[]> {
    try {
      // Use npm search API
      const response = await fetch(`https://registry.npmjs.org/-/v1/search?text=${encodeURIComponent(query)}&size=${limit}`);
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      return data.objects.map((obj: any) => ({
        name: obj.package.name,
        version: obj.package.version,
        description: obj.package.description || '',
        keywords: obj.package.keywords || [],
        author: obj.package.author?.name || obj.package.publisher?.username || '',
        homepage: obj.package.links?.homepage,
        repository: obj.package.links?.repository,
        downloads: obj.score?.detail?.popularity || 0,
      }));
    } catch (error) {
      console.error('Error searching packages:', error);
      return [];
    }
  }

  /**
   * Get package information
   */
  async getPackageInfo(packageName: string): Promise<any> {
    try {
      const response = await fetch(`https://registry.npmjs.org/${packageName}`);
      
      if (!response.ok) {
        throw new Error(`Package not found: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting package info:', error);
      return null;
    }
  }

  /**
   * Run npm script
   */
  async runScript(
    workspaceId: string,
    scriptName: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm',
    projectPath?: string
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      const pm = this.getPackageManager(packageManager);
      if (!pm) {
        throw new Error(`Package manager '${packageManager}' not found`);
      }

      const command = `${pm.runCommand} ${scriptName}`;
      const workingDir = projectPath || '.';

      // Execute command in workspace
      const result = await dockerService.executeCommand(workspaceId, command, workingDir);
      
      return {
        success: result.exitCode === 0,
        output: result.stdout,
        error: result.stderr,
      };
    } catch (error) {
      console.error('Error running script:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Initialize new Node.js project
   */
  async initProject(
    workspaceId: string,
    projectName: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm',
    options: { typescript?: boolean; private?: boolean } = {}
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      let command: string;
      
      if (packageManager === 'npm') {
        command = 'npm init -y';
      } else if (packageManager === 'yarn') {
        command = 'yarn init -y';
      } else { // pnpm
        command = 'pnpm init';
      }

      // Create project directory and initialize
      await dockerService.executeCommand(workspaceId, `mkdir -p ${projectName}`);
      const result = await dockerService.executeCommand(workspaceId, command, projectName);
      
      if (result.exitCode === 0 && options.typescript) {
        // Add TypeScript configuration
        await this.installPackage(workspaceId, 'typescript', packageManager, { isDev: true });
        await this.installPackage(workspaceId, '@types/node', packageManager, { isDev: true });
        
        // Create tsconfig.json
        const tsConfig = {
          compilerOptions: {
            target: 'ES2020',
            module: 'commonjs',
            lib: ['ES2020'],
            outDir: './dist',
            rootDir: './src',
            strict: true,
            esModuleInterop: true,
            skipLibCheck: true,
            forceConsistentCasingInFileNames: true,
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist'],
        };
        
        await dockerService.executeCommand(
          workspaceId, 
          `echo '${JSON.stringify(tsConfig, null, 2)}' > tsconfig.json`,
          projectName
        );
      }
      
      return {
        success: result.exitCode === 0,
        output: result.stdout,
        error: result.stderr,
      };
    } catch (error) {
      console.error('Error initializing project:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Categorize package based on name
   */
  private categorizePackage(packageName: string): NodePackage['category'] {
    const name = packageName.toLowerCase();
    
    if (name.includes('react') || name.includes('vue') || name.includes('angular') || name.includes('svelte')) {
      return 'framework';
    }
    
    if (name.includes('test') || name.includes('jest') || name.includes('mocha') || name.includes('cypress')) {
      return 'testing';
    }
    
    if (name.includes('eslint') || name.includes('prettier') || name.includes('babel') || name.includes('webpack')) {
      return 'development';
    }
    
    if (name.includes('db') || name.includes('mongo') || name.includes('sql') || name.includes('prisma')) {
      return 'database';
    }
    
    if (name.includes('ui') || name.includes('component') || name.includes('material') || name.includes('chakra')) {
      return 'ui';
    }
    
    if (name.includes('redux') || name.includes('zustand') || name.includes('recoil') || name.includes('mobx')) {
      return 'state';
    }
    
    if (name.includes('vite') || name.includes('rollup') || name.includes('esbuild') || name.includes('turbo')) {
      return 'build';
    }
    
    return 'utility';
  }
}

// Export singleton instance
export const nodeReactPackageManagerService = new NodeReactPackageManagerService();
