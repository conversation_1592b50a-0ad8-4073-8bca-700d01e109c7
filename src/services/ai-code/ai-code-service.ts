import { BaseAICodeService, AIServiceResult, AIRequestContext } from './base';
import {
  AICompletionRequest,
  AICompletionSuggestion,
  AIAnalysisRequest,
  CodeError,
  RefactoringSuggestion,
  CodeMetrics,
  AIAssistantRequest,
  AIAssistantMessage,
} from '@/types/ai-code-editor';
import { generateObject, generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Schemas for structured AI responses
const CompletionSuggestionSchema = z.object({
  text: z.string(),
  insertText: z.string(),
  confidence: z.number().min(0).max(1),
  type: z.enum(['completion', 'snippet', 'import', 'function', 'variable']),
  description: z.string().optional(),
  documentation: z.string().optional(),
  priority: z.number().min(0).max(10),
});

const CodeErrorSchema = z.object({
  message: z.string(),
  severity: z.enum(['error', 'warning', 'info', 'hint']),
  line: z.number(),
  column: z.number(),
  endLine: z.number().optional(),
  endColumn: z.number().optional(),
  code: z.string().optional(),
  source: z.string(),
  fixes: z.array(z.object({
    title: z.string(),
    description: z.string(),
    newText: z.string(),
    confidence: z.number().min(0).max(1),
  })).optional(),
});

const RefactoringSuggestionSchema = z.object({
  title: z.string(),
  description: z.string(),
  type: z.enum(['extract-method', 'rename', 'inline', 'move', 'optimize', 'style']),
  line: z.number(),
  column: z.number(),
  endLine: z.number(),
  endColumn: z.number(),
  preview: z.string(),
  newText: z.string(),
  confidence: z.number().min(0).max(1),
  impact: z.enum(['low', 'medium', 'high']),
});

const CodeMetricsSchema = z.object({
  complexity: z.number().min(0).max(10),
  maintainability: z.number().min(0).max(10),
  readability: z.number().min(0).max(10),
  performance: z.number().min(0).max(10),
  security: z.number().min(0).max(10),
  testCoverage: z.number().min(0).max(100).optional(),
});

export class AICodeService extends BaseAICodeService {
  constructor() {
    super('AICodeService', {
      model: 'gpt-4',
      temperature: 0.1,
      maxTokens: 2048,
    });
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await generateText({
        model: openai('gpt-3.5-turbo'),
        prompt: 'Hello, respond with "OK" if you are working.',
        maxTokens: 10,
      });
      return result.text.trim().toLowerCase().includes('ok');
    } catch (error) {
      return false;
    }
  }

  // Generate code completions
  async generateCompletions(
    request: AICompletionRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<AICompletionSuggestion[]>> {
    return this.executeAIOperation(
      'generateCompletions',
      async () => {
        const prompt = this.buildCompletionPrompt(request);
        
        const result = await generateObject({
          model: openai(this.config.model),
          schema: z.object({
            suggestions: z.array(CompletionSuggestionSchema),
          }),
          prompt,
          temperature: this.config.temperature,
          maxTokens: this.config.maxTokens,
        });

        return result.object.suggestions.map((suggestion, index) => ({
          id: `completion-${Date.now()}-${index}`,
          ...suggestion,
          range: {
            start: request.position,
            end: request.position,
          },
        }));
      },
      context
    );
  }

  // Analyze code for errors and issues
  async analyzeCode(
    request: AIAnalysisRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<{
    errors: CodeError[];
    suggestions: RefactoringSuggestion[];
    metrics: CodeMetrics;
  }>> {
    return this.executeAIOperation(
      'analyzeCode',
      async () => {
        const prompt = this.buildAnalysisPrompt(request);
        
        const result = await generateObject({
          model: openai(this.config.model),
          schema: z.object({
            errors: z.array(CodeErrorSchema),
            suggestions: z.array(RefactoringSuggestionSchema),
            metrics: CodeMetricsSchema,
          }),
          prompt,
          temperature: 0.1,
          maxTokens: this.config.maxTokens,
        });

        // Transform the results to match our types
        const errors: CodeError[] = result.object.errors.map((error, index) => ({
          id: `error-${Date.now()}-${index}`,
          message: error.message,
          severity: error.severity,
          range: {
            start: { line: error.line, column: error.column },
            end: { 
              line: error.endLine || error.line, 
              column: error.endColumn || error.column + 1 
            },
          },
          code: error.code,
          source: error.source,
          fixes: error.fixes?.map((fix, fixIndex) => ({
            id: `fix-${Date.now()}-${index}-${fixIndex}`,
            title: fix.title,
            description: fix.description,
            edits: [{
              range: {
                start: { line: error.line, column: error.column },
                end: { 
                  line: error.endLine || error.line, 
                  column: error.endColumn || error.column + 1 
                },
              },
              newText: fix.newText,
            }],
            confidence: fix.confidence,
          })),
        }));

        const suggestions: RefactoringSuggestion[] = result.object.suggestions.map((suggestion, index) => ({
          id: `refactor-${Date.now()}-${index}`,
          title: suggestion.title,
          description: suggestion.description,
          type: suggestion.type,
          range: {
            start: { line: suggestion.line, column: suggestion.column },
            end: { line: suggestion.endLine, column: suggestion.endColumn },
          },
          preview: suggestion.preview,
          edits: [{
            range: {
              start: { line: suggestion.line, column: suggestion.column },
              end: { line: suggestion.endLine, column: suggestion.endColumn },
            },
            newText: suggestion.newText,
          }],
          confidence: suggestion.confidence,
          impact: suggestion.impact,
        }));

        return {
          errors,
          suggestions,
          metrics: result.object.metrics,
        };
      },
      context
    );
  }

  // Generate AI assistant response
  async generateAssistantResponse(
    request: AIAssistantRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<AIAssistantMessage>> {
    return this.executeAIOperation(
      'generateAssistantResponse',
      async () => {
        const prompt = this.buildAssistantPrompt(request);
        
        const result = await generateText({
          model: openai(this.config.model),
          prompt,
          temperature: this.config.temperature,
          maxTokens: this.config.maxTokens,
        });

        return {
          id: `assistant-${Date.now()}`,
          type: 'assistant' as const,
          content: result.text,
          timestamp: new Date(),
          codeContext: request.codeContext,
        };
      },
      context
    );
  }

  // Build completion prompt
  private buildCompletionPrompt(request: AICompletionRequest): string {
    const { code, position, language, context } = request;
    
    const beforeCursor = code.split('\n').slice(0, position.line).join('\n') + 
                        code.split('\n')[position.line]?.substring(0, position.column) || '';
    const afterCursor = code.split('\n')[position.line]?.substring(position.column) || '' +
                       code.split('\n').slice(position.line + 1).join('\n');

    return `You are an expert ${language} developer. Provide intelligent code completions for the cursor position.

Context:
${context?.fileName ? `File: ${context.fileName}` : ''}
${context?.projectContext ? `Project: ${context.projectContext}` : ''}

Code before cursor:
\`\`\`${language}
${beforeCursor}
\`\`\`

Code after cursor:
\`\`\`${language}
${afterCursor}
\`\`\`

Provide up to 5 relevant completions with high confidence scores. Consider:
- Variable and function names in scope
- Import statements and available modules
- Code patterns and best practices
- Type information and interfaces

Return completions as structured data with text, insertText, confidence, type, and priority.`;
  }

  // Build analysis prompt
  private buildAnalysisPrompt(request: AIAnalysisRequest): string {
    const { code, language, fileName } = request;
    
    return `You are an expert code analyzer. Analyze the following ${language} code for errors, issues, and improvement opportunities.

${fileName ? `File: ${fileName}` : ''}

Code to analyze:
\`\`\`${language}
${code}
\`\`\`

Provide:
1. Errors and warnings with specific line/column positions
2. Refactoring suggestions with previews
3. Code quality metrics (0-10 scale)

Focus on:
- Syntax and semantic errors
- Code style and best practices
- Performance optimizations
- Security vulnerabilities
- Maintainability improvements

Return structured data with errors, suggestions, and metrics.`;
  }

  // Build assistant prompt
  private buildAssistantPrompt(request: AIAssistantRequest): string {
    const { message, codeContext, conversationHistory } = request;
    
    let prompt = `You are an expert programming assistant. Help the user with their coding questions and tasks.

User message: ${message}

`;

    if (codeContext) {
      prompt += `Code context:
File: ${codeContext.file}
${codeContext.selection ? `Selected code:
\`\`\`
${codeContext.selection.text}
\`\`\`
` : ''}
${codeContext.fullCode ? `Full file content:
\`\`\`
${codeContext.fullCode}
\`\`\`
` : ''}
`;
    }

    if (conversationHistory && conversationHistory.length > 0) {
      prompt += `\nConversation history:
${conversationHistory.slice(-5).map(msg => 
  `${msg.type}: ${msg.content}`
).join('\n')}
`;
    }

    prompt += `\nProvide helpful, accurate, and actionable advice. Include code examples when relevant.`;

    return prompt;
  }
}
