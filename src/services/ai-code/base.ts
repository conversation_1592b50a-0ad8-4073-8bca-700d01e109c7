import { BaseAppwriteService, ServiceResult } from '@/services/appwrite/base';
import { handleAIError, withRetry } from '@/lib/ai-error-handler';
import { logger } from '@/lib/appwrite-server';

// Base AI Code Service interface
export interface IAICodeService {
  healthCheck(): Promise<boolean>;
}

// AI Service configuration
export interface AIServiceConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  timeout: number;
  retryAttempts: number;
  enableCaching: boolean;
  rateLimitConfig?: {
    requestsPerMinute: number;
    requestsPerHour: number;
  };
}

// Default configuration
export const DEFAULT_AI_CONFIG: AIServiceConfig = {
  model: 'gpt-4',
  temperature: 0.1,
  maxTokens: 2048,
  timeout: 30000,
  retryAttempts: 3,
  enableCaching: true,
  rateLimitConfig: {
    requestsPerMinute: 60,
    requestsPerHour: 1000,
  },
};

// AI Request context
export interface AIRequestContext {
  userId?: string;
  sessionId?: string;
  workspaceId?: string;
  fileId?: string;
  language?: string;
  timestamp: Date;
}

// AI Response metadata
export interface AIResponseMetadata {
  model: string;
  tokensUsed: number;
  processingTime: number;
  cacheHit: boolean;
  confidence?: number;
}

// Enhanced service result for AI operations
export interface AIServiceResult<T> extends ServiceResult<T> {
  aiMetadata?: AIResponseMetadata;
}

// Base AI Code Service class
export abstract class BaseAICodeService extends BaseAppwriteService implements IAICodeService {
  protected config: AIServiceConfig;
  protected requestCache: Map<string, { data: any; timestamp: number }>;
  protected rateLimitTracker: Map<string, number[]>;

  constructor(serviceName: string, config: Partial<AIServiceConfig> = {}) {
    super(serviceName);
    this.config = { ...DEFAULT_AI_CONFIG, ...config };
    this.requestCache = new Map();
    this.rateLimitTracker = new Map();
  }

  // Execute AI operation with enhanced error handling and caching
  protected async executeAIOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    context: AIRequestContext,
    useCache: boolean = true
  ): Promise<AIServiceResult<T>> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(operationName, context);

    try {
      // Check rate limits
      if (!this.checkRateLimit(context.userId || 'anonymous')) {
        throw new Error('Rate limit exceeded');
      }

      // Check cache if enabled
      if (useCache && this.config.enableCaching) {
        const cached = this.getCachedResult<T>(cacheKey);
        if (cached) {
          logger.debug(`Cache hit for ${this.serviceName}.${operationName}`);
          return {
            success: true,
            data: cached,
            aiMetadata: {
              model: this.config.model,
              tokensUsed: 0,
              processingTime: Date.now() - startTime,
              cacheHit: true,
            },
            metadata: {
              timestamp: new Date().toISOString(),
              operation: `${this.serviceName}.${operationName}`,
              duration: Date.now() - startTime,
            },
          };
        }
      }

      logger.debug(`Starting AI operation ${this.serviceName}.${operationName}`, {
        context,
        config: this.config,
      });

      // Execute operation with retry logic
      const result = await withRetry(operation, this.config.retryAttempts);
      const duration = Date.now() - startTime;

      // Cache result if enabled
      if (useCache && this.config.enableCaching) {
        this.setCachedResult(cacheKey, result);
      }

      // Update rate limit tracker
      this.updateRateLimit(context.userId || 'anonymous');

      logger.info(`AI operation ${this.serviceName}.${operationName} completed`, {
        duration: `${duration}ms`,
        cacheHit: false,
      });

      return {
        success: true,
        data: result,
        aiMetadata: {
          model: this.config.model,
          tokensUsed: this.estimateTokens(JSON.stringify(result)),
          processingTime: duration,
          cacheHit: false,
        },
        metadata: {
          timestamp: new Date().toISOString(),
          operation: `${this.serviceName}.${operationName}`,
          duration,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const aiError = handleAIError(error);

      logger.error(`AI operation ${this.serviceName}.${operationName} failed`, {
        error: aiError.error,
        code: aiError.code,
        duration: `${duration}ms`,
        context,
      });

      return {
        success: false,
        error: {
          message: aiError.error,
          code: aiError.code,
          details: aiError.details,
        },
        metadata: {
          timestamp: new Date().toISOString(),
          operation: `${this.serviceName}.${operationName}`,
          duration,
        },
      };
    }
  }

  // Rate limiting implementation
  protected checkRateLimit(identifier: string): boolean {
    const now = Date.now();
    const requests = this.rateLimitTracker.get(identifier) || [];
    
    // Clean old requests (older than 1 hour)
    const recentRequests = requests.filter(timestamp => now - timestamp < 3600000);
    
    // Check minute limit
    const minuteRequests = recentRequests.filter(timestamp => now - timestamp < 60000);
    if (minuteRequests.length >= (this.config.rateLimitConfig?.requestsPerMinute || 60)) {
      return false;
    }
    
    // Check hour limit
    if (recentRequests.length >= (this.config.rateLimitConfig?.requestsPerHour || 1000)) {
      return false;
    }
    
    return true;
  }

  protected updateRateLimit(identifier: string): void {
    const now = Date.now();
    const requests = this.rateLimitTracker.get(identifier) || [];
    requests.push(now);
    this.rateLimitTracker.set(identifier, requests);
  }

  // Cache management
  protected getCachedResult<T>(key: string): T | null {
    const cached = this.requestCache.get(key);
    if (!cached) return null;
    
    // Check if cache is expired (1 hour)
    if (Date.now() - cached.timestamp > 3600000) {
      this.requestCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  protected setCachedResult<T>(key: string, data: T): void {
    this.requestCache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  protected generateCacheKey(operation: string, context: AIRequestContext): string {
    const keyParts = [
      operation,
      context.userId || 'anonymous',
      context.workspaceId || '',
      context.fileId || '',
      context.language || '',
    ];
    return keyParts.join(':');
  }

  // Token estimation (rough approximation)
  protected estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  // Abstract health check method
  abstract healthCheck(): Promise<boolean>;

  // Get service configuration
  public getConfig(): AIServiceConfig {
    return { ...this.config };
  }

  // Update service configuration
  public updateConfig(updates: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...updates };
    logger.info(`Updated configuration for ${this.serviceName}`, { updates });
  }

  // Clear cache
  public clearCache(): void {
    this.requestCache.clear();
    logger.info(`Cleared cache for ${this.serviceName}`);
  }

  // Get cache statistics
  public getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.requestCache.size,
      hitRate: 0, // TODO: Implement hit rate tracking
    };
  }

  // Get rate limit status
  public getRateLimitStatus(identifier: string): {
    minuteRequests: number;
    hourRequests: number;
    minuteLimit: number;
    hourLimit: number;
  } {
    const now = Date.now();
    const requests = this.rateLimitTracker.get(identifier) || [];
    const recentRequests = requests.filter(timestamp => now - timestamp < 3600000);
    const minuteRequests = recentRequests.filter(timestamp => now - timestamp < 60000);

    return {
      minuteRequests: minuteRequests.length,
      hourRequests: recentRequests.length,
      minuteLimit: this.config.rateLimitConfig?.requestsPerMinute || 60,
      hourLimit: this.config.rateLimitConfig?.requestsPerHour || 1000,
    };
  }
}
