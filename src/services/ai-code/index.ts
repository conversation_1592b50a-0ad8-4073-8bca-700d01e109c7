// Export all AI Code services
export { BaseAICodeService } from './base';
export { AICodeService } from './ai-code-service';
export { CodeAnalysisService } from './code-analysis-service';
export { AIModelService } from './ai-model-service';
export { CodeFormattingService } from './code-formatting-service';

// Export types
export type {
  IAICodeService,
  AIServiceConfig,
  AIRequestContext,
  AIResponseMetadata,
  AIServiceResult,
} from './base';

export type {
  AIModel,
  ModelConfig,
  ModelCapabilities,
  ModelMetrics,
} from './ai-model-service';

export type {
  FormattingOptions,
} from './code-formatting-service';

// Service factory
export class AICodeServiceFactory {
  private static aiCodeService: AICodeService;
  private static codeAnalysisService: CodeAnalysisService;
  private static aiModelService: AIModelService;
  private static codeFormattingService: CodeFormattingService;

  static getAICodeService(): AICodeService {
    if (!this.aiCodeService) {
      this.aiCodeService = new AICodeService();
    }
    return this.aiCodeService;
  }

  static getCodeAnalysisService(): CodeAnalysisService {
    if (!this.codeAnalysisService) {
      this.codeAnalysisService = new CodeAnalysisService();
    }
    return this.codeAnalysisService;
  }

  static getAIModelService(): AIModelService {
    if (!this.aiModelService) {
      this.aiModelService = new AIModelService();
    }
    return this.aiModelService;
  }

  static getCodeFormattingService(): CodeFormattingService {
    if (!this.codeFormattingService) {
      this.codeFormattingService = new CodeFormattingService();
    }
    return this.codeFormattingService;
  }

  // Health check for all services
  static async healthCheck(): Promise<{
    aiCode: boolean;
    codeAnalysis: boolean;
    aiModel: boolean;
    codeFormatting: boolean;
    overall: boolean;
  }> {
    const [aiCode, codeAnalysis, aiModel, codeFormatting] = await Promise.all([
      this.getAICodeService().healthCheck(),
      this.getCodeAnalysisService().healthCheck(),
      this.getAIModelService().healthCheck(),
      this.getCodeFormattingService().healthCheck(),
    ]);

    return {
      aiCode,
      codeAnalysis,
      aiModel,
      codeFormatting,
      overall: aiCode && codeAnalysis && aiModel && codeFormatting,
    };
  }

  // Reset all service instances (useful for testing)
  static reset(): void {
    this.aiCodeService = null as any;
    this.codeAnalysisService = null as any;
    this.aiModelService = null as any;
    this.codeFormattingService = null as any;
  }
}
