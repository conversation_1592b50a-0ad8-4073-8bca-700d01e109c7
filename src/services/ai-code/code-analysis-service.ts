import { BaseAICodeService, AIServiceResult, AIRequestContext } from './base';
import {
  CodeFile,
  CodeError,
  CodeFix,
  CodeMetrics,
  RefactoringSuggestion,
  CodePosition,
  CodeRange,
} from '@/types/ai-code-editor';

// Language-specific analysis patterns
interface LanguageAnalyzer {
  language: string;
  patterns: {
    errors: RegExp[];
    warnings: RegExp[];
    suggestions: RegExp[];
  };
  metrics: {
    complexityKeywords: string[];
    maintainabilityIndicators: string[];
    performanceAntiPatterns: string[];
    securityVulnerabilities: string[];
  };
}

// Built-in language analyzers
const LANGUAGE_ANALYZERS: LanguageAnalyzer[] = [
  {
    language: 'typescript',
    patterns: {
      errors: [
        /\bany\b/g, // Using 'any' type
        /console\.log/g, // Console statements
        /debugger/g, // Debugger statements
      ],
      warnings: [
        /var\s+/g, // Using 'var' instead of 'let'/'const'
        /==\s*[^=]/g, // Using '==' instead of '==='
        /!=\s*[^=]/g, // Using '!=' instead of '!=='
      ],
      suggestions: [
        /function\s+\w+\s*\([^)]*\)\s*{[^}]*}/g, // Functions that could be arrow functions
        /if\s*\([^)]+\)\s*{\s*return\s+[^}]+;\s*}/g, // If statements that could be ternary
      ],
    },
    metrics: {
      complexityKeywords: ['if', 'else', 'for', 'while', 'switch', 'case', 'try', 'catch'],
      maintainabilityIndicators: ['class', 'interface', 'type', 'enum'],
      performanceAntiPatterns: ['nested loops', 'recursive calls', 'large objects'],
      securityVulnerabilities: ['eval', 'innerHTML', 'document.write'],
    },
  },
  {
    language: 'javascript',
    patterns: {
      errors: [
        /console\.log/g,
        /debugger/g,
        /alert\(/g,
      ],
      warnings: [
        /var\s+/g,
        /==\s*[^=]/g,
        /!=\s*[^=]/g,
        /\bwith\b/g,
      ],
      suggestions: [
        /function\s+\w+\s*\([^)]*\)\s*{[^}]*}/g,
        /if\s*\([^)]+\)\s*{\s*return\s+[^}]+;\s*}/g,
      ],
    },
    metrics: {
      complexityKeywords: ['if', 'else', 'for', 'while', 'switch', 'case', 'try', 'catch'],
      maintainabilityIndicators: ['function', 'class', 'const', 'let'],
      performanceAntiPatterns: ['nested loops', 'global variables'],
      securityVulnerabilities: ['eval', 'innerHTML', 'document.write'],
    },
  },
];

export class CodeAnalysisService extends BaseAICodeService {
  private languageAnalyzers: Map<string, LanguageAnalyzer>;

  constructor() {
    super('CodeAnalysisService');
    this.languageAnalyzers = new Map();
    
    // Initialize built-in analyzers
    LANGUAGE_ANALYZERS.forEach(analyzer => {
      this.languageAnalyzers.set(analyzer.language, analyzer);
    });
  }

  async healthCheck(): Promise<boolean> {
    return true; // Static analysis doesn't require external services
  }

  // Analyze code structure and patterns
  async analyzeCodeStructure(
    code: string,
    language: string,
    context: AIRequestContext
  ): Promise<AIServiceResult<{
    errors: CodeError[];
    suggestions: RefactoringSuggestion[];
    metrics: CodeMetrics;
  }>> {
    return this.executeAIOperation(
      'analyzeCodeStructure',
      async () => {
        const analyzer = this.languageAnalyzers.get(language.toLowerCase());
        if (!analyzer) {
          throw new Error(`Unsupported language: ${language}`);
        }

        const lines = code.split('\n');
        const errors: CodeError[] = [];
        const suggestions: RefactoringSuggestion[] = [];

        // Analyze errors
        analyzer.patterns.errors.forEach((pattern, index) => {
          lines.forEach((line, lineIndex) => {
            const matches = Array.from(line.matchAll(pattern));
            matches.forEach(match => {
              if (match.index !== undefined) {
                errors.push({
                  id: `error-${Date.now()}-${index}-${lineIndex}`,
                  message: this.getErrorMessage(pattern, match[0]),
                  severity: 'error',
                  range: {
                    start: { line: lineIndex + 1, column: match.index + 1 },
                    end: { line: lineIndex + 1, column: match.index + match[0].length + 1 },
                  },
                  source: 'static-analysis',
                  fixes: this.generateFixes(pattern, match[0], {
                    start: { line: lineIndex + 1, column: match.index + 1 },
                    end: { line: lineIndex + 1, column: match.index + match[0].length + 1 },
                  }),
                });
              }
            });
          });
        });

        // Analyze warnings
        analyzer.patterns.warnings.forEach((pattern, index) => {
          lines.forEach((line, lineIndex) => {
            const matches = Array.from(line.matchAll(pattern));
            matches.forEach(match => {
              if (match.index !== undefined) {
                errors.push({
                  id: `warning-${Date.now()}-${index}-${lineIndex}`,
                  message: this.getWarningMessage(pattern, match[0]),
                  severity: 'warning',
                  range: {
                    start: { line: lineIndex + 1, column: match.index + 1 },
                    end: { line: lineIndex + 1, column: match.index + match[0].length + 1 },
                  },
                  source: 'static-analysis',
                  fixes: this.generateFixes(pattern, match[0], {
                    start: { line: lineIndex + 1, column: match.index + 1 },
                    end: { line: lineIndex + 1, column: match.index + match[0].length + 1 },
                  }),
                });
              }
            });
          });
        });

        // Generate refactoring suggestions
        analyzer.patterns.suggestions.forEach((pattern, index) => {
          lines.forEach((line, lineIndex) => {
            const matches = Array.from(line.matchAll(pattern));
            matches.forEach(match => {
              if (match.index !== undefined) {
                suggestions.push({
                  id: `suggestion-${Date.now()}-${index}-${lineIndex}`,
                  title: this.getSuggestionTitle(pattern),
                  description: this.getSuggestionDescription(pattern, match[0]),
                  type: this.getSuggestionType(pattern),
                  range: {
                    start: { line: lineIndex + 1, column: match.index + 1 },
                    end: { line: lineIndex + 1, column: match.index + match[0].length + 1 },
                  },
                  preview: this.generatePreview(match[0], pattern),
                  edits: [{
                    range: {
                      start: { line: lineIndex + 1, column: match.index + 1 },
                      end: { line: lineIndex + 1, column: match.index + match[0].length + 1 },
                    },
                    newText: this.generateRefactoredCode(match[0], pattern),
                  }],
                  confidence: 0.8,
                  impact: 'low',
                });
              }
            });
          });
        });

        // Calculate metrics
        const metrics = this.calculateMetrics(code, analyzer);

        return { errors, suggestions, metrics };
      },
      context,
      false // Don't cache static analysis results
    );
  }

  // Parse code to extract symbols and structure
  async parseCodeSymbols(
    code: string,
    language: string,
    context: AIRequestContext
  ): Promise<AIServiceResult<{
    functions: Array<{ name: string; range: CodeRange; parameters: string[] }>;
    classes: Array<{ name: string; range: CodeRange; methods: string[] }>;
    variables: Array<{ name: string; range: CodeRange; type?: string }>;
    imports: Array<{ module: string; range: CodeRange; items: string[] }>;
  }>> {
    return this.executeAIOperation(
      'parseCodeSymbols',
      async () => {
        const lines = code.split('\n');
        const functions: Array<{ name: string; range: CodeRange; parameters: string[] }> = [];
        const classes: Array<{ name: string; range: CodeRange; methods: string[] }> = [];
        const variables: Array<{ name: string; range: CodeRange; type?: string }> = [];
        const imports: Array<{ module: string; range: CodeRange; items: string[] }> = [];

        // Simple regex-based parsing (in production, use proper AST parsing)
        lines.forEach((line, index) => {
          // Function declarations
          const functionMatch = line.match(/(?:function|const|let|var)\s+(\w+)\s*(?:=\s*)?(?:\([^)]*\)|=>)/);
          if (functionMatch) {
            const paramMatch = line.match(/\(([^)]*)\)/);
            const parameters = paramMatch ? paramMatch[1].split(',').map(p => p.trim()).filter(Boolean) : [];
            
            functions.push({
              name: functionMatch[1],
              range: {
                start: { line: index + 1, column: 1 },
                end: { line: index + 1, column: line.length + 1 },
              },
              parameters,
            });
          }

          // Class declarations
          const classMatch = line.match(/class\s+(\w+)/);
          if (classMatch) {
            classes.push({
              name: classMatch[1],
              range: {
                start: { line: index + 1, column: 1 },
                end: { line: index + 1, column: line.length + 1 },
              },
              methods: [], // TODO: Parse methods
            });
          }

          // Variable declarations
          const variableMatch = line.match(/(?:const|let|var)\s+(\w+)(?:\s*:\s*(\w+))?/);
          if (variableMatch) {
            variables.push({
              name: variableMatch[1],
              range: {
                start: { line: index + 1, column: 1 },
                end: { line: index + 1, column: line.length + 1 },
              },
              type: variableMatch[2],
            });
          }

          // Import statements
          const importMatch = line.match(/import\s+(?:{([^}]+)}|\*\s+as\s+\w+|(\w+))\s+from\s+['"]([^'"]+)['"]/);
          if (importMatch) {
            const items = importMatch[1] ? importMatch[1].split(',').map(i => i.trim()) : [importMatch[2] || '*'];
            imports.push({
              module: importMatch[3],
              range: {
                start: { line: index + 1, column: 1 },
                end: { line: index + 1, column: line.length + 1 },
              },
              items,
            });
          }
        });

        return { functions, classes, variables, imports };
      },
      context
    );
  }

  // Helper methods
  private getErrorMessage(pattern: RegExp, match: string): string {
    const errorMessages: Record<string, string> = {
      'any': 'Avoid using "any" type. Use specific types instead.',
      'console.log': 'Remove console.log statements before production.',
      'debugger': 'Remove debugger statements before production.',
    };

    for (const [key, message] of Object.entries(errorMessages)) {
      if (match.includes(key)) return message;
    }

    return `Code issue detected: ${match}`;
  }

  private getWarningMessage(pattern: RegExp, match: string): string {
    const warningMessages: Record<string, string> = {
      'var ': 'Use "let" or "const" instead of "var".',
      '==': 'Use strict equality (===) instead of loose equality (==).',
      '!=': 'Use strict inequality (!==) instead of loose inequality (!=).',
    };

    for (const [key, message] of Object.entries(warningMessages)) {
      if (match.includes(key)) return message;
    }

    return `Code warning: ${match}`;
  }

  private generateFixes(pattern: RegExp, match: string, range: CodeRange): CodeFix[] {
    const fixes: CodeFix[] = [];

    if (match.includes('var ')) {
      fixes.push({
        id: `fix-${Date.now()}`,
        title: 'Replace with "const"',
        description: 'Use "const" for immutable variables',
        edits: [{ range, newText: match.replace('var ', 'const ') }],
        confidence: 0.9,
      });
    }

    if (match.includes('==') && !match.includes('===')) {
      fixes.push({
        id: `fix-${Date.now()}`,
        title: 'Use strict equality',
        description: 'Replace == with ===',
        edits: [{ range, newText: match.replace('==', '===') }],
        confidence: 0.95,
      });
    }

    return fixes;
  }

  private getSuggestionTitle(pattern: RegExp): string {
    if (pattern.source.includes('function')) return 'Convert to arrow function';
    if (pattern.source.includes('if')) return 'Simplify conditional';
    return 'Code improvement suggestion';
  }

  private getSuggestionDescription(pattern: RegExp, match: string): string {
    if (pattern.source.includes('function')) return 'This function can be converted to an arrow function for better readability.';
    if (pattern.source.includes('if')) return 'This conditional can be simplified using a ternary operator.';
    return 'This code can be improved for better readability and maintainability.';
  }

  private getSuggestionType(pattern: RegExp): RefactoringSuggestion['type'] {
    if (pattern.source.includes('function')) return 'style';
    if (pattern.source.includes('if')) return 'optimize';
    return 'style';
  }

  private generatePreview(match: string, pattern: RegExp): string {
    // Generate a preview of the refactored code
    return this.generateRefactoredCode(match, pattern);
  }

  private generateRefactoredCode(match: string, pattern: RegExp): string {
    // Simple refactoring examples
    if (pattern.source.includes('function') && match.includes('function')) {
      return match.replace(/function\s+(\w+)\s*\(([^)]*)\)\s*{/, 'const $1 = ($2) => {');
    }
    return match;
  }

  private calculateMetrics(code: string, analyzer: LanguageAnalyzer): CodeMetrics {
    const lines = code.split('\n');
    const totalLines = lines.length;
    const codeLines = lines.filter(line => line.trim() && !line.trim().startsWith('//')).length;

    // Calculate complexity based on keywords
    let complexity = 0;
    analyzer.metrics.complexityKeywords.forEach(keyword => {
      const matches = code.match(new RegExp(`\\b${keyword}\\b`, 'g'));
      complexity += matches ? matches.length : 0;
    });

    // Normalize complexity (0-10 scale)
    const normalizedComplexity = Math.min(10, complexity / Math.max(1, codeLines / 10));

    // Calculate other metrics (simplified)
    const maintainability = Math.max(0, 10 - normalizedComplexity);
    const readability = codeLines > 0 ? Math.min(10, (code.match(/\n\s*\n/g)?.length || 0) / codeLines * 10) : 5;
    const performance = 8; // Default good performance score
    const security = 9; // Default good security score

    return {
      complexity: Math.round(normalizedComplexity * 10) / 10,
      maintainability: Math.round(maintainability * 10) / 10,
      readability: Math.round(readability * 10) / 10,
      performance: Math.round(performance * 10) / 10,
      security: Math.round(security * 10) / 10,
    };
  }

  // Add custom language analyzer
  addLanguageAnalyzer(analyzer: LanguageAnalyzer): void {
    this.languageAnalyzers.set(analyzer.language, analyzer);
  }

  // Get supported languages
  getSupportedLanguages(): string[] {
    return Array.from(this.languageAnalyzers.keys());
  }
}
