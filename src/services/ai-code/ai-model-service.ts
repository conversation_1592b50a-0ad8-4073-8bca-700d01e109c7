import { BaseAICodeService, AIServiceResult, AIRequestContext } from './base';
import { generateText, generateObject, streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { z } from 'zod';

// Supported AI models
export type AIModel = 'gpt-4' | 'gpt-3.5-turbo' | 'claude-3-sonnet' | 'claude-3-haiku';

// Model configuration
export interface ModelConfig {
  model: AIModel;
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

// Model capabilities
export interface ModelCapabilities {
  textGeneration: boolean;
  codeGeneration: boolean;
  structuredOutput: boolean;
  streaming: boolean;
  functionCalling: boolean;
  maxContextLength: number;
  costPerToken: number;
}

// Model performance metrics
export interface ModelMetrics {
  averageResponseTime: number;
  successRate: number;
  errorRate: number;
  totalRequests: number;
  totalTokensUsed: number;
  totalCost: number;
}

export class AIModelService extends BaseAICodeService {
  private modelConfigs: Map<AIModel, ModelConfig>;
  private modelCapabilities: Map<AIModel, ModelCapabilities>;
  private modelMetrics: Map<AIModel, ModelMetrics>;

  constructor() {
    super('AIModelService');
    this.initializeModels();
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Test with the fastest model
      const result = await generateText({
        model: openai('gpt-3.5-turbo'),
        prompt: 'Hello',
        maxTokens: 5,
      });
      return result.text.length > 0;
    } catch (error) {
      return false;
    }
  }

  // Generate text with specified model
  async generateText(
    prompt: string,
    modelConfig: Partial<ModelConfig>,
    context: AIRequestContext
  ): Promise<AIServiceResult<string>> {
    return this.executeAIOperation(
      'generateText',
      async () => {
        const config = this.getModelConfig(modelConfig.model || 'gpt-4', modelConfig);
        const model = this.getModelInstance(config.model);

        const startTime = Date.now();
        const result = await generateText({
          model,
          prompt,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
          frequencyPenalty: config.frequencyPenalty,
          presencePenalty: config.presencePenalty,
        });

        // Update metrics
        this.updateModelMetrics(config.model, Date.now() - startTime, true, result.usage?.totalTokens || 0);

        return result.text;
      },
      context
    );
  }

  // Generate structured object with specified model
  async generateObject<T>(
    prompt: string,
    schema: z.ZodSchema<T>,
    modelConfig: Partial<ModelConfig>,
    context: AIRequestContext
  ): Promise<AIServiceResult<T>> {
    return this.executeAIOperation(
      'generateObject',
      async () => {
        const config = this.getModelConfig(modelConfig.model || 'gpt-4', modelConfig);
        const model = this.getModelInstance(config.model);

        // Check if model supports structured output
        const capabilities = this.modelCapabilities.get(config.model);
        if (!capabilities?.structuredOutput) {
          throw new Error(`Model ${config.model} does not support structured output`);
        }

        const startTime = Date.now();
        const result = await generateObject({
          model,
          schema,
          prompt,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
        });

        // Update metrics
        this.updateModelMetrics(config.model, Date.now() - startTime, true, result.usage?.totalTokens || 0);

        return result.object;
      },
      context
    );
  }

  // Stream text generation
  async streamText(
    prompt: string,
    modelConfig: Partial<ModelConfig>,
    context: AIRequestContext,
    onChunk?: (chunk: string) => void
  ): Promise<AIServiceResult<AsyncIterable<string>>> {
    return this.executeAIOperation(
      'streamText',
      async () => {
        const config = this.getModelConfig(modelConfig.model || 'gpt-4', modelConfig);
        const model = this.getModelInstance(config.model);

        // Check if model supports streaming
        const capabilities = this.modelCapabilities.get(config.model);
        if (!capabilities?.streaming) {
          throw new Error(`Model ${config.model} does not support streaming`);
        }

        const startTime = Date.now();
        const result = streamText({
          model,
          prompt,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
        });

        // Create async iterable for streaming
        const streamIterable = async function* () {
          let totalTokens = 0;
          try {
            for await (const chunk of result.textStream) {
              totalTokens += chunk.length / 4; // Rough token estimation
              onChunk?.(chunk);
              yield chunk;
            }
          } finally {
            // Update metrics when streaming completes
            this.updateModelMetrics(config.model, Date.now() - startTime, true, totalTokens);
          }
        }.bind(this);

        return streamIterable();
      },
      context,
      false // Don't cache streaming results
    );
  }

  // Get model recommendations based on task
  getModelRecommendation(task: 'completion' | 'analysis' | 'chat' | 'refactoring'): AIModel {
    switch (task) {
      case 'completion':
        return 'gpt-3.5-turbo'; // Fast and cost-effective for completions
      case 'analysis':
        return 'gpt-4'; // More accurate for complex analysis
      case 'chat':
        return 'claude-3-sonnet'; // Good conversational abilities
      case 'refactoring':
        return 'gpt-4'; // Best for complex code transformations
      default:
        return 'gpt-4';
    }
  }

  // Get model capabilities
  getModelCapabilities(model: AIModel): ModelCapabilities | undefined {
    return this.modelCapabilities.get(model);
  }

  // Get model metrics
  getModelMetrics(model: AIModel): ModelMetrics | undefined {
    return this.modelMetrics.get(model);
  }

  // Get all available models
  getAvailableModels(): AIModel[] {
    return Array.from(this.modelConfigs.keys());
  }

  // Update model configuration
  updateModelConfig(model: AIModel, config: Partial<ModelConfig>): void {
    const currentConfig = this.modelConfigs.get(model);
    if (currentConfig) {
      this.modelConfigs.set(model, { ...currentConfig, ...config });
    }
  }

  // Private methods
  private initializeModels(): void {
    // Initialize model configurations
    this.modelConfigs = new Map([
      ['gpt-4', {
        model: 'gpt-4',
        temperature: 0.1,
        maxTokens: 2048,
        topP: 1,
        frequencyPenalty: 0,
        presencePenalty: 0,
      }],
      ['gpt-3.5-turbo', {
        model: 'gpt-3.5-turbo',
        temperature: 0.1,
        maxTokens: 2048,
        topP: 1,
        frequencyPenalty: 0,
        presencePenalty: 0,
      }],
      ['claude-3-sonnet', {
        model: 'claude-3-sonnet',
        temperature: 0.1,
        maxTokens: 2048,
      }],
      ['claude-3-haiku', {
        model: 'claude-3-haiku',
        temperature: 0.1,
        maxTokens: 2048,
      }],
    ]);

    // Initialize model capabilities
    this.modelCapabilities = new Map([
      ['gpt-4', {
        textGeneration: true,
        codeGeneration: true,
        structuredOutput: true,
        streaming: true,
        functionCalling: true,
        maxContextLength: 128000,
        costPerToken: 0.00003,
      }],
      ['gpt-3.5-turbo', {
        textGeneration: true,
        codeGeneration: true,
        structuredOutput: true,
        streaming: true,
        functionCalling: true,
        maxContextLength: 16384,
        costPerToken: 0.000002,
      }],
      ['claude-3-sonnet', {
        textGeneration: true,
        codeGeneration: true,
        structuredOutput: false,
        streaming: true,
        functionCalling: false,
        maxContextLength: 200000,
        costPerToken: 0.000015,
      }],
      ['claude-3-haiku', {
        textGeneration: true,
        codeGeneration: true,
        structuredOutput: false,
        streaming: true,
        functionCalling: false,
        maxContextLength: 200000,
        costPerToken: 0.00000025,
      }],
    ]);

    // Initialize model metrics
    this.modelMetrics = new Map();
    this.modelConfigs.forEach((_, model) => {
      this.modelMetrics.set(model, {
        averageResponseTime: 0,
        successRate: 0,
        errorRate: 0,
        totalRequests: 0,
        totalTokensUsed: 0,
        totalCost: 0,
      });
    });
  }

  private getModelConfig(model: AIModel, overrides: Partial<ModelConfig> = {}): ModelConfig {
    const baseConfig = this.modelConfigs.get(model);
    if (!baseConfig) {
      throw new Error(`Unsupported model: ${model}`);
    }
    return { ...baseConfig, ...overrides };
  }

  private getModelInstance(model: AIModel) {
    switch (model) {
      case 'gpt-4':
        return openai('gpt-4');
      case 'gpt-3.5-turbo':
        return openai('gpt-3.5-turbo');
      case 'claude-3-sonnet':
        return anthropic('claude-3-sonnet-20240229');
      case 'claude-3-haiku':
        return anthropic('claude-3-haiku-20240307');
      default:
        throw new Error(`Unsupported model: ${model}`);
    }
  }

  private updateModelMetrics(
    model: AIModel,
    responseTime: number,
    success: boolean,
    tokensUsed: number
  ): void {
    const metrics = this.modelMetrics.get(model);
    if (!metrics) return;

    const capabilities = this.modelCapabilities.get(model);
    const cost = tokensUsed * (capabilities?.costPerToken || 0);

    // Update metrics
    metrics.totalRequests++;
    metrics.totalTokensUsed += tokensUsed;
    metrics.totalCost += cost;

    if (success) {
      metrics.averageResponseTime = 
        (metrics.averageResponseTime * (metrics.totalRequests - 1) + responseTime) / metrics.totalRequests;
    }

    // Calculate rates
    const successCount = metrics.totalRequests - (metrics.errorRate * metrics.totalRequests);
    metrics.successRate = success 
      ? (successCount + 1) / metrics.totalRequests
      : successCount / metrics.totalRequests;
    
    metrics.errorRate = 1 - metrics.successRate;

    this.modelMetrics.set(model, metrics);
  }
}
