/**
 * Python AI Code Service
 * Specialized AI service for Python development with framework-aware features
 */

import { BaseAICodeService, AIServiceResult, AIRequestContext } from './base';
import {
  PythonCompletionRequest,
  PythonCompletionSuggestion,
  PythonAnalysisRequest,
  PythonCodeError,
  PythonRefactoringSuggestion,
  PythonAssistantRequest,
  PythonAssistantMessage,
  PythonCodeGenerationRequest,
  PythonCodeGenerationResponse,
  PythonFramework,
  PythonProjectStructure,
} from '@/types/python-ai-editor';
import { generateObject, generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Schemas for Python-specific AI responses
const PythonCompletionSchema = z.object({
  suggestions: z.array(z.object({
    text: z.string(),
    insertText: z.string(),
    confidence: z.number().min(0).max(1),
    type: z.enum(['completion', 'snippet', 'import', 'function', 'variable', 'class', 'method']),
    category: z.enum(['framework', 'builtin', 'package', 'variable', 'function', 'class', 'method']),
    description: z.string().optional(),
    documentation: z.string().optional(),
    priority: z.number().min(0).max(10),
    framework: z.string().optional(),
    packageName: z.string().optional(),
    importStatement: z.string().optional(),
    isFrameworkSpecific: z.boolean().optional(),
  })),
});

const PythonAnalysisSchema = z.object({
  errors: z.array(z.object({
    id: z.string(),
    message: z.string(),
    severity: z.enum(['error', 'warning', 'info']),
    line: z.number(),
    column: z.number(),
    endLine: z.number().optional(),
    endColumn: z.number().optional(),
    category: z.enum(['syntax', 'import', 'framework', 'security', 'performance', 'style', 'type']),
    code: z.string().optional(),
    framework: z.string().optional(),
    packageRequired: z.string().optional(),
    frameworkSpecific: z.boolean().optional(),
    fixes: z.array(z.object({
      title: z.string(),
      description: z.string(),
      newText: z.string(),
      range: z.object({
        startLine: z.number(),
        startColumn: z.number(),
        endLine: z.number(),
        endColumn: z.number(),
      }),
    })).optional(),
  })),
  suggestions: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    category: z.enum(['framework-pattern', 'performance', 'security', 'style', 'structure']),
    priority: z.number().min(0).max(10),
    framework: z.string().optional(),
    beforeCode: z.string(),
    afterCode: z.string(),
    explanation: z.string(),
    benefits: z.array(z.string()),
    range: z.object({
      startLine: z.number(),
      startColumn: z.number(),
      endLine: z.number(),
      endColumn: z.number(),
    }),
  })),
});

const PythonCodeGenerationSchema = z.object({
  success: z.boolean(),
  code: z.string().optional(),
  files: z.array(z.object({
    path: z.string(),
    content: z.string(),
    type: z.enum(['python', 'config', 'template', 'test']),
  })).optional(),
  dependencies: z.array(z.string()).optional(),
  commands: z.array(z.string()).optional(),
  explanation: z.string().optional(),
  nextSteps: z.array(z.string()).optional(),
  error: z.string().optional(),
});

export class PythonAIService extends BaseAICodeService {
  private frameworkPrompts: Record<PythonFramework, string> = {
    django: `You are a Django expert. Provide Django-specific code completions, analysis, and suggestions following Django best practices, patterns, and conventions.`,
    flask: `You are a Flask expert. Provide Flask-specific code completions, analysis, and suggestions following Flask best practices, patterns, and conventions.`,
    fastapi: `You are a FastAPI expert. Provide FastAPI-specific code completions, analysis, and suggestions following FastAPI best practices, patterns, and conventions.`,
    streamlit: `You are a Streamlit expert. Provide Streamlit-specific code completions, analysis, and suggestions for building interactive data applications.`,
    gradio: `You are a Gradio expert. Provide Gradio-specific code completions, analysis, and suggestions for building ML interfaces and demos.`,
  };

  /**
   * Get Python-specific code completions with framework awareness
   */
  async getPythonCompletions(
    request: PythonCompletionRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<{ suggestions: PythonCompletionSuggestion[] }>> {
    const cacheKey = this.generateCacheKey('python-completion', request, context);
    
    return this.withCaching(cacheKey, async () => {
      return this.withRateLimit(context.userId, async () => {
        const systemPrompt = this.buildPythonCompletionPrompt(request);
        
        const result = await generateObject({
          model: openai('gpt-4'),
          system: systemPrompt,
          prompt: this.buildCompletionUserPrompt(request),
          schema: PythonCompletionSchema,
          temperature: 0.3,
        });

        return {
          success: true,
          data: {
            suggestions: result.object.suggestions.map(s => ({
              ...s,
              id: this.generateId(),
              framework: request.framework,
            })) as PythonCompletionSuggestion[],
          },
        };
      });
    });
  }

  /**
   * Analyze Python code with framework-specific checks
   */
  async analyzePythonCode(
    request: PythonAnalysisRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<{ errors: PythonCodeError[]; suggestions: PythonRefactoringSuggestion[] }>> {
    const cacheKey = this.generateCacheKey('python-analysis', request, context);
    
    return this.withCaching(cacheKey, async () => {
      return this.withRateLimit(context.userId, async () => {
        const systemPrompt = this.buildPythonAnalysisPrompt(request);
        
        const result = await generateObject({
          model: openai('gpt-4'),
          system: systemPrompt,
          prompt: this.buildAnalysisUserPrompt(request),
          schema: PythonAnalysisSchema,
          temperature: 0.2,
        });

        return {
          success: true,
          data: {
            errors: result.object.errors.map(e => ({
              ...e,
              framework: request.framework,
            })) as PythonCodeError[],
            suggestions: result.object.suggestions.map(s => ({
              ...s,
              framework: request.framework,
            })) as PythonRefactoringSuggestion[],
          },
        };
      });
    });
  }

  /**
   * Generate Python code with framework-specific patterns
   */
  async generatePythonCode(
    request: PythonCodeGenerationRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<PythonCodeGenerationResponse>> {
    const cacheKey = this.generateCacheKey('python-generation', request, context);
    
    return this.withCaching(cacheKey, async () => {
      return this.withRateLimit(context.userId, async () => {
        const systemPrompt = this.buildPythonGenerationPrompt(request);
        
        const result = await generateObject({
          model: openai('gpt-4'),
          system: systemPrompt,
          prompt: this.buildGenerationUserPrompt(request),
          schema: PythonCodeGenerationSchema,
          temperature: 0.4,
        });

        return {
          success: true,
          data: result.object as PythonCodeGenerationResponse,
        };
      });
    });
  }

  /**
   * Get framework-specific assistant response
   */
  async getPythonAssistantResponse(
    request: PythonAssistantRequest,
    context: AIRequestContext
  ): Promise<AIServiceResult<{ message: PythonAssistantMessage }>> {
    return this.withRateLimit(context.userId, async () => {
      const systemPrompt = this.buildPythonAssistantPrompt(request);
      
      const result = await generateText({
        model: openai('gpt-4'),
        system: systemPrompt,
        prompt: request.message,
        temperature: 0.7,
        maxTokens: 2000,
      });

      const message: PythonAssistantMessage = {
        id: this.generateId(),
        role: 'assistant',
        content: result.text,
        timestamp: new Date(),
        framework: request.framework,
        // TODO: Parse code blocks and actions from response
        codeBlocks: [],
        actions: [],
      };

      return {
        success: true,
        data: { message },
      };
    });
  }

  private buildPythonCompletionPrompt(request: PythonCompletionRequest): string {
    const basePrompt = request.framework 
      ? this.frameworkPrompts[request.framework]
      : 'You are a Python expert. Provide accurate Python code completions.';

    return `${basePrompt}

Context:
- Python version: ${request.pythonVersion || '3.11'}
- Framework: ${request.framework || 'None'}
- Virtual environment: ${request.virtualEnv || 'None'}
- Installed packages: ${request.installedPackages?.join(', ') || 'None'}

Provide intelligent code completions that:
1. Follow Python and framework best practices
2. Include relevant imports when needed
3. Suggest framework-specific patterns and methods
4. Consider the project structure and context
5. Prioritize commonly used and relevant suggestions

Return completions as a JSON object with suggestions array.`;
  }

  private buildPythonAnalysisPrompt(request: PythonAnalysisRequest): string {
    const basePrompt = request.framework 
      ? this.frameworkPrompts[request.framework]
      : 'You are a Python expert. Analyze Python code for issues and improvements.';

    const checks = [];
    if (request.checkFrameworkBestPractices) checks.push('framework best practices');
    if (request.checkSecurityVulnerabilities) checks.push('security vulnerabilities');
    if (request.checkPerformanceIssues) checks.push('performance issues');

    return `${basePrompt}

Context:
- Python version: ${request.pythonVersion || '3.11'}
- Framework: ${request.framework || 'None'}
- Analysis focus: ${checks.join(', ') || 'general code quality'}

Analyze the code for:
1. Syntax errors and type issues
2. Import problems and missing dependencies
3. Framework-specific anti-patterns
4. Security vulnerabilities
5. Performance bottlenecks
6. Code style and maintainability issues

Provide specific, actionable suggestions with code examples.`;
  }

  private buildPythonGenerationPrompt(request: PythonCodeGenerationRequest): string {
    const basePrompt = request.framework 
      ? this.frameworkPrompts[request.framework]
      : 'You are a Python expert. Generate high-quality Python code.';

    return `${basePrompt}

Generate code for: ${request.intent}
Framework: ${request.framework}
Description: ${request.context.description}

Requirements:
${request.context.requirements?.map(r => `- ${r}`).join('\n') || '- Follow best practices'}

Options:
- Include tests: ${request.options?.includeTests || false}
- Include documentation: ${request.options?.includeDocumentation || false}
- Follow best practices: ${request.options?.followBestPractices !== false}
- Optimize performance: ${request.options?.optimizePerformance || false}

Generate complete, working code that follows framework conventions and best practices.`;
  }

  private buildPythonAssistantPrompt(request: PythonAssistantRequest): string {
    const basePrompt = request.framework 
      ? this.frameworkPrompts[request.framework]
      : 'You are a helpful Python development assistant.';

    return `${basePrompt}

You are helping with a Python project using ${request.framework || 'Python'}.

Project context:
${request.projectStructure ? `
- Framework: ${request.projectStructure.framework}
- Python version: ${request.projectStructure.pythonVersion}
- Package manager: ${request.projectStructure.packageManager}
- Installed packages: ${request.projectStructure.installedPackages.map(p => p.name).join(', ')}
` : '- No project context available'}

Provide helpful, accurate responses with:
1. Clear explanations
2. Code examples when relevant
3. Best practices and recommendations
4. Framework-specific guidance
5. Actionable next steps

Be concise but thorough in your responses.`;
  }

  private buildCompletionUserPrompt(request: PythonCompletionRequest): string {
    return `Complete the following Python code:

\`\`\`python
${request.code}
\`\`\`

Cursor position: Line ${request.position.line}, Column ${request.position.column}
File: ${request.context?.fileName || 'untitled.py'}

Provide relevant completions for the current context.`;
  }

  private buildAnalysisUserPrompt(request: PythonAnalysisRequest): string {
    return `Analyze the following Python code:

\`\`\`python
${request.code}
\`\`\`

File: ${request.fileName || 'untitled.py'}
Language: ${request.language}

Identify issues and provide improvement suggestions.`;
  }

  private buildGenerationUserPrompt(request: PythonCodeGenerationRequest): string {
    return `Generate Python code with the following specifications:

Intent: ${request.intent}
Framework: ${request.framework}
Description: ${request.context.description}

${request.context.existingCode ? `
Existing code context:
\`\`\`python
${request.context.existingCode}
\`\`\`
` : ''}

Generate complete, working code that meets the requirements.`;
  }
}

// Export singleton instance
export const pythonAIService = new PythonAIService();
