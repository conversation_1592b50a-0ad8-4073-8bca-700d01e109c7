import { BaseAICodeService, AIServiceResult, AIRequestContext } from './base';
import { CodeFile, CodeEdit, CodeRange } from '@/types/ai-code-editor';

// Formatting options
export interface FormattingOptions {
  indentSize: number;
  indentType: 'spaces' | 'tabs';
  maxLineLength: number;
  insertFinalNewline: boolean;
  trimTrailingWhitespace: boolean;
  semicolons: boolean;
  singleQuotes: boolean;
  trailingCommas: boolean;
  bracketSpacing: boolean;
  arrowParens: 'avoid' | 'always';
}

// Language-specific formatting rules
interface LanguageFormatter {
  language: string;
  defaultOptions: FormattingOptions;
  rules: {
    indentation: (code: string, options: FormattingOptions) => string;
    spacing: (code: string, options: FormattingOptions) => string;
    lineBreaks: (code: string, options: FormattingOptions) => string;
    quotes: (code: string, options: FormattingOptions) => string;
    semicolons: (code: string, options: FormattingOptions) => string;
  };
}

// Default formatting options
const DEFAULT_FORMATTING_OPTIONS: FormattingOptions = {
  indentSize: 2,
  indentType: 'spaces',
  maxLineLength: 100,
  insertFinalNewline: true,
  trimTrailingWhitespace: true,
  semicolons: true,
  singleQuotes: true,
  trailingCommas: true,
  bracketSpacing: true,
  arrowParens: 'avoid',
};

export class CodeFormattingService extends BaseAICodeService {
  private languageFormatters: Map<string, LanguageFormatter>;

  constructor() {
    super('CodeFormattingService');
    this.initializeFormatters();
  }

  async healthCheck(): Promise<boolean> {
    return true; // Formatting doesn't require external services
  }

  // Format code with AI-powered suggestions
  async formatCode(
    code: string,
    language: string,
    options: Partial<FormattingOptions> = {},
    context: AIRequestContext
  ): Promise<AIServiceResult<{
    formattedCode: string;
    edits: CodeEdit[];
    suggestions: string[];
  }>> {
    return this.executeAIOperation(
      'formatCode',
      async () => {
        const formatter = this.languageFormatters.get(language.toLowerCase());
        if (!formatter) {
          throw new Error(`Unsupported language for formatting: ${language}`);
        }

        const formattingOptions = { ...formatter.defaultOptions, ...options };
        let formattedCode = code;
        const edits: CodeEdit[] = [];
        const suggestions: string[] = [];

        // Apply formatting rules in order
        const originalLines = code.split('\n');
        
        // 1. Fix indentation
        formattedCode = formatter.rules.indentation(formattedCode, formattingOptions);
        
        // 2. Fix spacing
        formattedCode = formatter.rules.spacing(formattedCode, formattingOptions);
        
        // 3. Fix line breaks
        formattedCode = formatter.rules.lineBreaks(formattedCode, formattingOptions);
        
        // 4. Fix quotes
        formattedCode = formatter.rules.quotes(formattedCode, formattingOptions);
        
        // 5. Fix semicolons
        formattedCode = formatter.rules.semicolons(formattedCode, formattingOptions);

        // Generate edits by comparing original and formatted code
        const formattedLines = formattedCode.split('\n');
        for (let i = 0; i < Math.max(originalLines.length, formattedLines.length); i++) {
          const originalLine = originalLines[i] || '';
          const formattedLine = formattedLines[i] || '';
          
          if (originalLine !== formattedLine) {
            edits.push({
              range: {
                start: { line: i + 1, column: 1 },
                end: { line: i + 1, column: originalLine.length + 1 },
              },
              newText: formattedLine,
            });
          }
        }

        // Generate formatting suggestions
        suggestions.push(...this.generateFormattingSuggestions(code, formattedCode, formattingOptions));

        return {
          formattedCode,
          edits,
          suggestions,
        };
      },
      context
    );
  }

  // Get formatting suggestions without applying changes
  async getFormattingSuggestions(
    code: string,
    language: string,
    context: AIRequestContext
  ): Promise<AIServiceResult<string[]>> {
    return this.executeAIOperation(
      'getFormattingSuggestions',
      async () => {
        const suggestions: string[] = [];
        const lines = code.split('\n');

        // Check for common formatting issues
        lines.forEach((line, index) => {
          // Mixed indentation
          if (line.match(/^\s*\t.*\s{2,}/)) {
            suggestions.push(`Line ${index + 1}: Mixed tabs and spaces for indentation`);
          }

          // Trailing whitespace
          if (line.match(/\s+$/)) {
            suggestions.push(`Line ${index + 1}: Trailing whitespace`);
          }

          // Long lines
          if (line.length > 120) {
            suggestions.push(`Line ${index + 1}: Line too long (${line.length} characters)`);
          }

          // Missing semicolons (for JavaScript/TypeScript)
          if (language.match(/javascript|typescript/) && line.match(/[^;{}]\s*$/)) {
            suggestions.push(`Line ${index + 1}: Consider adding semicolon`);
          }

          // Inconsistent quotes
          if (line.includes('"') && line.includes("'")) {
            suggestions.push(`Line ${index + 1}: Mixed quote styles`);
          }
        });

        return suggestions;
      },
      context
    );
  }

  // Apply specific formatting rule
  async applyFormattingRule(
    code: string,
    language: string,
    rule: keyof LanguageFormatter['rules'],
    options: Partial<FormattingOptions> = {},
    context: AIRequestContext
  ): Promise<AIServiceResult<string>> {
    return this.executeAIOperation(
      'applyFormattingRule',
      async () => {
        const formatter = this.languageFormatters.get(language.toLowerCase());
        if (!formatter) {
          throw new Error(`Unsupported language for formatting: ${language}`);
        }

        const formattingOptions = { ...formatter.defaultOptions, ...options };
        return formatter.rules[rule](code, formattingOptions);
      },
      context
    );
  }

  // Get default formatting options for language
  getDefaultOptions(language: string): FormattingOptions | null {
    const formatter = this.languageFormatters.get(language.toLowerCase());
    return formatter ? { ...formatter.defaultOptions } : null;
  }

  // Get supported languages for formatting
  getSupportedLanguages(): string[] {
    return Array.from(this.languageFormatters.keys());
  }

  // Private methods
  private initializeFormatters(): void {
    this.languageFormatters = new Map();

    // TypeScript/JavaScript formatter
    const jsFormatter: LanguageFormatter = {
      language: 'javascript',
      defaultOptions: DEFAULT_FORMATTING_OPTIONS,
      rules: {
        indentation: (code, options) => {
          const indent = options.indentType === 'tabs' ? '\t' : ' '.repeat(options.indentSize);
          const lines = code.split('\n');
          let indentLevel = 0;
          
          return lines.map(line => {
            const trimmed = line.trim();
            if (!trimmed) return '';
            
            // Decrease indent for closing brackets
            if (trimmed.match(/^[}\]]/)) {
              indentLevel = Math.max(0, indentLevel - 1);
            }
            
            const indentedLine = indent.repeat(indentLevel) + trimmed;
            
            // Increase indent for opening brackets
            if (trimmed.match(/[{[]$/)) {
              indentLevel++;
            }
            
            return indentedLine;
          }).join('\n');
        },
        
        spacing: (code, options) => {
          return code
            .replace(/\s*{\s*/g, options.bracketSpacing ? ' { ' : '{')
            .replace(/\s*}\s*/g, options.bracketSpacing ? ' } ' : '}')
            .replace(/,\s*/g, ', ')
            .replace(/:\s*/g, ': ')
            .replace(/=\s*/g, ' = ')
            .replace(/\+\s*/g, ' + ')
            .replace(/-\s*/g, ' - ')
            .replace(/\*\s*/g, ' * ')
            .replace(/\/\s*/g, ' / ');
        },
        
        lineBreaks: (code, options) => {
          const lines = code.split('\n');
          return lines.map(line => {
            if (line.length > options.maxLineLength) {
              // Simple line breaking for long lines
              return line.replace(/,\s*/g, ',\n  ');
            }
            return line;
          }).join('\n');
        },
        
        quotes: (code, options) => {
          if (options.singleQuotes) {
            return code.replace(/"/g, "'");
          } else {
            return code.replace(/'/g, '"');
          }
        },
        
        semicolons: (code, options) => {
          if (options.semicolons) {
            return code.replace(/([^;{}])\s*$/gm, '$1;');
          } else {
            return code.replace(/;$/gm, '');
          }
        },
      },
    };

    // Add TypeScript formatter (same as JavaScript)
    const tsFormatter: LanguageFormatter = {
      ...jsFormatter,
      language: 'typescript',
    };

    this.languageFormatters.set('javascript', jsFormatter);
    this.languageFormatters.set('typescript', tsFormatter);
    this.languageFormatters.set('jsx', jsFormatter);
    this.languageFormatters.set('tsx', tsFormatter);
  }

  private generateFormattingSuggestions(
    originalCode: string,
    formattedCode: string,
    options: FormattingOptions
  ): string[] {
    const suggestions: string[] = [];
    
    if (originalCode !== formattedCode) {
      suggestions.push('Code formatting has been improved');
    }
    
    if (originalCode.includes('\t') && options.indentType === 'spaces') {
      suggestions.push('Converted tabs to spaces for consistent indentation');
    }
    
    if (originalCode.match(/\s+$/m)) {
      suggestions.push('Removed trailing whitespace');
    }
    
    if (options.singleQuotes && originalCode.includes('"')) {
      suggestions.push('Converted double quotes to single quotes');
    }
    
    return suggestions;
  }

  // Add custom language formatter
  addLanguageFormatter(formatter: LanguageFormatter): void {
    this.languageFormatters.set(formatter.language, formatter);
  }
}
