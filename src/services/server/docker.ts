import Docker from 'dockerode';
import { ContainerInfo, WorkspaceInfo, CreateContainerOptions, CreateWorkspaceOptions } from '@/types/docker';

// Runtime check to prevent client-side usage
if (typeof window !== 'undefined') {
  throw new Error(
    'Docker service cannot be used on the client side. Use the client-side Docker API service instead.'
  );
}

export class DockerService {
  private docker: Docker;

  constructor() {
    // Additional runtime check
    if (typeof window !== 'undefined') {
      throw new Error(
        'Docker service cannot be instantiated on the client side. Use the client-side Docker API service instead.'
      );
    }

    // Initialize Docker connection
    this.docker = new Docker({
      socketPath: process.env.DOCKER_SOCKET_PATH || '/var/run/docker.sock',
      host: process.env.DOCKER_HOST,
      port: process.env.DOCKER_PORT ? parseInt(process.env.DOCKER_PORT) : undefined,
      ca: process.env.DOCKER_CA,
      cert: process.env.DOCKER_CERT,
      key: process.env.DOCKER_KEY,
    });
  }

  /**
   * Test Docker connection
   */
  async ping(): Promise<boolean> {
    try {
      await this.docker.ping();
      return true;
    } catch (error) {
      console.error('Docker connection failed:', error);
      return false;
    }
  }

  /**
   * Get Docker system information
   */
  async getSystemInfo() {
    try {
      const info = await this.docker.info();
      return {
        containers: info.Containers,
        containersRunning: info.ContainersRunning,
        containersPaused: info.ContainersPaused,
        containersStopped: info.ContainersStopped,
        images: info.Images,
        serverVersion: info.ServerVersion,
        memTotal: info.MemTotal,
        ncpu: info.NCPU,
        architecture: info.Architecture,
        kernelVersion: info.KernelVersion,
        operatingSystem: info.OperatingSystem,
      };
    } catch (error) {
      console.error('Failed to get Docker system info:', error);
      throw error;
    }
  }

  /**
   * List all containers
   */
  async listContainers(all: boolean = true): Promise<ContainerInfo[]> {
    try {
      const containers = await this.docker.listContainers({ all });
      
      return containers.map(container => ({
        id: container.Id,
        name: container.Names[0]?.replace('/', '') || '',
        image: container.Image,
        status: this.mapContainerState(container.State),
        created: new Date(container.Created * 1000),
        ports: this.formatPorts(container.Ports),
        networkMode: container.HostConfig?.NetworkMode,
      }));
    } catch (error) {
      console.error('Failed to list containers:', error);
      throw error;
    }
  }

  /**
   * Get container details
   */
  async getContainer(containerId: string): Promise<ContainerInfo | null> {
    try {
      const container = this.docker.getContainer(containerId);
      const info = await container.inspect();
      
      return {
        id: info.Id,
        name: info.Name.replace('/', ''),
        image: info.Config.Image,
        status: this.mapContainerState(info.State.Status),
        created: new Date(info.Created),
        ports: this.formatPortsFromInspect(info.NetworkSettings.Ports),
        cpu: info.HostConfig.NanoCpus ? info.HostConfig.NanoCpus / 1000000000 : undefined,
        memory: info.HostConfig.Memory ? Math.round(info.HostConfig.Memory / 1024 / 1024) : undefined,
        networkMode: info.HostConfig.NetworkMode,
        mountedVolumes: info.Mounts?.map(mount => `${mount.Source}:${mount.Destination}`) || [],
      };
    } catch (error) {
      console.error(`Failed to get container ${containerId}:`, error);
      return null;
    }
  }

  /**
   * Create a new container
   */
  async createContainer(options: CreateContainerOptions): Promise<string> {
    try {
      // Prepare port bindings
      const portBindings: { [key: string]: Array<{ HostPort: string }> } = {};
      const exposedPorts: { [key: string]: {} } = {};
      
      if (options.ports) {
        Object.entries(options.ports).forEach(([containerPort, hostPort]) => {
          const portKey = `${containerPort}/tcp`;
          exposedPorts[portKey] = {};
          portBindings[portKey] = [{ HostPort: hostPort }];
        });
      }

      // Prepare volume bindings
      const binds: string[] = [];
      if (options.volumes) {
        Object.entries(options.volumes).forEach(([hostPath, containerPath]) => {
          binds.push(`${hostPath}:${containerPath}`);
        });
      }

      // Prepare environment variables
      const env: string[] = [];
      if (options.environment) {
        Object.entries(options.environment).forEach(([key, value]) => {
          env.push(`${key}=${value}`);
        });
      }

      const createOptions: Docker.ContainerCreateOptions = {
        name: options.name,
        Image: options.image,
        Cmd: options.cmd,
        WorkingDir: options.workingDir,
        User: options.user,
        Env: env.length > 0 ? env : undefined,
        ExposedPorts: Object.keys(exposedPorts).length > 0 ? exposedPorts : undefined,
        HostConfig: {
          PortBindings: Object.keys(portBindings).length > 0 ? portBindings : undefined,
          Binds: binds.length > 0 ? binds : undefined,
          Memory: options.memory ? options.memory * 1024 * 1024 : undefined,
          NanoCpus: options.cpu ? options.cpu * 1000000000 : undefined,
          NetworkMode: options.networkMode || 'bridge',
          Privileged: options.privileged || false,
          AutoRemove: options.autoRemove || false,
          RestartPolicy: { Name: 'unless-stopped' },
        },
      };

      const container = await this.docker.createContainer(createOptions);
      return container.id;
    } catch (error) {
      console.error('Failed to create container:', error);
      throw error;
    }
  }

  /**
   * Start a container
   */
  async startContainer(containerId: string): Promise<void> {
    try {
      const container = this.docker.getContainer(containerId);
      await container.start();
    } catch (error) {
      console.error(`Failed to start container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Stop a container
   */
  async stopContainer(containerId: string, timeout: number = 10): Promise<void> {
    try {
      const container = this.docker.getContainer(containerId);
      await container.stop({ t: timeout });
    } catch (error) {
      console.error(`Failed to stop container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Restart a container
   */
  async restartContainer(containerId: string): Promise<void> {
    try {
      const container = this.docker.getContainer(containerId);
      await container.restart();
    } catch (error) {
      console.error(`Failed to restart container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a container
   */
  async removeContainer(containerId: string, force: boolean = false): Promise<void> {
    try {
      const container = this.docker.getContainer(containerId);
      await container.remove({ force });
    } catch (error) {
      console.error(`Failed to remove container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Get container logs
   */
  async getContainerLogs(containerId: string, tail: number = 100): Promise<string> {
    try {
      const container = this.docker.getContainer(containerId);
      const stream = await container.logs({
        stdout: true,
        stderr: true,
        tail,
        timestamps: true,
      });
      
      return stream.toString();
    } catch (error) {
      console.error(`Failed to get logs for container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Get container stats
   */
  async getContainerStats(containerId: string): Promise<any> {
    try {
      const container = this.docker.getContainer(containerId);
      const stats = await container.stats({ stream: false });
      
      return {
        cpu: this.calculateCpuPercent(stats),
        memory: {
          usage: stats.memory_stats.usage,
          limit: stats.memory_stats.limit,
          percent: (stats.memory_stats.usage / stats.memory_stats.limit) * 100,
        },
        network: stats.networks,
        blockIO: stats.blkio_stats,
      };
    } catch (error) {
      console.error(`Failed to get stats for container ${containerId}:`, error);
      throw error;
    }
  }

  // Helper methods will be added in the next chunk
  private mapContainerState(state: string): ContainerInfo['status'] {
    switch (state.toLowerCase()) {
      case 'running':
        return 'running';
      case 'exited':
      case 'dead':
        return 'stopped';
      case 'paused':
        return 'paused';
      case 'restarting':
        return 'restarting';
      default:
        return 'stopped';
    }
  }

  private formatPorts(ports: any[]): { [key: string]: string } {
    const portMap: { [key: string]: string } = {};
    
    ports.forEach(port => {
      if (port.PublicPort && port.PrivatePort) {
        portMap[`${port.PrivatePort}/${port.Type}`] = port.PublicPort.toString();
      }
    });
    
    return portMap;
  }

  private formatPortsFromInspect(ports: any): { [key: string]: string } {
    const portMap: { [key: string]: string } = {};
    
    if (ports) {
      Object.entries(ports).forEach(([containerPort, hostBindings]: [string, any]) => {
        if (hostBindings && hostBindings.length > 0) {
          portMap[containerPort] = hostBindings[0].HostPort;
        }
      });
    }
    
    return portMap;
  }

  private calculateCpuPercent(stats: any): number {
    const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - stats.precpu_stats.cpu_usage.total_usage;
    const systemDelta = stats.cpu_stats.system_cpu_usage - stats.precpu_stats.system_cpu_usage;
    const cpuCount = stats.cpu_stats.cpu_usage.percpu_usage?.length || 1;

    if (systemDelta > 0 && cpuDelta > 0) {
      return (cpuDelta / systemDelta) * cpuCount * 100;
    }

    return 0;
  }

  /**
   * Pull an image
   */
  async pullImage(imageName: string, onProgress?: (progress: any) => void): Promise<void> {
    try {
      const stream = await this.docker.pull(imageName);

      return new Promise((resolve, reject) => {
        this.docker.modem.followProgress(
          stream,
          (err: any, _res: any) => {
            if (err) reject(err);
            else resolve();
          },
          onProgress
        );
      });
    } catch (error) {
      console.error(`Failed to pull image ${imageName}:`, error);
      throw error;
    }
  }

  /**
   * List available images
   */
  async listImages(): Promise<any[]> {
    try {
      const images = await this.docker.listImages();
      return images.map(image => ({
        id: image.Id,
        repoTags: image.RepoTags,
        size: image.Size,
        created: new Date(image.Created * 1000),
      }));
    } catch (error) {
      console.error('Failed to list images:', error);
      throw error;
    }
  }

  /**
   * Execute command in container
   */
  async execInContainer(containerId: string, cmd: string[]): Promise<string> {
    try {
      const container = this.docker.getContainer(containerId);
      const exec = await container.exec({
        Cmd: cmd,
        AttachStdout: true,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: false });

      return new Promise((resolve, reject) => {
        let output = '';

        stream.on('data', (chunk: Buffer) => {
          output += chunk.toString();
        });

        stream.on('end', () => {
          resolve(output);
        });

        stream.on('error', (error: Error) => {
          reject(error);
        });
      });
    } catch (error) {
      console.error(`Failed to execute command in container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Execute shell command in container with better interface
   */
  async executeCommand(
    containerId: string,
    command: string,
    workingDir?: string
  ): Promise<{ exitCode: number; stdout: string; stderr: string }> {
    try {
      const container = this.docker.getContainer(containerId);

      // Prepare command array
      let cmd: string[];
      if (workingDir) {
        cmd = ['sh', '-c', `cd "${workingDir}" && ${command}`];
      } else {
        cmd = ['sh', '-c', command];
      }

      const exec = await container.exec({
        Cmd: cmd,
        AttachStdout: true,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: false });

      return new Promise((resolve, reject) => {
        let stdout = '';
        let stderr = '';

        stream.on('data', (chunk: Buffer) => {
          // Docker multiplexes stdout and stderr
          // First byte indicates stream type: 1=stdout, 2=stderr
          const header = chunk.readUInt8(0);
          const data = chunk.subarray(8).toString(); // Skip 8-byte header

          if (header === 1) {
            stdout += data;
          } else if (header === 2) {
            stderr += data;
          } else {
            // Fallback for non-multiplexed streams
            stdout += chunk.toString();
          }
        });

        stream.on('end', async () => {
          try {
            // Get exit code
            const inspectResult = await exec.inspect();
            const exitCode = inspectResult.ExitCode || 0;

            resolve({
              exitCode,
              stdout: stdout.trim(),
              stderr: stderr.trim(),
            });
          } catch (error) {
            resolve({
              exitCode: 0,
              stdout: stdout.trim(),
              stderr: stderr.trim(),
            });
          }
        });

        stream.on('error', (error: Error) => {
          reject(error);
        });
      });
    } catch (error) {
      console.error(`Failed to execute command in container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new workspace container
   */
  async createWorkspace(options: CreateWorkspaceOptions): Promise<string> {
    try {
      // Generate container name if not provided
      const containerName = options.name || this.generateWorkspaceName(options.workspaceType, options.userId);

      // Generate VNC password if not provided
      const vncPassword = options.vncPassword || this.generateSecurePassword();

      // Get next available VNC port
      const vncPort = await this.getNextAvailablePort(5901);

      // Set default resources
      const resources = {
        cpu: options.resources?.cpu || this.getDefaultResources(options.workspaceType).cpu,
        memory: options.resources?.memory || this.getDefaultResources(options.workspaceType).memory,
        storage: options.resources?.storage || this.getDefaultResources(options.workspaceType).storage,
      };

      // Prepare environment variables
      const environment: { [key: string]: string } = {
        VNC_PASSWORD: vncPassword,
        DISPLAY_WIDTH: (options.displayWidth || 1920).toString(),
        DISPLAY_HEIGHT: (options.displayHeight || 1080).toString(),
        USER_NAME: options.userId,
        ...options.environment,
      };

      // Prepare volumes for development environment
      const volumes: { [hostPath: string]: string } = {};
      if (options.workspaceType === 'development-env') {
        volumes['/var/run/docker.sock'] = '/var/run/docker.sock';
      }

      // Create container options
      const createOptions: CreateContainerOptions = {
        name: containerName,
        image: this.getWorkspaceImage(options.workspaceType),
        cpu: resources.cpu,
        memory: resources.memory,
        ports: { '5901': vncPort.toString() },
        environment,
        volumes,
        networkMode: 'omnispace-workspace-network',
        autoRemove: false,
      };

      // Create the container
      const containerId = await this.createContainer(createOptions);

      // Add workspace-specific labels
      const container = this.docker.getContainer(containerId);
      await container.update({
        Labels: {
          'omnispace.workspace': 'true',
          'omnispace.workspace.type': options.workspaceType,
          'omnispace.workspace.user': options.userId,
          'omnispace.workspace.vnc-port': vncPort.toString(),
          'omnispace.workspace.created': new Date().toISOString(),
        },
      });

      return containerId;
    } catch (error) {
      console.error('Failed to create workspace:', error);
      throw error;
    }
  }

  /**
   * Get workspace containers only
   */
  async listWorkspaces(): Promise<WorkspaceInfo[]> {
    try {
      const containers = await this.docker.listContainers({
        all: true,
        filters: {
          label: ['omnispace.workspace=true']
        }
      });

      const workspaces: WorkspaceInfo[] = [];

      for (const container of containers) {
        const workspaceInfo = await this.getWorkspaceInfo(container.Id);
        if (workspaceInfo) {
          workspaces.push(workspaceInfo);
        }
      }

      return workspaces;
    } catch (error) {
      console.error('Failed to list workspaces:', error);
      throw error;
    }
  }

  /**
   * Get detailed workspace information
   */
  async getWorkspaceInfo(containerId: string): Promise<WorkspaceInfo | null> {
    try {
      const container = this.docker.getContainer(containerId);
      const info = await container.inspect();

      // Check if this is a workspace container
      const labels = info.Config.Labels || {};
      if (!labels['omnispace.workspace']) {
        return null;
      }

      // Extract VNC port from port bindings
      let vncPort = 0;
      const portBindings = info.NetworkSettings.Ports;
      if (portBindings && portBindings['5901/tcp']) {
        const binding = portBindings['5901/tcp'][0];
        vncPort = parseInt(binding.HostPort);
      }

      return {
        id: info.Id,
        name: info.Name.replace('/', ''),
        image: info.Config.Image,
        status: this.mapContainerState(info.State.Status),
        created: new Date(info.Created),
        ports: this.formatPortsFromInspect(info.NetworkSettings.Ports),
        cpu: info.HostConfig.NanoCpus ? info.HostConfig.NanoCpus / 1000000000 : undefined,
        memory: info.HostConfig.Memory ? Math.round(info.HostConfig.Memory / 1024 / 1024) : undefined,
        networkMode: info.HostConfig.NetworkMode,
        mountedVolumes: info.Mounts?.map(mount => `${mount.Source}:${mount.Destination}`) || [],
        workspaceType: labels['omnispace.workspace.type'] as WorkspaceInfo['workspaceType'],
        userId: labels['omnispace.workspace.user'] || 'unknown',
        vncPort,
        displayWidth: parseInt(info.Config.Env?.find(env => env.startsWith('DISPLAY_WIDTH='))?.split('=')[1] || '1920'),
        displayHeight: parseInt(info.Config.Env?.find(env => env.startsWith('DISPLAY_HEIGHT='))?.split('=')[1] || '1080'),
        guacamoleConnectionId: labels['omnispace.guacamole.connection'],
        resources: {
          cpuLimit: info.HostConfig.NanoCpus ? info.HostConfig.NanoCpus / 1000000000 : 0,
          memoryLimit: info.HostConfig.Memory ? Math.round(info.HostConfig.Memory / 1024 / 1024) : 0,
        },
      };
    } catch (error) {
      console.error(`Failed to get workspace info for ${containerId}:`, error);
      return null;
    }
  }

  // Workspace helper methods
  private generateWorkspaceName(workspaceType: string, userId: string): string {
    const timestamp = Date.now();
    return `workspace-${userId}-${workspaceType}-${timestamp}`;
  }

  private generateSecurePassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  private async getNextAvailablePort(basePort: number): Promise<number> {
    let port = basePort;
    const containers = await this.docker.listContainers({ all: true });

    const usedPorts = new Set<number>();
    containers.forEach(container => {
      Object.values(container.Ports || {}).forEach(portInfo => {
        if (typeof portInfo === 'object' && portInfo.PublicPort) {
          usedPorts.add(portInfo.PublicPort);
        }
      });
    });

    while (usedPorts.has(port)) {
      port++;
    }

    return port;
  }

  private getDefaultResources(workspaceType: string): { cpu: number; memory: number; storage: number } {
    switch (workspaceType) {
      case 'ubuntu-desktop':
        return { cpu: 2, memory: 2048, storage: 20 };
      case 'development-env':
        return { cpu: 4, memory: 4096, storage: 40 };
      case 'minimal-desktop':
        return { cpu: 1, memory: 1024, storage: 10 };
      case 'python-dev':
        return { cpu: 4, memory: 6144, storage: 50 };
      default:
        return { cpu: 2, memory: 2048, storage: 20 };
    }
  }

  private getWorkspaceImage(workspaceType: string): string {
    const registry = process.env.WORKSPACE_IMAGE_REGISTRY || 'omnispace';
    switch (workspaceType) {
      case 'ubuntu-desktop':
        return `${registry}/ubuntu-desktop:latest`;
      case 'development-env':
        return `${registry}/development-env:latest`;
      case 'minimal-desktop':
        return `${registry}/minimal-desktop:latest`;
      case 'python-dev':
        return `${registry}/python-dev:latest`;
      default:
        return `${registry}/ubuntu-desktop:latest`;
    }
  }
}

export const dockerService = new DockerService();
