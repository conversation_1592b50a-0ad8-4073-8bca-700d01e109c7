/**
 * Node/React Workspace Client Service
 * Client-safe service that only uses API calls, no server-side dependencies
 */

import { 
  NodeProjectTemplate, 
  NodeWorkspaceConfig, 
  NodeEnvironment,
  NodeLivePreview,
  CreateNodeProjectRequest,
  CreateNodeProjectResponse,
  NodeWorkspaceStatus,
  NodeFramework
} from '@/types/node-react-workspace';

export class NodeReactWorkspaceClientService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/node-react-workspace') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get available project templates
   */
  async getTemplates(): Promise<NodeProjectTemplate[]> {
    const response = await fetch(`${this.baseUrl}/templates`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch templates');
    }
    
    return data.data;
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string): Promise<NodeProjectTemplate | null> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}`);
    const data = await response.json();
    
    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(data.message || 'Failed to fetch template');
    }
    
    return data.success ? data.data : null;
  }

  /**
   * Create a new Node/React project
   */
  async createProject(request: CreateNodeProjectRequest): Promise<CreateNodeProjectResponse> {
    const response = await fetch(`${this.baseUrl}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to create project');
    }
    
    return data.data;
  }

  /**
   * Get workspace status
   */
  async getWorkspaceStatus(workspaceId: string): Promise<NodeWorkspaceStatus> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/status`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to get workspace status');
    }
    
    return data.data;
  }

  /**
   * Get live preview status
   */
  async getLivePreview(workspaceId: string): Promise<NodeLivePreview | null> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview`);
    const data = await response.json();
    
    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(data.message || 'Failed to get live preview status');
    }
    
    return data.success ? data.data : null;
  }

  /**
   * Start live preview
   */
  async startLivePreview(
    workspaceId: string,
    projectPath: string,
    framework: NodeFramework,
    port?: number,
    command?: string
  ): Promise<NodeLivePreview> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectPath,
        framework,
        port,
        command,
      }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to start live preview');
    }
    
    return data.data;
  }

  /**
   * Stop live preview
   */
  async stopLivePreview(workspaceId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/stop`, {
      method: 'POST',
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to stop live preview');
    }
  }

  /**
   * Get project structure
   */
  async getProjectStructure(workspaceId: string, projectPath?: string): Promise<any> {
    const params = new URLSearchParams();
    if (projectPath) params.set('path', projectPath);
    
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/structure?${params}`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to get project structure');
    }
    
    return data.data;
  }

  /**
   * Get environment variables
   */
  async getEnvironment(workspaceId: string): Promise<NodeEnvironment> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/environment`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to get environment');
    }
    
    return data.data;
  }

  /**
   * Update environment variables
   */
  async updateEnvironment(workspaceId: string, environment: Partial<NodeEnvironment>): Promise<NodeEnvironment> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/environment`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(environment),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to update environment');
    }
    
    return data.data;
  }

  /**
   * Execute command in workspace
   */
  async executeCommand(workspaceId: string, command: string, cwd?: string): Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
  }> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ command, cwd }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to execute command');
    }
    
    return data.data;
  }

  /**
   * Get workspace logs
   */
  async getLogs(workspaceId: string, tail?: number): Promise<string[]> {
    const params = new URLSearchParams();
    if (tail) params.set('tail', tail.toString());
    
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/logs?${params}`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to get logs');
    }
    
    return data.data.logs || [];
  }

  /**
   * Delete workspace
   */
  async deleteWorkspace(workspaceId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}`, {
      method: 'DELETE',
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to delete workspace');
    }
  }
}

// Export singleton instance for client-side use
export const nodeReactWorkspaceClientService = new NodeReactWorkspaceClientService();
