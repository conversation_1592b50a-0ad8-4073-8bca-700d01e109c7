import { ContainerInfo, WorkspaceInfo, CreateContainerOptions, CreateWorkspaceOptions } from '@/types/docker';

// Runtime check to ensure this is only used on the client side
if (typeof window === 'undefined') {
  console.warn('Client-side Docker API service is being imported on the server side. Use the server-side Docker service instead.');
}

/**
 * Client-side Docker API Service
 * Provides the same interface as the server-side Docker service but communicates via API calls
 */
export class ClientDockerAPIService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }

  /**
   * Make API call with error handling
   */
  private async apiCall(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok || !data.success) {
      throw new Error(data.message || data.error || 'API request failed');
    }

    return data;
  }

  /**
   * Test Docker connection
   */
  async ping(): Promise<boolean> {
    try {
      const response = await this.apiCall('/system');
      return response.data.connected;
    } catch (error) {
      console.error('Docker connection failed:', error);
      return false;
    }
  }

  /**
   * Get Docker system information
   */
  async getSystemInfo() {
    try {
      const response = await this.apiCall('/system');
      return response.data;
    } catch (error) {
      console.error('Failed to get Docker system info:', error);
      throw error;
    }
  }

  /**
   * List all containers
   */
  async listContainers(all: boolean = true): Promise<ContainerInfo[]> {
    try {
      const response = await this.apiCall(`/containers?all=${all}`);
      return response.data;
    } catch (error) {
      console.error('Failed to list containers:', error);
      throw error;
    }
  }

  /**
   * Get container details
   */
  async getContainer(containerId: string): Promise<ContainerInfo | null> {
    try {
      const response = await this.apiCall(`/containers/${containerId}`);
      return response.data;
    } catch (error) {
      if (error instanceof Error && error.message.includes('not found')) {
        return null;
      }
      console.error(`Failed to get container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new container
   */
  async createContainer(options: CreateContainerOptions): Promise<string> {
    try {
      const response = await this.apiCall('/containers', {
        method: 'POST',
        body: JSON.stringify(options),
      });
      return response.data.id;
    } catch (error) {
      console.error('Failed to create container:', error);
      throw error;
    }
  }

  /**
   * Start a container
   */
  async startContainer(containerId: string): Promise<void> {
    try {
      await this.apiCall(`/containers/${containerId}/start`, {
        method: 'POST',
      });
    } catch (error) {
      console.error(`Failed to start container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Stop a container
   */
  async stopContainer(containerId: string, timeout: number = 10): Promise<void> {
    try {
      await this.apiCall(`/containers/${containerId}/stop?timeout=${timeout}`, {
        method: 'POST',
      });
    } catch (error) {
      console.error(`Failed to stop container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Restart a container
   */
  async restartContainer(containerId: string): Promise<void> {
    try {
      await this.apiCall(`/containers/${containerId}/restart`, {
        method: 'POST',
      });
    } catch (error) {
      console.error(`Failed to restart container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a container
   */
  async removeContainer(containerId: string, force: boolean = false): Promise<void> {
    try {
      await this.apiCall(`/containers/${containerId}?force=${force}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Failed to remove container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Get container logs
   */
  async getContainerLogs(containerId: string, tail: number = 100): Promise<string> {
    try {
      const response = await this.apiCall(`/containers/${containerId}/logs?tail=${tail}`);
      return response.data.logs;
    } catch (error) {
      console.error(`Failed to get logs for container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Get container stats
   */
  async getContainerStats(containerId: string): Promise<any> {
    try {
      const response = await this.apiCall(`/containers/${containerId}/stats`);
      return response.data.stats;
    } catch (error) {
      console.error(`Failed to get stats for container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Pull an image
   */
  async pullImage(imageName: string, onProgress?: (progress: any) => void): Promise<void> {
    try {
      // Note: Progress callback is not supported in the API version
      // This would require WebSocket or Server-Sent Events for real-time progress
      await this.apiCall('/images', {
        method: 'POST',
        body: JSON.stringify({ image: imageName }),
      });
    } catch (error) {
      console.error(`Failed to pull image ${imageName}:`, error);
      throw error;
    }
  }

  /**
   * List available images
   */
  async listImages(): Promise<any[]> {
    try {
      const response = await this.apiCall('/images');
      return response.data;
    } catch (error) {
      console.error('Failed to list images:', error);
      throw error;
    }
  }

  /**
   * Execute command in container (simple interface)
   */
  async execInContainer(containerId: string, cmd: string[]): Promise<string> {
    try {
      const response = await this.apiCall(`/containers/${containerId}/exec`, {
        method: 'POST',
        body: JSON.stringify({ cmd }),
      });
      return response.data.output;
    } catch (error) {
      console.error(`Failed to execute command in container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Execute shell command in container with better interface
   */
  async executeCommand(
    containerId: string,
    command: string,
    workingDir?: string
  ): Promise<{ exitCode: number; stdout: string; stderr: string }> {
    try {
      const response = await this.apiCall(`/containers/${containerId}/execute`, {
        method: 'POST',
        body: JSON.stringify({ command, workingDir }),
      });
      
      return {
        exitCode: response.data.exitCode,
        stdout: response.data.stdout,
        stderr: response.data.stderr,
      };
    } catch (error) {
      console.error(`Failed to execute command in container ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new workspace container
   */
  async createWorkspace(options: CreateWorkspaceOptions): Promise<string> {
    try {
      const response = await this.apiCall('/workspaces', {
        method: 'POST',
        body: JSON.stringify(options),
      });
      return response.data.id;
    } catch (error) {
      console.error('Failed to create workspace:', error);
      throw error;
    }
  }

  /**
   * Get workspace containers only
   */
  async listWorkspaces(userId?: string): Promise<WorkspaceInfo[]> {
    try {
      const url = userId ? `/workspaces?userId=${userId}` : '/workspaces';
      const response = await this.apiCall(url);
      return response.data;
    } catch (error) {
      console.error('Failed to list workspaces:', error);
      throw error;
    }
  }

  /**
   * Get detailed workspace information
   */
  async getWorkspaceInfo(containerId: string): Promise<WorkspaceInfo | null> {
    try {
      const response = await this.apiCall(`/workspaces/${containerId}`);
      return response.data;
    } catch (error) {
      if (error instanceof Error && error.message.includes('not found')) {
        return null;
      }
      console.error(`Failed to get workspace info for ${containerId}:`, error);
      throw error;
    }
  }

  /**
   * Start a workspace
   */
  async startWorkspace(workspaceId: string): Promise<void> {
    try {
      await this.apiCall(`/workspaces/${workspaceId}?action=start`, {
        method: 'POST',
      });
    } catch (error) {
      console.error(`Failed to start workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  /**
   * Stop a workspace
   */
  async stopWorkspace(workspaceId: string): Promise<void> {
    try {
      await this.apiCall(`/workspaces/${workspaceId}?action=stop`, {
        method: 'POST',
      });
    } catch (error) {
      console.error(`Failed to stop workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  /**
   * Restart a workspace
   */
  async restartWorkspace(workspaceId: string): Promise<void> {
    try {
      await this.apiCall(`/workspaces/${workspaceId}?action=restart`, {
        method: 'POST',
      });
    } catch (error) {
      console.error(`Failed to restart workspace ${workspaceId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a workspace
   */
  async deleteWorkspace(workspaceId: string): Promise<void> {
    try {
      await this.apiCall(`/workspaces/${workspaceId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Failed to delete workspace ${workspaceId}:`, error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const clientDockerAPIService = new ClientDockerAPIService();
