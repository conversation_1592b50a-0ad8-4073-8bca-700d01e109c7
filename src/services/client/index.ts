/**
 * Client Services Index
 * 
 * Exports all client-safe services that only use API calls
 * and don't import any server-side dependencies.
 * 
 * These services are safe to use in client-side components
 * and won't cause Docker bundling issues.
 */

// Node/React Workspace Services
export {
  NodeReactWorkspaceClientService,
  nodeReactWorkspaceClientService
} from './node-react-workspace-client';

export {
  NodeReactPackageManagerClientService,
  nodeReactPackageManagerClientService,
  type PackageSearchResult,
  type InstallPackageOptions,
  type PackageInstallResult
} from './node-react-package-manager-client';

// Docker API Service
export {
  ClientDockerAPIService,
  clientDockerAPIService
} from './docker-api';

// Re-export types for convenience
export type {
  NodeProjectTemplate,
  NodeWorkspaceConfig,
  NodeEnvironment,
  NodeLivePreview,
  CreateNodeProjectRequest,
  CreateNodeProjectResponse,
  NodeWorkspaceStatus,
  NodeFramework,
  NodePackage,
  NodePackageManager
} from '@/types/node-react-workspace';
