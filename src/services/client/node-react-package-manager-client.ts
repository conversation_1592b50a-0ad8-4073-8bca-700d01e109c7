/**
 * Node/React Package Manager Client Service
 * Client-safe service that only uses API calls, no server-side dependencies
 */

import { NodePackage, NodePackageManager } from '@/types/node-react-workspace';

export interface PackageSearchResult {
  name: string;
  version: string;
  description: string;
  keywords: string[];
  author?: string;
  license?: string;
  homepage?: string;
  repository?: string;
  downloads?: number;
  lastPublished?: string;
  isPopular?: boolean;
  isRecommended?: boolean;
}

export interface InstallPackageOptions {
  version?: string;
  isDev?: boolean;
  exact?: boolean;
  save?: boolean;
}

export interface PackageInstallResult {
  success: boolean;
  packageName: string;
  version: string;
  installTime: number;
  dependencies?: string[];
  warnings?: string[];
  errors?: string[];
}

export class NodeReactPackageManagerClientService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/node-react-workspace') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get installed packages for a workspace
   */
  async getInstalledPackages(workspaceId: string): Promise<NodePackage[]> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch installed packages');
    }
    
    return data.data.packages || [];
  }

  /**
   * Search for packages in npm registry
   */
  async searchPackages(query: string, limit: number = 20): Promise<PackageSearchResult[]> {
    const params = new URLSearchParams({
      q: query,
      limit: limit.toString(),
    });
    
    const response = await fetch(`${this.baseUrl}/packages/search?${params}`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to search packages');
    }
    
    return data.data.results || [];
  }

  /**
   * Get package details
   */
  async getPackageDetails(packageName: string): Promise<PackageSearchResult | null> {
    const response = await fetch(`${this.baseUrl}/packages/${encodeURIComponent(packageName)}`);
    const data = await response.json();
    
    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(data.message || 'Failed to fetch package details');
    }
    
    return data.success ? data.data : null;
  }

  /**
   * Install a package
   */
  async installPackage(
    workspaceId: string,
    packageName: string,
    packageManager: NodePackageManager = 'npm',
    options: InstallPackageOptions = {}
  ): Promise<PackageInstallResult> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        packageName,
        packageManager,
        ...options,
      }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to install package');
    }
    
    return data.data;
  }

  /**
   * Uninstall a package
   */
  async uninstallPackage(
    workspaceId: string,
    packageName: string,
    packageManager: NodePackageManager = 'npm'
  ): Promise<{ success: boolean; packageName: string }> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages/${encodeURIComponent(packageName)}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ packageManager }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to uninstall package');
    }
    
    return data.data;
  }

  /**
   * Update a package
   */
  async updatePackage(
    workspaceId: string,
    packageName: string,
    packageManager: NodePackageManager = 'npm',
    version?: string
  ): Promise<PackageInstallResult> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages/${encodeURIComponent(packageName)}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        packageManager,
        version,
      }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to update package');
    }
    
    return data.data;
  }

  /**
   * Get package.json content
   */
  async getPackageJson(workspaceId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/package-json`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch package.json');
    }
    
    return data.data.packageJson;
  }

  /**
   * Update package.json
   */
  async updatePackageJson(workspaceId: string, packageJson: any): Promise<void> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/package-json`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ packageJson }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to update package.json');
    }
  }

  /**
   * Run npm/yarn/pnpm scripts
   */
  async runScript(
    workspaceId: string,
    scriptName: string,
    packageManager: NodePackageManager = 'npm'
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/scripts/${scriptName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ packageManager }),
    });
    
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to run script');
    }
    
    return data.data;
  }

  /**
   * Get available scripts from package.json
   */
  async getAvailableScripts(workspaceId: string): Promise<Record<string, string>> {
    const packageJson = await this.getPackageJson(workspaceId);
    return packageJson.scripts || {};
  }

  /**
   * Check for outdated packages
   */
  async checkOutdatedPackages(
    workspaceId: string,
    packageManager: NodePackageManager = 'npm'
  ): Promise<Array<{
    name: string;
    current: string;
    wanted: string;
    latest: string;
    location: string;
  }>> {
    const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages/outdated?packageManager=${packageManager}`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to check outdated packages');
    }
    
    return data.data.outdated || [];
  }

  /**
   * Get popular packages for a framework
   */
  async getPopularPackages(framework: string, category?: string): Promise<PackageSearchResult[]> {
    const params = new URLSearchParams({ framework });
    if (category) params.set('category', category);
    
    const response = await fetch(`${this.baseUrl}/packages/popular?${params}`);
    const data = await response.json();
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch popular packages');
    }
    
    return data.data.packages || [];
  }
}

// Export singleton instance for client-side use
export const nodeReactPackageManagerClientService = new NodeReactPackageManagerClientService();
