/**
 * File Synchronization Service
 * Handles cloud-based file storage and real-time synchronization for Python workspaces
 */

import { createHash } from 'crypto';
import { dockerService } from '../docker';

export interface SyncedFile {
  id: string;
  workspaceId: string;
  path: string;
  content: string;
  hash: string;
  size: number;
  mimeType: string;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  lastSyncedAt: Date;
  isDeleted: boolean;
  metadata: {
    language?: string;
    framework?: string;
    encoding: string;
    lineEndings: 'lf' | 'crlf' | 'cr';
  };
}

export interface FileConflict {
  id: string;
  fileId: string;
  workspaceId: string;
  localVersion: number;
  remoteVersion: number;
  localContent: string;
  remoteContent: string;
  conflictType: 'content' | 'delete' | 'rename';
  createdAt: Date;
  resolvedAt?: Date;
  resolution?: 'local' | 'remote' | 'merge';
}

export interface SyncOperation {
  id: string;
  workspaceId: string;
  type: 'upload' | 'download' | 'delete' | 'conflict';
  filePath: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  error?: string;
  startedAt: Date;
  completedAt?: Date;
}

export interface WorkspaceSnapshot {
  id: string;
  workspaceId: string;
  name: string;
  description?: string;
  files: SyncedFile[];
  createdAt: Date;
  createdBy: string;
  size: number;
  fileCount: number;
}

class FileSyncService {
  // In-memory storage for demo - replace with cloud storage in production
  private files: Map<string, SyncedFile> = new Map();
  private conflicts: Map<string, FileConflict> = new Map();
  private operations: Map<string, SyncOperation> = new Map();
  private snapshots: Map<string, WorkspaceSnapshot> = new Map();
  private workspaceFiles: Map<string, Set<string>> = new Map(); // workspaceId -> fileIds

  /**
   * Sync workspace files with cloud storage
   */
  async syncWorkspace(workspaceId: string): Promise<{
    uploaded: number;
    downloaded: number;
    conflicts: number;
    errors: string[];
  }> {
    try {
      const result = {
        uploaded: 0,
        downloaded: 0,
        conflicts: 0,
        errors: [] as string[],
      };

      // Get local files from workspace container
      const localFiles = await this.getWorkspaceFiles(workspaceId);
      
      // Get remote files from cloud storage
      const remoteFiles = this.getRemoteFiles(workspaceId);

      // Compare and sync files
      for (const localFile of localFiles) {
        try {
          const remoteFile = remoteFiles.find(f => f.path === localFile.path);
          
          if (!remoteFile) {
            // Upload new file
            await this.uploadFile(workspaceId, localFile);
            result.uploaded++;
          } else if (localFile.hash !== remoteFile.hash) {
            // Handle conflict or update
            if ((localFile.version || 0) > remoteFile.version) {
              await this.uploadFile(workspaceId, localFile);
              result.uploaded++;
            } else if ((localFile.version || 0) < remoteFile.version) {
              await this.downloadFile(workspaceId, remoteFile);
              result.downloaded++;
            } else {
              // Same version but different content - conflict
              await this.createConflict(workspaceId, localFile, remoteFile);
              result.conflicts++;
            }
          }
        } catch (error) {
          result.errors.push(`Error syncing ${localFile.path}: ${error}`);
        }
      }

      // Download remote files that don't exist locally
      for (const remoteFile of remoteFiles) {
        if (!localFiles.find(f => f.path === remoteFile.path) && !remoteFile.isDeleted) {
          try {
            await this.downloadFile(workspaceId, remoteFile);
            result.downloaded++;
          } catch (error) {
            result.errors.push(`Error downloading ${remoteFile.path}: ${error}`);
          }
        }
      }

      return result;
    } catch (error) {
      console.error('Error syncing workspace:', error);
      throw error;
    }
  }

  /**
   * Upload file to cloud storage
   */
  async uploadFile(workspaceId: string, file: Partial<SyncedFile>): Promise<SyncedFile> {
    try {
      const operation = this.createOperation(workspaceId, 'upload', file.path!);
      
      // Read file content from workspace if not provided
      let content = file.content;
      if (!content) {
        try {
          content = await dockerService.execInContainer(
            workspaceId,
            ['cat', file.path!]
          );
        } catch (error) {
          throw new Error(`Failed to read file: ${error instanceof Error ? error.message : String(error)}`);
        }
        content = content || '';
      }

      // Create or update synced file
      const syncedFile: SyncedFile = {
        id: file.id || this.generateId(),
        workspaceId,
        path: file.path!,
        content,
        hash: this.calculateHash(content),
        size: Buffer.byteLength(content, 'utf8'),
        mimeType: this.getMimeType(file.path!),
        version: (file.version || 0) + 1,
        createdAt: file.createdAt || new Date(),
        updatedAt: new Date(),
        lastSyncedAt: new Date(),
        isDeleted: false,
        metadata: {
          language: this.detectLanguage(file.path!),
          framework: this.detectFramework(content),
          encoding: 'utf8',
          lineEndings: this.detectLineEndings(content),
        },
      };

      // Store file
      this.files.set(syncedFile.id, syncedFile);
      
      // Update workspace file mapping
      let workspaceFileIds = this.workspaceFiles.get(workspaceId);
      if (!workspaceFileIds) {
        workspaceFileIds = new Set();
        this.workspaceFiles.set(workspaceId, workspaceFileIds);
      }
      workspaceFileIds.add(syncedFile.id);

      // Complete operation
      this.completeOperation(operation.id);

      return syncedFile;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  /**
   * Download file from cloud storage
   */
  async downloadFile(workspaceId: string, file: SyncedFile): Promise<void> {
    try {
      const operation = this.createOperation(workspaceId, 'download', file.path);

      // Write file to workspace container
      const escapedContent = file.content.replace(/'/g, "'\"'\"'");
      try {
        await dockerService.execInContainer(
          workspaceId,
          ['sh', '-c', `mkdir -p "$(dirname "${file.path}")" && echo '${escapedContent}' > "${file.path}"`]
        );
      } catch (error) {
        throw new Error(`Failed to write file: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Update last synced time
      file.lastSyncedAt = new Date();
      this.files.set(file.id, file);

      // Complete operation
      this.completeOperation(operation.id);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }

  /**
   * Delete file from cloud storage
   */
  async deleteFile(workspaceId: string, filePath: string): Promise<void> {
    try {
      const operation = this.createOperation(workspaceId, 'delete', filePath);

      // Find and mark file as deleted
      const file = Array.from(this.files.values()).find(
        f => f.workspaceId === workspaceId && f.path === filePath
      );

      if (file) {
        file.isDeleted = true;
        file.updatedAt = new Date();
        file.lastSyncedAt = new Date();
        this.files.set(file.id, file);
      }

      // Delete from workspace container
      try {
        await dockerService.execInContainer(workspaceId, ['rm', '-f', filePath]);
      } catch (error) {
        console.warn(`Failed to delete file in container: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Complete operation
      this.completeOperation(operation.id);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }

  /**
   * Create file conflict
   */
  async createConflict(
    workspaceId: string,
    localFile: Partial<SyncedFile>,
    remoteFile: SyncedFile
  ): Promise<FileConflict> {
    const conflict: FileConflict = {
      id: this.generateId(),
      fileId: remoteFile.id,
      workspaceId,
      localVersion: localFile.version || 0,
      remoteVersion: remoteFile.version,
      localContent: localFile.content || '',
      remoteContent: remoteFile.content,
      conflictType: 'content',
      createdAt: new Date(),
    };

    this.conflicts.set(conflict.id, conflict);
    return conflict;
  }

  /**
   * Resolve file conflict
   */
  async resolveConflict(
    conflictId: string,
    resolution: 'local' | 'remote' | 'merge',
    mergedContent?: string
  ): Promise<void> {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict) {
      throw new Error('Conflict not found');
    }

    let finalContent: string;
    
    switch (resolution) {
      case 'local':
        finalContent = conflict.localContent;
        break;
      case 'remote':
        finalContent = conflict.remoteContent;
        break;
      case 'merge':
        if (!mergedContent) {
          throw new Error('Merged content required for merge resolution');
        }
        finalContent = mergedContent;
        break;
    }

    // Update file with resolved content
    const file = this.files.get(conflict.fileId);
    if (file) {
      file.content = finalContent;
      file.hash = this.calculateHash(finalContent);
      file.version++;
      file.updatedAt = new Date();
      file.lastSyncedAt = new Date();
      this.files.set(file.id, file);

      // Write resolved content to workspace
      await this.downloadFile(conflict.workspaceId, file);
    }

    // Mark conflict as resolved
    conflict.resolvedAt = new Date();
    conflict.resolution = resolution;
    this.conflicts.set(conflictId, conflict);
  }

  /**
   * Create workspace snapshot
   */
  async createSnapshot(
    workspaceId: string,
    name: string,
    description: string,
    createdBy: string
  ): Promise<WorkspaceSnapshot> {
    const files = this.getRemoteFiles(workspaceId).filter(f => !f.isDeleted);
    
    const snapshot: WorkspaceSnapshot = {
      id: this.generateId(),
      workspaceId,
      name,
      description,
      files: [...files], // Deep copy
      createdAt: new Date(),
      createdBy,
      size: files.reduce((total, file) => total + file.size, 0),
      fileCount: files.length,
    };

    this.snapshots.set(snapshot.id, snapshot);
    return snapshot;
  }

  /**
   * Restore workspace from snapshot
   */
  async restoreSnapshot(workspaceId: string, snapshotId: string): Promise<void> {
    const snapshot = this.snapshots.get(snapshotId);
    if (!snapshot || snapshot.workspaceId !== workspaceId) {
      throw new Error('Snapshot not found');
    }

    // Clear current workspace files
    const currentFiles = this.getRemoteFiles(workspaceId);
    for (const file of currentFiles) {
      await this.deleteFile(workspaceId, file.path);
    }

    // Restore files from snapshot
    for (const file of snapshot.files) {
      const restoredFile: SyncedFile = {
        ...file,
        id: this.generateId(),
        version: file.version + 1,
        updatedAt: new Date(),
        lastSyncedAt: new Date(),
      };
      
      await this.downloadFile(workspaceId, restoredFile);
    }
  }

  /**
   * Get workspace files from container
   */
  private async getWorkspaceFiles(workspaceId: string): Promise<Partial<SyncedFile>[]> {
    try {
      // Get list of Python files in workspace
      let result: string;
      try {
        result = await dockerService.execInContainer(
          workspaceId,
          [
            'find',
            '/home/<USER>',
            '-type',
            'f',
            '\\(',
            '-name',
            '"*.py"',
            '-o',
            '-name',
            '"*.txt"',
            '-o',
            '-name',
            '"*.md"',
            '-o',
            '-name',
            '"*.json"',
            '-o',
            '-name',
            '"*.yml"',
            '-o',
            '-name',
            '"*.yaml"',
            '-o',
            '-name',
            '"*.toml"',
            '\\)',
            '-not',
            '-path',
            '"*/venv/*"',
            '-not',
            '-path',
            '"*/.git/*"',
            '-not',
            '-path',
            '"*/node_modules/*"'
          ]
        );
      } catch (error) {
        console.error('Failed to execute find command:', error);
        return [];
      }

      if (!result) {
        return [];
      }

      const filePaths = result.trim().split('\n').filter((path: string) => path.trim());
      const files: Partial<SyncedFile>[] = [];

      for (const filePath of filePaths) {
        try {
          // Read file content
          let content: string | undefined;
          try {
            content = await dockerService.execInContainer(
              workspaceId,
              ['cat', filePath]
            );
          } catch (error) {
            console.error(`Error reading file ${filePath}:`, error);
            continue;
          }

          if (content !== undefined) {
            const relativePath = filePath.replace('/home/<USER>/', '');
            
            files.push({
              path: relativePath,
              content,
              hash: this.calculateHash(content),
              size: Buffer.byteLength(content, 'utf8'),
              version: 1,
            });
          }
        } catch (error) {
          console.error(`Error reading file ${filePath}:`, error);
        }
      }

      return files;
    } catch (error) {
      console.error('Error getting workspace files:', error);
      return [];
    }
  }

  /**
   * Get remote files for workspace
   */
  private getRemoteFiles(workspaceId: string): SyncedFile[] {
    const fileIds = this.workspaceFiles.get(workspaceId);
    if (!fileIds) return [];

    return Array.from(fileIds)
      .map(id => this.files.get(id))
      .filter((file): file is SyncedFile => file !== undefined);
  }

  /**
   * Create sync operation
   */
  private createOperation(
    workspaceId: string,
    type: SyncOperation['type'],
    filePath: string
  ): SyncOperation {
    const operation: SyncOperation = {
      id: this.generateId(),
      workspaceId,
      type,
      filePath,
      status: 'pending',
      progress: 0,
      startedAt: new Date(),
    };

    this.operations.set(operation.id, operation);
    return operation;
  }

  /**
   * Complete sync operation
   */
  private completeOperation(operationId: string): void {
    const operation = this.operations.get(operationId);
    if (operation) {
      operation.status = 'completed';
      operation.progress = 100;
      operation.completedAt = new Date();
      this.operations.set(operationId, operation);
    }
  }

  /**
   * Calculate file hash
   */
  private calculateHash(content: string): string {
    return createHash('sha256').update(content, 'utf8').digest('hex');
  }

  /**
   * Get MIME type for file
   */
  private getMimeType(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      py: 'text/x-python',
      js: 'text/javascript',
      ts: 'text/typescript',
      json: 'application/json',
      md: 'text/markdown',
      txt: 'text/plain',
      yml: 'text/yaml',
      yaml: 'text/yaml',
      toml: 'text/toml',
      html: 'text/html',
      css: 'text/css',
    };
    return mimeTypes[ext || ''] || 'text/plain';
  }

  /**
   * Detect programming language
   */
  private detectLanguage(filePath: string): string | undefined {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const languages: Record<string, string> = {
      py: 'python',
      js: 'javascript',
      ts: 'typescript',
      html: 'html',
      css: 'css',
      md: 'markdown',
      json: 'json',
      yml: 'yaml',
      yaml: 'yaml',
      toml: 'toml',
    };
    return languages[ext || ''];
  }

  /**
   * Detect Python framework
   */
  private detectFramework(content: string): string | undefined {
    if (content.includes('from django') || content.includes('import django')) return 'django';
    if (content.includes('from flask') || content.includes('import flask')) return 'flask';
    if (content.includes('from fastapi') || content.includes('import fastapi')) return 'fastapi';
    if (content.includes('import streamlit') || content.includes('streamlit as st')) return 'streamlit';
    if (content.includes('import gradio') || content.includes('gradio as gr')) return 'gradio';
    return undefined;
  }

  /**
   * Detect line endings
   */
  private detectLineEndings(content: string): 'lf' | 'crlf' | 'cr' {
    if (content.includes('\r\n')) return 'crlf';
    if (content.includes('\r')) return 'cr';
    return 'lf';
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Get workspace conflicts
   */
  getWorkspaceConflicts(workspaceId: string): FileConflict[] {
    return Array.from(this.conflicts.values()).filter(
      conflict => conflict.workspaceId === workspaceId && !conflict.resolvedAt
    );
  }

  /**
   * Get workspace operations
   */
  getWorkspaceOperations(workspaceId: string): SyncOperation[] {
    return Array.from(this.operations.values()).filter(
      operation => operation.workspaceId === workspaceId
    );
  }

  /**
   * Get workspace snapshots
   */
  getWorkspaceSnapshots(workspaceId: string): WorkspaceSnapshot[] {
    return Array.from(this.snapshots.values()).filter(
      snapshot => snapshot.workspaceId === workspaceId
    );
  }
}

// Export singleton instance
export const fileSyncService = new FileSyncService();




