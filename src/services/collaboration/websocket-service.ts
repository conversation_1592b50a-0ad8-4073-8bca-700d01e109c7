/**
 * WebSocket Service for Real-time Collaboration
 * Handles real-time communication for Python workspace collaboration
 */

import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { PythonFramework } from '@/types/python-workspace';

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  cursor?: {
    line: number;
    column: number;
    file: string;
  };
  selection?: {
    start: { line: number; column: number };
    end: { line: number; column: number };
    file: string;
  };
  isActive: boolean;
  lastSeen: Date;
}

export interface CollaborationSession {
  id: string;
  workspaceId: string;
  users: Map<string, CollaborationUser>;
  files: Map<string, CollaborationFile>;
  createdAt: Date;
  lastActivity: Date;
}

export interface CollaborationFile {
  path: string;
  content: string;
  version: number;
  lastModified: Date;
  lockedBy?: string;
  operations: CollaborationOperation[];
}

export interface CollaborationOperation {
  id: string;
  type: 'insert' | 'delete' | 'replace';
  userId: string;
  timestamp: Date;
  position: {
    line: number;
    column: number;
  };
  content?: string;
  length?: number;
  file: string;
}

export interface CollaborationEvent {
  type: 'user_joined' | 'user_left' | 'cursor_moved' | 'selection_changed' | 
        'file_opened' | 'file_closed' | 'file_modified' | 'file_locked' | 'file_unlocked' |
        'code_executed' | 'preview_started' | 'preview_stopped' | 'package_installed' |
        'terminal_output' | 'chat_message';
  userId: string;
  workspaceId: string;
  timestamp: Date;
  data: any;
}

class WebSocketCollaborationService {
  private io: SocketIOServer | null = null;
  private sessions: Map<string, CollaborationSession> = new Map();
  private userSockets: Map<string, string> = new Map(); // userId -> socketId
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId

  /**
   * Initialize WebSocket server
   */
  initialize(server: HTTPServer): void {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      path: '/api/collaboration/socket.io',
    });

    this.setupEventHandlers();
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);

      // Handle user authentication and workspace joining
      socket.on('join_workspace', async (data: {
        userId: string;
        userName: string;
        userEmail: string;
        workspaceId: string;
        userAvatar?: string;
      }) => {
        try {
          await this.handleUserJoin(socket, data);
        } catch (error) {
          console.error('Error handling user join:', error);
          socket.emit('error', { message: 'Failed to join workspace' });
        }
      });

      // Handle user leaving workspace
      socket.on('leave_workspace', async (data: { workspaceId: string }) => {
        try {
          await this.handleUserLeave(socket, data.workspaceId);
        } catch (error) {
          console.error('Error handling user leave:', error);
        }
      });

      // Handle cursor movement
      socket.on('cursor_moved', (data: {
        workspaceId: string;
        file: string;
        line: number;
        column: number;
      }) => {
        this.handleCursorMove(socket, data);
      });

      // Handle text selection
      socket.on('selection_changed', (data: {
        workspaceId: string;
        file: string;
        start: { line: number; column: number };
        end: { line: number; column: number };
      }) => {
        this.handleSelectionChange(socket, data);
      });

      // Handle file operations
      socket.on('file_opened', (data: { workspaceId: string; file: string }) => {
        this.handleFileOpen(socket, data);
      });

      socket.on('file_closed', (data: { workspaceId: string; file: string }) => {
        this.handleFileClose(socket, data);
      });

      socket.on('file_modified', (data: {
        workspaceId: string;
        file: string;
        operations: CollaborationOperation[];
      }) => {
        this.handleFileModification(socket, data);
      });

      // Handle file locking
      socket.on('lock_file', (data: { workspaceId: string; file: string }) => {
        this.handleFileLock(socket, data);
      });

      socket.on('unlock_file', (data: { workspaceId: string; file: string }) => {
        this.handleFileUnlock(socket, data);
      });

      // Handle Python-specific events
      socket.on('code_executed', (data: {
        workspaceId: string;
        code: string;
        framework: PythonFramework;
        output: string;
        success: boolean;
      }) => {
        this.handleCodeExecution(socket, data);
      });

      socket.on('preview_event', (data: {
        workspaceId: string;
        type: 'started' | 'stopped' | 'restarted';
        framework: PythonFramework;
        port?: number;
        url?: string;
      }) => {
        this.handlePreviewEvent(socket, data);
      });

      socket.on('package_event', (data: {
        workspaceId: string;
        type: 'installed' | 'uninstalled';
        packageName: string;
        packageManager: string;
      }) => {
        this.handlePackageEvent(socket, data);
      });

      // Handle terminal events
      socket.on('terminal_input', (data: {
        workspaceId: string;
        command: string;
        terminalId?: string;
      }) => {
        this.handleTerminalInput(socket, data);
      });

      // Handle chat messages
      socket.on('chat_message', (data: {
        workspaceId: string;
        message: string;
        type: 'text' | 'code' | 'file';
        metadata?: any;
      }) => {
        this.handleChatMessage(socket, data);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
        this.handleDisconnection(socket);
      });
    });
  }

  /**
   * Handle user joining workspace
   */
  private async handleUserJoin(socket: any, data: {
    userId: string;
    userName: string;
    userEmail: string;
    workspaceId: string;
    userAvatar?: string;
  }): Promise<void> {
    const { userId, userName, userEmail, workspaceId, userAvatar } = data;

    // Get or create collaboration session
    let session = this.sessions.get(workspaceId);
    if (!session) {
      session = {
        id: workspaceId,
        workspaceId,
        users: new Map(),
        files: new Map(),
        createdAt: new Date(),
        lastActivity: new Date(),
      };
      this.sessions.set(workspaceId, session);
    }

    // Create or update user
    const user: CollaborationUser = {
      id: userId,
      name: userName,
      email: userEmail,
      avatar: userAvatar,
      isActive: true,
      lastSeen: new Date(),
    };

    session.users.set(userId, user);
    session.lastActivity = new Date();

    // Track socket mappings
    this.userSockets.set(userId, socket.id);
    this.socketUsers.set(socket.id, userId);

    // Join socket room
    socket.join(workspaceId);

    // Notify other users
    socket.to(workspaceId).emit('user_joined', {
      user,
      timestamp: new Date(),
    });

    // Send current session state to new user
    socket.emit('session_state', {
      users: Array.from(session.users.values()),
      files: Array.from(session.files.values()),
      timestamp: new Date(),
    });

    console.log(`User ${userName} joined workspace ${workspaceId}`);
  }

  /**
   * Handle user leaving workspace
   */
  private async handleUserLeave(socket: any, workspaceId: string): Promise<void> {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const session = this.sessions.get(workspaceId);
    if (!session) return;

    // Remove user from session
    session.users.delete(userId);
    session.lastActivity = new Date();

    // Clean up socket mappings
    this.userSockets.delete(userId);
    this.socketUsers.delete(socket.id);

    // Leave socket room
    socket.leave(workspaceId);

    // Notify other users
    socket.to(workspaceId).emit('user_left', {
      userId,
      timestamp: new Date(),
    });

    // Clean up empty sessions
    if (session.users.size === 0) {
      this.sessions.delete(workspaceId);
    }

    console.log(`User ${userId} left workspace ${workspaceId}`);
  }

  /**
   * Handle cursor movement
   */
  private handleCursorMove(socket: any, data: {
    workspaceId: string;
    file: string;
    line: number;
    column: number;
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const session = this.sessions.get(data.workspaceId);
    if (!session) return;

    const user = session.users.get(userId);
    if (!user) return;

    // Update user cursor position
    user.cursor = {
      line: data.line,
      column: data.column,
      file: data.file,
    };
    user.lastSeen = new Date();

    // Broadcast to other users
    socket.to(data.workspaceId).emit('cursor_moved', {
      userId,
      cursor: user.cursor,
      timestamp: new Date(),
    });
  }

  /**
   * Handle text selection change
   */
  private handleSelectionChange(socket: any, data: {
    workspaceId: string;
    file: string;
    start: { line: number; column: number };
    end: { line: number; column: number };
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const session = this.sessions.get(data.workspaceId);
    if (!session) return;

    const user = session.users.get(userId);
    if (!user) return;

    // Update user selection
    user.selection = {
      start: data.start,
      end: data.end,
      file: data.file,
    };
    user.lastSeen = new Date();

    // Broadcast to other users
    socket.to(data.workspaceId).emit('selection_changed', {
      userId,
      selection: user.selection,
      timestamp: new Date(),
    });
  }

  /**
   * Handle file opening
   */
  private handleFileOpen(socket: any, data: { workspaceId: string; file: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to other users
    socket.to(data.workspaceId).emit('file_opened', {
      userId,
      file: data.file,
      timestamp: new Date(),
    });
  }

  /**
   * Handle file closing
   */
  private handleFileClose(socket: any, data: { workspaceId: string; file: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to other users
    socket.to(data.workspaceId).emit('file_closed', {
      userId,
      file: data.file,
      timestamp: new Date(),
    });
  }

  /**
   * Handle file modification
   */
  private handleFileModification(socket: any, data: {
    workspaceId: string;
    file: string;
    operations: CollaborationOperation[];
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const session = this.sessions.get(data.workspaceId);
    if (!session) return;

    // Get or create file
    let file = session.files.get(data.file);
    if (!file) {
      file = {
        path: data.file,
        content: '',
        version: 0,
        lastModified: new Date(),
        operations: [],
      };
      session.files.set(data.file, file);
    }

    // Apply operations and update file
    file.operations.push(...data.operations);
    file.version++;
    file.lastModified = new Date();

    // Broadcast to other users
    socket.to(data.workspaceId).emit('file_modified', {
      userId,
      file: data.file,
      operations: data.operations,
      version: file.version,
      timestamp: new Date(),
    });
  }

  /**
   * Handle file locking
   */
  private handleFileLock(socket: any, data: { workspaceId: string; file: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const session = this.sessions.get(data.workspaceId);
    if (!session) return;

    let file = session.files.get(data.file);
    if (!file) {
      file = {
        path: data.file,
        content: '',
        version: 0,
        lastModified: new Date(),
        operations: [],
      };
      session.files.set(data.file, file);
    }

    // Lock file if not already locked
    if (!file.lockedBy) {
      file.lockedBy = userId;

      // Broadcast to other users
      socket.to(data.workspaceId).emit('file_locked', {
        userId,
        file: data.file,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Handle file unlocking
   */
  private handleFileUnlock(socket: any, data: { workspaceId: string; file: string }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    const session = this.sessions.get(data.workspaceId);
    if (!session) return;

    const file = session.files.get(data.file);
    if (!file) return;

    // Unlock file if locked by this user
    if (file.lockedBy === userId) {
      file.lockedBy = undefined;

      // Broadcast to other users
      socket.to(data.workspaceId).emit('file_unlocked', {
        userId,
        file: data.file,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Handle code execution
   */
  private handleCodeExecution(socket: any, data: {
    workspaceId: string;
    code: string;
    framework: PythonFramework;
    output: string;
    success: boolean;
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to other users
    socket.to(data.workspaceId).emit('code_executed', {
      userId,
      ...data,
      timestamp: new Date(),
    });
  }

  /**
   * Handle preview events
   */
  private handlePreviewEvent(socket: any, data: {
    workspaceId: string;
    type: 'started' | 'stopped' | 'restarted';
    framework: PythonFramework;
    port?: number;
    url?: string;
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to other users
    socket.to(data.workspaceId).emit('preview_event', {
      userId,
      ...data,
      timestamp: new Date(),
    });
  }

  /**
   * Handle package events
   */
  private handlePackageEvent(socket: any, data: {
    workspaceId: string;
    type: 'installed' | 'uninstalled';
    packageName: string;
    packageManager: string;
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to other users
    socket.to(data.workspaceId).emit('package_event', {
      userId,
      ...data,
      timestamp: new Date(),
    });
  }

  /**
   * Handle terminal input
   */
  private handleTerminalInput(socket: any, data: {
    workspaceId: string;
    command: string;
    terminalId?: string;
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to other users
    socket.to(data.workspaceId).emit('terminal_input', {
      userId,
      ...data,
      timestamp: new Date(),
    });
  }

  /**
   * Handle chat messages
   */
  private handleChatMessage(socket: any, data: {
    workspaceId: string;
    message: string;
    type: 'text' | 'code' | 'file';
    metadata?: any;
  }): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Broadcast to all users including sender
    this.io?.to(data.workspaceId).emit('chat_message', {
      userId,
      ...data,
      timestamp: new Date(),
    });
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(socket: any): void {
    const userId = this.socketUsers.get(socket.id);
    if (!userId) return;

    // Find and update user in all sessions
    for (const [workspaceId, session] of this.sessions.entries()) {
      const user = session.users.get(userId);
      if (user) {
        user.isActive = false;
        user.lastSeen = new Date();

        // Notify other users
        socket.to(workspaceId).emit('user_disconnected', {
          userId,
          timestamp: new Date(),
        });
      }
    }

    // Clean up socket mappings
    this.userSockets.delete(userId);
    this.socketUsers.delete(socket.id);
  }

  /**
   * Get active users in workspace
   */
  getWorkspaceUsers(workspaceId: string): CollaborationUser[] {
    const session = this.sessions.get(workspaceId);
    return session ? Array.from(session.users.values()) : [];
  }

  /**
   * Get workspace session
   */
  getWorkspaceSession(workspaceId: string): CollaborationSession | undefined {
    return this.sessions.get(workspaceId);
  }

  /**
   * Broadcast event to workspace
   */
  broadcastToWorkspace(workspaceId: string, event: string, data: any): void {
    this.io?.to(workspaceId).emit(event, data);
  }

  /**
   * Send event to specific user
   */
  sendToUser(userId: string, event: string, data: any): void {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      this.io?.to(socketId).emit(event, data);
    }
  }
}

// Export singleton instance
export const webSocketCollaborationService = new WebSocketCollaborationService();
