/**
 * Authentication Service for Python Workspace Collaboration
 * Handles secure authentication and authorization for workspace access
 */

import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { randomBytes } from 'crypto';

export interface WorkspaceUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  permissions: WorkspacePermission[];
  createdAt: Date;
  lastLogin?: Date;
  isActive: boolean;
}

export interface WorkspacePermission {
  resource: 'files' | 'terminal' | 'packages' | 'preview' | 'settings' | 'collaboration';
  actions: ('read' | 'write' | 'execute' | 'delete' | 'admin')[];
}

export interface WorkspaceInvitation {
  id: string;
  workspaceId: string;
  email: string;
  role: WorkspaceUser['role'];
  invitedBy: string;
  token: string;
  expiresAt: Date;
  acceptedAt?: Date;
  createdAt: Date;
}

export interface AuthToken {
  userId: string;
  workspaceId: string;
  role: string;
  permissions: WorkspacePermission[];
  iat: number;
  exp: number;
}

export interface SessionInfo {
  userId: string;
  workspaceId: string;
  user: WorkspaceUser;
  permissions: WorkspacePermission[];
  expiresAt: Date;
}

class WorkspaceAuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
  private readonly JWT_EXPIRES_IN = '24h';
  private readonly INVITATION_EXPIRES_IN = 7 * 24 * 60 * 60 * 1000; // 7 days

  // In-memory storage for demo - replace with database in production
  private users: Map<string, WorkspaceUser> = new Map();
  private invitations: Map<string, WorkspaceInvitation> = new Map();
  private workspaceUsers: Map<string, Set<string>> = new Map(); // workspaceId -> userIds
  private sessions: Map<string, SessionInfo> = new Map();

  /**
   * Initialize with default permissions
   */
  constructor() {
    this.initializeDefaultPermissions();
  }

  /**
   * Authenticate user and generate JWT token
   */
  async authenticateUser(
    email: string,
    password: string,
    workspaceId: string
  ): Promise<{ token: string; user: WorkspaceUser } | null> {
    try {
      // Find user by email
      const user = Array.from(this.users.values()).find(u => u.email === email);
      if (!user || !user.isActive) {
        return null;
      }

      // Check if user has access to workspace
      const workspaceUserIds = this.workspaceUsers.get(workspaceId);
      if (!workspaceUserIds || !workspaceUserIds.has(user.id)) {
        return null;
      }

      // In a real implementation, verify password hash
      // const isValidPassword = await bcrypt.compare(password, user.passwordHash);
      // For demo, we'll skip password verification

      // Update last login
      user.lastLogin = new Date();

      // Generate JWT token
      const token = this.generateToken(user, workspaceId);

      // Create session
      const sessionInfo: SessionInfo = {
        userId: user.id,
        workspaceId,
        user,
        permissions: user.permissions,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };
      this.sessions.set(token, sessionInfo);

      return { token, user };
    } catch (error) {
      console.error('Authentication error:', error);
      return null;
    }
  }

  /**
   * Verify JWT token and get session info
   */
  async verifyToken(token: string): Promise<SessionInfo | null> {
    try {
      // Check if session exists
      const session = this.sessions.get(token);
      if (!session) {
        return null;
      }

      // Check if session is expired
      if (session.expiresAt < new Date()) {
        this.sessions.delete(token);
        return null;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, this.JWT_SECRET) as AuthToken;
      
      // Ensure token matches session
      if (decoded.userId !== session.userId || decoded.workspaceId !== session.workspaceId) {
        return null;
      }

      return session;
    } catch (error) {
      console.error('Token verification error:', error);
      return null;
    }
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(
    user: WorkspaceUser,
    resource: WorkspacePermission['resource'],
    action: WorkspacePermission['actions'][0]
  ): boolean {
    // Owner has all permissions
    if (user.role === 'owner') {
      return true;
    }

    // Check specific permissions
    const permission = user.permissions.find(p => p.resource === resource);
    return permission ? permission.actions.includes(action) : false;
  }

  /**
   * Create workspace invitation
   */
  async createInvitation(
    workspaceId: string,
    email: string,
    role: WorkspaceUser['role'],
    invitedBy: string
  ): Promise<WorkspaceInvitation> {
    const invitation: WorkspaceInvitation = {
      id: this.generateId(),
      workspaceId,
      email,
      role,
      invitedBy,
      token: this.generateInvitationToken(),
      expiresAt: new Date(Date.now() + this.INVITATION_EXPIRES_IN),
      createdAt: new Date(),
    };

    this.invitations.set(invitation.id, invitation);
    return invitation;
  }

  /**
   * Accept workspace invitation
   */
  async acceptInvitation(
    invitationToken: string,
    userInfo: {
      name: string;
      email: string;
      password: string;
      avatar?: string;
    }
  ): Promise<{ user: WorkspaceUser; token: string } | null> {
    try {
      // Find invitation by token
      const invitation = Array.from(this.invitations.values()).find(
        inv => inv.token === invitationToken && inv.expiresAt > new Date()
      );

      if (!invitation || invitation.acceptedAt) {
        return null;
      }

      // Create or update user
      let user = Array.from(this.users.values()).find(u => u.email === userInfo.email);
      
      if (!user) {
        user = {
          id: this.generateId(),
          email: userInfo.email,
          name: userInfo.name,
          avatar: userInfo.avatar,
          role: invitation.role,
          permissions: this.getDefaultPermissions(invitation.role),
          createdAt: new Date(),
          isActive: true,
        };
        this.users.set(user.id, user);
      }

      // Add user to workspace
      let workspaceUserIds = this.workspaceUsers.get(invitation.workspaceId);
      if (!workspaceUserIds) {
        workspaceUserIds = new Set();
        this.workspaceUsers.set(invitation.workspaceId, workspaceUserIds);
      }
      workspaceUserIds.add(user.id);

      // Mark invitation as accepted
      invitation.acceptedAt = new Date();

      // Generate authentication token
      const token = this.generateToken(user, invitation.workspaceId);

      // Create session
      const sessionInfo: SessionInfo = {
        userId: user.id,
        workspaceId: invitation.workspaceId,
        user,
        permissions: user.permissions,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      };
      this.sessions.set(token, sessionInfo);

      return { user, token };
    } catch (error) {
      console.error('Error accepting invitation:', error);
      return null;
    }
  }

  /**
   * Get workspace users
   */
  getWorkspaceUsers(workspaceId: string): WorkspaceUser[] {
    const userIds = this.workspaceUsers.get(workspaceId);
    if (!userIds) return [];

    return Array.from(userIds)
      .map(userId => this.users.get(userId))
      .filter((user): user is WorkspaceUser => user !== undefined && user.isActive);
  }

  /**
   * Update user role and permissions
   */
  async updateUserRole(
    workspaceId: string,
    userId: string,
    newRole: WorkspaceUser['role'],
    updatedBy: string
  ): Promise<boolean> {
    try {
      const user = this.users.get(userId);
      if (!user) return false;

      // Check if user is in workspace
      const workspaceUserIds = this.workspaceUsers.get(workspaceId);
      if (!workspaceUserIds || !workspaceUserIds.has(userId)) {
        return false;
      }

      // Update role and permissions
      user.role = newRole;
      user.permissions = this.getDefaultPermissions(newRole);

      return true;
    } catch (error) {
      console.error('Error updating user role:', error);
      return false;
    }
  }

  /**
   * Remove user from workspace
   */
  async removeUserFromWorkspace(workspaceId: string, userId: string): Promise<boolean> {
    try {
      const workspaceUserIds = this.workspaceUsers.get(workspaceId);
      if (!workspaceUserIds) return false;

      workspaceUserIds.delete(userId);

      // Invalidate user sessions for this workspace
      for (const [token, session] of this.sessions.entries()) {
        if (session.userId === userId && session.workspaceId === workspaceId) {
          this.sessions.delete(token);
        }
      }

      return true;
    } catch (error) {
      console.error('Error removing user from workspace:', error);
      return false;
    }
  }

  /**
   * Revoke session
   */
  revokeSession(token: string): boolean {
    return this.sessions.delete(token);
  }

  /**
   * Get active sessions for workspace
   */
  getActiveSessions(workspaceId: string): SessionInfo[] {
    return Array.from(this.sessions.values()).filter(
      session => session.workspaceId === workspaceId && session.expiresAt > new Date()
    );
  }

  /**
   * Generate JWT token
   */
  private generateToken(user: WorkspaceUser, workspaceId: string): string {
    const payload: Omit<AuthToken, 'iat' | 'exp'> = {
      userId: user.id,
      workspaceId,
      role: user.role,
      permissions: user.permissions,
    };

    return jwt.sign(payload, this.JWT_SECRET, { expiresIn: this.JWT_EXPIRES_IN });
  }

  /**
   * Generate invitation token
   */
  private generateInvitationToken(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return randomBytes(16).toString('hex');
  }

  /**
   * Get default permissions for role
   */
  private getDefaultPermissions(role: WorkspaceUser['role']): WorkspacePermission[] {
    const permissions: Record<WorkspaceUser['role'], WorkspacePermission[]> = {
      owner: [
        { resource: 'files', actions: ['read', 'write', 'execute', 'delete', 'admin'] },
        { resource: 'terminal', actions: ['read', 'write', 'execute', 'admin'] },
        { resource: 'packages', actions: ['read', 'write', 'execute', 'admin'] },
        { resource: 'preview', actions: ['read', 'write', 'execute', 'admin'] },
        { resource: 'settings', actions: ['read', 'write', 'admin'] },
        { resource: 'collaboration', actions: ['read', 'write', 'admin'] },
      ],
      admin: [
        { resource: 'files', actions: ['read', 'write', 'execute', 'delete'] },
        { resource: 'terminal', actions: ['read', 'write', 'execute'] },
        { resource: 'packages', actions: ['read', 'write', 'execute'] },
        { resource: 'preview', actions: ['read', 'write', 'execute'] },
        { resource: 'settings', actions: ['read', 'write'] },
        { resource: 'collaboration', actions: ['read', 'write'] },
      ],
      editor: [
        { resource: 'files', actions: ['read', 'write', 'execute'] },
        { resource: 'terminal', actions: ['read', 'write', 'execute'] },
        { resource: 'packages', actions: ['read', 'write'] },
        { resource: 'preview', actions: ['read', 'write', 'execute'] },
        { resource: 'settings', actions: ['read'] },
        { resource: 'collaboration', actions: ['read', 'write'] },
      ],
      viewer: [
        { resource: 'files', actions: ['read'] },
        { resource: 'terminal', actions: ['read'] },
        { resource: 'packages', actions: ['read'] },
        { resource: 'preview', actions: ['read'] },
        { resource: 'settings', actions: ['read'] },
        { resource: 'collaboration', actions: ['read'] },
      ],
    };

    return permissions[role];
  }

  /**
   * Initialize default permissions
   */
  private initializeDefaultPermissions(): void {
    // Create a default owner user for demo
    const defaultUser: WorkspaceUser = {
      id: 'default-owner',
      email: '<EMAIL>',
      name: 'Workspace Owner',
      role: 'owner',
      permissions: this.getDefaultPermissions('owner'),
      createdAt: new Date(),
      isActive: true,
    };

    this.users.set(defaultUser.id, defaultUser);

    // Add to default workspace
    const defaultWorkspaceId = 'default-workspace';
    this.workspaceUsers.set(defaultWorkspaceId, new Set([defaultUser.id]));
  }
}

// Export singleton instance
export const workspaceAuthService = new WorkspaceAuthService();
