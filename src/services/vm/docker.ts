/**
 * VM Docker Operations Service
 * Manages Docker operations through VM connections including container management, image operations, and Docker daemon communication
 */

import { BaseVMService, VMServiceResult } from './base';
import { VMConnectionService } from './connection';
import { vm<PERSON><PERSON><PERSON>, VM_SERVER_CONFIG } from '@/lib/vm-server';
import {
  VMConnectionConfig,
  VMDockerInfo,
  VMContainerOperation,
  VMOperationResult
} from '@/types/vm';

// Docker container information
export interface DockerContainer {
  id: string;
  name: string;
  image: string;
  status: 'created' | 'running' | 'paused' | 'restarting' | 'removing' | 'exited' | 'dead';
  state: string;
  ports: Array<{
    privatePort: number;
    publicPort?: number;
    type: 'tcp' | 'udp';
  }>;
  mounts: Array<{
    source: string;
    destination: string;
    mode: string;
    rw: boolean;
  }>;
  networks: Record<string, any>;
  created: Date;
  startedAt?: Date;
  finishedAt?: Date;
}

// Docker image information
export interface DockerImage {
  id: string;
  repository: string;
  tag: string;
  digest: string;
  created: Date;
  size: number;
  virtualSize: number;
  labels: Record<string, string>;
}

// Docker network information
export interface DockerNetwork {
  id: string;
  name: string;
  driver: string;
  scope: 'local' | 'global' | 'swarm';
  internal: boolean;
  attachable: boolean;
  ingress: boolean;
  ipam: {
    driver: string;
    config: Array<{
      subnet: string;
      gateway: string;
    }>;
  };
  containers: Record<string, any>;
  created: Date;
}

// Docker volume information
export interface DockerVolume {
  name: string;
  driver: string;
  mountpoint: string;
  scope: 'local' | 'global';
  labels: Record<string, string>;
  options: Record<string, string>;
  created: Date;
}

// Container creation options
export interface ContainerCreateOptions {
  name?: string;
  image: string;
  command?: string[];
  entrypoint?: string[];
  workingDir?: string;
  environment?: Record<string, string>;
  ports?: Record<string, string>;
  volumes?: Record<string, string>;
  networks?: string[];
  labels?: Record<string, string>;
  restartPolicy?: 'no' | 'always' | 'unless-stopped' | 'on-failure';
  memory?: number; // in MB
  cpu?: number; // CPU shares
  privileged?: boolean;
  user?: string;
  hostname?: string;
  domainname?: string;
  attachStdin?: boolean;
  attachStdout?: boolean;
  attachStderr?: boolean;
  tty?: boolean;
  openStdin?: boolean;
  stdinOnce?: boolean;
  autoRemove?: boolean;
}

// Docker operations service
export class VMDockerService extends BaseVMService {
  private connectionService: VMConnectionService;

  constructor(vmId: string, connectionConfig: VMConnectionConfig) {
    super('VMDockerService', vmId, connectionConfig);
    this.connectionService = new VMConnectionService(vmId, connectionConfig);
  }

  // Get Docker system information
  async getDockerInfo(connectionId: string): Promise<VMServiceResult<VMDockerInfo>> {
    return this.executeVMOperation('getDockerInfo', async () => {
      const command = 'docker system info --format "{{json .}}"';
      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to get Docker system information');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker info command failed: ${result.data.stderr}`);
      }

      try {
        const dockerInfo = JSON.parse(result.data.stdout) as VMDockerInfo;
        return dockerInfo;
      } catch (error) {
        throw new Error(`Failed to parse Docker info: ${error}`);
      }
    }, true, connectionId);
  }

  // List Docker containers
  async listContainers(
    connectionId: string,
    options: {
      all?: boolean;
      filters?: Record<string, string>;
      limit?: number;
    } = {}
  ): Promise<VMServiceResult<DockerContainer[]>> {
    return this.executeVMOperation('listContainers', async () => {
      let command = 'docker ps --format "{{json .}}" --no-trunc';
      
      if (options.all) {
        command += ' --all';
      }

      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          command += ` --filter "${key}=${value}"`;
        });
      }

      if (options.limit) {
        command += ` --last ${options.limit}`;
      }

      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to list Docker containers');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker ps command failed: ${result.data.stderr}`);
      }

      // Parse JSON lines
      const containers: DockerContainer[] = [];
      const lines = result.data.stdout.trim().split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const containerData = JSON.parse(line);
          containers.push(this.parseContainerData(containerData));
        } catch (error) {
          vmLogger.warn(`Failed to parse container data: ${line}`, error);
        }
      }

      return containers;
    }, true, connectionId);
  }

  // Create Docker container
  async createContainer(
    connectionId: string,
    options: ContainerCreateOptions
  ): Promise<VMServiceResult<{ containerId: string; warnings: string[] }>> {
    return this.executeVMOperation('createContainer', async () => {
      this.validateRequired(options, ['image']);

      const command = this.buildCreateCommand(options);
      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to create Docker container');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker create command failed: ${result.data.stderr}`);
      }

      const containerId = result.data.stdout.trim();
      const warnings = result.data.stderr ? [result.data.stderr] : [];

      vmLogger.info(`Docker container created: ${containerId}`, {
        vmId: this.vmId,
        connectionId,
        image: options.image,
        name: options.name
      });

      return { containerId, warnings };
    }, true, connectionId);
  }

  // Start Docker container
  async startContainer(connectionId: string, containerId: string): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('startContainer', async () => {
      const command = `docker start ${containerId}`;
      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to start Docker container');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker start command failed: ${result.data.stderr}`);
      }

      vmLogger.info(`Docker container started: ${containerId}`, {
        vmId: this.vmId,
        connectionId
      });

      return true;
    }, true, connectionId);
  }

  // Stop Docker container
  async stopContainer(
    connectionId: string,
    containerId: string,
    timeout: number = 10
  ): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('stopContainer', async () => {
      const command = `docker stop --time ${timeout} ${containerId}`;
      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to stop Docker container');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker stop command failed: ${result.data.stderr}`);
      }

      vmLogger.info(`Docker container stopped: ${containerId}`, {
        vmId: this.vmId,
        connectionId,
        timeout
      });

      return true;
    }, true, connectionId);
  }

  // Remove Docker container
  async removeContainer(
    connectionId: string,
    containerId: string,
    options: {
      force?: boolean;
      removeVolumes?: boolean;
      removeLinks?: boolean;
    } = {}
  ): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('removeContainer', async () => {
      let command = `docker rm ${containerId}`;
      
      if (options.force) {
        command += ' --force';
      }
      
      if (options.removeVolumes) {
        command += ' --volumes';
      }
      
      if (options.removeLinks) {
        command += ' --link';
      }

      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to remove Docker container');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker rm command failed: ${result.data.stderr}`);
      }

      vmLogger.info(`Docker container removed: ${containerId}`, {
        vmId: this.vmId,
        connectionId,
        options
      });

      return true;
    }, true, connectionId);
  }

  // Execute command in container
  async execInContainer(
    connectionId: string,
    containerId: string,
    command: string[],
    options: {
      interactive?: boolean;
      tty?: boolean;
      user?: string;
      workingDir?: string;
      environment?: Record<string, string>;
    } = {}
  ): Promise<VMServiceResult<{ stdout: string; stderr: string; exitCode: number }>> {
    return this.executeVMOperation('execInContainer', async () => {
      let execCommand = 'docker exec';
      
      if (options.interactive) {
        execCommand += ' --interactive';
      }
      
      if (options.tty) {
        execCommand += ' --tty';
      }
      
      if (options.user) {
        execCommand += ` --user "${options.user}"`;
      }
      
      if (options.workingDir) {
        execCommand += ` --workdir "${options.workingDir}"`;
      }
      
      if (options.environment) {
        Object.entries(options.environment).forEach(([key, value]) => {
          execCommand += ` --env "${key}=${value}"`;
        });
      }

      execCommand += ` ${containerId} ${command.join(' ')}`;

      const result = await this.connectionService.executeCommand(connectionId, execCommand);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to execute command in Docker container');
      }

      return {
        stdout: result.data.stdout,
        stderr: result.data.stderr,
        exitCode: result.data.exitCode
      };
    }, true, connectionId);
  }

  // Get container logs
  async getContainerLogs(
    connectionId: string,
    containerId: string,
    options: {
      follow?: boolean;
      tail?: number;
      since?: string;
      until?: string;
      timestamps?: boolean;
    } = {}
  ): Promise<VMServiceResult<string>> {
    return this.executeVMOperation('getContainerLogs', async () => {
      let command = `docker logs ${containerId}`;
      
      if (options.follow) {
        command += ' --follow';
      }
      
      if (options.tail) {
        command += ` --tail ${options.tail}`;
      }
      
      if (options.since) {
        command += ` --since "${options.since}"`;
      }
      
      if (options.until) {
        command += ` --until "${options.until}"`;
      }
      
      if (options.timestamps) {
        command += ' --timestamps';
      }

      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to get Docker container logs');
      }

      if (result.data.exitCode !== 0) {
        throw new Error(`Docker logs command failed: ${result.data.stderr}`);
      }

      return result.data.stdout;
    }, true, connectionId);
  }

  // Health check implementation
  async healthCheck(): Promise<boolean> {
    try {
      const connectResult = await this.connectionService.connect();
      if (!connectResult.success || !connectResult.data) {
        return false;
      }

      const dockerInfoResult = await this.getDockerInfo(connectResult.data.connectionId);
      await this.connectionService.disconnect(connectResult.data.connectionId);

      return dockerInfoResult.success;
    } catch (error) {
      vmLogger.error(`VM Docker service health check failed for ${this.vmId}`, error);
      return false;
    }
  }

  // Private helper methods
  private parseContainerData(data: any): DockerContainer {
    return {
      id: data.ID || data.Id,
      name: data.Names || data.Name,
      image: data.Image,
      status: this.normalizeContainerStatus(data.Status || data.State),
      state: data.State || 'unknown',
      ports: this.parsePorts(data.Ports || []),
      mounts: this.parseMounts(data.Mounts || []),
      networks: data.Networks || {},
      created: new Date(data.Created || data.CreatedAt),
      startedAt: data.StartedAt ? new Date(data.StartedAt) : undefined,
      finishedAt: data.FinishedAt ? new Date(data.FinishedAt) : undefined,
    };
  }

  private normalizeContainerStatus(status: string): DockerContainer['status'] {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('running')) return 'running';
    if (statusLower.includes('exited')) return 'exited';
    if (statusLower.includes('created')) return 'created';
    if (statusLower.includes('paused')) return 'paused';
    if (statusLower.includes('restarting')) return 'restarting';
    if (statusLower.includes('removing')) return 'removing';
    if (statusLower.includes('dead')) return 'dead';
    return 'exited';
  }

  private parsePorts(ports: any[]): DockerContainer['ports'] {
    return ports.map(port => ({
      privatePort: port.PrivatePort || port.privatePort,
      publicPort: port.PublicPort || port.publicPort,
      type: (port.Type || port.type || 'tcp') as 'tcp' | 'udp',
    }));
  }

  private parseMounts(mounts: any[]): DockerContainer['mounts'] {
    return mounts.map(mount => ({
      source: mount.Source || mount.source,
      destination: mount.Destination || mount.destination,
      mode: mount.Mode || mount.mode || 'rw',
      rw: mount.RW !== false && mount.rw !== false,
    }));
  }

  private buildCreateCommand(options: ContainerCreateOptions): string {
    let command = 'docker create';

    if (options.name) {
      command += ` --name "${options.name}"`;
    }

    if (options.hostname) {
      command += ` --hostname "${options.hostname}"`;
    }

    if (options.domainname) {
      command += ` --domainname "${options.domainname}"`;
    }

    if (options.user) {
      command += ` --user "${options.user}"`;
    }

    if (options.workingDir) {
      command += ` --workdir "${options.workingDir}"`;
    }

    if (options.environment) {
      Object.entries(options.environment).forEach(([key, value]) => {
        command += ` --env "${key}=${value}"`;
      });
    }

    if (options.ports) {
      Object.entries(options.ports).forEach(([hostPort, containerPort]) => {
        command += ` --publish "${hostPort}:${containerPort}"`;
      });
    }

    if (options.volumes) {
      Object.entries(options.volumes).forEach(([hostPath, containerPath]) => {
        command += ` --volume "${hostPath}:${containerPath}"`;
      });
    }

    if (options.networks) {
      options.networks.forEach(network => {
        command += ` --network "${network}"`;
      });
    }

    if (options.labels) {
      Object.entries(options.labels).forEach(([key, value]) => {
        command += ` --label "${key}=${value}"`;
      });
    }

    if (options.restartPolicy) {
      command += ` --restart "${options.restartPolicy}"`;
    }

    if (options.memory) {
      command += ` --memory "${options.memory}m"`;
    }

    if (options.cpu) {
      command += ` --cpu-shares ${options.cpu}`;
    }

    if (options.privileged) {
      command += ' --privileged';
    }

    if (options.tty) {
      command += ' --tty';
    }

    if (options.attachStdin) {
      command += ' --attach stdin';
    }

    if (options.attachStdout) {
      command += ' --attach stdout';
    }

    if (options.attachStderr) {
      command += ' --attach stderr';
    }

    if (options.openStdin) {
      command += ' --interactive';
    }

    if (options.autoRemove) {
      command += ' --rm';
    }

    command += ` "${options.image}"`;

    if (options.command && options.command.length > 0) {
      command += ` ${options.command.join(' ')}`;
    }

    return command;
  }

  // Cleanup
  async cleanup(): Promise<void> {
    await this.connectionService.cleanup();
    vmLogger.info(`VM Docker service cleaned up for ${this.vmId}`);
  }
}
