/**
 * VM Security Service
 * Handles authentication, authorization, secure credential management, and access control for VM operations
 */

import { BaseVMService, VMServiceResult } from './base';
import { vmLogger, VM_SERVER_CONFIG, VMServerError } from '@/lib/vm-server';
import { createHash, randomBytes, pbkdf2Sync } from 'crypto';
import {
  VMConnectionConfig,
  VMAuthCredentials,
  VMSecurityConfig,
  VMSession,
  FirewallRule
} from '@/types/vm';

// User permission levels
export type VMPermissionLevel = 'read' | 'write' | 'admin' | 'owner';

// Access control entry
export interface VMAccessControlEntry {
  userId: string;
  username: string;
  permissions: VMPermissionLevel[];
  allowedCommands: string[];
  restrictedPaths: string[];
  ipWhitelist?: string[];
  sessionTimeout: number;
  maxConcurrentSessions: number;
  createdAt: Date;
  expiresAt?: Date;
  isActive: boolean;
}

// Authentication token
export interface VMAuthToken {
  token: string;
  userId: string;
  vmId: string;
  permissions: VMPermissionLevel[];
  issuedAt: Date;
  expiresAt: Date;
  sessionId?: string;
}

// Audit log entry
export interface VMAuditLogEntry {
  id: string;
  userId: string;
  vmId: string;
  action: string;
  resource: string;
  timestamp: Date;
  success: boolean;
  ipAddress?: string;
  userAgent?: string;
  details?: Record<string, any>;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

// Security event
export interface VMSecurityEvent {
  id: string;
  type: 'authentication_failure' | 'unauthorized_access' | 'suspicious_activity' | 'policy_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  vmId: string;
  description: string;
  timestamp: Date;
  ipAddress?: string;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

// VM Security Service
export class VMSecurityService extends BaseVMService {
  private accessControlList: Map<string, VMAccessControlEntry> = new Map();
  private activeTokens: Map<string, VMAuthToken> = new Map();
  private auditLog: VMAuditLogEntry[] = [];
  private securityEvents: VMSecurityEvent[] = [];
  private failedAttempts: Map<string, { count: number; lastAttempt: Date }> = new Map();

  constructor(vmId: string, connectionConfig: VMConnectionConfig) {
    super('VMSecurityService', vmId, connectionConfig);
  }

  // Create access control entry for user
  async createAccessControl(
    userId: string,
    username: string,
    permissions: VMPermissionLevel[],
    options: {
      allowedCommands?: string[];
      restrictedPaths?: string[];
      ipWhitelist?: string[];
      sessionTimeout?: number;
      maxConcurrentSessions?: number;
      expiresAt?: Date;
    } = {}
  ): Promise<VMServiceResult<VMAccessControlEntry>> {
    return this.executeVMOperation('createAccessControl', async () => {
      this.validateRequired({ userId, username, permissions }, ['userId', 'username', 'permissions']);

      if (this.accessControlList.has(userId)) {
        throw new VMServerError(
          `Access control entry already exists for user ${userId}`,
          'ACCESS_CONTROL_EXISTS',
          'validation_error',
          409,
          this.vmId
        );
      }

      const entry: VMAccessControlEntry = {
        userId,
        username,
        permissions,
        allowedCommands: options.allowedCommands || VM_SERVER_CONFIG.security.allowedCommands,
        restrictedPaths: options.restrictedPaths || VM_SERVER_CONFIG.security.restrictedPaths,
        ipWhitelist: options.ipWhitelist,
        sessionTimeout: options.sessionTimeout || VM_SERVER_CONFIG.security.sessionTimeout,
        maxConcurrentSessions: options.maxConcurrentSessions || VM_SERVER_CONFIG.security.maxConcurrentConnections,
        createdAt: new Date(),
        expiresAt: options.expiresAt,
        isActive: true
      };

      this.accessControlList.set(userId, entry);

      await this.logAuditEvent(userId, 'create_access_control', 'access_control', true, {
        permissions,
        username
      });

      vmLogger.info(`Access control created for user ${userId} on VM ${this.vmId}`, {
        permissions,
        username
      });

      return entry;
    });
  }

  // Authenticate user and generate token
  async authenticateUser(
    userId: string,
    credentials: VMAuthCredentials,
    clientInfo: {
      ipAddress?: string;
      userAgent?: string;
    } = {}
  ): Promise<VMServiceResult<VMAuthToken>> {
    return this.executeVMOperation('authenticateUser', async () => {
      this.validateRequired({ userId, credentials }, ['userId', 'credentials']);

      // Check if user has access control entry
      const accessControl = this.accessControlList.get(userId);
      if (!accessControl || !accessControl.isActive) {
        await this.recordFailedAttempt(userId, clientInfo.ipAddress);
        throw new VMServerError(
          'User not authorized for this VM',
          'USER_NOT_AUTHORIZED',
          'auth_error',
          403,
          this.vmId
        );
      }

      // Check if access has expired
      if (accessControl.expiresAt && accessControl.expiresAt < new Date()) {
        await this.recordSecurityEvent('authentication_failure', 'medium', userId, 'Access expired');
        throw new VMServerError(
          'User access has expired',
          'ACCESS_EXPIRED',
          'auth_error',
          403,
          this.vmId
        );
      }

      // Check IP whitelist
      if (accessControl.ipWhitelist && clientInfo.ipAddress) {
        const isAllowed = accessControl.ipWhitelist.some(ip => 
          this.isIpInRange(clientInfo.ipAddress!, ip)
        );
        if (!isAllowed) {
          await this.recordSecurityEvent('unauthorized_access', 'high', userId, 
            `Access from unauthorized IP: ${clientInfo.ipAddress}`);
          throw new VMServerError(
            'Access denied from this IP address',
            'IP_NOT_ALLOWED',
            'auth_error',
            403,
            this.vmId
          );
        }
      }

      // Check failed attempts
      const failedAttempts = this.failedAttempts.get(userId);
      if (failedAttempts && failedAttempts.count >= 5) {
        const timeSinceLastAttempt = Date.now() - failedAttempts.lastAttempt.getTime();
        if (timeSinceLastAttempt < 300000) { // 5 minutes
          await this.recordSecurityEvent('authentication_failure', 'high', userId, 
            'Too many failed attempts');
          throw new VMServerError(
            'Too many failed attempts. Please try again later.',
            'TOO_MANY_ATTEMPTS',
            'auth_error',
            429,
            this.vmId
          );
        }
      }

      // Validate credentials (simplified - in production, use proper authentication)
      const isValidCredentials = await this.validateCredentials(credentials);
      if (!isValidCredentials) {
        await this.recordFailedAttempt(userId, clientInfo.ipAddress);
        throw new VMServerError(
          'Invalid credentials',
          'INVALID_CREDENTIALS',
          'auth_error',
          401,
          this.vmId
        );
      }

      // Clear failed attempts on successful authentication
      this.failedAttempts.delete(userId);

      // Generate authentication token
      const token = this.generateSecureToken();
      const authToken: VMAuthToken = {
        token,
        userId,
        vmId: this.vmId,
        permissions: accessControl.permissions,
        issuedAt: new Date(),
        expiresAt: new Date(Date.now() + accessControl.sessionTimeout)
      };

      this.activeTokens.set(token, authToken);

      await this.logAuditEvent(userId, 'authenticate', 'authentication', true, {
        ipAddress: clientInfo.ipAddress,
        userAgent: clientInfo.userAgent
      });

      vmLogger.info(`User ${userId} authenticated successfully for VM ${this.vmId}`, {
        permissions: accessControl.permissions
      });

      return authToken;
    });
  }

  // Validate authentication token
  async validateToken(token: string): Promise<VMServiceResult<VMAuthToken>> {
    return this.executeVMOperation('validateToken', async () => {
      const authToken = this.activeTokens.get(token);
      if (!authToken) {
        throw new VMServerError(
          'Invalid or expired token',
          'INVALID_TOKEN',
          'auth_error',
          401,
          this.vmId
        );
      }

      if (authToken.expiresAt < new Date()) {
        this.activeTokens.delete(token);
        throw new VMServerError(
          'Token has expired',
          'TOKEN_EXPIRED',
          'auth_error',
          401,
          this.vmId
        );
      }

      return authToken;
    });
  }

  // Check if user has permission for specific action
  async checkPermission(
    token: string,
    action: string,
    resource?: string
  ): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('checkPermission', async () => {
      const tokenResult = await this.validateToken(token);
      if (!tokenResult.success || !tokenResult.data) {
        return false;
      }

      const authToken = tokenResult.data;
      const accessControl = this.accessControlList.get(authToken.userId);
      if (!accessControl) {
        return false;
      }

      // Check permission level
      const hasPermission = this.hasRequiredPermission(authToken.permissions, action);
      if (!hasPermission) {
        await this.recordSecurityEvent('unauthorized_access', 'medium', authToken.userId,
          `Attempted unauthorized action: ${action}`);
        return false;
      }

      // Check resource-specific permissions
      if (resource && !this.hasResourceAccess(accessControl, resource)) {
        await this.recordSecurityEvent('policy_violation', 'medium', authToken.userId,
          `Attempted access to restricted resource: ${resource}`);
        return false;
      }

      return true;
    });
  }

  // Revoke authentication token
  async revokeToken(token: string): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('revokeToken', async () => {
      const authToken = this.activeTokens.get(token);
      if (!authToken) {
        return false;
      }

      this.activeTokens.delete(token);

      await this.logAuditEvent(authToken.userId, 'revoke_token', 'authentication', true);

      vmLogger.info(`Token revoked for user ${authToken.userId} on VM ${this.vmId}`);
      return true;
    });
  }

  // Get audit log entries
  async getAuditLog(
    options: {
      userId?: string;
      action?: string;
      startTime?: Date;
      endTime?: Date;
      limit?: number;
    } = {}
  ): Promise<VMServiceResult<VMAuditLogEntry[]>> {
    return this.executeVMOperation('getAuditLog', async () => {
      let filteredLog = this.auditLog;

      if (options.userId) {
        filteredLog = filteredLog.filter(entry => entry.userId === options.userId);
      }

      if (options.action) {
        filteredLog = filteredLog.filter(entry => entry.action === options.action);
      }

      if (options.startTime) {
        filteredLog = filteredLog.filter(entry => entry.timestamp >= options.startTime!);
      }

      if (options.endTime) {
        filteredLog = filteredLog.filter(entry => entry.timestamp <= options.endTime!);
      }

      // Sort by timestamp (newest first)
      filteredLog.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      if (options.limit) {
        filteredLog = filteredLog.slice(0, options.limit);
      }

      return filteredLog;
    });
  }

  // Get security events
  async getSecurityEvents(
    options: {
      type?: VMSecurityEvent['type'];
      severity?: VMSecurityEvent['severity'];
      resolved?: boolean;
      limit?: number;
    } = {}
  ): Promise<VMServiceResult<VMSecurityEvent[]>> {
    return this.executeVMOperation('getSecurityEvents', async () => {
      let filteredEvents = this.securityEvents;

      if (options.type) {
        filteredEvents = filteredEvents.filter(event => event.type === options.type);
      }

      if (options.severity) {
        filteredEvents = filteredEvents.filter(event => event.severity === options.severity);
      }

      if (options.resolved !== undefined) {
        filteredEvents = filteredEvents.filter(event => event.resolved === options.resolved);
      }

      // Sort by timestamp (newest first)
      filteredEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      if (options.limit) {
        filteredEvents = filteredEvents.slice(0, options.limit);
      }

      return filteredEvents;
    });
  }

  // Update access control entry
  async updateAccessControl(
    userId: string,
    updates: Partial<VMAccessControlEntry>
  ): Promise<VMServiceResult<VMAccessControlEntry>> {
    return this.executeVMOperation('updateAccessControl', async () => {
      const existingEntry = this.accessControlList.get(userId);
      if (!existingEntry) {
        throw new VMServerError(
          `Access control entry not found for user ${userId}`,
          'ACCESS_CONTROL_NOT_FOUND',
          'validation_error',
          404,
          this.vmId
        );
      }

      const updatedEntry = { ...existingEntry, ...updates };
      this.accessControlList.set(userId, updatedEntry);

      await this.logAuditEvent(userId, 'update_access_control', 'access_control', true, updates);

      vmLogger.info(`Access control updated for user ${userId} on VM ${this.vmId}`, updates);
      return updatedEntry;
    });
  }

  // Health check implementation
  async healthCheck(): Promise<boolean> {
    try {
      // Check if security service is operational
      const now = new Date();
      const activeTokenCount = Array.from(this.activeTokens.values())
        .filter(token => token.expiresAt > now).length;
      
      const activeAccessControlCount = Array.from(this.accessControlList.values())
        .filter(entry => entry.isActive).length;

      vmLogger.debug(`VM Security service health check for ${this.vmId}`, {
        activeTokens: activeTokenCount,
        activeAccessControls: activeAccessControlCount,
        auditLogEntries: this.auditLog.length,
        securityEvents: this.securityEvents.length
      });

      return true;
    } catch (error) {
      vmLogger.error(`VM Security service health check failed for ${this.vmId}`, error);
      return false;
    }
  }

  // Private helper methods
  private async validateCredentials(credentials: VMAuthCredentials): Promise<boolean> {
    // Simplified credential validation
    // In production, this would integrate with proper authentication systems
    return credentials.username && (credentials.password || credentials.privateKey);
  }

  private generateSecureToken(): string {
    return randomBytes(32).toString('hex');
  }

  private hasRequiredPermission(userPermissions: VMPermissionLevel[], action: string): boolean {
    // Define action-permission mapping
    const permissionMap: Record<string, VMPermissionLevel[]> = {
      'read': ['read', 'write', 'admin', 'owner'],
      'write': ['write', 'admin', 'owner'],
      'admin': ['admin', 'owner'],
      'owner': ['owner']
    };

    const requiredPermissions = permissionMap[action] || ['owner'];
    return userPermissions.some(permission => requiredPermissions.includes(permission));
  }

  private hasResourceAccess(accessControl: VMAccessControlEntry, resource: string): boolean {
    // Check if resource is in restricted paths
    return !accessControl.restrictedPaths.some(path => resource.startsWith(path));
  }

  private isIpInRange(ip: string, range: string): boolean {
    // Simplified IP range check
    // In production, use proper CIDR matching
    if (range.includes('/')) {
      // CIDR notation
      return ip.startsWith(range.split('/')[0].slice(0, -1));
    } else {
      // Exact match
      return ip === range;
    }
  }

  private async recordFailedAttempt(userId: string, ipAddress?: string): Promise<void> {
    const existing = this.failedAttempts.get(userId) || { count: 0, lastAttempt: new Date() };
    existing.count++;
    existing.lastAttempt = new Date();
    this.failedAttempts.set(userId, existing);

    await this.recordSecurityEvent('authentication_failure', 'medium', userId,
      `Failed authentication attempt from ${ipAddress || 'unknown IP'}`);
  }

  private async recordSecurityEvent(
    type: VMSecurityEvent['type'],
    severity: VMSecurityEvent['severity'],
    userId?: string,
    description?: string
  ): Promise<void> {
    const event: VMSecurityEvent = {
      id: randomBytes(16).toString('hex'),
      type,
      severity,
      userId,
      vmId: this.vmId,
      description: description || `Security event: ${type}`,
      timestamp: new Date(),
      resolved: false
    };

    this.securityEvents.push(event);

    // Keep only last 1000 events
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    vmLogger.warn(`Security event recorded for VM ${this.vmId}`, event);
  }

  private async logAuditEvent(
    userId: string,
    action: string,
    resource: string,
    success: boolean,
    details?: Record<string, any>
  ): Promise<void> {
    const entry: VMAuditLogEntry = {
      id: randomBytes(16).toString('hex'),
      userId,
      vmId: this.vmId,
      action,
      resource,
      timestamp: new Date(),
      success,
      details,
      riskLevel: this.calculateRiskLevel(action, success)
    };

    this.auditLog.push(entry);

    // Keep only last 10000 entries
    if (this.auditLog.length > 10000) {
      this.auditLog = this.auditLog.slice(-10000);
    }
  }

  private calculateRiskLevel(action: string, success: boolean): VMAuditLogEntry['riskLevel'] {
    if (!success) return 'medium';
    
    const highRiskActions = ['create_access_control', 'update_access_control', 'admin'];
    const mediumRiskActions = ['authenticate', 'execute_command'];
    
    if (highRiskActions.includes(action)) return 'high';
    if (mediumRiskActions.includes(action)) return 'medium';
    return 'low';
  }

  // Cleanup
  async cleanup(): Promise<void> {
    this.accessControlList.clear();
    this.activeTokens.clear();
    this.auditLog = [];
    this.securityEvents = [];
    this.failedAttempts.clear();
    vmLogger.info(`VM Security service cleaned up for ${this.vmId}`);
  }
}
