/**
 * VM Services Index
 * Exports all VM services and provides a service factory for VM operations
 */

// Export all VM services
export { BaseVMService, VMConnectionPool, vmConnectionPoolManager } from './base';
export { VMConnectionService } from './connection';
export { VMDockerService } from './docker';
export { VMMonitoringService } from './monitoring';
export { VMSecurityService } from './security';

// Export types from base
export type {
  IVMService,
  VMServiceResult,
  VMPaginationParams,
  VMPaginatedResult,
  VMQueryParams
} from './base';

// Export types from connection
export type {
  SSHConnection
} from './connection';

// Export types from docker
export type {
  DockerContainer,
  DockerImage,
  DockerNetwork,
  DockerVolume,
  ContainerCreateOptions
} from './docker';

// Export types from monitoring
export type {
  SystemProcess,
  ServiceStatus,
  AlertThreshold,
  Alert
} from './monitoring';

// Export types from security
export type {
  VMPermissionLevel,
  VMAccessControlEntry,
  VMAuthToken,
  VMAuditLogEntry,
  VMSecurityEvent
} from './security';

// Import VM types
import {
  VMConnectionConfig,
  VMHealthCheck,
  VMSystemInfo,
  VMResourceMetrics,
  VMDockerInfo
} from '@/types/vm';

import { vmLogger } from '@/lib/vm-server';

// VM Service Factory Interface
export interface VMServiceFactory {
  createConnectionService(vmId: string, config: VMConnectionConfig): VMConnectionService;
  createDockerService(vmId: string, config: VMConnectionConfig): VMDockerService;
  createMonitoringService(vmId: string, config: VMConnectionConfig): VMMonitoringService;
  createSecurityService(vmId: string, config: VMConnectionConfig): VMSecurityService;
}

// VM Service Factory Implementation
export class VMServiceFactoryImpl implements VMServiceFactory {
  private connectionServices: Map<string, VMConnectionService> = new Map();
  private dockerServices: Map<string, VMDockerService> = new Map();
  private monitoringServices: Map<string, VMMonitoringService> = new Map();
  private securityServices: Map<string, VMSecurityService> = new Map();

  // Create or get connection service instance
  createConnectionService(vmId: string, config: VMConnectionConfig): VMConnectionService {
    const key = this.generateServiceKey(vmId, config);
    
    if (!this.connectionServices.has(key)) {
      const service = new VMConnectionService(vmId, config);
      this.connectionServices.set(key, service);
      vmLogger.info(`Created VM connection service for ${vmId}`);
    }
    
    return this.connectionServices.get(key)!;
  }

  // Create or get Docker service instance
  createDockerService(vmId: string, config: VMConnectionConfig): VMDockerService {
    const key = this.generateServiceKey(vmId, config);
    
    if (!this.dockerServices.has(key)) {
      const service = new VMDockerService(vmId, config);
      this.dockerServices.set(key, service);
      vmLogger.info(`Created VM Docker service for ${vmId}`);
    }
    
    return this.dockerServices.get(key)!;
  }

  // Create or get monitoring service instance
  createMonitoringService(vmId: string, config: VMConnectionConfig): VMMonitoringService {
    const key = this.generateServiceKey(vmId, config);
    
    if (!this.monitoringServices.has(key)) {
      const service = new VMMonitoringService(vmId, config);
      this.monitoringServices.set(key, service);
      vmLogger.info(`Created VM monitoring service for ${vmId}`);
    }
    
    return this.monitoringServices.get(key)!;
  }

  // Create or get security service instance
  createSecurityService(vmId: string, config: VMConnectionConfig): VMSecurityService {
    const key = this.generateServiceKey(vmId, config);
    
    if (!this.securityServices.has(key)) {
      const service = new VMSecurityService(vmId, config);
      this.securityServices.set(key, service);
      vmLogger.info(`Created VM security service for ${vmId}`);
    }
    
    return this.securityServices.get(key)!;
  }

  // Get all services for a VM
  getAllServices(vmId: string, config: VMConnectionConfig) {
    return {
      connection: this.createConnectionService(vmId, config),
      docker: this.createDockerService(vmId, config),
      monitoring: this.createMonitoringService(vmId, config),
      security: this.createSecurityService(vmId, config)
    };
  }

  // Health check for all services
  async healthCheckAll(vmId: string, config: VMConnectionConfig): Promise<{
    overall: boolean;
    services: {
      connection: boolean;
      docker: boolean;
      monitoring: boolean;
      security: boolean;
    };
    timestamp: string;
    vmId: string;
  }> {
    const services = this.getAllServices(vmId, config);
    
    const [connectionHealth, dockerHealth, monitoringHealth, securityHealth] = await Promise.all([
      services.connection.healthCheck().catch(() => false),
      services.docker.healthCheck().catch(() => false),
      services.monitoring.healthCheck().catch(() => false),
      services.security.healthCheck().catch(() => false)
    ]);

    const overall = connectionHealth && dockerHealth && monitoringHealth && securityHealth;

    return {
      overall,
      services: {
        connection: connectionHealth,
        docker: dockerHealth,
        monitoring: monitoringHealth,
        security: securityHealth
      },
      timestamp: new Date().toISOString(),
      vmId
    };
  }

  // Cleanup services for a specific VM
  async cleanupVM(vmId: string): Promise<void> {
    const keysToRemove: string[] = [];
    
    // Find all services for this VM
    for (const key of this.connectionServices.keys()) {
      if (key.startsWith(`${vmId}:`)) {
        keysToRemove.push(key);
      }
    }

    // Cleanup connection services
    for (const key of keysToRemove) {
      const service = this.connectionServices.get(key);
      if (service) {
        await service.cleanup().catch(error => 
          vmLogger.error(`Error cleaning up connection service for ${vmId}`, error)
        );
        this.connectionServices.delete(key);
      }
    }

    // Cleanup Docker services
    for (const key of keysToRemove) {
      const service = this.dockerServices.get(key);
      if (service) {
        await service.cleanup().catch(error => 
          vmLogger.error(`Error cleaning up Docker service for ${vmId}`, error)
        );
        this.dockerServices.delete(key);
      }
    }

    // Cleanup monitoring services
    for (const key of keysToRemove) {
      const service = this.monitoringServices.get(key);
      if (service) {
        await service.cleanup().catch(error => 
          vmLogger.error(`Error cleaning up monitoring service for ${vmId}`, error)
        );
        this.monitoringServices.delete(key);
      }
    }

    // Cleanup security services
    for (const key of keysToRemove) {
      const service = this.securityServices.get(key);
      if (service) {
        await service.cleanup().catch(error => 
          vmLogger.error(`Error cleaning up security service for ${vmId}`, error)
        );
        this.securityServices.delete(key);
      }
    }

    // Cleanup connection pool
    await vmConnectionPoolManager.cleanupPool(vmId);

    vmLogger.info(`Cleaned up all services for VM ${vmId}`);
  }

  // Cleanup all services
  async cleanupAll(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];

    // Get all unique VM IDs
    const vmIds = new Set<string>();
    
    for (const key of this.connectionServices.keys()) {
      const vmId = key.split(':')[0];
      vmIds.add(vmId);
    }

    // Cleanup each VM
    for (const vmId of vmIds) {
      cleanupPromises.push(this.cleanupVM(vmId));
    }

    await Promise.all(cleanupPromises);
    
    // Cleanup all connection pools
    await vmConnectionPoolManager.cleanupAllPools();

    vmLogger.info('Cleaned up all VM services');
  }

  // Get service statistics
  getServiceStats(): {
    connectionServices: number;
    dockerServices: number;
    monitoringServices: number;
    securityServices: number;
    connectionPools: Array<ReturnType<VMConnectionPool['getStats']>>;
    timestamp: string;
  } {
    return {
      connectionServices: this.connectionServices.size,
      dockerServices: this.dockerServices.size,
      monitoringServices: this.monitoringServices.size,
      securityServices: this.securityServices.size,
      connectionPools: vmConnectionPoolManager.getAllPoolStats(),
      timestamp: new Date().toISOString()
    };
  }

  // Private helper methods
  private generateServiceKey(vmId: string, config: VMConnectionConfig): string {
    // Create a unique key based on VM ID and connection config
    return `${vmId}:${config.host}:${config.port}:${config.username}`;
  }
}

// Singleton instance
export const vmServiceFactory = new VMServiceFactoryImpl();

// Convenience exports for direct service access
export function createVMConnectionService(vmId: string, config: VMConnectionConfig) {
  return vmServiceFactory.createConnectionService(vmId, config);
}

export function createVMDockerService(vmId: string, config: VMConnectionConfig) {
  return vmServiceFactory.createDockerService(vmId, config);
}

export function createVMMonitoringService(vmId: string, config: VMConnectionConfig) {
  return vmServiceFactory.createMonitoringService(vmId, config);
}

export function createVMSecurityService(vmId: string, config: VMConnectionConfig) {
  return vmServiceFactory.createSecurityService(vmId, config);
}

// Export factory as default
export default vmServiceFactory;

// VM Service Manager for high-level operations
export class VMServiceManager {
  private factory: VMServiceFactoryImpl;

  constructor(factory: VMServiceFactoryImpl = vmServiceFactory) {
    this.factory = factory;
  }

  // Comprehensive VM setup
  async setupVM(vmId: string, config: VMConnectionConfig): Promise<{
    success: boolean;
    services: ReturnType<VMServiceFactoryImpl['getAllServices']>;
    healthCheck: Awaited<ReturnType<VMServiceFactoryImpl['healthCheckAll']>>;
    error?: string;
  }> {
    try {
      const services = this.factory.getAllServices(vmId, config);
      const healthCheck = await this.factory.healthCheckAll(vmId, config);

      return {
        success: true,
        services,
        healthCheck
      };
    } catch (error) {
      vmLogger.error(`Failed to setup VM ${vmId}`, error);
      return {
        success: false,
        services: this.factory.getAllServices(vmId, config),
        healthCheck: {
          overall: false,
          services: {
            connection: false,
            docker: false,
            monitoring: false,
            security: false
          },
          timestamp: new Date().toISOString(),
          vmId
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Comprehensive VM teardown
  async teardownVM(vmId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      await this.factory.cleanupVM(vmId);
      return { success: true };
    } catch (error) {
      vmLogger.error(`Failed to teardown VM ${vmId}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Get comprehensive VM status
  async getVMStatus(vmId: string, config: VMConnectionConfig): Promise<{
    vmId: string;
    health: Awaited<ReturnType<VMServiceFactoryImpl['healthCheckAll']>>;
    stats: ReturnType<VMServiceFactoryImpl['getServiceStats']>;
    timestamp: string;
  }> {
    const health = await this.factory.healthCheckAll(vmId, config);
    const stats = this.factory.getServiceStats();

    return {
      vmId,
      health,
      stats,
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton VM service manager
export const vmServiceManager = new VMServiceManager();
