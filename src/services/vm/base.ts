/**
 * Base VM Service Class
 * Provides common functionality for all VM-related services following the established Appwrite pattern
 */

import { 
  VMServerError, 
  handleVMServerError, 
  withV<PERSON><PERSON><PERSON>, 
  vmLogger,
  VM_SERVER_CONFIG,
  VM_SERVER_CONSTANTS
} from '@/lib/vm-server';
import {
  VMOperationResult,
  VMConnectionConfig,
  VMConnectionStatus,
  VMHealthCheck,
  VMSession
} from '@/types/vm';

// Base VM service interface
export interface IVMService {
  healthCheck(): Promise<boolean>;
}

// VM service operation result (extends the base VM operation result)
export interface VMServiceResult<T> extends VMOperationResult<T> {
  metadata?: {
    timestamp: Date;
    operation: string;
    duration: number;
    vmId: string;
    connectionId?: string;
    serviceName: string;
  };
}

// Pagination parameters for VM operations
export interface VMPaginationParams {
  limit?: number;
  offset?: number;
  cursor?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// Paginated result for VM operations
export interface VMPaginatedResult<T> {
  items: T[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

// Query parameters for VM operations
export interface VMQueryParams {
  filters?: Record<string, any>;
  search?: string;
  pagination?: VMPaginationParams;
}

// Base VM service class with common functionality
export abstract class BaseVMService implements IVMService {
  protected serviceName: string;
  protected vmId: string;
  protected connectionConfig: VMConnectionConfig;

  constructor(serviceName: string, vmId: string, connectionConfig: VMConnectionConfig) {
    this.serviceName = serviceName;
    this.vmId = vmId;
    this.connectionConfig = connectionConfig;
  }

  // Execute VM operation with error handling and logging
  protected async executeVMOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    useRetry: boolean = true,
    connectionId?: string
  ): Promise<VMServiceResult<T>> {
    const startTime = Date.now();
    
    try {
      vmLogger.debug(`Starting ${this.serviceName}.${operationName}`, {
        vmId: this.vmId,
        connectionId
      });
      
      const result = useRetry 
        ? await withVMRetry(operation, VM_SERVER_CONFIG.retryAttempts, VM_SERVER_CONFIG.retryDelay, this.vmId, connectionId)
        : await operation();
      
      const duration = Date.now() - startTime;
      
      vmLogger.info(`${this.serviceName}.${operationName} completed successfully`, {
        vmId: this.vmId,
        connectionId,
        duration: `${duration}ms`
      });

      return {
        success: true,
        data: result,
        metadata: {
          timestamp: new Date(),
          operation: `${this.serviceName}.${operationName}`,
          duration,
          vmId: this.vmId,
          connectionId,
          serviceName: this.serviceName
        }
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const vmError = handleVMServerError(error, this.vmId, connectionId);
      
      vmLogger.error(`${this.serviceName}.${operationName} failed`, {
        vmId: this.vmId,
        connectionId,
        error: vmError.message,
        code: vmError.code,
        duration: `${duration}ms`
      });

      return {
        success: false,
        error: {
          code: vmError.code || 'UNKNOWN_ERROR',
          message: vmError.message,
          details: {
            type: vmError.type,
            statusCode: vmError.statusCode,
            vmId: vmError.vmId,
            connectionId: vmError.connectionId
          }
        },
        metadata: {
          timestamp: new Date(),
          operation: `${this.serviceName}.${operationName}`,
          duration,
          vmId: this.vmId,
          connectionId,
          serviceName: this.serviceName
        }
      };
    }
  }

  // Validate required parameters
  protected validateRequired(params: Record<string, any>, requiredFields: string[]): void {
    const missing = requiredFields.filter(field => 
      params[field] === undefined || params[field] === null || params[field] === ''
    );
    
    if (missing.length > 0) {
      throw new VMServerError(
        `Missing required parameters: ${missing.join(', ')}`,
        'MISSING_PARAMETERS',
        'validation_error',
        400,
        this.vmId
      );
    }
  }

  // Validate VM connection configuration
  protected validateConnectionConfig(config: VMConnectionConfig): void {
    this.validateRequired(config, ['host', 'port', 'username']);
    
    if (!config.password && !config.privateKey) {
      throw new VMServerError(
        'Either password or privateKey must be provided for VM authentication',
        'INVALID_AUTH_CONFIG',
        'validation_error',
        400,
        this.vmId
      );
    }

    if (config.port < 1 || config.port > 65535) {
      throw new VMServerError(
        'Port must be between 1 and 65535',
        'INVALID_PORT',
        'validation_error',
        400,
        this.vmId
      );
    }
  }

  // Validate command for security
  protected validateCommand(command: string): void {
    const commandParts = command.trim().split(' ');
    const baseCommand = commandParts[0];

    // Check if command is in allowed list
    if (!VM_SERVER_CONFIG.security.allowedCommands.includes(baseCommand)) {
      throw new VMServerError(
        `Command '${baseCommand}' is not allowed for security reasons`,
        'COMMAND_NOT_ALLOWED',
        'security_error',
        403,
        this.vmId
      );
    }

    // Check for restricted paths
    const hasRestrictedPath = VM_SERVER_CONFIG.security.restrictedPaths.some(path => 
      command.includes(path)
    );

    if (hasRestrictedPath) {
      throw new VMServerError(
        'Command contains restricted paths',
        'RESTRICTED_PATH_ACCESS',
        'security_error',
        403,
        this.vmId
      );
    }

    // Basic injection protection
    const dangerousPatterns = [
      /;\s*rm\s+-rf/,
      /&&\s*rm\s+-rf/,
      /\|\s*rm\s+-rf/,
      /`.*`/,
      /\$\(.*\)/,
      />\s*\/dev\/null.*&/
    ];

    const hasDangerousPattern = dangerousPatterns.some(pattern => 
      pattern.test(command)
    );

    if (hasDangerousPattern) {
      throw new VMServerError(
        'Command contains potentially dangerous patterns',
        'DANGEROUS_COMMAND',
        'security_error',
        403,
        this.vmId
      );
    }
  }

  // Format pagination parameters
  protected formatPagination(params?: VMPaginationParams): VMPaginationParams {
    return {
      limit: Math.min(params?.limit || 50, VM_SERVER_CONSTANTS.BATCH_LIMITS.MAX_COMMANDS_PER_BATCH),
      offset: params?.offset || 0,
      cursor: params?.cursor,
      orderBy: params?.orderBy || 'timestamp',
      orderDirection: params?.orderDirection || 'desc'
    };
  }

  // Rate limiting check (basic implementation)
  protected async checkRateLimit(operation: string, identifier: string): Promise<boolean> {
    // This is a basic implementation - in production, you'd use Redis or similar
    // For now, we'll just log and return true
    vmLogger.debug(`Rate limit check for ${operation}:${identifier}`, {
      vmId: this.vmId
    });
    return true;
  }

  // Cache key generator
  protected generateCacheKey(prefix: string, ...parts: string[]): string {
    return `vm:${this.vmId}:${prefix}:${parts.join(':')}`;
  }

  // Get VM connection status
  protected async getConnectionStatus(connectionId?: string): Promise<VMConnectionStatus> {
    // This would be implemented by specific connection services
    // For now, return a basic status
    return {
      isConnected: false,
      connectionId: connectionId || 'unknown',
      establishedAt: new Date(),
      lastActivity: new Date(),
      latency: 0
    };
  }

  // Abstract health check method
  abstract healthCheck(): Promise<boolean>;

  // Get service metrics
  public getMetrics(): {
    serviceName: string;
    vmId: string;
    config: typeof VM_SERVER_CONFIG;
    timestamp: string;
  } {
    return {
      serviceName: this.serviceName,
      vmId: this.vmId,
      config: VM_SERVER_CONFIG,
      timestamp: new Date().toISOString()
    };
  }

  // Get VM configuration (sanitized)
  public getVMConfig(): Omit<VMConnectionConfig, 'password' | 'privateKey' | 'passphrase'> {
    const { password, privateKey, passphrase, ...sanitizedConfig } = this.connectionConfig;
    return sanitizedConfig;
  }
}

// VM Connection Pool for managing multiple VM connections
export class VMConnectionPool {
  private activeConnections: number = 0;
  private maxConnections: number;
  private vmId: string;
  private connectionQueue: Array<{
    resolve: (value: boolean) => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = [];

  constructor(vmId: string, maxConnections: number = VM_SERVER_CONFIG.connectionPoolSize) {
    this.vmId = vmId;
    this.maxConnections = maxConnections;
  }

  public async acquireConnection(): Promise<boolean> {
    if (this.activeConnections >= this.maxConnections) {
      vmLogger.warn(`VM connection pool exhausted for ${this.vmId}, queuing request...`, {
        active: this.activeConnections,
        max: this.maxConnections,
        queued: this.connectionQueue.length
      });

      // Queue the request
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          const index = this.connectionQueue.findIndex(item => item.resolve === resolve);
          if (index !== -1) {
            this.connectionQueue.splice(index, 1);
          }
          reject(new VMServerError(
            'Connection pool timeout',
            'CONNECTION_POOL_TIMEOUT',
            'timeout_error',
            408,
            this.vmId
          ));
        }, VM_SERVER_CONFIG.timeout);

        this.connectionQueue.push({
          resolve: (value: boolean) => {
            clearTimeout(timeout);
            resolve(value);
          },
          reject: (error: Error) => {
            clearTimeout(timeout);
            reject(error);
          },
          timestamp: Date.now()
        });
      });
    }

    this.activeConnections++;
    vmLogger.debug(`VM connection acquired for ${this.vmId}`, {
      active: this.activeConnections,
      max: this.maxConnections
    });
    return true;
  }

  public releaseConnection(): void {
    if (this.activeConnections > 0) {
      this.activeConnections--;
      vmLogger.debug(`VM connection released for ${this.vmId}`, {
        active: this.activeConnections,
        max: this.maxConnections
      });

      // Process queued requests
      if (this.connectionQueue.length > 0) {
        const nextRequest = this.connectionQueue.shift();
        if (nextRequest) {
          this.activeConnections++;
          nextRequest.resolve(true);
        }
      }
    }
  }

  public getStats(): {
    vmId: string;
    activeConnections: number;
    maxConnections: number;
    queuedRequests: number;
    utilizationPercent: number;
  } {
    return {
      vmId: this.vmId,
      activeConnections: this.activeConnections,
      maxConnections: this.maxConnections,
      queuedRequests: this.connectionQueue.length,
      utilizationPercent: Math.round((this.activeConnections / this.maxConnections) * 100)
    };
  }

  public async cleanup(): Promise<void> {
    // Reject all queued requests
    while (this.connectionQueue.length > 0) {
      const request = this.connectionQueue.shift();
      if (request) {
        request.reject(new VMServerError(
          'Connection pool is being cleaned up',
          'CONNECTION_POOL_CLEANUP',
          'cleanup_error',
          503,
          this.vmId
        ));
      }
    }

    // Reset active connections
    this.activeConnections = 0;
    vmLogger.info(`VM connection pool cleaned up for ${this.vmId}`);
  }
}

// Global VM connection pool manager
class VMConnectionPoolManager {
  private pools: Map<string, VMConnectionPool> = new Map();

  public getPool(vmId: string): VMConnectionPool {
    if (!this.pools.has(vmId)) {
      this.pools.set(vmId, new VMConnectionPool(vmId));
    }
    return this.pools.get(vmId)!;
  }

  public async cleanupPool(vmId: string): Promise<void> {
    const pool = this.pools.get(vmId);
    if (pool) {
      await pool.cleanup();
      this.pools.delete(vmId);
    }
  }

  public async cleanupAllPools(): Promise<void> {
    const cleanupPromises = Array.from(this.pools.keys()).map(vmId =>
      this.cleanupPool(vmId)
    );
    await Promise.all(cleanupPromises);
  }

  public getAllPoolStats(): Array<ReturnType<VMConnectionPool['getStats']>> {
    return Array.from(this.pools.values()).map(pool => pool.getStats());
  }
}

// Export singleton instance
export const vmConnectionPoolManager = new VMConnectionPoolManager();
