/**
 * VM Security Service Tests
 * Unit tests for the VM security service
 */

import { VMSecurityService, VMPermissionLevel, VMAccessControlEntry, VMAuthToken } from '../security';
import { VMConnectionConfig, VMAuthCredentials } from '@/types/vm';

// Mock the VM logger and server utilities
jest.mock('@/lib/vm-server', () => ({
  ...jest.requireActual('@/lib/vm-server'),
  vmLogger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock crypto functions
jest.mock('crypto', () => ({
  ...jest.requireActual('crypto'),
  randomBytes: jest.fn().mockReturnValue({
    toString: jest.fn().mockReturnValue('mocked-random-string'),
  }),
}));

describe('VMSecurityService', () => {
  let service: VMSecurityService;
  let mockConfig: VMConnectionConfig;

  beforeEach(() => {
    mockConfig = {
      host: 'test-vm.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
      timeout: 30000,
      keepAlive: true,
      maxRetries: 3,
      retryDelay: 1000,
    };

    service = new VMSecurityService('test-vm-001', mockConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(service).toBeDefined();
      expect(service.getVMConfig().host).toBe('test-vm.example.com');
    });
  });

  describe('createAccessControl', () => {
    it('should create access control entry successfully', async () => {
      const userId = 'test-user-123';
      const username = 'testuser';
      const permissions: VMPermissionLevel[] = ['read', 'write'];

      const result = await service.createAccessControl(userId, username, permissions);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.userId).toBe(userId);
      expect(result.data?.username).toBe(username);
      expect(result.data?.permissions).toEqual(permissions);
      expect(result.data?.isActive).toBe(true);
      expect(result.data?.createdAt).toBeInstanceOf(Date);
    });

    it('should create access control with custom options', async () => {
      const userId = 'test-user-123';
      const username = 'testuser';
      const permissions: VMPermissionLevel[] = ['admin'];
      const options = {
        allowedCommands: ['docker', 'ls'],
        restrictedPaths: ['/etc/passwd'],
        ipWhitelist: ['***********/24'],
        sessionTimeout: 7200000,
        maxConcurrentSessions: 2,
        expiresAt: new Date(Date.now() + 86400000), // 24 hours
      };

      const result = await service.createAccessControl(userId, username, permissions, options);

      expect(result.success).toBe(true);
      expect(result.data?.allowedCommands).toEqual(options.allowedCommands);
      expect(result.data?.restrictedPaths).toEqual(options.restrictedPaths);
      expect(result.data?.ipWhitelist).toEqual(options.ipWhitelist);
      expect(result.data?.sessionTimeout).toBe(options.sessionTimeout);
      expect(result.data?.maxConcurrentSessions).toBe(options.maxConcurrentSessions);
      expect(result.data?.expiresAt).toEqual(options.expiresAt);
    });

    it('should fail when access control already exists', async () => {
      const userId = 'test-user-123';
      const username = 'testuser';
      const permissions: VMPermissionLevel[] = ['read'];

      // Create first access control
      await service.createAccessControl(userId, username, permissions);

      // Try to create duplicate
      const result = await service.createAccessControl(userId, username, permissions);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('ACCESS_CONTROL_EXISTS');
    });

    it('should validate required fields', async () => {
      const result = await service.createAccessControl('', '', []);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('MISSING_PARAMETERS');
    });
  });

  describe('authenticateUser', () => {
    let userId: string;
    let credentials: VMAuthCredentials;

    beforeEach(async () => {
      userId = 'test-user-123';
      credentials = {
        type: 'password',
        username: 'testuser',
        password: 'testpass',
      };

      // Create access control for the user
      await service.createAccessControl(userId, 'testuser', ['read', 'write']);
    });

    it('should authenticate user successfully', async () => {
      const clientInfo = {
        ipAddress: '*************',
        userAgent: 'test-agent',
      };

      const result = await service.authenticateUser(userId, credentials, clientInfo);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.userId).toBe(userId);
      expect(result.data?.vmId).toBe('test-vm-001');
      expect(result.data?.permissions).toEqual(['read', 'write']);
      expect(result.data?.token).toBeDefined();
      expect(result.data?.issuedAt).toBeInstanceOf(Date);
      expect(result.data?.expiresAt).toBeInstanceOf(Date);
    });

    it('should fail authentication for non-existent user', async () => {
      const result = await service.authenticateUser('non-existent-user', credentials);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('USER_NOT_AUTHORIZED');
    });

    it('should fail authentication for expired access', async () => {
      const expiredUserId = 'expired-user';
      const expiredDate = new Date(Date.now() - 86400000); // Yesterday

      await service.createAccessControl(expiredUserId, 'expireduser', ['read'], {
        expiresAt: expiredDate,
      });

      const result = await service.authenticateUser(expiredUserId, credentials);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('ACCESS_EXPIRED');
    });

    it('should fail authentication from unauthorized IP', async () => {
      const restrictedUserId = 'restricted-user';
      await service.createAccessControl(restrictedUserId, 'restricteduser', ['read'], {
        ipWhitelist: ['10.0.0.0/8'],
      });

      const clientInfo = { ipAddress: '*************' };
      const result = await service.authenticateUser(restrictedUserId, credentials, clientInfo);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('IP_NOT_ALLOWED');
    });

    it('should handle too many failed attempts', async () => {
      // Simulate multiple failed attempts
      for (let i = 0; i < 5; i++) {
        const invalidCredentials = { ...credentials, password: 'wrong-password' };
        await service.authenticateUser(userId, invalidCredentials);
      }

      // Next attempt should be blocked
      const result = await service.authenticateUser(userId, credentials);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('TOO_MANY_ATTEMPTS');
    });
  });

  describe('validateToken', () => {
    let authToken: VMAuthToken;

    beforeEach(async () => {
      const userId = 'test-user-123';
      const credentials: VMAuthCredentials = {
        type: 'password',
        username: 'testuser',
        password: 'testpass',
      };

      await service.createAccessControl(userId, 'testuser', ['read', 'write']);
      const authResult = await service.authenticateUser(userId, credentials);
      authToken = authResult.data!;
    });

    it('should validate valid token', async () => {
      const result = await service.validateToken(authToken.token);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.token).toBe(authToken.token);
      expect(result.data?.userId).toBe(authToken.userId);
    });

    it('should fail validation for invalid token', async () => {
      const result = await service.validateToken('invalid-token');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_TOKEN');
    });

    it('should fail validation for expired token', async () => {
      // Mock expired token by setting past expiration date
      const expiredToken = { ...authToken, expiresAt: new Date(Date.now() - 1000) };
      
      // Manually add expired token to simulate the scenario
      const service2 = new VMSecurityService('test-vm-002', mockConfig);
      await service2.createAccessControl('user', 'user', ['read']);
      
      const result = await service2.validateToken('expired-token');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_TOKEN');
    });
  });

  describe('checkPermission', () => {
    let authToken: VMAuthToken;

    beforeEach(async () => {
      const userId = 'test-user-123';
      const credentials: VMAuthCredentials = {
        type: 'password',
        username: 'testuser',
        password: 'testpass',
      };

      await service.createAccessControl(userId, 'testuser', ['read', 'write']);
      const authResult = await service.authenticateUser(userId, credentials);
      authToken = authResult.data!;
    });

    it('should allow action with sufficient permissions', async () => {
      const result = await service.checkPermission(authToken.token, 'read');

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
    });

    it('should deny action with insufficient permissions', async () => {
      const result = await service.checkPermission(authToken.token, 'admin');

      expect(result.success).toBe(true);
      expect(result.data).toBe(false);
    });

    it('should deny access to restricted resources', async () => {
      const result = await service.checkPermission(authToken.token, 'read', '/etc/passwd');

      expect(result.success).toBe(true);
      expect(result.data).toBe(false);
    });

    it('should fail permission check for invalid token', async () => {
      const result = await service.checkPermission('invalid-token', 'read');

      expect(result.success).toBe(true);
      expect(result.data).toBe(false);
    });
  });

  describe('revokeToken', () => {
    let authToken: VMAuthToken;

    beforeEach(async () => {
      const userId = 'test-user-123';
      const credentials: VMAuthCredentials = {
        type: 'password',
        username: 'testuser',
        password: 'testpass',
      };

      await service.createAccessControl(userId, 'testuser', ['read', 'write']);
      const authResult = await service.authenticateUser(userId, credentials);
      authToken = authResult.data!;
    });

    it('should revoke valid token', async () => {
      const result = await service.revokeToken(authToken.token);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);

      // Token should no longer be valid
      const validateResult = await service.validateToken(authToken.token);
      expect(validateResult.success).toBe(false);
    });

    it('should handle revoking non-existent token', async () => {
      const result = await service.revokeToken('non-existent-token');

      expect(result.success).toBe(true);
      expect(result.data).toBe(false);
    });
  });

  describe('updateAccessControl', () => {
    let userId: string;

    beforeEach(async () => {
      userId = 'test-user-123';
      await service.createAccessControl(userId, 'testuser', ['read']);
    });

    it('should update access control successfully', async () => {
      const updates = {
        permissions: ['read', 'write', 'admin'] as VMPermissionLevel[],
        sessionTimeout: 7200000,
      };

      const result = await service.updateAccessControl(userId, updates);

      expect(result.success).toBe(true);
      expect(result.data?.permissions).toEqual(updates.permissions);
      expect(result.data?.sessionTimeout).toBe(updates.sessionTimeout);
    });

    it('should fail to update non-existent access control', async () => {
      const result = await service.updateAccessControl('non-existent-user', {
        permissions: ['read'],
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('ACCESS_CONTROL_NOT_FOUND');
    });
  });

  describe('getAuditLog', () => {
    beforeEach(async () => {
      // Create some audit log entries by performing operations
      const userId = 'test-user-123';
      await service.createAccessControl(userId, 'testuser', ['read']);
      
      const credentials: VMAuthCredentials = {
        type: 'password',
        username: 'testuser',
        password: 'testpass',
      };
      
      await service.authenticateUser(userId, credentials);
    });

    it('should return audit log entries', async () => {
      const result = await service.getAuditLog();

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data!.length).toBeGreaterThan(0);
    });

    it('should filter audit log by user ID', async () => {
      const result = await service.getAuditLog({ userId: 'test-user-123' });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.every(entry => entry.userId === 'test-user-123')).toBe(true);
    });

    it('should filter audit log by action', async () => {
      const result = await service.getAuditLog({ action: 'create_access_control' });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.every(entry => entry.action === 'create_access_control')).toBe(true);
    });

    it('should limit audit log entries', async () => {
      const result = await service.getAuditLog({ limit: 1 });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(1);
    });
  });

  describe('getSecurityEvents', () => {
    beforeEach(async () => {
      // Trigger some security events
      const userId = 'test-user-123';
      await service.createAccessControl(userId, 'testuser', ['read']);
      
      const invalidCredentials: VMAuthCredentials = {
        type: 'password',
        username: 'testuser',
        password: 'wrong-password',
      };
      
      // This should create security events
      await service.authenticateUser(userId, invalidCredentials);
    });

    it('should return security events', async () => {
      const result = await service.getSecurityEvents();

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
    });

    it('should filter security events by type', async () => {
      const result = await service.getSecurityEvents({ type: 'authentication_failure' });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.every(event => event.type === 'authentication_failure')).toBe(true);
    });

    it('should filter security events by severity', async () => {
      const result = await service.getSecurityEvents({ severity: 'medium' });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.every(event => event.severity === 'medium')).toBe(true);
    });

    it('should filter security events by resolved status', async () => {
      const result = await service.getSecurityEvents({ resolved: false });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.every(event => event.resolved === false)).toBe(true);
    });
  });

  describe('healthCheck', () => {
    it('should return true when service is operational', async () => {
      const result = await service.healthCheck();

      expect(result).toBe(true);
    });

    it('should handle health check errors gracefully', async () => {
      // Mock an error in the health check process
      const originalConsoleError = console.error;
      console.error = jest.fn();

      const result = await service.healthCheck();

      // Should still return true as the basic service is operational
      expect(result).toBe(true);

      console.error = originalConsoleError;
    });
  });

  describe('cleanup', () => {
    it('should cleanup all security data', async () => {
      // Create some data
      const userId = 'test-user-123';
      await service.createAccessControl(userId, 'testuser', ['read']);
      
      const credentials: VMAuthCredentials = {
        type: 'password',
        username: 'testuser',
        password: 'testpass',
      };
      
      await service.authenticateUser(userId, credentials);

      // Verify data exists
      const auditResult = await service.getAuditLog();
      expect(auditResult.data!.length).toBeGreaterThan(0);

      // Cleanup
      await service.cleanup();

      // Verify cleanup
      const auditResultAfter = await service.getAuditLog();
      expect(auditResultAfter.data).toHaveLength(0);

      const eventsResult = await service.getSecurityEvents();
      expect(eventsResult.data).toHaveLength(0);
    });
  });
});
