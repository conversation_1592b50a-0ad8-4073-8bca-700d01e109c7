/**
 * VM Connection Service Tests
 * Unit tests for the VM connection service
 */

import { VMConnectionService } from '../connection';
import { VMConnectionConfig, VMAuthCredentials, VMSession } from '@/types/vm';
import { Client as SSHClient } from 'ssh2';

// Mock SSH2 client
jest.mock('ssh2', () => ({
  Client: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    destroy: jest.fn(),
    exec: jest.fn(),
    on: jest.fn(),
  })),
}));

// Mock the VM logger and server utilities
jest.mock('@/lib/vm-server', () => ({
  ...jest.requireActual('@/lib/vm-server'),
  vmLogger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock the connection pool manager
jest.mock('../base', () => ({
  ...jest.requireActual('../base'),
  vmConnectionPoolManager: {
    getPool: jest.fn().mockReturnValue({
      acquireConnection: jest.fn().mockResolvedValue(true),
      releaseConnection: jest.fn(),
    }),
  },
}));

describe('VMConnectionService', () => {
  let service: VMConnectionService;
  let mockConfig: VMConnectionConfig;
  let mockSSHClient: jest.Mocked<SSHClient>;

  beforeEach(() => {
    mockConfig = {
      host: 'test-vm.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
      timeout: 30000,
      keepAlive: true,
      maxRetries: 3,
      retryDelay: 1000,
    };

    service = new VMConnectionService('test-vm-001', mockConfig);
    
    // Get the mocked SSH client
    mockSSHClient = new (SSHClient as jest.MockedClass<typeof SSHClient>)() as jest.Mocked<SSHClient>;
    (SSHClient as jest.MockedClass<typeof SSHClient>).mockReturnValue(mockSSHClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(service).toBeDefined();
      expect(service.getVMConfig().host).toBe('test-vm.example.com');
      expect(service.getVMConfig().port).toBe(22);
      expect(service.getVMConfig().username).toBe('testuser');
    });

    it('should validate connection configuration on creation', () => {
      const invalidConfig = { ...mockConfig, host: '' };
      
      expect(() => {
        new VMConnectionService('test-vm-001', invalidConfig);
      }).toThrow();
    });
  });

  describe('connect', () => {
    it('should establish SSH connection successfully', async () => {
      // Mock successful connection
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const result = await service.connect();

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.isConnected).toBe(true);
      expect(result.data?.connectionId).toBeDefined();
      expect(mockSSHClient.connect).toHaveBeenCalled();
    });

    it('should handle connection failure', async () => {
      const connectionError = new Error('Connection refused');
      
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          setTimeout(() => callback(connectionError), 10);
        }
        return mockSSHClient;
      });

      const result = await service.connect();

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain('Connection refused');
    });

    it('should handle connection timeout', async () => {
      // Mock timeout by not calling any event handlers
      mockSSHClient.on.mockImplementation(() => mockSSHClient);

      const result = await service.connect();

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain('timeout');
    });

    it('should use custom credentials when provided', async () => {
      const customCredentials: VMAuthCredentials = {
        type: 'key',
        username: 'customuser',
        privateKey: 'custom-private-key',
      };

      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const result = await service.connect(customCredentials);

      expect(result.success).toBe(true);
      expect(mockSSHClient.connect).toHaveBeenCalledWith(
        expect.objectContaining({
          username: 'customuser',
          privateKey: 'custom-private-key',
        })
      );
    });
  });

  describe('executeCommand', () => {
    let mockConnectionId: string;

    beforeEach(async () => {
      // Establish a connection first
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const connectResult = await service.connect();
      mockConnectionId = connectResult.data!.connectionId;
    });

    it('should execute command successfully', async () => {
      const mockStream = {
        on: jest.fn(),
        stderr: { on: jest.fn() },
        destroy: jest.fn(),
      };

      mockSSHClient.exec.mockImplementation((command: string, callback: Function) => {
        callback(null, mockStream);
        
        // Simulate successful command execution
        setTimeout(() => {
          const closeHandler = mockStream.on.mock.calls.find(call => call[0] === 'close')?.[1];
          const dataHandler = mockStream.on.mock.calls.find(call => call[0] === 'data')?.[1];
          
          if (dataHandler) dataHandler(Buffer.from('command output'));
          if (closeHandler) closeHandler(0);
        }, 10);
      });

      const result = await service.executeCommand(mockConnectionId, 'ls -la');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.stdout).toBe('command output');
      expect(result.data?.exitCode).toBe(0);
      expect(mockSSHClient.exec).toHaveBeenCalledWith('ls -la', expect.any(Function));
    });

    it('should handle command execution failure', async () => {
      const execError = new Error('Command execution failed');
      
      mockSSHClient.exec.mockImplementation((command: string, callback: Function) => {
        callback(execError);
      });

      const result = await service.executeCommand(mockConnectionId, 'invalid-command');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain('Command execution failed');
    });

    it('should validate command for security', async () => {
      const result = await service.executeCommand(mockConnectionId, 'rm -rf /');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DANGEROUS_COMMAND');
    });

    it('should handle command with options', async () => {
      const mockStream = {
        on: jest.fn(),
        stderr: { on: jest.fn() },
        destroy: jest.fn(),
      };

      mockSSHClient.exec.mockImplementation((command: string, callback: Function) => {
        expect(command).toContain('cd "/tmp"');
        expect(command).toContain('TEST_VAR="test_value"');
        callback(null, mockStream);
        
        setTimeout(() => {
          const closeHandler = mockStream.on.mock.calls.find(call => call[0] === 'close')?.[1];
          if (closeHandler) closeHandler(0);
        }, 10);
      });

      const options = {
        cwd: '/tmp',
        env: { TEST_VAR: 'test_value' },
        timeout: 5000,
      };

      const result = await service.executeCommand(mockConnectionId, 'echo $TEST_VAR', options);

      expect(result.success).toBe(true);
    });

    it('should handle command timeout', async () => {
      const mockStream = {
        on: jest.fn(),
        stderr: { on: jest.fn() },
        destroy: jest.fn(),
      };

      mockSSHClient.exec.mockImplementation((command: string, callback: Function) => {
        callback(null, mockStream);
        // Don't call the close handler to simulate timeout
      });

      const result = await service.executeCommand(mockConnectionId, 'sleep 100', { timeout: 100 });

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timeout');
      expect(mockStream.destroy).toHaveBeenCalled();
    });

    it('should capture stderr output', async () => {
      const mockStream = {
        on: jest.fn(),
        stderr: { on: jest.fn() },
        destroy: jest.fn(),
      };

      mockSSHClient.exec.mockImplementation((command: string, callback: Function) => {
        callback(null, mockStream);
        
        setTimeout(() => {
          const closeHandler = mockStream.on.mock.calls.find(call => call[0] === 'close')?.[1];
          const stderrHandler = mockStream.stderr.on.mock.calls.find(call => call[0] === 'data')?.[1];
          
          if (stderrHandler) stderrHandler(Buffer.from('error output'));
          if (closeHandler) closeHandler(1);
        }, 10);
      });

      const result = await service.executeCommand(mockConnectionId, 'invalid-command');

      expect(result.success).toBe(true);
      expect(result.data?.stderr).toBe('error output');
      expect(result.data?.exitCode).toBe(1);
    });
  });

  describe('disconnect', () => {
    let mockConnectionId: string;

    beforeEach(async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const connectResult = await service.connect();
      mockConnectionId = connectResult.data!.connectionId;
    });

    it('should disconnect successfully', async () => {
      const result = await service.disconnect(mockConnectionId);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockSSHClient.destroy).toHaveBeenCalled();
    });

    it('should handle disconnect from non-existent connection', async () => {
      const result = await service.disconnect('non-existent-connection');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('not found');
    });
  });

  describe('createSession', () => {
    let mockConnectionId: string;

    beforeEach(async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const connectResult = await service.connect();
      mockConnectionId = connectResult.data!.connectionId;
    });

    it('should create session successfully', async () => {
      const userId = 'test-user-123';
      const metadata = { userAgent: 'test-agent', ipAddress: '***********' };

      const result = await service.createSession(mockConnectionId, userId, metadata);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.userId).toBe(userId);
      expect(result.data?.vmId).toBe('test-vm-001');
      expect(result.data?.connectionId).toBe(mockConnectionId);
      expect(result.data?.isActive).toBe(true);
      expect(result.data?.metadata).toEqual(metadata);
    });

    it('should handle session creation with invalid connection', async () => {
      const result = await service.createSession('invalid-connection', 'test-user');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('No active connection found');
    });
  });

  describe('closeSession', () => {
    let mockSessionId: string;

    beforeEach(async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const connectResult = await service.connect();
      const sessionResult = await service.createSession(connectResult.data!.connectionId, 'test-user');
      mockSessionId = sessionResult.data!.id;
    });

    it('should close session successfully', async () => {
      const result = await service.closeSession(mockSessionId);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
    });

    it('should handle closing non-existent session', async () => {
      const result = await service.closeSession('non-existent-session');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('not found');
    });
  });

  describe('getConnectionStatus', () => {
    let mockConnectionId: string;

    beforeEach(async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const connectResult = await service.connect();
      mockConnectionId = connectResult.data!.connectionId;
    });

    it('should return connection status', async () => {
      const result = await service.getConnectionStatus(mockConnectionId);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.isConnected).toBe(true);
      expect(result.data?.connectionId).toBe(mockConnectionId);
    });

    it('should handle status request for non-existent connection', async () => {
      const result = await service.getConnectionStatus('non-existent-connection');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('not found');
    });
  });

  describe('listConnections', () => {
    it('should return empty list when no connections', async () => {
      const result = await service.listConnections();

      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
    });

    it('should return list of active connections', async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      await service.connect();
      await service.connect();

      const result = await service.listConnections();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0].isConnected).toBe(true);
      expect(result.data?.[1].isConnected).toBe(true);
    });
  });

  describe('healthCheck', () => {
    it('should return true when connection can be established', async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      const result = await service.healthCheck();

      expect(result).toBe(true);
    });

    it('should return false when connection fails', async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          setTimeout(() => callback(new Error('Connection failed')), 10);
        }
        return mockSSHClient;
      });

      const result = await service.healthCheck();

      expect(result).toBe(false);
    });
  });

  describe('cleanup', () => {
    it('should cleanup all connections and sessions', async () => {
      mockSSHClient.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'ready') {
          setTimeout(() => callback(), 10);
        }
        return mockSSHClient;
      });

      // Create connections and sessions
      const connectResult1 = await service.connect();
      const connectResult2 = await service.connect();
      await service.createSession(connectResult1.data!.connectionId, 'user1');
      await service.createSession(connectResult2.data!.connectionId, 'user2');

      // Verify connections exist
      const listResult = await service.listConnections();
      expect(listResult.data).toHaveLength(2);

      // Cleanup
      await service.cleanup();

      // Verify cleanup
      const listResultAfter = await service.listConnections();
      expect(listResultAfter.data).toHaveLength(0);
    });
  });
});
