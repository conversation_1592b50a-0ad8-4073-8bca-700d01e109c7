/**
 * Base VM Service Tests
 * Unit tests for the base VM service class and connection pool
 */

import { BaseVMService, VMConnectionPool, vmConnectionPoolManager } from '../base';
import { VMConnectionConfig, VMOperationResult } from '@/types/vm';
import { VMServerError } from '@/lib/vm-server';

// Mock the VM logger
jest.mock('@/lib/vm-server', () => ({
  ...jest.requireActual('@/lib/vm-server'),
  vmLogger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
}));

// Test implementation of BaseVMService
class TestVMService extends BaseVMService {
  constructor(vmId: string, connectionConfig: VMConnectionConfig) {
    super('TestVMService', vmId, connectionConfig);
  }

  async healthCheck(): Promise<boolean> {
    return true;
  }

  // Expose protected methods for testing
  public async testExecuteVMOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    useRetry: boolean = true,
    connectionId?: string
  ) {
    return this.executeVMOperation(operationName, operation, useRetry, connectionId);
  }

  public testValidateRequired(params: Record<string, any>, requiredFields: string[]) {
    return this.validateRequired(params, requiredFields);
  }

  public testValidateConnectionConfig(config: VMConnectionConfig) {
    return this.validateConnectionConfig(config);
  }

  public testValidateCommand(command: string) {
    return this.validateCommand(command);
  }

  public testFormatPagination(params?: any) {
    return this.formatPagination(params);
  }

  public testGenerateCacheKey(prefix: string, ...parts: string[]) {
    return this.generateCacheKey(prefix, ...parts);
  }
}

describe('BaseVMService', () => {
  let service: TestVMService;
  let mockConfig: VMConnectionConfig;

  beforeEach(() => {
    mockConfig = {
      host: 'test-vm.example.com',
      port: 22,
      username: 'testuser',
      password: 'testpass',
      timeout: 30000,
      keepAlive: true,
      maxRetries: 3,
      retryDelay: 1000,
    };

    service = new TestVMService('test-vm-001', mockConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with correct properties', () => {
      expect(service.getMetrics().serviceName).toBe('TestVMService');
      expect(service.getMetrics().vmId).toBe('test-vm-001');
      expect(service.getVMConfig().host).toBe('test-vm.example.com');
      expect(service.getVMConfig().port).toBe(22);
      expect(service.getVMConfig().username).toBe('testuser');
    });

    it('should not expose sensitive configuration', () => {
      const config = service.getVMConfig();
      expect(config).not.toHaveProperty('password');
      expect(config).not.toHaveProperty('privateKey');
      expect(config).not.toHaveProperty('passphrase');
    });
  });

  describe('executeVMOperation', () => {
    it('should execute operation successfully', async () => {
      const mockOperation = jest.fn().mockResolvedValue('test-result');
      
      const result = await service.testExecuteVMOperation('testOp', mockOperation);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('test-result');
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.operation).toBe('TestVMService.testOp');
      expect(result.metadata?.vmId).toBe('test-vm-001');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should handle operation failure', async () => {
      const mockError = new Error('Test error');
      const mockOperation = jest.fn().mockRejectedValue(mockError);
      
      const result = await service.testExecuteVMOperation('testOp', mockOperation);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Test error');
      expect(result.metadata).toBeDefined();
    });

    it('should handle VM-specific errors', async () => {
      const vmError = new VMServerError('VM connection failed', 'CONNECTION_FAILED', 'connection_error', 503, 'test-vm-001');
      const mockOperation = jest.fn().mockRejectedValue(vmError);
      
      const result = await service.testExecuteVMOperation('testOp', mockOperation);
      
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('CONNECTION_FAILED');
      expect(result.error?.details?.statusCode).toBe(503);
      expect(result.error?.details?.vmId).toBe('test-vm-001');
    });

    it('should include connection ID in metadata when provided', async () => {
      const mockOperation = jest.fn().mockResolvedValue('test-result');
      const connectionId = 'conn-123';
      
      const result = await service.testExecuteVMOperation('testOp', mockOperation, true, connectionId);
      
      expect(result.success).toBe(true);
      expect(result.metadata?.connectionId).toBe(connectionId);
    });
  });

  describe('validateRequired', () => {
    it('should pass validation with all required fields', () => {
      const params = { field1: 'value1', field2: 'value2', field3: 'value3' };
      const requiredFields = ['field1', 'field2'];
      
      expect(() => {
        service.testValidateRequired(params, requiredFields);
      }).not.toThrow();
    });

    it('should throw error for missing required fields', () => {
      const params = { field1: 'value1' };
      const requiredFields = ['field1', 'field2', 'field3'];
      
      expect(() => {
        service.testValidateRequired(params, requiredFields);
      }).toThrow(VMServerError);
    });

    it('should throw error for null/undefined/empty values', () => {
      const params = { field1: null, field2: undefined, field3: '' };
      const requiredFields = ['field1', 'field2', 'field3'];
      
      expect(() => {
        service.testValidateRequired(params, requiredFields);
      }).toThrow(VMServerError);
    });
  });

  describe('validateConnectionConfig', () => {
    it('should pass validation with valid config', () => {
      expect(() => {
        service.testValidateConnectionConfig(mockConfig);
      }).not.toThrow();
    });

    it('should throw error for missing host', () => {
      const invalidConfig = { ...mockConfig, host: '' };
      
      expect(() => {
        service.testValidateConnectionConfig(invalidConfig);
      }).toThrow(VMServerError);
    });

    it('should throw error for invalid port', () => {
      const invalidConfig = { ...mockConfig, port: 70000 };
      
      expect(() => {
        service.testValidateConnectionConfig(invalidConfig);
      }).toThrow(VMServerError);
    });

    it('should throw error when neither password nor privateKey is provided', () => {
      const invalidConfig = { ...mockConfig };
      delete invalidConfig.password;
      
      expect(() => {
        service.testValidateConnectionConfig(invalidConfig);
      }).toThrow(VMServerError);
    });

    it('should pass validation with privateKey instead of password', () => {
      const configWithKey = { ...mockConfig };
      delete configWithKey.password;
      configWithKey.privateKey = 'test-private-key';
      
      expect(() => {
        service.testValidateConnectionConfig(configWithKey);
      }).not.toThrow();
    });
  });

  describe('validateCommand', () => {
    it('should pass validation for allowed commands', () => {
      const allowedCommands = ['docker', 'ls', 'cat', 'echo', 'pwd'];
      
      allowedCommands.forEach(cmd => {
        expect(() => {
          service.testValidateCommand(cmd);
        }).not.toThrow();
      });
    });

    it('should throw error for disallowed commands', () => {
      const disallowedCommands = ['rm', 'sudo', 'chmod', 'chown'];
      
      disallowedCommands.forEach(cmd => {
        expect(() => {
          service.testValidateCommand(cmd);
        }).toThrow(VMServerError);
      });
    });

    it('should throw error for commands with restricted paths', () => {
      const restrictedCommands = [
        'cat /etc/passwd',
        'ls /root/.ssh',
        'echo test > /etc/shadow'
      ];
      
      restrictedCommands.forEach(cmd => {
        expect(() => {
          service.testValidateCommand(cmd);
        }).toThrow(VMServerError);
      });
    });

    it('should throw error for dangerous command patterns', () => {
      const dangerousCommands = [
        'ls; rm -rf /',
        'echo test && rm -rf /',
        'ls | rm -rf /',
        'echo `rm -rf /`',
        'echo $(rm -rf /)'
      ];
      
      dangerousCommands.forEach(cmd => {
        expect(() => {
          service.testValidateCommand(cmd);
        }).toThrow(VMServerError);
      });
    });
  });

  describe('formatPagination', () => {
    it('should return default pagination when no params provided', () => {
      const result = service.testFormatPagination();
      
      expect(result.limit).toBe(50);
      expect(result.offset).toBe(0);
      expect(result.orderBy).toBe('timestamp');
      expect(result.orderDirection).toBe('desc');
    });

    it('should use provided pagination params', () => {
      const params = {
        limit: 25,
        offset: 10,
        orderBy: 'name',
        orderDirection: 'asc' as const
      };
      
      const result = service.testFormatPagination(params);
      
      expect(result.limit).toBe(25);
      expect(result.offset).toBe(10);
      expect(result.orderBy).toBe('name');
      expect(result.orderDirection).toBe('asc');
    });

    it('should enforce maximum limit', () => {
      const params = { limit: 1000 };
      
      const result = service.testFormatPagination(params);
      
      expect(result.limit).toBeLessThanOrEqual(10); // Based on BATCH_LIMITS.MAX_COMMANDS_PER_BATCH
    });
  });

  describe('generateCacheKey', () => {
    it('should generate cache key with VM ID prefix', () => {
      const key = service.testGenerateCacheKey('test', 'part1', 'part2');
      
      expect(key).toBe('vm:test-vm-001:test:part1:part2');
    });

    it('should handle empty parts', () => {
      const key = service.testGenerateCacheKey('test');
      
      expect(key).toBe('vm:test-vm-001:test:');
    });
  });

  describe('healthCheck', () => {
    it('should return true for test implementation', async () => {
      const result = await service.healthCheck();
      
      expect(result).toBe(true);
    });
  });

  describe('getMetrics', () => {
    it('should return service metrics', () => {
      const metrics = service.getMetrics();
      
      expect(metrics.serviceName).toBe('TestVMService');
      expect(metrics.vmId).toBe('test-vm-001');
      expect(metrics.config).toBeDefined();
      expect(metrics.timestamp).toBeDefined();
    });
  });
});

describe('VMConnectionPool', () => {
  let pool: VMConnectionPool;
  const vmId = 'test-vm-pool';

  beforeEach(() => {
    pool = new VMConnectionPool(vmId, 2); // Small pool for testing
  });

  afterEach(() => {
    pool.cleanup();
  });

  describe('acquireConnection', () => {
    it('should acquire connection when pool has capacity', async () => {
      const result = await pool.acquireConnection();
      
      expect(result).toBe(true);
      expect(pool.getStats().activeConnections).toBe(1);
    });

    it('should queue requests when pool is full', async () => {
      // Fill the pool
      await pool.acquireConnection();
      await pool.acquireConnection();
      
      expect(pool.getStats().activeConnections).toBe(2);
      
      // This should be queued
      const queuedPromise = pool.acquireConnection();
      
      expect(pool.getStats().queuedRequests).toBe(1);
      
      // Release a connection to process the queue
      pool.releaseConnection();
      
      const result = await queuedPromise;
      expect(result).toBe(true);
    });

    it('should timeout queued requests', async () => {
      // Fill the pool
      await pool.acquireConnection();
      await pool.acquireConnection();
      
      // Mock timeout to be very short for testing
      jest.useFakeTimers();
      
      const queuedPromise = pool.acquireConnection();
      
      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(31000); // Longer than VM_SERVER_CONFIG.timeout
      
      await expect(queuedPromise).rejects.toThrow(VMServerError);
      
      jest.useRealTimers();
    });
  });

  describe('releaseConnection', () => {
    it('should release connection and update stats', async () => {
      await pool.acquireConnection();
      expect(pool.getStats().activeConnections).toBe(1);
      
      pool.releaseConnection();
      expect(pool.getStats().activeConnections).toBe(0);
    });

    it('should process queued requests when connection is released', async () => {
      // Fill the pool
      await pool.acquireConnection();
      await pool.acquireConnection();
      
      // Queue a request
      const queuedPromise = pool.acquireConnection();
      expect(pool.getStats().queuedRequests).toBe(1);
      
      // Release a connection
      pool.releaseConnection();
      
      // The queued request should be processed
      const result = await queuedPromise;
      expect(result).toBe(true);
      expect(pool.getStats().queuedRequests).toBe(0);
    });
  });

  describe('getStats', () => {
    it('should return accurate pool statistics', async () => {
      const initialStats = pool.getStats();
      expect(initialStats.vmId).toBe(vmId);
      expect(initialStats.activeConnections).toBe(0);
      expect(initialStats.maxConnections).toBe(2);
      expect(initialStats.queuedRequests).toBe(0);
      expect(initialStats.utilizationPercent).toBe(0);
      
      await pool.acquireConnection();
      
      const afterAcquireStats = pool.getStats();
      expect(afterAcquireStats.activeConnections).toBe(1);
      expect(afterAcquireStats.utilizationPercent).toBe(50);
    });
  });

  describe('cleanup', () => {
    it('should reject all queued requests and reset connections', async () => {
      // Fill the pool and queue requests
      await pool.acquireConnection();
      await pool.acquireConnection();
      
      const queuedPromise1 = pool.acquireConnection();
      const queuedPromise2 = pool.acquireConnection();
      
      expect(pool.getStats().queuedRequests).toBe(2);
      
      // Cleanup should reject queued requests
      await pool.cleanup();
      
      await expect(queuedPromise1).rejects.toThrow(VMServerError);
      await expect(queuedPromise2).rejects.toThrow(VMServerError);
      
      expect(pool.getStats().activeConnections).toBe(0);
      expect(pool.getStats().queuedRequests).toBe(0);
    });
  });
});

describe('VMConnectionPoolManager', () => {
  beforeEach(() => {
    // Clean up any existing pools
    vmConnectionPoolManager.cleanupAllPools();
  });

  afterEach(() => {
    vmConnectionPoolManager.cleanupAllPools();
  });

  describe('getPool', () => {
    it('should create new pool for new VM ID', () => {
      const pool1 = vmConnectionPoolManager.getPool('vm-1');
      const pool2 = vmConnectionPoolManager.getPool('vm-2');
      
      expect(pool1).toBeDefined();
      expect(pool2).toBeDefined();
      expect(pool1).not.toBe(pool2);
    });

    it('should return same pool for same VM ID', () => {
      const pool1 = vmConnectionPoolManager.getPool('vm-1');
      const pool2 = vmConnectionPoolManager.getPool('vm-1');
      
      expect(pool1).toBe(pool2);
    });
  });

  describe('cleanupPool', () => {
    it('should cleanup specific pool', async () => {
      const pool = vmConnectionPoolManager.getPool('vm-1');
      await pool.acquireConnection();
      
      expect(pool.getStats().activeConnections).toBe(1);
      
      await vmConnectionPoolManager.cleanupPool('vm-1');
      
      // Pool should be removed from manager
      const newPool = vmConnectionPoolManager.getPool('vm-1');
      expect(newPool).not.toBe(pool);
      expect(newPool.getStats().activeConnections).toBe(0);
    });
  });

  describe('getAllPoolStats', () => {
    it('should return stats for all pools', async () => {
      const pool1 = vmConnectionPoolManager.getPool('vm-1');
      const pool2 = vmConnectionPoolManager.getPool('vm-2');
      
      await pool1.acquireConnection();
      await pool2.acquireConnection();
      await pool2.acquireConnection();
      
      const allStats = vmConnectionPoolManager.getAllPoolStats();
      
      expect(allStats).toHaveLength(2);
      expect(allStats.find(s => s.vmId === 'vm-1')?.activeConnections).toBe(1);
      expect(allStats.find(s => s.vmId === 'vm-2')?.activeConnections).toBe(2);
    });
  });
});
