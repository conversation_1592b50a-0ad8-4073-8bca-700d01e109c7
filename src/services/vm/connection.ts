/**
 * VM Connection Service
 * Handles establishing secure connections to VMs, managing connection pools, authentication, and lifecycle
 */

import { Client as SSHClient, ConnectConfig } from 'ssh2';
import { BaseVMService, VMServiceResult, vmConnectionPoolManager } from './base';
import { vmLogger, VM_SERVER_CONFIG, handleVMServerError } from '@/lib/vm-server';
import {
  VMConnectionConfig,
  VMConnectionStatus,
  VMAuthCredentials,
  VMSession,
  VMSessionOperation
} from '@/types/vm';

// SSH connection wrapper
export interface SSHConnection {
  id: string;
  client: SSHClient;
  config: VMConnectionConfig;
  status: VMConnectionStatus;
  createdAt: Date;
  lastUsed: Date;
  isActive: boolean;
}

// Connection service class
export class VMConnectionService extends BaseVMService {
  private connections: Map<string, SSHConnection> = new Map();
  private activeSessions: Map<string, VMSession> = new Map();

  constructor(vmId: string, connectionConfig: VMConnectionConfig) {
    super('VMConnectionService', vmId, connectionConfig);
    this.validateConnectionConfig(connectionConfig);
  }

  // Establish SSH connection to VM
  async connect(credentials?: VMAuthCredentials): Promise<VMServiceResult<VMConnectionStatus>> {
    return this.executeVMOperation('connect', async () => {
      const connectionId = this.generateConnectionId();
      const pool = vmConnectionPoolManager.getPool(this.vmId);

      // Acquire connection from pool
      await pool.acquireConnection();

      try {
        const sshConfig = this.buildSSHConfig(credentials);
        const client = new SSHClient();
        
        const connectionPromise = new Promise<VMConnectionStatus>((resolve, reject) => {
          const timeout = setTimeout(() => {
            client.destroy();
            reject(new Error('Connection timeout'));
          }, VM_SERVER_CONFIG.ssh.timeout);

          client.on('ready', () => {
            clearTimeout(timeout);
            vmLogger.info(`SSH connection established to VM ${this.vmId}`, {
              connectionId,
              host: this.connectionConfig.host
            });

            const status: VMConnectionStatus = {
              isConnected: true,
              connectionId,
              establishedAt: new Date(),
              lastActivity: new Date(),
              latency: Date.now() - startTime
            };

            // Store connection
            const connection: SSHConnection = {
              id: connectionId,
              client,
              config: this.connectionConfig,
              status,
              createdAt: new Date(),
              lastUsed: new Date(),
              isActive: true
            };

            this.connections.set(connectionId, connection);
            resolve(status);
          });

          client.on('error', (error) => {
            clearTimeout(timeout);
            pool.releaseConnection();
            reject(handleVMServerError(error, this.vmId, connectionId));
          });

          client.on('close', () => {
            vmLogger.debug(`SSH connection closed for VM ${this.vmId}`, { connectionId });
            this.connections.delete(connectionId);
            pool.releaseConnection();
          });

          const startTime = Date.now();
          client.connect(sshConfig);
        });

        return await connectionPromise;
      } catch (error) {
        pool.releaseConnection();
        throw error;
      }
    });
  }

  // Disconnect from VM
  async disconnect(connectionId: string): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('disconnect', async () => {
      const connection = this.connections.get(connectionId);
      if (!connection) {
        throw new Error(`Connection ${connectionId} not found`);
      }

      connection.client.destroy();
      this.connections.delete(connectionId);

      // Clean up any active sessions for this connection
      const sessionsToRemove = Array.from(this.activeSessions.entries())
        .filter(([_, session]) => session.connectionId === connectionId)
        .map(([sessionId]) => sessionId);

      sessionsToRemove.forEach(sessionId => {
        this.activeSessions.delete(sessionId);
      });

      vmLogger.info(`Disconnected from VM ${this.vmId}`, { connectionId });
      return true;
    }, false, connectionId);
  }

  // Execute command on VM
  async executeCommand(
    connectionId: string,
    command: string,
    options: {
      timeout?: number;
      cwd?: string;
      env?: Record<string, string>;
    } = {}
  ): Promise<VMServiceResult<{ stdout: string; stderr: string; exitCode: number }>> {
    return this.executeVMOperation('executeCommand', async () => {
      this.validateCommand(command);
      
      const connection = this.connections.get(connectionId);
      if (!connection || !connection.isActive) {
        throw new Error(`No active connection found: ${connectionId}`);
      }

      // Update last used timestamp
      connection.lastUsed = new Date();
      connection.status.lastActivity = new Date();

      const commandWithOptions = this.buildCommandWithOptions(command, options);
      
      return new Promise<{ stdout: string; stderr: string; exitCode: number }>((resolve, reject) => {
        connection.client.exec(commandWithOptions, (err, stream) => {
          if (err) {
            reject(handleVMServerError(err, this.vmId, connectionId));
            return;
          }

          let stdout = '';
          let stderr = '';
          let exitCode = 0;

          const timeout = setTimeout(() => {
            stream.destroy();
            reject(new Error(`Command execution timeout: ${command}`));
          }, options.timeout || VM_SERVER_CONFIG.ssh.timeout);

          stream.on('close', (code: number) => {
            clearTimeout(timeout);
            exitCode = code;
            resolve({ stdout, stderr, exitCode });
          });

          stream.on('data', (data: Buffer) => {
            stdout += data.toString();
          });

          stream.stderr.on('data', (data: Buffer) => {
            stderr += data.toString();
          });

          stream.on('error', (error: Error) => {
            clearTimeout(timeout);
            reject(handleVMServerError(error, this.vmId, connectionId));
          });
        });
      });
    }, true, connectionId);
  }

  // Get connection status
  async getConnectionStatus(connectionId?: string): Promise<VMConnectionStatus> {
    if (!connectionId) {
      throw new Error('Connection ID is required');
    }
    const result = await this.executeVMOperation('getConnectionStatus', async () => {
      const connection = this.connections.get(connectionId);
      if (!connection) {
        throw new Error(`Connection ${connectionId} not found`);
      }

      return connection.status;
    }, false, connectionId);
    if (!result.data) {
      throw new Error('Failed to get connection status');
    }
    return result.data;
  }

  // List all active connections
  async listConnections(): Promise<VMServiceResult<VMConnectionStatus[]>> {
    return this.executeVMOperation('listConnections', async () => {
      return Array.from(this.connections.values()).map(conn => conn.status);
    }, false);
  }

  // Create session for tracking operations
  async createSession(connectionId: string, userId: string, metadata?: Record<string, any>): Promise<VMServiceResult<VMSession>> {
    return this.executeVMOperation('createSession', async () => {
      const connection = this.connections.get(connectionId);
      if (!connection || !connection.isActive) {
        throw new Error(`No active connection found: ${connectionId}`);
      }

      const sessionId = this.generateSessionId();
      const session: VMSession = {
        id: sessionId,
        vmId: this.vmId,
        userId,
        connectionId,
        startTime: new Date(),
        lastActivity: new Date(),
        isActive: true,
        operations: [],
        metadata: metadata || {}
      };

      this.activeSessions.set(sessionId, session);
      vmLogger.info(`Session created for VM ${this.vmId}`, { sessionId, connectionId, userId });

      return session;
    }, false, connectionId);
  }

  // Close session
  async closeSession(sessionId: string): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('closeSession', async () => {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }

      session.isActive = false;
      this.activeSessions.delete(sessionId);

      vmLogger.info(`Session closed for VM ${this.vmId}`, { sessionId });
      return true;
    }, false);
  }

  // Health check implementation
  async healthCheck(): Promise<boolean> {
    try {
      // Check if we can establish a basic connection
      const result = await this.connect();
      if (result.success && result.data) {
        await this.disconnect(result.data.connectionId);
        return true;
      }
      return false;
    } catch (error) {
      vmLogger.error(`VM connection health check failed for ${this.vmId}`, error);
      return false;
    }
  }

  // Private helper methods
  private generateConnectionId(): string {
    return `conn_${this.vmId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `sess_${this.vmId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private buildSSHConfig(credentials?: VMAuthCredentials): ConnectConfig {
    const config: ConnectConfig = {
      host: this.connectionConfig.host,
      port: this.connectionConfig.port,
      username: credentials?.username || this.connectionConfig.username,
      keepaliveInterval: VM_SERVER_CONFIG.ssh.keepAliveInterval,
      keepaliveCountMax: 3,
      readyTimeout: VM_SERVER_CONFIG.ssh.timeout,
    };

    // Authentication
    if (credentials?.privateKey || this.connectionConfig.privateKey) {
      config.privateKey = credentials?.privateKey || this.connectionConfig.privateKey;
      if (credentials?.passphrase || this.connectionConfig.passphrase) {
        config.passphrase = credentials?.passphrase || this.connectionConfig.passphrase;
      }
    } else if (credentials?.password || this.connectionConfig.password) {
      config.password = credentials?.password || this.connectionConfig.password;
    }

    return config;
  }

  private buildCommandWithOptions(command: string, options: { cwd?: string; env?: Record<string, string> }): string {
    let fullCommand = command;

    // Add environment variables
    if (options.env) {
      const envVars = Object.entries(options.env)
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');
      fullCommand = `${envVars} ${fullCommand}`;
    }

    // Add working directory
    if (options.cwd) {
      fullCommand = `cd "${options.cwd}" && ${fullCommand}`;
    }

    return fullCommand;
  }

  // Cleanup all connections
  async cleanup(): Promise<void> {
    const disconnectPromises = Array.from(this.connections.keys()).map(connectionId =>
      this.disconnect(connectionId).catch(error => 
        vmLogger.error(`Error disconnecting ${connectionId}`, error)
      )
    );

    await Promise.all(disconnectPromises);
    this.connections.clear();
    this.activeSessions.clear();

    vmLogger.info(`VM connection service cleaned up for ${this.vmId}`);
  }
}
