/**
 * VM Infrastructure Monitoring Service
 * Monitors VM infrastructure status, resource usage tracking, system health checks, and performance metrics collection
 */

import { BaseVMService, VMServiceResult } from './base';
import { VMConnectionService } from './connection';
import { vmL<PERSON>ger, VM_SERVER_CONFIG } from '@/lib/vm-server';
import {
  VMConnectionConfig,
  VMSystemInfo,
  VMResourceMetrics,
  VMHealthCheck,
  DiskUsage,
  NetworkInterfaceInfo
} from '@/types/vm';

// System process information
export interface SystemProcess {
  pid: number;
  name: string;
  command: string;
  user: string;
  cpu: number;
  memory: number;
  startTime: Date;
  status: 'running' | 'sleeping' | 'stopped' | 'zombie';
}

// Service status information
export interface ServiceStatus {
  name: string;
  status: 'active' | 'inactive' | 'failed' | 'unknown';
  enabled: boolean;
  description: string;
  mainPid?: number;
  memory?: number;
  uptime?: number;
}

// Alert configuration
export interface AlertThreshold {
  metric: 'cpu' | 'memory' | 'disk' | 'network';
  threshold: number;
  operator: 'gt' | 'lt' | 'eq';
  duration: number; // seconds
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Alert information
export interface Alert {
  id: string;
  metric: string;
  currentValue: number;
  threshold: number;
  severity: AlertThreshold['severity'];
  message: string;
  triggeredAt: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
}

// VM Infrastructure Monitoring Service
export class VMMonitoringService extends BaseVMService {
  private connectionService: VMConnectionService;
  private activeAlerts: Map<string, Alert> = new Map();
  private metricsHistory: VMResourceMetrics[] = [];
  private monitoringInterval?: NodeJS.Timeout;

  constructor(vmId: string, connectionConfig: VMConnectionConfig) {
    super('VMMonitoringService', vmId, connectionConfig);
    this.connectionService = new VMConnectionService(vmId, connectionConfig);
  }

  // Get comprehensive system information
  async getSystemInfo(connectionId: string): Promise<VMServiceResult<VMSystemInfo>> {
    return this.executeVMOperation('getSystemInfo', async () => {
      const commands = {
        hostname: 'hostname',
        platform: 'uname -s',
        arch: 'uname -m',
        release: 'uname -r',
        uptime: 'cat /proc/uptime | cut -d" " -f1',
        loadavg: 'cat /proc/loadavg',
        meminfo: 'cat /proc/meminfo',
        cpuinfo: 'nproc',
        diskusage: 'df -h --output=source,size,used,avail,pcent,target | tail -n +2',
        networkinfo: 'ip -j addr show'
      };

      const results: Record<string, string> = {};
      
      // Execute all commands
      for (const [key, command] of Object.entries(commands)) {
        const result = await this.connectionService.executeCommand(connectionId, command);
        if (result.success && result.data && result.data.exitCode === 0) {
          results[key] = result.data.stdout.trim();
        } else {
          vmLogger.warn(`Failed to execute ${key} command`, { command, error: result.error });
          results[key] = '';
        }
      }

      // Parse results
      const systemInfo: VMSystemInfo = {
        hostname: results.hostname,
        platform: results.platform,
        arch: results.arch,
        release: results.release,
        uptime: parseFloat(results.uptime) || 0,
        loadAverage: this.parseLoadAverage(results.loadavg),
        totalMemory: this.parseMemoryInfo(results.meminfo).total,
        freeMemory: this.parseMemoryInfo(results.meminfo).free,
        cpuCount: parseInt(results.cpuinfo) || 1,
        diskUsage: this.parseDiskUsage(results.diskusage),
        networkInterfaces: this.parseNetworkInterfaces(results.networkinfo)
      };

      return systemInfo;
    }, true, connectionId);
  }

  // Get current resource metrics
  async getResourceMetrics(connectionId: string): Promise<VMServiceResult<VMResourceMetrics>> {
    return this.executeVMOperation('getResourceMetrics', async () => {
      const commands = {
        cpu: 'top -bn1 | grep "Cpu(s)" | sed "s/.*, *\\([0-9.]*\\)%* id.*/\\1/" | awk \'{print 100 - $1}\'',
        loadavg: 'cat /proc/loadavg',
        processes: 'ps aux | wc -l',
        meminfo: 'cat /proc/meminfo',
        diskstats: 'cat /proc/diskstats',
        netstats: 'cat /proc/net/dev'
      };

      const results: Record<string, string> = {};
      
      for (const [key, command] of Object.entries(commands)) {
        const result = await this.connectionService.executeCommand(connectionId, command);
        if (result.success && result.data && result.data.exitCode === 0) {
          results[key] = result.data.stdout.trim();
        } else {
          results[key] = '';
        }
      }

      const memInfo = this.parseMemoryInfo(results.meminfo);
      const diskStats = this.parseDiskStats(results.diskstats);
      const netStats = this.parseNetworkStats(results.netstats);

      const metrics: VMResourceMetrics = {
        timestamp: new Date(),
        cpu: {
          usage: parseFloat(results.cpu) || 0,
          loadAverage: this.parseLoadAverage(results.loadavg),
          processes: parseInt(results.processes) || 0
        },
        memory: {
          total: memInfo.total,
          used: memInfo.used,
          free: memInfo.free,
          cached: memInfo.cached,
          buffers: memInfo.buffers,
          usagePercent: Math.round((memInfo.used / memInfo.total) * 100)
        },
        disk: {
          total: diskStats.total,
          used: diskStats.used,
          free: diskStats.free,
          usagePercent: Math.round((diskStats.used / diskStats.total) * 100),
          ioStats: diskStats.ioStats
        },
        network: {
          bytesIn: netStats.bytesIn,
          bytesOut: netStats.bytesOut,
          packetsIn: netStats.packetsIn,
          packetsOut: netStats.packetsOut,
          errorsIn: netStats.errorsIn,
          errorsOut: netStats.errorsOut
        }
      };

      // Store in history
      this.metricsHistory.push(metrics);
      
      // Keep only last 100 entries
      if (this.metricsHistory.length > 100) {
        this.metricsHistory = this.metricsHistory.slice(-100);
      }

      return metrics;
    }, true, connectionId);
  }

  // Get running processes
  async getProcesses(
    connectionId: string,
    options: {
      sortBy?: 'cpu' | 'memory' | 'name';
      limit?: number;
      filter?: string;
    } = {}
  ): Promise<VMServiceResult<SystemProcess[]>> {
    return this.executeVMOperation('getProcesses', async () => {
      let command = 'ps aux --no-headers';
      
      if (options.sortBy === 'cpu') {
        command += ' --sort=-%cpu';
      } else if (options.sortBy === 'memory') {
        command += ' --sort=-%mem';
      }

      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data || result.data.exitCode !== 0) {
        throw new Error('Failed to get process list');
      }

      const processes = this.parseProcessList(result.data.stdout);
      
      // Apply filter
      let filteredProcesses = processes;
      if (options.filter) {
        const filterLower = options.filter.toLowerCase();
        filteredProcesses = processes.filter(proc => 
          proc.name.toLowerCase().includes(filterLower) ||
          proc.command.toLowerCase().includes(filterLower) ||
          proc.user.toLowerCase().includes(filterLower)
        );
      }

      // Apply limit
      if (options.limit) {
        filteredProcesses = filteredProcesses.slice(0, options.limit);
      }

      return filteredProcesses;
    }, true, connectionId);
  }

  // Get service statuses
  async getServiceStatuses(
    connectionId: string,
    serviceNames?: string[]
  ): Promise<VMServiceResult<ServiceStatus[]>> {
    return this.executeVMOperation('getServiceStatuses', async () => {
      let command = 'systemctl list-units --type=service --no-pager --no-legend';
      
      if (serviceNames && serviceNames.length > 0) {
        command = `systemctl status ${serviceNames.join(' ')} --no-pager --lines=0`;
      }

      const result = await this.connectionService.executeCommand(connectionId, command);
      
      if (!result.success || !result.data) {
        throw new Error('Failed to get service statuses');
      }

      return this.parseServiceStatuses(result.data.stdout);
    }, true, connectionId);
  }

  // Perform comprehensive health check
  async performHealthCheck(connectionId: string): Promise<VMServiceResult<VMHealthCheck>> {
    return this.executeVMOperation('performHealthCheck', async () => {
      const checks = {
        connection: false,
        ssh: false,
        docker: false,
        system: false,
        resources: false
      };

      const details: Record<string, string> = {};

      try {
        // Connection check
        const connectionStatus = await this.connectionService.getConnectionStatus(connectionId);
        checks.connection = connectionStatus.success && connectionStatus.data?.isConnected || false;
        if (!checks.connection) {
          details.connection = 'Connection not established or inactive';
        }

        // SSH check (already connected if we got here)
        checks.ssh = true;

        // Docker check
        try {
          const dockerResult = await this.connectionService.executeCommand(connectionId, 'docker version --format "{{.Server.Version}}"');
          checks.docker = dockerResult.success && dockerResult.data?.exitCode === 0;
          if (!checks.docker) {
            details.docker = 'Docker daemon not accessible';
          }
        } catch (error) {
          checks.docker = false;
          details.docker = `Docker check failed: ${error}`;
        }

        // System check
        try {
          const systemResult = await this.connectionService.executeCommand(connectionId, 'uptime');
          checks.system = systemResult.success && systemResult.data?.exitCode === 0;
          if (!checks.system) {
            details.system = 'System commands not responding';
          }
        } catch (error) {
          checks.system = false;
          details.system = `System check failed: ${error}`;
        }

        // Resources check
        try {
          const metricsResult = await this.getResourceMetrics(connectionId);
          if (metricsResult.success && metricsResult.data) {
            const metrics = metricsResult.data;
            const cpuOk = metrics.cpu.usage < VM_SERVER_CONFIG.monitoring.alertThresholds.cpuUsage;
            const memOk = metrics.memory.usagePercent < VM_SERVER_CONFIG.monitoring.alertThresholds.memoryUsage;
            const diskOk = metrics.disk.usagePercent < VM_SERVER_CONFIG.monitoring.alertThresholds.diskUsage;
            
            checks.resources = cpuOk && memOk && diskOk;
            
            if (!checks.resources) {
              const issues = [];
              if (!cpuOk) issues.push(`CPU: ${metrics.cpu.usage}%`);
              if (!memOk) issues.push(`Memory: ${metrics.memory.usagePercent}%`);
              if (!diskOk) issues.push(`Disk: ${metrics.disk.usagePercent}%`);
              details.resources = `Resource thresholds exceeded: ${issues.join(', ')}`;
            }
          } else {
            checks.resources = false;
            details.resources = 'Failed to collect resource metrics';
          }
        } catch (error) {
          checks.resources = false;
          details.resources = `Resource check failed: ${error}`;
        }

      } catch (error) {
        vmLogger.error(`Health check failed for VM ${this.vmId}`, error);
      }

      // Determine overall status
      const allChecks = Object.values(checks);
      const passedChecks = allChecks.filter(check => check).length;
      const totalChecks = allChecks.length;

      let status: VMHealthCheck['status'];
      if (passedChecks === totalChecks) {
        status = 'healthy';
      } else if (passedChecks >= totalChecks * 0.7) {
        status = 'degraded';
      } else {
        status = 'unhealthy';
      }

      const healthCheck: VMHealthCheck = {
        status,
        checks,
        lastCheck: new Date(),
        nextCheck: new Date(Date.now() + VM_SERVER_CONFIG.monitoring.healthCheckInterval),
        details: Object.keys(details).length > 0 ? details : undefined
      };

      return healthCheck;
    }, true, connectionId);
  }

  // Start continuous monitoring
  async startMonitoring(connectionId: string): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('startMonitoring', async () => {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }

      this.monitoringInterval = setInterval(async () => {
        try {
          const metricsResult = await this.getResourceMetrics(connectionId);
          if (metricsResult.success && metricsResult.data) {
            await this.checkAlerts(metricsResult.data);
          }
        } catch (error) {
          vmLogger.error(`Monitoring interval error for VM ${this.vmId}`, error);
        }
      }, VM_SERVER_CONFIG.monitoring.metricsInterval);

      vmLogger.info(`Started monitoring for VM ${this.vmId}`, { connectionId });
      return true;
    }, false, connectionId);
  }

  // Stop continuous monitoring
  async stopMonitoring(): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('stopMonitoring', async () => {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = undefined;
      }

      vmLogger.info(`Stopped monitoring for VM ${this.vmId}`);
      return true;
    }, false);
  }

  // Get metrics history
  async getMetricsHistory(
    limit: number = 50,
    startTime?: Date,
    endTime?: Date
  ): Promise<VMServiceResult<VMResourceMetrics[]>> {
    return this.executeVMOperation('getMetricsHistory', async () => {
      let filteredMetrics = this.metricsHistory;

      if (startTime) {
        filteredMetrics = filteredMetrics.filter(metric => metric.timestamp >= startTime);
      }

      if (endTime) {
        filteredMetrics = filteredMetrics.filter(metric => metric.timestamp <= endTime);
      }

      return filteredMetrics.slice(-limit);
    }, false);
  }

  // Get active alerts
  async getActiveAlerts(): Promise<VMServiceResult<Alert[]>> {
    return this.executeVMOperation('getActiveAlerts', async () => {
      return Array.from(this.activeAlerts.values()).filter(alert => !alert.resolvedAt);
    }, false);
  }

  // Acknowledge alert
  async acknowledgeAlert(alertId: string): Promise<VMServiceResult<boolean>> {
    return this.executeVMOperation('acknowledgeAlert', async () => {
      const alert = this.activeAlerts.get(alertId);
      if (!alert) {
        throw new Error(`Alert ${alertId} not found`);
      }

      alert.acknowledged = true;
      vmLogger.info(`Alert acknowledged: ${alertId}`, { vmId: this.vmId });
      return true;
    }, false);
  }

  // Health check implementation
  async healthCheck(): Promise<boolean> {
    try {
      const connectResult = await this.connectionService.connect();
      if (!connectResult.success || !connectResult.data) {
        return false;
      }

      const healthResult = await this.performHealthCheck(connectResult.data.connectionId);
      await this.connectionService.disconnect(connectResult.data.connectionId);

      return healthResult.success && healthResult.data?.status !== 'unhealthy';
    } catch (error) {
      vmLogger.error(`VM monitoring service health check failed for ${this.vmId}`, error);
      return false;
    }
  }

  // Private helper methods
  private parseLoadAverage(loadavg: string): number[] {
    const parts = loadavg.split(' ');
    return [
      parseFloat(parts[0]) || 0,
      parseFloat(parts[1]) || 0,
      parseFloat(parts[2]) || 0
    ];
  }

  private parseMemoryInfo(meminfo: string): {
    total: number;
    free: number;
    used: number;
    cached: number;
    buffers: number;
  } {
    const lines = meminfo.split('\n');
    const values: Record<string, number> = {};

    lines.forEach(line => {
      const match = line.match(/^(\w+):\s*(\d+)\s*kB/);
      if (match) {
        values[match[1]] = parseInt(match[2]) * 1024; // Convert to bytes
      }
    });

    const total = values.MemTotal || 0;
    const free = values.MemFree || 0;
    const cached = values.Cached || 0;
    const buffers = values.Buffers || 0;
    const used = total - free - cached - buffers;

    return { total, free, used, cached, buffers };
  }

  private parseDiskUsage(diskusage: string): DiskUsage[] {
    const lines = diskusage.split('\n').filter(line => line.trim());
    return lines.map(line => {
      const parts = line.split(/\s+/);
      return {
        filesystem: parts[0],
        size: this.parseSize(parts[1]),
        used: this.parseSize(parts[2]),
        available: this.parseSize(parts[3]),
        usePercent: parseInt(parts[4].replace('%', '')),
        mountPoint: parts[5]
      };
    });
  }

  private parseNetworkInterfaces(networkinfo: string): NetworkInterfaceInfo[] {
    try {
      const interfaces = JSON.parse(networkinfo);
      return interfaces.map((iface: any) => ({
        name: iface.ifname,
        address: iface.addr_info?.[0]?.local || '',
        netmask: iface.addr_info?.[0]?.prefixlen ? `/${iface.addr_info[0].prefixlen}` : '',
        family: iface.addr_info?.[0]?.family === 'inet' ? 'IPv4' : 'IPv6',
        mac: iface.address || '',
        internal: iface.ifname === 'lo',
        cidr: iface.addr_info?.[0] ? `${iface.addr_info[0].local}/${iface.addr_info[0].prefixlen}` : ''
      }));
    } catch (error) {
      vmLogger.warn('Failed to parse network interfaces', error);
      return [];
    }
  }

  private parseSize(sizeStr: string): number {
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)([KMGT]?)$/);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'K': return value * 1024;
      case 'M': return value * 1024 * 1024;
      case 'G': return value * 1024 * 1024 * 1024;
      case 'T': return value * 1024 * 1024 * 1024 * 1024;
      default: return value;
    }
  }

  private parseDiskStats(diskstats: string): {
    total: number;
    used: number;
    free: number;
    ioStats: {
      readBytes: number;
      writeBytes: number;
      readOps: number;
      writeOps: number;
    };
  } {
    // This is a simplified implementation
    // In a real implementation, you'd parse /proc/diskstats properly
    return {
      total: 0,
      used: 0,
      free: 0,
      ioStats: {
        readBytes: 0,
        writeBytes: 0,
        readOps: 0,
        writeOps: 0
      }
    };
  }

  private parseNetworkStats(netstats: string): {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    errorsIn: number;
    errorsOut: number;
  } {
    // This is a simplified implementation
    // In a real implementation, you'd parse /proc/net/dev properly
    return {
      bytesIn: 0,
      bytesOut: 0,
      packetsIn: 0,
      packetsOut: 0,
      errorsIn: 0,
      errorsOut: 0
    };
  }

  private parseProcessList(psOutput: string): SystemProcess[] {
    const lines = psOutput.split('\n').filter(line => line.trim());
    return lines.map(line => {
      const parts = line.trim().split(/\s+/);
      return {
        pid: parseInt(parts[1]),
        name: parts[10] || 'unknown',
        command: parts.slice(10).join(' '),
        user: parts[0],
        cpu: parseFloat(parts[2]),
        memory: parseFloat(parts[3]),
        startTime: new Date(), // This would need proper parsing
        status: 'running' // This would need proper parsing
      };
    });
  }

  private parseServiceStatuses(statusOutput: string): ServiceStatus[] {
    // This is a simplified implementation
    // In a real implementation, you'd parse systemctl output properly
    return [];
  }

  private async checkAlerts(metrics: VMResourceMetrics): Promise<void> {
    const thresholds = VM_SERVER_CONFIG.monitoring.alertThresholds;

    // Check CPU usage
    if (metrics.cpu.usage > thresholds.cpuUsage) {
      this.createAlert('cpu', metrics.cpu.usage, thresholds.cpuUsage, 'high');
    }

    // Check memory usage
    if (metrics.memory.usagePercent > thresholds.memoryUsage) {
      this.createAlert('memory', metrics.memory.usagePercent, thresholds.memoryUsage, 'high');
    }

    // Check disk usage
    if (metrics.disk.usagePercent > thresholds.diskUsage) {
      this.createAlert('disk', metrics.disk.usagePercent, thresholds.diskUsage, 'medium');
    }
  }

  private createAlert(
    metric: string,
    currentValue: number,
    threshold: number,
    severity: Alert['severity']
  ): void {
    const alertId = `${metric}_${Date.now()}`;
    const alert: Alert = {
      id: alertId,
      metric,
      currentValue,
      threshold,
      severity,
      message: `${metric.toUpperCase()} usage (${currentValue}%) exceeded threshold (${threshold}%)`,
      triggeredAt: new Date(),
      acknowledged: false
    };

    this.activeAlerts.set(alertId, alert);
    vmLogger.warn(`Alert triggered for VM ${this.vmId}`, alert);
  }

  // Cleanup
  async cleanup(): Promise<void> {
    await this.stopMonitoring();
    await this.connectionService.cleanup();
    this.activeAlerts.clear();
    this.metricsHistory = [];
    vmLogger.info(`VM monitoring service cleaned up for ${this.vmId}`);
  }
}
