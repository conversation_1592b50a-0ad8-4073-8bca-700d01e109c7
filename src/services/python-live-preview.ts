/**
 * Python Live Preview Service
 * Manages live preview for Python web applications with framework-specific handling
 */

import { 
  PythonLivePreview, 
  PythonFramework,
} from '@/types/python-workspace';
import { dockerService } from './docker';

export interface LivePreviewConfig {
  framework: PythonFramework;
  projectPath: string;
  port?: number;
  autoReload?: boolean;
  environment?: Record<string, string>;
  command?: string;
}

export interface LivePreviewStatus {
  isRunning: boolean;
  port?: number;
  url?: string;
  logs: string[];
  error?: string;
  startTime?: Date;
  lastActivity?: Date;
}

class PythonLivePreviewService {
  private previews: Map<string, PythonLivePreview> = new Map();
  private logBuffers: Map<string, string[]> = new Map();
  private maxLogLines = 1000;

  /**
   * Start live preview for a Python project
   */
  async startPreview(
    workspaceId: string,
    config: LivePreviewConfig
  ): Promise<PythonLivePreview> {
    const previewId = `${workspaceId}-${config.framework}`;
    
    // Stop existing preview if running
    if (this.previews.has(previewId)) {
      await this.stopPreview(workspaceId, config.framework);
    }

    try {
      // Get available port
      const port = config.port || this.getDefaultPort(config.framework);
      const availablePort = await this.findAvailablePort(workspaceId, port);

      // Build command for the framework
      const command = config.command || this.buildFrameworkCommand(config.framework, config.projectPath, availablePort);

      // Start the development server in the workspace container
      const result = await dockerService.execInContainer(workspaceId, command.split(' '));

      if (!result) {
        throw new Error('Failed to start development server');
      }

      // Create preview object
      const preview: PythonLivePreview = {
        framework: config.framework,
        port: availablePort,
        url: `http://localhost:${availablePort}`,
        status: 'starting',
        logs: [],
        process: {
          pid: 0, // Will be updated when we get the actual PID
          command,
          startTime: new Date(),
        },
      };

      // Store preview
      this.previews.set(previewId, preview);
      this.logBuffers.set(previewId, []);

      // Start monitoring the process
      this.monitorPreview(workspaceId, previewId, config);

      // Wait a moment for the server to start
      await this.waitForServerStart(workspaceId, availablePort);

      // Update status
      preview.status = 'running';
      this.previews.set(previewId, preview);

      return preview;

    } catch (error) {
      console.error('Error starting live preview:', error);
      
      const errorPreview: PythonLivePreview = {
        framework: config.framework,
        port: config.port || this.getDefaultPort(config.framework),
        url: '',
        status: 'error',
        logs: [error instanceof Error ? error.message : 'Unknown error'],
      };

      this.previews.set(previewId, errorPreview);
      return errorPreview;
    }
  }

  /**
   * Stop live preview
   */
  async stopPreview(workspaceId: string, framework: PythonFramework): Promise<void> {
    const previewId = `${workspaceId}-${framework}`;
    const preview = this.previews.get(previewId);

    if (!preview) {
      return;
    }

    try {
      // Kill the process
      if (preview.process?.pid) {
        await dockerService.execInContainer(workspaceId, ['kill', String(preview.process.pid)]);
      }

      // Also try to kill by port
      const killCommand = `lsof -ti:${preview.port} | xargs kill -9`;
      await dockerService.execInContainer(workspaceId, killCommand.split(' '));

      // Update status
      preview.status = 'stopped';
      this.previews.set(previewId, preview);

    } catch (error) {
      console.error('Error stopping live preview:', error);
    }
  }

  /**
   * Get preview status
   */
  getPreviewStatus(workspaceId: string, framework: PythonFramework): PythonLivePreview | null {
    const previewId = `${workspaceId}-${framework}`;
    return this.previews.get(previewId) || null;
  }

  /**
   * Get preview logs
   */
  getPreviewLogs(workspaceId: string, framework: PythonFramework): string[] {
    const previewId = `${workspaceId}-${framework}`;
    return this.logBuffers.get(previewId) || [];
  }

  /**
   * Restart preview
   */
  async restartPreview(workspaceId: string, config: LivePreviewConfig): Promise<PythonLivePreview> {
    await this.stopPreview(workspaceId, config.framework);
    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 2000));
    return this.startPreview(workspaceId, config);
  }

  /**
   * Get all active previews for a workspace
   */
  getWorkspacePreviews(workspaceId: string): PythonLivePreview[] {
    const previews: PythonLivePreview[] = [];
    
    for (const [previewId, preview] of this.previews.entries()) {
      if (previewId.startsWith(workspaceId)) {
        previews.push(preview);
      }
    }
    
    return previews;
  }

  /**
   * Build framework-specific command
   */
  private buildFrameworkCommand(framework: PythonFramework, projectPath: string, port: number): string {
    const commands: Record<PythonFramework, string> = {
      django: `cd ${projectPath} && source venv/bin/activate && python manage.py runserver 0.0.0.0:${port}`,
      flask: `cd ${projectPath} && source venv/bin/activate && FLASK_ENV=development FLASK_APP=app.py flask run --host=0.0.0.0 --port=${port}`,
      fastapi: `cd ${projectPath} && source venv/bin/activate && uvicorn main:app --host 0.0.0.0 --port ${port} --reload`,
      streamlit: `cd ${projectPath} && source venv/bin/activate && streamlit run app.py --server.address 0.0.0.0 --server.port ${port}`,
      gradio: `cd ${projectPath} && source venv/bin/activate && python app.py`,
    };

    return commands[framework];
  }

  /**
   * Get default port for framework
   */
  private getDefaultPort(framework: PythonFramework): number {
    const defaultPorts: Record<PythonFramework, number> = {
      django: 8000,
      flask: 5000,
      fastapi: 8000,
      streamlit: 8501,
      gradio: 7860,
    };

    return defaultPorts[framework];
  }

  /**
   * Find available port starting from the preferred port
   */
  private async findAvailablePort(workspaceId: string, preferredPort: number): Promise<number> {
    for (let port = preferredPort; port < preferredPort + 100; port++) {
      try {
        // Check if port is available in the container
        const result = await dockerService.execInContainer(
          workspaceId, 
          ['netstat', '-tuln', '|', 'grep', `:${port}`]
        );
        
        // If command fails or returns empty, port is available
        if (!result.trim()) {
          return port;
        }
      } catch (error) {
        // If there's an error checking, assume port is available
        return port;
      }
    }

    // Fallback to preferred port if no available port found
    return preferredPort;
  }

  /**
   * Wait for server to start
   */
  private async waitForServerStart(workspaceId: string, port: number, maxWaitMs = 30000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitMs) {
      try {
        // Check if server is responding
        const result = await dockerService.execInContainer(
          workspaceId,
          ['curl', '-s', '-o', '/dev/null', '-w', '"%{http_code}"', `http://localhost:${port}`, '||', 'echo', '"000"']
        );

        if (result.trim() !== '"000"') {
          return; // Server is responding
        }
      } catch (error) {
        // Continue waiting
      }

      // Wait 1 second before next check
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error('Server failed to start within timeout period');
  }

  /**
   * Monitor preview process
   */
  private async monitorPreview(
    workspaceId: string, 
    previewId: string, 
    config: LivePreviewConfig
  ): Promise<void> {
    const preview = this.previews.get(previewId);
    if (!preview) return;

    // Start log monitoring
    this.startLogMonitoring(workspaceId, previewId, config);

    // Start health monitoring
    this.startHealthMonitoring(workspaceId, previewId);
  }

  /**
   * Start log monitoring
   */
  private startLogMonitoring(
    workspaceId: string, 
    previewId: string, 
    config: LivePreviewConfig
  ): void {
    // This would ideally use WebSocket or Server-Sent Events for real-time logs
    // For now, we'll poll for logs periodically
    const logInterval = setInterval(async () => {
      try {
        const preview = this.previews.get(previewId);
        if (!preview || preview.status === 'stopped') {
          clearInterval(logInterval);
          return;
        }

        // Get recent logs (this is a simplified implementation)
        const result = await dockerService.execInContainer(
          workspaceId,
          ['tail', '-n', '10', '/tmp/preview-${config.framework}.log', '2>/dev/null', '||', 'echo', '"No logs available"']
        );

        if (result) {
          const logs = this.logBuffers.get(previewId) || [];
          const newLines = result.split('\n').filter((line: string) => line.trim());
          
          // Add new lines and limit buffer size
          logs.push(...newLines);
          if (logs.length > this.maxLogLines) {
            logs.splice(0, logs.length - this.maxLogLines);
          }
          
          this.logBuffers.set(previewId, logs);
          
          // Update preview logs
          preview.logs = logs.slice(-50); // Keep last 50 lines in preview object
          this.previews.set(previewId, preview);
        }
      } catch (error) {
        console.error('Error monitoring logs:', error);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(workspaceId: string, previewId: string): void {
    const healthInterval = setInterval(async () => {
      try {
        const preview = this.previews.get(previewId);
        if (!preview || preview.status === 'stopped') {
          clearInterval(healthInterval);
          return;
        }

        // Check if server is still responding
        const result = await dockerService.execInContainer(
          workspaceId,
          ['curl', '-s', '-o', '/dev/null', '-w', '"%{http_code}"', `http://localhost:${preview.port}`, '||', 'echo', '"000"']
        );

        const isHealthy = result.trim() !== '"000"';
        
        if (!isHealthy && preview.status === 'running') {
          preview.status = 'error';
          preview.logs.push('Server stopped responding');
          this.previews.set(previewId, preview);
        } else if (isHealthy && preview.status === 'error') {
          preview.status = 'running';
          preview.logs.push('Server recovered');
          this.previews.set(previewId, preview);
        }
      } catch (error) {
        console.error('Error monitoring health:', error);
      }
    }, 10000); // Check every 10 seconds
  }

  /**
   * Cleanup all previews for a workspace
   */
  async cleanupWorkspace(workspaceId: string): Promise<void> {
    const previewIds = Array.from(this.previews.keys()).filter(id => id.startsWith(workspaceId));
    
    for (const previewId of previewIds) {
      const preview = this.previews.get(previewId);
      if (preview) {
        await this.stopPreview(workspaceId, preview.framework);
        this.previews.delete(previewId);
        this.logBuffers.delete(previewId);
      }
    }
  }
}

// Export singleton instance
export const pythonLivePreviewService = new PythonLivePreviewService();
