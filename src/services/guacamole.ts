// Guacamole API Integration Service
// Manages connections and users in Apache Guacamole

export interface GuacamoleConnection {
  identifier: string;
  name: string;
  protocol: 'vnc' | 'rdp' | 'ssh';
  parameters: {
    hostname: string;
    port: string;
    password?: string;
    username?: string;
    [key: string]: string | undefined;
  };
  attributes: {
    'max-connections': string;
    'max-connections-per-user': string;
    [key: string]: string;
  };
}

export interface GuacamoleUser {
  username: string;
  password?: string;
  attributes: {
    disabled?: string;
    expired?: string;
    'access-window-start'?: string;
    'access-window-end'?: string;
    'valid-from'?: string;
    'valid-until'?: string;
    timezone?: string;
  };
}

export interface GuacamoleSession {
  identifier: string;
  username: string;
  remoteHost: string;
  startDate: string;
  endDate?: string;
  duration: number;
  active: boolean;
}

class GuacamoleService {
  private baseUrl: string;
  private authToken: string | null = null;
  private dataSource: string = 'postgresql';

  constructor() {
    this.baseUrl = process.env.GUACAMOLE_URL || 'http://localhost:8080';
  }

  /**
   * Authenticate with Guacamole API
   */
  async authenticate(username?: string, password?: string): Promise<boolean> {
    try {
      const authUsername = username || process.env.GUACAMOLE_USERNAME || 'guacadmin';
      const authPassword = password || process.env.GUACAMOLE_PASSWORD || 'guacadmin';

      const response = await fetch(`${this.baseUrl}/api/tokens`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: authUsername,
          password: authPassword,
        }),
      });

      if (!response.ok) {
        throw new Error(`Authentication failed: ${response.statusText}`);
      }

      const data = await response.json();
      this.authToken = data.authToken;
      this.dataSource = data.dataSource || 'postgresql';
      
      return true;
    } catch (error) {
      console.error('Guacamole authentication failed:', error);
      this.authToken = null;
      return false;
    }
  }

  /**
   * Ensure authentication before API calls
   */
  private async ensureAuthenticated(): Promise<void> {
    if (!this.authToken) {
      const authenticated = await this.authenticate();
      if (!authenticated) {
        throw new Error('Failed to authenticate with Guacamole');
      }
    }
  }

  /**
   * Make authenticated API request
   */
  private async apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    await this.ensureAuthenticated();

    const url = `${this.baseUrl}/api/session/data/${this.dataSource}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Guacamole-Token': this.authToken!,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Token expired, try to re-authenticate
        this.authToken = null;
        await this.ensureAuthenticated();
        
        // Retry the request
        return this.apiRequest(endpoint, options);
      }
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Create a new VNC connection for a workspace
   */
  async createWorkspaceConnection(
    workspaceId: string,
    workspaceName: string,
    hostname: string,
    port: number,
    password: string,
    userId: string
  ): Promise<string> {
    try {
      const connection: Omit<GuacamoleConnection, 'identifier'> = {
        name: `${workspaceName} (${userId})`,
        protocol: 'vnc',
        parameters: {
          hostname,
          port: port.toString(),
          password,
          'color-depth': '24',
          'cursor': 'remote',
          'swap-red-blue': 'false',
          'dest-port': port.toString(),
          'enable-wallpaper': 'true',
          'enable-theming': 'true',
          'enable-font-smoothing': 'true',
          'resize-method': 'reconnect',
        },
        attributes: {
          'max-connections': '1',
          'max-connections-per-user': '1',
        },
      };

      const result = await this.apiRequest('/connections', {
        method: 'POST',
        body: JSON.stringify(connection),
      });

      // Store the connection ID for later reference
      const connectionId = result.identifier;
      
      // Grant access to the user
      await this.grantConnectionAccess(connectionId, userId);

      return connectionId;
    } catch (error) {
      console.error('Failed to create Guacamole connection:', error);
      throw error;
    }
  }

  /**
   * Grant connection access to a user
   */
  async grantConnectionAccess(connectionId: string, username: string): Promise<void> {
    try {
      await this.apiRequest(`/users/${username}/permissions`, {
        method: 'PATCH',
        body: JSON.stringify([
          {
            op: 'add',
            path: `/connectionPermissions/${connectionId}`,
            value: ['READ'],
          },
        ]),
      });
    } catch (error) {
      console.error('Failed to grant connection access:', error);
      throw error;
    }
  }

  /**
   * Delete a connection
   */
  async deleteConnection(connectionId: string): Promise<void> {
    try {
      await this.apiRequest(`/connections/${connectionId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Failed to delete connection:', error);
      throw error;
    }
  }

  /**
   * List all connections
   */
  async listConnections(): Promise<GuacamoleConnection[]> {
    try {
      const connections = await this.apiRequest('/connections');
      return Object.values(connections) as GuacamoleConnection[];
    } catch (error) {
      console.error('Failed to list connections:', error);
      throw error;
    }
  }

  /**
   * Get connection details
   */
  async getConnection(connectionId: string): Promise<GuacamoleConnection | null> {
    try {
      return await this.apiRequest(`/connections/${connectionId}`);
    } catch (error) {
      console.error('Failed to get connection:', error);
      return null;
    }
  }

  /**
   * Create or update a user
   */
  async createUser(username: string, password: string): Promise<void> {
    try {
      const user: Omit<GuacamoleUser, 'username'> = {
        password,
        attributes: {
          disabled: 'false',
          expired: 'false',
        },
      };

      await this.apiRequest(`/users/${username}`, {
        method: 'POST',
        body: JSON.stringify(user),
      });
    } catch (error) {
      // User might already exist, try to update
      try {
        await this.apiRequest(`/users/${username}`, {
          method: 'PUT',
          body: JSON.stringify({ password }),
        });
      } catch (updateError) {
        console.error('Failed to create or update user:', error);
        throw error;
      }
    }
  }

  /**
   * Get active sessions
   */
  async getActiveSessions(): Promise<GuacamoleSession[]> {
    try {
      const sessions = await this.apiRequest('/activeConnections');
      return Object.values(sessions) as GuacamoleSession[];
    } catch (error) {
      console.error('Failed to get active sessions:', error);
      throw error;
    }
  }

  /**
   * Get connection URL for embedding
   */
  getConnectionUrl(connectionId: string, username: string): string {
    return `${this.baseUrl}/#/client/${connectionId}?username=${username}`;
  }

  /**
   * Get connection embed URL for iframe
   */
  getEmbedUrl(connectionId: string): string {
    if (!this.authToken) {
      throw new Error('Not authenticated');
    }
    return `${this.baseUrl}/client.xhtml?id=${connectionId}&token=${this.authToken}`;
  }
}

export const guacamoleService = new GuacamoleService();
