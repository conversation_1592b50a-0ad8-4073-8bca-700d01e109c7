/**
 * File Management Service
 * Handles file operations, uploads, downloads, and file system management
 */

import { dockerService } from '../docker';

export interface FileItem {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  size: number;
  mimeType?: string;
  encoding?: string;
  permissions: string;
  owner: string;
  group: string;
  createdAt: Date;
  modifiedAt: Date;
  accessedAt: Date;
  isHidden: boolean;
  isSymlink: boolean;
  children?: FileItem[];
}

export interface FileContent {
  content: string;
  encoding: 'utf8' | 'base64' | 'binary';
  size: number;
  hash: string;
  language?: string;
  lineCount: number;
}

export interface FileUpload {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  progress: number;
  error?: string;
  uploadedAt?: Date;
}

export interface FileOperation {
  id: string;
  type: 'copy' | 'move' | 'delete' | 'rename' | 'create' | 'upload' | 'download';
  source: string;
  destination?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  error?: string;
  startTime: Date;
  endTime?: Date;
}

export interface DirectoryTree {
  path: string;
  name: string;
  type: 'file' | 'directory';
  size: number;
  children?: DirectoryTree[];
  isExpanded?: boolean;
  level: number;
}

class FileManagerService {
  // In-memory storage for demo - replace with persistent storage in production
  private uploads: Map<string, FileUpload> = new Map();
  private operations: Map<string, FileOperation> = new Map();

  /**
   * List directory contents
   */
  async listDirectory(
    workspaceId: string,
    path: string = '/home/<USER>',
    options: {
      showHidden?: boolean;
      recursive?: boolean;
      maxDepth?: number;
    } = {}
  ): Promise<FileItem[]> {
    try {
      const { showHidden = false, recursive = false, maxDepth = 1 } = options;
      
      // Build ls command with appropriate flags
      let command = `ls -la "${path}"`;
      if (!showHidden) {
        command = `ls -l "${path}"`;
      }

      const result = await dockerService.execInContainer(workspaceId, command.split(' '));

      const files = this.parseLsOutput(result || '', path);
      
      // If recursive, get children for directories
      if (recursive && maxDepth > 1) {
        for (const file of files) {
          if (file.type === 'directory' && !file.name.startsWith('.')) {
            file.children = await this.listDirectory(
              workspaceId,
              file.path,
              { showHidden, recursive, maxDepth: maxDepth - 1 }
            );
          }
        }
      }

      return files;
    } catch (error) {
      console.error('Error listing directory:', error);
      throw error;
    }
  }

  /**
   * Read file content
   */
  async readFile(workspaceId: string, filePath: string): Promise<FileContent> {
    try {
      // Check if file exists and get info
      const statResult = await dockerService.execInContainer(
        workspaceId,
        ['stat', '-c', '%s %Y', filePath]
      );

      if (!statResult) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Read file content
      const readResult = await dockerService.execInContainer(
        workspaceId,
        ['cat', filePath]
      );

      if (!readResult) {
        throw new Error(`Failed to read file`);
      }

      const content = readResult || '';
      const [size] = statResult?.split(' ') || ['0'];

      return {
        content,
        encoding: this.detectEncoding(content),
        size: parseInt(size),
        hash: this.calculateHash(content),
        language: this.detectLanguage(filePath),
        lineCount: content.split('\n').length,
      };
    } catch (error) {
      console.error('Error reading file:', error);
      throw error;
    }
  }

  /**
   * Write file content
   */
  async writeFile(
    workspaceId: string,
    filePath: string,
    content: string,
    options: {
      createDirectories?: boolean;
      backup?: boolean;
      encoding?: 'utf8' | 'base64';
    } = {}
  ): Promise<void> {
    try {
      const { createDirectories = true, backup = false, encoding = 'utf8' } = options;

      // Create directories if needed
      if (createDirectories) {
        const dirPath = filePath.substring(0, filePath.lastIndexOf('/'));
        if (dirPath) {
          await dockerService.execInContainer(
            workspaceId,
            ['mkdir', '-p', dirPath]
          );
        }
      }

      // Create backup if requested
      if (backup) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await dockerService.execInContainer(
          workspaceId,
          ['sh', '-c', `cp "${filePath}" "${backupPath}" 2>/dev/null || true`]
        );
      }

      // Write file content
      const escapedContent = content.replace(/'/g, "'\"'\"'");
      const writeCommand = encoding === 'base64'
        ? ['sh', '-c', `echo '${escapedContent}' | base64 -d > "${filePath}"`]
        : ['sh', '-c', `echo '${escapedContent}' > "${filePath}"`];

      const result = await dockerService.execInContainer(workspaceId, writeCommand);

      if (!result) {
        throw new Error(`Failed to write file`);
      }
    } catch (error) {
      console.error('Error writing file:', error);
      throw error;
    }
  }

  /**
   * Create directory
   */
  async createDirectory(
    workspaceId: string,
    dirPath: string,
    options: { recursive?: boolean } = {}
  ): Promise<void> {
    try {
      const { recursive = true } = options;
      const command = recursive ? ['mkdir', '-p', dirPath] : ['mkdir', dirPath];

      const result = await dockerService.execInContainer(workspaceId, command);

      if (!result) {
        throw new Error(`Failed to create directory`);
      }
    } catch (error) {
      console.error('Error creating directory:', error);
      throw error;
    }
  }

  /**
   * Delete file or directory
   */
  async deleteItem(
    workspaceId: string,
    itemPath: string,
    options: { recursive?: boolean; force?: boolean } = {}
  ): Promise<void> {
    try {
      const { recursive = false, force = false } = options;
      
      let command = ['rm'];
      if (recursive) command.push('-r');
      if (force) command.push('-f');
      command.push(itemPath);

      const result = await dockerService.execInContainer(workspaceId, command);

      if (!result) {
        throw new Error(`Failed to delete item`);
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      throw error;
    }
  }

  /**
   * Copy file or directory
   */
  async copyItem(
    workspaceId: string,
    sourcePath: string,
    destinationPath: string,
    options: { recursive?: boolean; preserveAttributes?: boolean } = {}
  ): Promise<void> {
    try {
      const { recursive = false, preserveAttributes = true } = options;

      let command = ['cp'];
      if (recursive) command.push('-r');
      if (preserveAttributes) command.push('-p');
      command.push(sourcePath, destinationPath);

      const result = await dockerService.execInContainer(workspaceId, command);

      if (!result) {
        throw new Error(`Failed to copy item`);
      }
    } catch (error) {
      console.error('Error copying item:', error);
      throw error;
    }
  }

  /**
   * Move/rename file or directory
   */
  async moveItem(
    workspaceId: string,
    sourcePath: string,
    destinationPath: string
  ): Promise<void> {
    try {
      const result = await dockerService.execInContainer(
        workspaceId,
        ['mv', sourcePath, destinationPath]
      );

      if (!result) {
        throw new Error(`Failed to move item`);
      }
    } catch (error) {
      console.error('Error moving item:', error);
      throw error;
    }
  }

  /**
   * Search files
   */
  async searchFiles(
    workspaceId: string,
    query: string,
    options: {
      path?: string;
      fileTypes?: string[];
      caseSensitive?: boolean;
      regex?: boolean;
      maxResults?: number;
    } = {}
  ): Promise<Array<{ path: string; matches: Array<{ line: number; content: string }> }>> {
    try {
      const {
        path = '/home/<USER>',
        fileTypes = [],
        caseSensitive = false,
        regex = false,
        maxResults = 100,
      } = options;

      // Build grep command
      let command = 'grep -rn';
      if (!caseSensitive) command += 'i';
      if (!regex) command += 'F'; // Fixed strings
      
      // Add file type filters
      if (fileTypes.length > 0) {
        const includePatterns = fileTypes.map(type => `--include="*.${type}"`).join(' ');
        command += ` ${includePatterns}`;
      }

      command += ` "${query}" "${path}" | head -${maxResults}`;

      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      if (!result) {
        return []; // No matches found
      }

      return this.parseGrepOutput(result || '');
    } catch (error) {
      console.error('Error searching files:', error);
      throw error;
    }
  }

  /**
   * Get file/directory info
   */
  async getItemInfo(workspaceId: string, itemPath: string): Promise<FileItem> {
    try {
      const result = await dockerService.execInContainer(
        workspaceId,
        ['stat', '-c', '%n|%s|%F|%a|%U|%G|%Y|%X|%Z', itemPath]
      );

      if (!result) {
        throw new Error(`Failed to get item info`);
      }

      return this.parseStatOutput(result || '', itemPath);
    } catch (error) {
      console.error('Error getting item info:', error);
      throw error;
    }
  }

  /**
   * Upload file
   */
  async uploadFile(
    workspaceId: string,
    file: File,
    destinationPath: string
  ): Promise<FileUpload> {
    try {
      const upload: FileUpload = {
        id: this.generateId(),
        name: file.name,
        size: file.size,
        type: file.type,
        status: 'uploading',
        progress: 0,
      };

      this.uploads.set(upload.id, upload);

      // Convert file to base64 for transfer
      const base64Content = await this.fileToBase64(file);
      
      // Write file to workspace
      await this.writeFile(workspaceId, destinationPath, base64Content, {
        encoding: 'base64',
        createDirectories: true,
      });

      // Update upload status
      upload.status = 'completed';
      upload.progress = 100;
      upload.uploadedAt = new Date();
      this.uploads.set(upload.id, upload);

      return upload;
    } catch (error) {
      console.error('Error uploading file:', error);
      
      // Update upload status on error
      const upload = this.uploads.get('temp-id');
      if (upload) {
        upload.status = 'failed';
        upload.error = error instanceof Error ? error.message : 'Upload failed';
        this.uploads.set(upload.id, upload);
      }
      
      throw error;
    }
  }

  /**
   * Download file
   */
  async downloadFile(workspaceId: string, filePath: string): Promise<Blob> {
    try {
      const fileContent = await this.readFile(workspaceId, filePath);
      
      // Convert content to blob
      const blob = new Blob([fileContent.content], {
        type: this.getMimeType(filePath),
      });

      return blob;
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }

  /**
   * Get directory tree
   */
  async getDirectoryTree(
    workspaceId: string,
    rootPath: string = '/home/<USER>',
    maxDepth: number = 3
  ): Promise<DirectoryTree> {
    try {
      const result = await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `find "${rootPath}" -maxdepth ${maxDepth} -type d | head -100`]
      );

      if (!result) {
        throw new Error(`Failed to get directory tree`);
      }

      return this.buildDirectoryTree(result || '', rootPath);
    } catch (error) {
      console.error('Error getting directory tree:', error);
      throw error;
    }
  }

  // Private helper methods

  private parseLsOutput(output: string, basePath: string): FileItem[] {
    const lines = output.split('\n').filter(line => line.trim());
    const files: FileItem[] = [];

    for (const line of lines) {
      if (line.startsWith('total ') || !line.trim()) continue;

      const parts = line.split(/\s+/);
      if (parts.length < 9) continue;

      const permissions = parts[0];
      const owner = parts[2];
      const group = parts[3];
      const size = parseInt(parts[4]);
      const name = parts.slice(8).join(' ');

      if (name === '.' || name === '..') continue;

      const isDirectory = permissions.startsWith('d');
      const isSymlink = permissions.startsWith('l');
      const isHidden = name.startsWith('.');

      files.push({
        id: this.generateId(),
        name,
        path: `${basePath}/${name}`.replace('//', '/'),
        type: isDirectory ? 'directory' : 'file',
        size,
        permissions,
        owner,
        group,
        createdAt: new Date(), // Would parse from ls output in real implementation
        modifiedAt: new Date(),
        accessedAt: new Date(),
        isHidden,
        isSymlink,
      });
    }

    return files;
  }

  private parseStatOutput(output: string, itemPath: string): FileItem {
    const parts = output.trim().split('|');
    
    return {
      id: this.generateId(),
      name: itemPath.split('/').pop() || '',
      path: itemPath,
      type: parts[2]?.includes('directory') ? 'directory' : 'file',
      size: parseInt(parts[1]) || 0,
      permissions: parts[3] || '',
      owner: parts[4] || '',
      group: parts[5] || '',
      createdAt: new Date(parseInt(parts[8]) * 1000),
      modifiedAt: new Date(parseInt(parts[6]) * 1000),
      accessedAt: new Date(parseInt(parts[7]) * 1000),
      isHidden: itemPath.split('/').pop()?.startsWith('.') || false,
      isSymlink: false,
    };
  }

  private parseGrepOutput(output: string): Array<{ path: string; matches: Array<{ line: number; content: string }> }> {
    const lines = output.split('\n').filter(line => line.trim());
    const results: Record<string, Array<{ line: number; content: string }>> = {};

    for (const line of lines) {
      const colonIndex = line.indexOf(':');
      const secondColonIndex = line.indexOf(':', colonIndex + 1);
      
      if (colonIndex === -1 || secondColonIndex === -1) continue;

      const filePath = line.substring(0, colonIndex);
      const lineNumber = parseInt(line.substring(colonIndex + 1, secondColonIndex));
      const content = line.substring(secondColonIndex + 1);

      if (!results[filePath]) {
        results[filePath] = [];
      }

      results[filePath].push({ line: lineNumber, content });
    }

    return Object.entries(results).map(([path, matches]) => ({ path, matches }));
  }

  private buildDirectoryTree(output: string, rootPath: string): DirectoryTree {
    const paths = output.split('\n').filter(path => path.trim());
    const tree: DirectoryTree = {
      path: rootPath,
      name: rootPath.split('/').pop() || 'workspace',
      type: 'directory',
      size: 0,
      children: [],
      level: 0,
    };

    // Build tree structure (simplified implementation)
    for (const path of paths) {
      if (path === rootPath) continue;
      
      const relativePath = path.replace(rootPath, '').replace(/^\//, '');
      const parts = relativePath.split('/');
      
      let current = tree;
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        if (!part) continue;

        let child = current.children?.find(c => c.name === part);
        if (!child) {
          child = {
            path: `${rootPath}/${parts.slice(0, i + 1).join('/')}`,
            name: part,
            type: 'directory',
            size: 0,
            children: [],
            level: i + 1,
          };
          current.children = current.children || [];
          current.children.push(child);
        }
        current = child;
      }
    }

    return tree;
  }

  private detectEncoding(content: string): 'utf8' | 'base64' | 'binary' {
    // Simple encoding detection
    try {
      // Check if it's valid UTF-8
      new TextEncoder().encode(content);
      return 'utf8';
    } catch {
      // Check if it looks like base64
      if (/^[A-Za-z0-9+/]*={0,2}$/.test(content)) {
        return 'base64';
      }
      return 'binary';
    }
  }

  private detectLanguage(filePath: string): string | undefined {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      py: 'python',
      js: 'javascript',
      ts: 'typescript',
      html: 'html',
      css: 'css',
      json: 'json',
      md: 'markdown',
      yml: 'yaml',
      yaml: 'yaml',
      toml: 'toml',
      sql: 'sql',
      sh: 'bash',
      txt: 'text',
    };
    return ext ? languageMap[ext] : undefined;
  }

  private getMimeType(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      py: 'text/x-python',
      js: 'text/javascript',
      ts: 'text/typescript',
      html: 'text/html',
      css: 'text/css',
      json: 'application/json',
      md: 'text/markdown',
      txt: 'text/plain',
      pdf: 'application/pdf',
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      zip: 'application/zip',
    };
    return mimeTypes[ext || ''] || 'application/octet-stream';
  }

  private calculateHash(content: string): string {
    // Simple hash function for demo
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // Public getter methods
  getUploads(): FileUpload[] {
    return Array.from(this.uploads.values());
  }

  getUpload(id: string): FileUpload | undefined {
    return this.uploads.get(id);
  }

  getOperations(): FileOperation[] {
    return Array.from(this.operations.values());
  }
}

// Export singleton instance
export const fileManagerService = new FileManagerService();
