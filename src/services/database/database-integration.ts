/**
 * Database Integration Service
 * Handles database connections, ORM setup, and database management for Python frameworks
 */

import { PythonFramework } from '@/types/python-workspace';
import { dockerService } from '../docker';

export type DatabaseType = 'postgresql' | 'mysql' | 'sqlite' | 'redis' | 'mongodb';

export interface DatabaseConnection {
  id: string;
  name: string;
  type: DatabaseType;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  options?: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  lastConnected?: Date;
}

export interface DatabaseSchema {
  name: string;
  tables: DatabaseTable[];
  views: DatabaseView[];
  functions: DatabaseFunction[];
  indexes: DatabaseIndex[];
}

export interface DatabaseTable {
  name: string;
  schema: string;
  columns: DatabaseColumn[];
  primaryKey: string[];
  foreignKeys: DatabaseForeignKey[];
  indexes: DatabaseIndex[];
  rowCount?: number;
  size?: string;
}

export interface DatabaseColumn {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: string;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  isUnique: boolean;
  maxLength?: number;
  precision?: number;
  scale?: number;
}

export interface DatabaseForeignKey {
  name: string;
  column: string;
  referencedTable: string;
  referencedColumn: string;
  onDelete: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
  onUpdate: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
}

export interface DatabaseIndex {
  name: string;
  table: string;
  columns: string[];
  isUnique: boolean;
  type: 'btree' | 'hash' | 'gin' | 'gist';
}

export interface DatabaseView {
  name: string;
  schema: string;
  definition: string;
  columns: DatabaseColumn[];
}

export interface DatabaseFunction {
  name: string;
  schema: string;
  returnType: string;
  parameters: Array<{
    name: string;
    type: string;
    defaultValue?: string;
  }>;
  definition: string;
}

export interface ORMConfig {
  framework: PythonFramework;
  database: DatabaseConnection;
  models: ORMModel[];
  migrations: ORMMigration[];
  settings: Record<string, any>;
}

export interface ORMModel {
  name: string;
  tableName: string;
  fields: ORMField[];
  relationships: ORMRelationship[];
  meta: {
    ordering?: string[];
    indexes?: string[][];
    constraints?: string[];
  };
  code: string;
}

export interface ORMField {
  name: string;
  type: string;
  options: Record<string, any>;
  isPrimaryKey: boolean;
  isRequired: boolean;
  isUnique: boolean;
  defaultValue?: any;
}

export interface ORMRelationship {
  name: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  relatedModel: string;
  foreignKey?: string;
  throughModel?: string;
}

export interface ORMMigration {
  id: string;
  name: string;
  framework: PythonFramework;
  operations: MigrationOperation[];
  dependencies: string[];
  code: string;
  applied: boolean;
  appliedAt?: Date;
}

export interface MigrationOperation {
  type: 'create_table' | 'alter_table' | 'drop_table' | 'add_column' | 'alter_column' | 'drop_column' | 'create_index' | 'drop_index';
  table: string;
  details: Record<string, any>;
}

class DatabaseIntegrationService {
  // In-memory storage for demo - replace with persistent storage in production
  private connections: Map<string, DatabaseConnection> = new Map();
  private schemas: Map<string, DatabaseSchema> = new Map();
  private ormConfigs: Map<string, ORMConfig> = new Map();

  /**
   * Test database connection
   */
  async testConnection(connection: Omit<DatabaseConnection, 'id' | 'isActive' | 'createdAt'>): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const startTime = Date.now();
      const testCommand = this.buildTestCommand(connection);

      // Execute test command in a temporary container or workspace
      const result = await dockerService.execInContainer('test-workspace', ['sh', '-c', testCommand]);

      if (result) {
        return {
          success: true,
          message: 'Connection successful',
          details: {
            version: this.extractVersionInfo(result),
            responseTime: Date.now() - startTime,
          },
        };
      } else {
        return {
          success: false,
          message: 'Connection failed',
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create database connection
   */
  async createConnection(
    workspaceId: string,
    connectionData: Omit<DatabaseConnection, 'id' | 'isActive' | 'createdAt'>
  ): Promise<DatabaseConnection> {
    // Test connection first
    const testResult = await this.testConnection(connectionData);
    if (!testResult.success) {
      throw new Error(`Connection test failed: ${testResult.message}`);
    }

    const connection: DatabaseConnection = {
      ...connectionData,
      id: this.generateId(),
      isActive: true,
      createdAt: new Date(),
      lastConnected: new Date(),
    };

    this.connections.set(connection.id, connection);
    
    // Install required database packages
    await this.installDatabasePackages(workspaceId, connection.type);
    
    return connection;
  }

  /**
   * Get database schema
   */
  async getDatabaseSchema(connectionId: string): Promise<DatabaseSchema> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    // Check if schema is cached
    const cachedSchema = this.schemas.get(connectionId);
    if (cachedSchema) {
      return cachedSchema;
    }

    // Fetch schema from database
    const schema = await this.fetchDatabaseSchema(connection);
    this.schemas.set(connectionId, schema);
    
    return schema;
  }

  /**
   * Generate ORM models from database schema
   */
  async generateORMModels(
    workspaceId: string,
    connectionId: string,
    framework: PythonFramework,
    options: {
      tables?: string[];
      includeRelationships?: boolean;
      generateMigrations?: boolean;
    } = {}
  ): Promise<ORMConfig> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    const schema = await this.getDatabaseSchema(connectionId);
    const models: ORMModel[] = [];
    const migrations: ORMMigration[] = [];

    // Filter tables if specified
    const tablesToProcess = options.tables 
      ? schema.tables.filter(t => options.tables!.includes(t.name))
      : schema.tables;

    // Generate models for each table
    for (const table of tablesToProcess) {
      const model = await this.generateModelFromTable(table, framework, options.includeRelationships);
      models.push(model);

      // Generate migration if requested
      if (options.generateMigrations) {
        const migration = this.generateMigrationFromModel(model, framework);
        migrations.push(migration);
      }
    }

    const ormConfig: ORMConfig = {
      framework,
      database: connection,
      models,
      migrations,
      settings: this.getFrameworkDatabaseSettings(framework, connection),
    };

    this.ormConfigs.set(`${workspaceId}-${connectionId}`, ormConfig);

    // Write model files to workspace
    await this.writeORMFiles(workspaceId, ormConfig);

    return ormConfig;
  }

  /**
   * Execute database query
   */
  async executeQuery(
    connectionId: string,
    query: string,
    parameters?: any[]
  ): Promise<{
    success: boolean;
    data?: any[];
    rowCount?: number;
    error?: string;
    executionTime?: number;
  }> {
    try {
      const connection = this.connections.get(connectionId);
      if (!connection) {
        throw new Error('Connection not found');
      }

      const startTime = Date.now();
      
      // Build query execution command
      const command = this.buildQueryCommand(connection, query, parameters);
      
      // Execute query
      const result = await dockerService.execInContainer('query-workspace', ['sh', '-c', command]);

      const executionTime = Date.now() - startTime;

      if (result) {
        const data = this.parseQueryResult(result, connection.type);
        return {
          success: true,
          data: data.rows,
          rowCount: data.rowCount,
          executionTime,
        };
      } else {
        return {
          success: false,
          error: 'Query execution failed',
          executionTime,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create database migration
   */
  async createMigration(
    workspaceId: string,
    framework: PythonFramework,
    name: string,
    operations: MigrationOperation[]
  ): Promise<ORMMigration> {
    const migration: ORMMigration = {
      id: this.generateId(),
      name,
      framework,
      operations,
      dependencies: [],
      code: this.generateMigrationCode(framework, name, operations),
      applied: false,
    };

    // Write migration file to workspace
    await this.writeMigrationFile(workspaceId, framework, migration);

    return migration;
  }

  /**
   * Apply database migration
   */
  async applyMigration(
    workspaceId: string,
    migrationId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Find migration in ORM configs
      let migration: ORMMigration | undefined;
      let framework: PythonFramework | undefined;

      for (const [key, config] of this.ormConfigs.entries()) {
        if (key.startsWith(workspaceId)) {
          migration = config.migrations.find(m => m.id === migrationId);
          if (migration) {
            framework = config.framework;
            break;
          }
        }
      }

      if (!migration || !framework) {
        throw new Error('Migration not found');
      }

      // Execute migration command
      const command = this.buildMigrationCommand(framework, migration);
      const result = await dockerService.execInContainer(workspaceId, ['sh', '-c', command]);

      if (result) {
        migration.applied = true;
        migration.appliedAt = new Date();

        return {
          success: true,
          message: `Migration ${migration.name} applied successfully`,
        };
      } else {
        return {
          success: false,
          message: 'Migration failed',
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Private helper methods

  private buildTestCommand(connection: Omit<DatabaseConnection, 'id' | 'isActive' | 'createdAt'>): string {
    switch (connection.type) {
      case 'postgresql':
        return `PGPASSWORD="${connection.password}" psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -c "SELECT version();"`;
      case 'mysql':
        return `mysql -h ${connection.host} -P ${connection.port} -u ${connection.username} -p${connection.password} ${connection.database} -e "SELECT VERSION();"`;
      case 'sqlite':
        return `sqlite3 ${connection.database} "SELECT sqlite_version();"`;
      case 'redis':
        return `redis-cli -h ${connection.host} -p ${connection.port} -a ${connection.password} ping`;
      case 'mongodb':
        return `mongosh --host ${connection.host}:${connection.port} --username ${connection.username} --password ${connection.password} --eval "db.version()"`;
      default:
        throw new Error(`Unsupported database type: ${connection.type}`);
    }
  }

  private async installDatabasePackages(workspaceId: string, dbType: DatabaseType): Promise<void> {
    const packages: Record<DatabaseType, string[]> = {
      postgresql: ['psycopg2-binary', 'sqlalchemy'],
      mysql: ['PyMySQL', 'sqlalchemy'],
      sqlite: ['sqlalchemy'], // SQLite is built into Python
      redis: ['redis', 'redis-py'],
      mongodb: ['pymongo', 'mongoengine'],
    };

    const packagesToInstall = packages[dbType];
    for (const pkg of packagesToInstall) {
      await dockerService.execInContainer(workspaceId, ['sh', '-c', `pip install ${pkg}`]);
    }
  }

  private async fetchDatabaseSchema(connection: DatabaseConnection): Promise<DatabaseSchema> {
    try {
      const schema: DatabaseSchema = {
        name: connection.database,
        tables: [],
        views: [],
        functions: [],
        indexes: [],
      };

      switch (connection.type) {
        case 'postgresql':
          schema.tables = await this.fetchPostgreSQLTables(connection);
          schema.views = await this.fetchPostgreSQLViews(connection);
          schema.functions = await this.fetchPostgreSQLFunctions(connection);
          schema.indexes = await this.fetchPostgreSQLIndexes(connection);
          break;
        case 'mysql':
          schema.tables = await this.fetchMySQLTables(connection);
          schema.views = await this.fetchMySQLViews(connection);
          schema.indexes = await this.fetchMySQLIndexes(connection);
          break;
        case 'sqlite':
          schema.tables = await this.fetchSQLiteTables(connection);
          schema.views = await this.fetchSQLiteViews(connection);
          schema.indexes = await this.fetchSQLiteIndexes(connection);
          break;
        default:
          console.warn(`Schema fetching not implemented for ${connection.type}`);
      }

      return schema;
    } catch (error) {
      console.error('Error fetching database schema:', error);
      return {
        name: connection.database,
        tables: [],
        views: [],
        functions: [],
        indexes: [],
      };
    }
  }

  private async fetchPostgreSQLTables(connection: DatabaseConnection): Promise<DatabaseTable[]> {
    const query = `
      SELECT
        t.table_name,
        t.table_schema,
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        tc.constraint_type
      FROM information_schema.tables t
      LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
      LEFT JOIN information_schema.key_column_usage kcu ON c.column_name = kcu.column_name AND c.table_name = kcu.table_name
      LEFT JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
      WHERE t.table_schema = 'public' AND t.table_type = 'BASE TABLE'
      ORDER BY t.table_name, c.ordinal_position;
    `;

    try {
      const command = `PGPASSWORD="${connection.password}" psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -t -c "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return this.parsePostgreSQLTableInfo(result);
      }
    } catch (error) {
      console.error('Error fetching PostgreSQL tables:', error);
    }

    return [];
  }

  private async fetchMySQLTables(connection: DatabaseConnection): Promise<DatabaseTable[]> {
    const query = `
      SELECT
        t.TABLE_NAME,
        t.TABLE_SCHEMA,
        c.COLUMN_NAME,
        c.DATA_TYPE,
        c.IS_NULLABLE,
        c.COLUMN_DEFAULT,
        c.COLUMN_KEY
      FROM information_schema.TABLES t
      LEFT JOIN information_schema.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
      WHERE t.TABLE_SCHEMA = '${connection.database}' AND t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;
    `;

    try {
      const command = `mysql -h ${connection.host} -P ${connection.port} -u ${connection.username} -p${connection.password} ${connection.database} -e "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return this.parseMySQLTableInfo(result, connection);
      }
    } catch (error) {
      console.error('Error fetching MySQL tables:', error);
    }

    return [];
  }

  private async fetchSQLiteTables(connection: DatabaseConnection): Promise<DatabaseTable[]> {
    const query = `
      SELECT
        name as table_name,
        sql
      FROM sqlite_master
      WHERE type = 'table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name;
    `;

    try {
      const command = `sqlite3 "${connection.database}" "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return this.parseSQLiteTableInfo(result, connection);
      }
    } catch (error) {
      console.error('Error fetching SQLite tables:', error);
    }

    return [];
  }

  private async generateModelFromTable(
    table: DatabaseTable,
    framework: PythonFramework,
    includeRelationships: boolean = true
  ): Promise<ORMModel> {
    const fields: ORMField[] = table.columns.map(col => ({
      name: col.name,
      type: this.mapDatabaseTypeToORM(col.type, framework),
      options: {
        max_length: col.maxLength,
        null: col.nullable,
        unique: col.isUnique,
        default: col.defaultValue,
      },
      isPrimaryKey: col.isPrimaryKey,
      isRequired: !col.nullable,
      isUnique: col.isUnique,
      defaultValue: col.defaultValue,
    }));

    const relationships: ORMRelationship[] = includeRelationships
      ? table.foreignKeys.map(fk => ({
          name: fk.referencedTable.toLowerCase(),
          type: 'one-to-many' as const,
          relatedModel: this.toPascalCase(fk.referencedTable),
          foreignKey: fk.column,
        }))
      : [];

    const model: ORMModel = {
      name: this.toPascalCase(table.name),
      tableName: table.name,
      fields,
      relationships,
      meta: {
        ordering: table.primaryKey,
        indexes: table.indexes.map(idx => idx.columns),
      },
      code: this.generateModelCode(framework, table.name, fields, relationships),
    };

    return model;
  }

  private generateMigrationFromModel(model: ORMModel, framework: PythonFramework): ORMMigration {
    const operations: MigrationOperation[] = [{
      type: 'create_table',
      table: model.tableName,
      details: {
        fields: model.fields,
        indexes: model.meta.indexes,
      },
    }];

    return {
      id: this.generateId(),
      name: `create_${model.tableName}`,
      framework,
      operations,
      dependencies: [],
      code: this.generateMigrationCode(framework, `create_${model.tableName}`, operations),
      applied: false,
    };
  }

  private generateModelCode(
    framework: PythonFramework,
    tableName: string,
    fields: ORMField[],
    relationships: ORMRelationship[]
  ): string {
    switch (framework) {
      case 'django':
        return this.generateDjangoModelCode(tableName, fields, relationships);
      case 'flask':
        return this.generateSQLAlchemyModelCode(tableName, fields, relationships);
      case 'fastapi':
        return this.generateSQLAlchemyModelCode(tableName, fields, relationships);
      default:
        return this.generateSQLAlchemyModelCode(tableName, fields, relationships);
    }
  }

  private generateDjangoModelCode(
    tableName: string,
    fields: ORMField[],
    relationships: ORMRelationship[]
  ): string {
    const className = this.toPascalCase(tableName);
    const fieldLines = fields.map(field => {
      const options = Object.entries(field.options)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
        .join(', ');
      
      return `    ${field.name} = models.${field.type}(${options})`;
    });

    const relationshipLines = relationships.map(rel => {
      return `    ${rel.name} = models.ForeignKey('${rel.relatedModel}', on_delete=models.CASCADE)`;
    });

    return `from django.db import models

class ${className}(models.Model):
${fieldLines.join('\n')}
${relationshipLines.join('\n')}

    class Meta:
        db_table = '${tableName}'
        
    def __str__(self):
        return str(self.id)`;
  }

  private generateSQLAlchemyModelCode(
    tableName: string,
    fields: ORMField[],
    relationships: ORMRelationship[]
  ): string {
    const className = this.toPascalCase(tableName);
    const fieldLines = fields.map(field => {
      const options = [];
      if (field.isPrimaryKey) options.push('primary_key=True');
      if (field.isUnique) options.push('unique=True');
      if (!field.isRequired) options.push('nullable=True');
      if (field.defaultValue) options.push(`default=${JSON.stringify(field.defaultValue)}`);
      
      const optionsStr = options.length > 0 ? `, ${options.join(', ')}` : '';
      return `    ${field.name} = Column(${field.type}${optionsStr})`;
    });

    return `from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class ${className}(Base):
    __tablename__ = '${tableName}'
    
${fieldLines.join('\n')}`;
  }

  private generateMigrationCode(
    framework: PythonFramework,
    name: string,
    operations: MigrationOperation[]
  ): string {
    switch (framework) {
      case 'django':
        return this.generateDjangoMigrationCode(name, operations);
      default:
        return this.generateAlembicMigrationCode(name, operations);
    }
  }

  private generateDjangoMigrationCode(name: string, operations: MigrationOperation[]): string {
    const operationLines = operations.map(op => {
      switch (op.type) {
        case 'create_table':
          return `        migrations.CreateModel(
            name='${this.toPascalCase(op.table)}',
            fields=[
                # Fields will be generated based on model
            ],
        )`;
        default:
          return `        # ${op.type} operation for ${op.table}`;
      }
    });

    return `from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = []
    
    operations = [
${operationLines.join(',\n')}
    ]`;
  }

  private generateAlembicMigrationCode(name: string, operations: MigrationOperation[]): string {
    return `"""${name}

Revision ID: ${this.generateId()}
Revises: 
Create Date: ${new Date().toISOString()}

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '${this.generateId()}'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Add upgrade operations here
    pass

def downgrade():
    # Add downgrade operations here
    pass`;
  }

  private async writeORMFiles(workspaceId: string, config: ORMConfig): Promise<void> {
    // Write model files
    for (const model of config.models) {
      const filePath = this.getModelFilePath(config.framework, model.name);
      await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `mkdir -p $(dirname "${filePath}") && echo '${model.code.replace(/'/g, "'\\''")}' > "${filePath}"`]
      );
    }

    // Write migration files
    for (const migration of config.migrations) {
      const filePath = this.getMigrationFilePath(config.framework, migration.name);
      await dockerService.execInContainer(
        workspaceId,
        ['sh', '-c', `mkdir -p $(dirname "${filePath}") && echo '${migration.code.replace(/'/g, "'\\''")}' > "${filePath}"`]
      );
    }
  }

  private async writeMigrationFile(
    workspaceId: string,
    framework: PythonFramework,
    migration: ORMMigration
  ): Promise<void> {
    const filePath = this.getMigrationFilePath(framework, migration.name);
    await dockerService.execInContainer(
      workspaceId,
      ['sh', '-c', `mkdir -p $(dirname "${filePath}") && echo '${migration.code.replace(/'/g, "'\\''")}' > "${filePath}"`]
    );
  }

  private buildQueryCommand(
    connection: DatabaseConnection,
    query: string,
    parameters?: any[]
  ): string {
    // This is a simplified implementation
    // In a real implementation, this would build proper query commands with parameter binding
    switch (connection.type) {
      case 'postgresql':
        return `PGPASSWORD="${connection.password}" psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -c "${query}"`;
      case 'mysql':
        return `mysql -h ${connection.host} -P ${connection.port} -u ${connection.username} -p${connection.password} ${connection.database} -e "${query}"`;
      case 'sqlite':
        return `sqlite3 ${connection.database} "${query}"`;
      default:
        throw new Error(`Query execution not supported for ${connection.type}`);
    }
  }

  private buildMigrationCommand(framework: PythonFramework, migration: ORMMigration): string {
    switch (framework) {
      case 'django':
        return 'python manage.py migrate';
      case 'flask':
      case 'fastapi':
        return 'alembic upgrade head';
      default:
        throw new Error(`Migration not supported for ${framework}`);
    }
  }

  private parseQueryResult(output: string, dbType: DatabaseType): { rows: any[]; rowCount: number } {
    try {
      const lines = output.trim().split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        return { rows: [], rowCount: 0 };
      }

      switch (dbType) {
        case 'postgresql':
          return this.parsePostgreSQLResult(lines);
        case 'mysql':
          return this.parseMySQLResult(lines);
        case 'sqlite':
          return this.parseSQLiteResult(lines);
        default:
          return {
            rows: lines.map(line => ({ data: line })),
            rowCount: lines.length,
          };
      }
    } catch (error) {
      console.error('Error parsing query result:', error);
      return { rows: [], rowCount: 0 };
    }
  }

  private parsePostgreSQLResult(lines: string[]): { rows: any[]; rowCount: number } {
    // PostgreSQL psql output format: columns separated by |
    const rows = lines
      .filter(line => !line.includes('---') && line.includes('|'))
      .map(line => {
        const columns = line.split('|').map(col => col.trim());
        return columns.reduce((obj, col, index) => {
          obj[`col_${index}`] = col;
          return obj;
        }, {} as any);
      });

    return { rows, rowCount: rows.length };
  }

  private parseMySQLResult(lines: string[]): { rows: any[]; rowCount: number } {
    // MySQL output format: tab-separated values
    const rows = lines
      .filter(line => line.includes('\t'))
      .map(line => {
        const columns = line.split('\t');
        return columns.reduce((obj, col, index) => {
          obj[`col_${index}`] = col;
          return obj;
        }, {} as any);
      });

    return { rows, rowCount: rows.length };
  }

  private parseSQLiteResult(lines: string[]): { rows: any[]; rowCount: number } {
    // SQLite output format: pipe-separated values by default
    const rows = lines
      .filter(line => line.includes('|'))
      .map(line => {
        const columns = line.split('|');
        return columns.reduce((obj, col, index) => {
          obj[`col_${index}`] = col;
          return obj;
        }, {} as any);
      });

    return { rows, rowCount: rows.length };
  }

  private parsePostgreSQLTableInfo(output: string): DatabaseTable[] {
    const tables = new Map<string, DatabaseTable>();
    const lines = output.trim().split('\n').filter(line => line.trim());

    for (const line of lines) {
      const parts = line.split('|').map(p => p.trim());
      if (parts.length >= 4) {
        const [tableName, , columnName, dataType, isNullable, columnDefault, constraintType] = parts;

        if (!tables.has(tableName)) {
          tables.set(tableName, {
            name: tableName,
            schema: 'public',
            columns: [],
            primaryKey: [],
            foreignKeys: [],
            indexes: [],
          });
        }

        const table = tables.get(tableName)!;

        if (columnName) {
          const column: DatabaseColumn = {
            name: columnName,
            type: dataType,
            nullable: isNullable === 'YES',
            defaultValue: columnDefault !== 'NULL' ? columnDefault : undefined,
            isPrimaryKey: constraintType === 'PRIMARY KEY',
            isForeignKey: constraintType === 'FOREIGN KEY',
            isUnique: constraintType === 'UNIQUE',
          };

          table.columns.push(column);

          if (column.isPrimaryKey) {
            table.primaryKey.push(columnName);
          }
        }
      }
    }

    return Array.from(tables.values());
  }

  private parseMySQLTableInfo(output: string, connection: DatabaseConnection): DatabaseTable[] {
    const tables = new Map<string, DatabaseTable>();
    const lines = output.trim().split('\n').filter(line => line.trim());

    for (const line of lines) {
      const parts = line.split('\t');
      if (parts.length >= 6) {
        const [tableName, , columnName, dataType, isNullable, columnDefault, columnKey] = parts;

        if (!tables.has(tableName)) {
          tables.set(tableName, {
            name: tableName,
            schema: connection.database,
            columns: [],
            primaryKey: [],
            foreignKeys: [],
            indexes: [],
          });
        }

        const table = tables.get(tableName)!;

        if (columnName) {
          const column: DatabaseColumn = {
            name: columnName,
            type: dataType,
            nullable: isNullable === 'YES',
            defaultValue: columnDefault,
            isPrimaryKey: columnKey === 'PRI',
            isForeignKey: columnKey === 'MUL',
            isUnique: columnKey === 'UNI',
          };

          table.columns.push(column);

          if (column.isPrimaryKey) {
            table.primaryKey.push(columnName);
          }
        }
      }
    }

    return Array.from(tables.values());
  }

  private parseSQLiteTableInfo(output: string, connection: DatabaseConnection): DatabaseTable[] {
    const tables: DatabaseTable[] = [];
    const lines = output.trim().split('\n').filter(line => line.trim());

    for (const line of lines) {
      const parts = line.split('|');
      if (parts.length >= 2) {
        const [tableName, createSql] = parts;

        const table: DatabaseTable = {
          name: tableName,
          schema: 'main',
          columns: this.parseSQLiteCreateStatement(createSql),
          primaryKey: [],
          foreignKeys: [],
          indexes: [],
        };

        // Extract primary keys from columns
        table.primaryKey = table.columns
          .filter(col => col.isPrimaryKey)
          .map(col => col.name);

        tables.push(table);
      }
    }

    return tables;
  }

  private parseSQLiteCreateStatement(createSql: string): DatabaseColumn[] {
    const columns: DatabaseColumn[] = [];

    // Extract column definitions from CREATE TABLE statement
    const match = createSql.match(/CREATE TABLE[^(]*\(([\s\S]*)\)/);
    if (match) {
      const columnDefs = match[1].split(',');

      for (const colDef of columnDefs) {
        const trimmed = colDef.trim();
        if (trimmed && !trimmed.toUpperCase().startsWith('CONSTRAINT')) {
          const parts = trimmed.split(/\s+/);
          if (parts.length >= 2) {
            const column: DatabaseColumn = {
              name: parts[0].replace(/["`]/g, ''),
              type: parts[1],
              nullable: !trimmed.toUpperCase().includes('NOT NULL'),
              isPrimaryKey: trimmed.toUpperCase().includes('PRIMARY KEY'),
              isForeignKey: trimmed.toUpperCase().includes('REFERENCES'),
              isUnique: trimmed.toUpperCase().includes('UNIQUE'),
            };

            // Extract default value
            const defaultMatch = trimmed.match(/DEFAULT\s+([^,\s]+)/i);
            if (defaultMatch) {
              column.defaultValue = defaultMatch[1];
            }

            columns.push(column);
          }
        }
      }
    }

    return columns;
  }

  private async fetchPostgreSQLViews(connection: DatabaseConnection): Promise<DatabaseView[]> {
    const query = `
      SELECT table_name as view_name, view_definition
      FROM information_schema.views
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;

    try {
      const command = `PGPASSWORD="${connection.password}" psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -t -c "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return result.trim().split('\n')
          .filter((line: string) => line.trim())
          .map((line: string) => {
            const [name, definition] = line.split('|').map((p: string) => p.trim());
            return {
              name,
              schema: 'public',
              definition: definition || '',
              columns: [] // Would need additional query to get view columns
            };
          });
      }
    } catch (error) {
      console.error('Error fetching PostgreSQL views:', error);
    }

    return [];
  }

  private async fetchPostgreSQLFunctions(connection: DatabaseConnection): Promise<DatabaseFunction[]> {
    const query = `
      SELECT routine_name, routine_definition, data_type
      FROM information_schema.routines
      WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
      ORDER BY routine_name;
    `;

    try {
      const command = `PGPASSWORD="${connection.password}" psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -t -c "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return result.trim().split('\n')
          .filter((line: string) => line.trim())
          .map((line: string) => {
            const [name, definition, returnType] = line.split('|').map((p: string) => p.trim());
            return {
              name,
              schema: 'public',
              definition: definition || '',
              returnType: returnType || 'void',
              parameters: []
            };
          });
      }
    } catch (error) {
      console.error('Error fetching PostgreSQL functions:', error);
    }

    return [];
  }

  private async fetchPostgreSQLIndexes(connection: DatabaseConnection): Promise<DatabaseIndex[]> {
    const query = `
      SELECT indexname, tablename, indexdef
      FROM pg_indexes
      WHERE schemaname = 'public'
      ORDER BY tablename, indexname;
    `;

    try {
      const command = `PGPASSWORD="${connection.password}" psql -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -t -c "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return result.trim().split('\n')
          .filter((line: string) => line.trim())
          .map((line: string) => {
            const [name, tableName, definition] = line.split('|').map((p: string) => p.trim());
            return {
              name,
              table: tableName,
              columns: this.extractIndexColumns(definition),
              isUnique: definition.includes('UNIQUE'),
              type: 'btree' as const
            };
          });
      }
    } catch (error) {
      console.error('Error fetching PostgreSQL indexes:', error);
    }

    return [];
  }

  private async fetchMySQLViews(connection: DatabaseConnection): Promise<DatabaseView[]> {
    const query = `
      SELECT TABLE_NAME as view_name, VIEW_DEFINITION
      FROM information_schema.VIEWS
      WHERE TABLE_SCHEMA = '${connection.database}'
      ORDER BY TABLE_NAME;
    `;

    try {
      const command = `mysql -h ${connection.host} -P ${connection.port} -u ${connection.username} -p${connection.password} ${connection.database} -e "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return result.trim().split('\n')
          .filter((line: string) => line.trim() && !line.includes('view_name'))
          .map((line: string) => {
            const [name, definition] = line.split('\t');
            return {
              name,
              schema: connection.database,
              definition: definition || '',
              columns: []
            };
          });
      }
    } catch (error) {
      console.error('Error fetching MySQL views:', error);
    }

    return [];
  }

  private async fetchMySQLIndexes(connection: DatabaseConnection): Promise<DatabaseIndex[]> {
    const query = `
      SELECT INDEX_NAME, TABLE_NAME, COLUMN_NAME, NON_UNIQUE
      FROM information_schema.STATISTICS
      WHERE TABLE_SCHEMA = '${connection.database}'
      ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
    `;

    try {
      const command = `mysql -h ${connection.host} -P ${connection.port} -u ${connection.username} -p${connection.password} ${connection.database} -e "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        const indexes = new Map<string, DatabaseIndex>();

        result.trim().split('\n')
          .filter((line: string) => line.trim() && !line.includes('INDEX_NAME'))
          .forEach((line: string) => {
            const [indexName, tableName, columnName, nonUnique] = line.split('\t');
            const key = `${tableName}.${indexName}`;

            if (!indexes.has(key)) {
              indexes.set(key, {
                name: indexName,
                table: tableName,
                columns: [],
                isUnique: nonUnique === '0',
                type: 'btree' as const
              });
            }

            indexes.get(key)!.columns.push(columnName);
          });

        return Array.from(indexes.values());
      }
    } catch (error) {
      console.error('Error fetching MySQL indexes:', error);
    }

    return [];
  }

  private async fetchSQLiteViews(connection: DatabaseConnection): Promise<DatabaseView[]> {
    const query = `
      SELECT name, sql
      FROM sqlite_master
      WHERE type = 'view'
      ORDER BY name;
    `;

    try {
      const command = `sqlite3 "${connection.database}" "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return result.trim().split('\n')
          .filter((line: string) => line.trim())
          .map((line: string) => {
            const [name, definition] = line.split('|');
            return {
              name,
              schema: 'main',
              definition: definition || '',
              columns: []
            };
          });
      }
    } catch (error) {
      console.error('Error fetching SQLite views:', error);
    }

    return [];
  }

  private async fetchSQLiteIndexes(connection: DatabaseConnection): Promise<DatabaseIndex[]> {
    const query = `
      SELECT name, tbl_name, sql
      FROM sqlite_master
      WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
      ORDER BY tbl_name, name;
    `;

    try {
      const command = `sqlite3 "${connection.database}" "${query}"`;
      const result = await dockerService.execInContainer('temp-workspace', ['sh', '-c', command]);

      if (result) {
        return result.trim().split('\n')
          .filter((line: string) => line.trim())
          .map((line: string) => {
            const [name, tableName, sql] = line.split('|');
            return {
              name,
              table: tableName,
              columns: this.extractIndexColumns(sql || ''),
              isUnique: (sql || '').includes('UNIQUE'),
              type: 'btree' as const
            };
          });
      }
    } catch (error) {
      console.error('Error fetching SQLite indexes:', error);
    }

    return [];
  }

  private extractIndexColumns(definition: string): string[] {
    // Extract column names from index definition
    const match = definition.match(/\((.*?)\)/);
    if (match) {
      return match[1].split(',').map(col => col.trim().replace(/["`]/g, ''));
    }
    return [];
  }

  private extractVersionInfo(output: string): string {
    // Extract version information from database output
    return output.split('\n')[0] || 'Unknown version';
  }

  private mapDatabaseTypeToORM(dbType: string, framework: PythonFramework): string {
    const typeMap: Record<PythonFramework, Record<string, string>> = {
      django: {
        'varchar': 'CharField',
        'text': 'TextField',
        'integer': 'IntegerField',
        'bigint': 'BigIntegerField',
        'boolean': 'BooleanField',
        'timestamp': 'DateTimeField',
        'date': 'DateField',
        'decimal': 'DecimalField',
      },
      flask: {
        'varchar': 'String',
        'text': 'Text',
        'integer': 'Integer',
        'bigint': 'BigInteger',
        'boolean': 'Boolean',
        'timestamp': 'DateTime',
        'date': 'Date',
        'decimal': 'Numeric',
      },
      fastapi: {
        'varchar': 'String',
        'text': 'Text',
        'integer': 'Integer',
        'bigint': 'BigInteger',
        'boolean': 'Boolean',
        'timestamp': 'DateTime',
        'date': 'Date',
        'decimal': 'Numeric',
      },
      streamlit: {
        'varchar': 'String',
        'text': 'Text',
        'integer': 'Integer',
        'bigint': 'BigInteger',
        'boolean': 'Boolean',
        'timestamp': 'DateTime',
        'date': 'Date',
        'decimal': 'Numeric',
      },
      gradio: {
        'varchar': 'String',
        'text': 'Text',
        'integer': 'Integer',
        'bigint': 'BigInteger',
        'boolean': 'Boolean',
        'timestamp': 'DateTime',
        'date': 'Date',
        'decimal': 'Numeric',
      },
    };

    return typeMap[framework][dbType.toLowerCase()] || 'String';
  }

  private getFrameworkDatabaseSettings(framework: PythonFramework, connection: DatabaseConnection): Record<string, any> {
    switch (framework) {
      case 'django':
        return {
          DATABASES: {
            default: {
              ENGINE: `django.db.backends.${connection.type}`,
              NAME: connection.database,
              USER: connection.username,
              PASSWORD: connection.password,
              HOST: connection.host,
              PORT: connection.port,
            },
          },
        };
      case 'flask':
      case 'fastapi':
        return {
          SQLALCHEMY_DATABASE_URI: this.buildConnectionString(connection),
          SQLALCHEMY_TRACK_MODIFICATIONS: false,
        };
      default:
        return {};
    }
  }

  private buildConnectionString(connection: DatabaseConnection): string {
    switch (connection.type) {
      case 'postgresql':
        return `postgresql://${connection.username}:${connection.password}@${connection.host}:${connection.port}/${connection.database}`;
      case 'mysql':
        return `mysql://${connection.username}:${connection.password}@${connection.host}:${connection.port}/${connection.database}`;
      case 'sqlite':
        return `sqlite:///${connection.database}`;
      default:
        throw new Error(`Connection string not supported for ${connection.type}`);
    }
  }

  private getModelFilePath(framework: PythonFramework, modelName: string): string {
    switch (framework) {
      case 'django':
        return `/home/<USER>/models/${modelName.toLowerCase()}.py`;
      case 'flask':
      case 'fastapi':
        return `/home/<USER>/models/${modelName.toLowerCase()}.py`;
      default:
        return `/home/<USER>/models.py`;
    }
  }

  private getMigrationFilePath(framework: PythonFramework, migrationName: string): string {
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
    switch (framework) {
      case 'django':
        return `/home/<USER>/migrations/${timestamp}_${migrationName}.py`;
      case 'flask':
      case 'fastapi':
        return `/home/<USER>/alembic/versions/${timestamp}_${migrationName}.py`;
      default:
        return `/home/<USER>/migrations/${migrationName}.py`;
    }
  }

  private toPascalCase(str: string): string {
    return str.replace(/(^\w|_\w)/g, match => match.replace('_', '').toUpperCase());
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // Public getter methods
  getConnections(): DatabaseConnection[] {
    return Array.from(this.connections.values());
  }

  getConnection(id: string): DatabaseConnection | undefined {
    return this.connections.get(id);
  }

  getORMConfig(workspaceId: string, connectionId: string): ORMConfig | undefined {
    return this.ormConfigs.get(`${workspaceId}-${connectionId}`);
  }
}

// Export singleton instance
export const databaseIntegrationService = new DatabaseIntegrationService();
