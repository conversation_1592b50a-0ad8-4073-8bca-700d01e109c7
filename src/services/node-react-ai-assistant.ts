/**
 * Node/React AI Assistant Service
 * Provides AI-powered assistance for Node.js and React development
 */

import {
  NodeReactProjectStructure,
  NodeReactCodeCompletion,
  NodeReactCodeAnalysis,
  NodeReactRefactoringAction,
  NodeReactCodeGeneration,
  NodeReactAIAssistantRequest,
  NodeReactAIAssistantResponse,
  NodeReactAIAssistantContext,
  NodeReactFileInfo,
} from '@/types/node-react-ai-editor';
import { NodeFramework } from '@/types/node-react-workspace';
import { dockerService } from './docker';

export class NodeReactAIAssistantService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/node-react-ai') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get project structure for AI context
   */
  async getProjectStructure(workspaceId: string, projectPath: string): Promise<NodeReactProjectStructure> {
    try {
      // Read package.json
      const packageJsonResult = await dockerService.executeCommand(
        workspaceId,
        `cat "${projectPath}/package.json"`
      );
      
      if (packageJsonResult.exitCode !== 0) {
        throw new Error('package.json not found');
      }

      const packageJson = JSON.parse(packageJsonResult.stdout);
      
      // Detect framework
      const framework = this.detectFramework(packageJson);
      
      // Get file structure
      const files = await this.getFileStructure(workspaceId, projectPath);
      
      // Read TypeScript config if exists
      let tsConfig;
      try {
        const tsConfigResult = await dockerService.executeCommand(
          workspaceId,
          `cat "${projectPath}/tsconfig.json"`
        );
        if (tsConfigResult.exitCode === 0) {
          tsConfig = JSON.parse(tsConfigResult.stdout);
        }
      } catch (error) {
        // TypeScript config is optional
      }

      return {
        name: packageJson.name || 'unknown',
        framework,
        rootPath: projectPath,
        packageJson,
        files,
        dependencies: Object.keys(packageJson.dependencies || {}),
        devDependencies: Object.keys(packageJson.devDependencies || {}),
        scripts: packageJson.scripts || {},
        tsConfig,
      };
    } catch (error) {
      console.error('Error getting project structure:', error);
      throw error;
    }
  }

  /**
   * Get AI-powered code completions
   */
  async getCodeCompletions(
    context: NodeReactAIAssistantContext,
    position: { line: number; character: number },
    triggerCharacter?: string
  ): Promise<NodeReactCodeCompletion[]> {
    try {
      const response = await fetch(`${this.baseUrl}/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          position,
          triggerCharacter,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to get completions: ${response.statusText}`);
      }

      const result = await response.json();
      return result.completions || [];
    } catch (error) {
      console.error('Error getting code completions:', error);
      return this.getFallbackCompletions(context, position);
    }
  }

  /**
   * Analyze code for issues and suggestions
   */
  async analyzeCode(
    context: NodeReactAIAssistantContext,
    fileContent: string
  ): Promise<NodeReactCodeAnalysis> {
    try {
      const response = await fetch(`${this.baseUrl}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          fileContent,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to analyze code: ${response.statusText}`);
      }

      const result = await response.json();
      return result.analysis;
    } catch (error) {
      console.error('Error analyzing code:', error);
      throw error;
    }
  }

  /**
   * Generate refactoring suggestions
   */
  async getRefactoringSuggestions(
    context: NodeReactAIAssistantContext,
    selectedText: string,
    refactoringType: string
  ): Promise<NodeReactRefactoringAction[]> {
    try {
      const response = await fetch(`${this.baseUrl}/refactor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          selectedText,
          refactoringType,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to get refactoring suggestions: ${response.statusText}`);
      }

      const result = await response.json();
      return result.refactorings || [];
    } catch (error) {
      console.error('Error getting refactoring suggestions:', error);
      return [];
    }
  }

  /**
   * Generate code based on prompt
   */
  async generateCode(
    context: NodeReactAIAssistantContext,
    prompt: string,
    type: 'component' | 'hook' | 'api' | 'test' | 'config' | 'utility'
  ): Promise<NodeReactCodeGeneration> {
    try {
      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          prompt,
          type,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate code: ${response.statusText}`);
      }

      const result = await response.json();
      return result.generation;
    } catch (error) {
      console.error('Error generating code:', error);
      throw error;
    }
  }

  /**
   * Get AI explanation for code
   */
  async explainCode(
    context: NodeReactAIAssistantContext,
    code: string
  ): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/explain`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          code,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to explain code: ${response.statusText}`);
      }

      const result = await response.json();
      return result.explanation;
    } catch (error) {
      console.error('Error explaining code:', error);
      return 'Unable to provide explanation at this time.';
    }
  }

  /**
   * Get debugging assistance
   */
  async getDebuggingHelp(
    context: NodeReactAIAssistantContext,
    error: string,
    stackTrace?: string
  ): Promise<{ explanation: string; suggestions: string[] }> {
    try {
      const response = await fetch(`${this.baseUrl}/debug`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          error,
          stackTrace,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to get debugging help: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        explanation: result.explanation,
        suggestions: result.suggestions || [],
      };
    } catch (error) {
      console.error('Error getting debugging help:', error);
      return {
        explanation: 'Unable to provide debugging assistance at this time.',
        suggestions: [],
      };
    }
  }

  /**
   * Process AI assistant request
   */
  async processRequest(request: NodeReactAIAssistantRequest): Promise<NodeReactAIAssistantResponse> {
    const startTime = Date.now();
    
    try {
      let response: NodeReactAIAssistantResponse;

      switch (request.type) {
        case 'completion':
          const completions = await this.getCodeCompletions(
            request.context,
            request.context.cursorPosition || { line: 0, character: 0 }
          );
          response = {
            type: 'completion',
            success: true,
            completions,
            confidence: 0.8,
            processingTime: Date.now() - startTime,
          };
          break;

        case 'analysis':
          const analysis = await this.analyzeCode(
            request.context,
            request.context.currentFile?.content || ''
          );
          response = {
            type: 'analysis',
            success: true,
            analysis,
            confidence: 0.9,
            processingTime: Date.now() - startTime,
          };
          break;

        case 'generation':
          const generation = await this.generateCode(
            request.context,
            request.prompt,
            'component' // Default type
          );
          response = {
            type: 'generation',
            success: true,
            generation,
            confidence: 0.7,
            processingTime: Date.now() - startTime,
          };
          break;

        case 'explanation':
          const explanation = await this.explainCode(
            request.context,
            request.context.selectedText || ''
          );
          response = {
            type: 'explanation',
            success: true,
            explanation,
            confidence: 0.8,
            processingTime: Date.now() - startTime,
          };
          break;

        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      return response;
    } catch (error) {
      return {
        type: request.type,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Detect framework from package.json
   */
  private detectFramework(packageJson: any): NodeFramework {
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    if (dependencies.next) return 'nextjs';
    if (dependencies['@nestjs/core']) return 'nestjs';
    if (dependencies.nuxt) return 'nuxt';
    if (dependencies.gatsby) return 'gatsby';
    if (dependencies['@remix-run/react']) return 'remix';
    if (dependencies.svelte) return 'svelte';
    if (dependencies.vue) return 'vue';
    if (dependencies['@angular/core']) return 'angular';
    if (dependencies.react) return 'react';
    if (dependencies.express) return 'express';
    
    return 'express'; // Default fallback
  }

  /**
   * Get file structure recursively
   */
  private async getFileStructure(
    workspaceId: string,
    path: string,
    maxDepth: number = 3,
    currentDepth: number = 0
  ): Promise<NodeReactFileInfo[]> {
    if (currentDepth >= maxDepth) return [];

    try {
      const result = await dockerService.executeCommand(
        workspaceId,
        `find "${path}" -maxdepth 1 -type f -o -type d | grep -v "node_modules" | grep -v ".git" | sort`
      );

      if (result.exitCode !== 0) return [];

      const items = result.stdout.trim().split('\n').filter(item => item && item !== path);
      const files: NodeReactFileInfo[] = [];

      for (const item of items) {
        const name = item.split('/').pop() || '';
        const isDirectory = await this.isDirectory(workspaceId, item);
        
        const fileInfo: NodeReactFileInfo = {
          path: item,
          name,
          type: this.getFileType(name),
          size: 0,
          lastModified: new Date(),
          isDirectory,
        };

        if (isDirectory && currentDepth < maxDepth - 1) {
          fileInfo.children = await this.getFileStructure(workspaceId, item, maxDepth, currentDepth + 1);
        }

        files.push(fileInfo);
      }

      return files;
    } catch (error) {
      console.error('Error getting file structure:', error);
      return [];
    }
  }

  /**
   * Check if path is directory
   */
  private async isDirectory(workspaceId: string, path: string): Promise<boolean> {
    try {
      const result = await dockerService.executeCommand(workspaceId, `test -d "${path}" && echo "dir" || echo "file"`);
      return result.stdout.trim() === 'dir';
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file type from extension
   */
  private getFileType(filename: string): NodeReactFileInfo['type'] {
    const ext = filename.split('.').pop()?.toLowerCase();
    
    switch (ext) {
      case 'js': return 'javascript';
      case 'ts': return 'typescript';
      case 'jsx': return 'jsx';
      case 'tsx': return 'tsx';
      case 'json': return 'json';
      case 'css': return 'css';
      case 'scss': return 'scss';
      case 'less': return 'less';
      case 'html': return 'html';
      case 'md': return 'md';
      default: return 'config';
    }
  }

  /**
   * Get fallback completions when AI service is unavailable
   */
  private getFallbackCompletions(
    context: NodeReactAIAssistantContext,
    position: { line: number; character: number }
  ): NodeReactCodeCompletion[] {
    const completions: NodeReactCodeCompletion[] = [];

    // Add framework-specific completions
    if (context.framework === 'react') {
      completions.push(
        {
          text: 'useState',
          displayText: 'useState',
          type: 'hook',
          detail: 'React Hook',
          documentation: 'Returns a stateful value and a function to update it.',
          insertText: 'useState($1)',
          category: 'react',
        },
        {
          text: 'useEffect',
          displayText: 'useEffect',
          type: 'hook',
          detail: 'React Hook',
          documentation: 'Accepts a function that contains imperative, possibly effectful code.',
          insertText: 'useEffect(() => {\n  $1\n}, [])',
          category: 'react',
        }
      );
    }

    return completions;
  }
}

// Export singleton instance
export const nodeReactAIAssistantService = new NodeReactAIAssistantService();
