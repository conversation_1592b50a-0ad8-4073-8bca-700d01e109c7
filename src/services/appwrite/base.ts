import { 
  AppwriteServerError, 
  handleServerAppwriteError, 
  withRetry, 
  logger,
  SERVER_CONFIG 
} from '@/lib/appwrite-server';

// Base service interface
export interface IAppwriteService {
  healthCheck(): Promise<boolean>;
}

// Service operation result
export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: AppwriteServerError;
  metadata?: {
    timestamp: string;
    operation: string;
    duration: number;
  };
}

// Pagination parameters
export interface PaginationParams {
  limit?: number;
  offset?: number;
  cursor?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// Pagination result
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

// Query parameters
export interface QueryParams {
  filters?: Record<string, any>;
  search?: string;
  pagination?: PaginationParams;
}

// Base service class with common functionality
export abstract class BaseAppwriteService implements IAppwriteService {
  protected serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  // Execute operation with error handling and logging
  protected async executeOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    useRetry: boolean = true
  ): Promise<ServiceResult<T>> {
    const startTime = Date.now();
    
    try {
      logger.debug(`Starting ${this.serviceName}.${operationName}`);
      
      const result = useRetry 
        ? await withRetry(operation)
        : await operation();
      
      const duration = Date.now() - startTime;
      
      logger.info(`${this.serviceName}.${operationName} completed successfully`, {
        duration: `${duration}ms`
      });

      return {
        success: true,
        data: result,
        metadata: {
          timestamp: new Date().toISOString(),
          operation: `${this.serviceName}.${operationName}`,
          duration
        }
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const appwriteError = handleServerAppwriteError(error);
      
      logger.error(`${this.serviceName}.${operationName} failed`, {
        error: appwriteError.message,
        code: appwriteError.code,
        duration: `${duration}ms`
      });

      return {
        success: false,
        error: appwriteError,
        metadata: {
          timestamp: new Date().toISOString(),
          operation: `${this.serviceName}.${operationName}`,
          duration
        }
      };
    }
  }

  // Validate required parameters
  protected validateRequired(params: Record<string, any>, requiredFields: string[]): void {
    const missing = requiredFields.filter(field => 
      params[field] === undefined || params[field] === null || params[field] === ''
    );
    
    if (missing.length > 0) {
      throw new AppwriteServerError(
        `Missing required parameters: ${missing.join(', ')}`,
        'MISSING_PARAMETERS',
        'validation_error',
        400
      );
    }
  }

  // Validate pagination parameters
  protected validatePagination(pagination?: PaginationParams): PaginationParams {
    if (!pagination) {
      return { limit: 25, offset: 0 };
    }

    const limit = Math.min(Math.max(pagination.limit || 25, 1), 100);
    const offset = Math.max(pagination.offset || 0, 0);

    return {
      ...pagination,
      limit,
      offset
    };
  }

  // Format pagination result
  protected formatPaginatedResult<T>(
    items: T[],
    total: number,
    pagination: PaginationParams
  ): PaginatedResult<T> {
    const limit = pagination.limit || 25;
    const offset = pagination.offset || 0;
    
    return {
      items,
      total,
      hasMore: offset + items.length < total,
      nextCursor: offset + items.length < total 
        ? Buffer.from(JSON.stringify({ offset: offset + limit })).toString('base64')
        : undefined,
      prevCursor: offset > 0 
        ? Buffer.from(JSON.stringify({ offset: Math.max(0, offset - limit) })).toString('base64')
        : undefined
    };
  }

  // Parse cursor for pagination
  protected parseCursor(cursor?: string): { offset: number } {
    if (!cursor) {
      return { offset: 0 };
    }

    try {
      const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
      const parsed = JSON.parse(decoded);
      return { offset: Math.max(0, parsed.offset || 0) };
    } catch {
      return { offset: 0 };
    }
  }

  // Sanitize input data
  protected sanitizeInput(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined && value !== null) {
        if (typeof value === 'string') {
          // Basic XSS prevention
          sanitized[key] = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        } else if (typeof value === 'object' && !Array.isArray(value)) {
          sanitized[key] = this.sanitizeInput(value);
        } else {
          sanitized[key] = value;
        }
      }
    }
    
    return sanitized;
  }

  // Rate limiting check (basic implementation)
  protected async checkRateLimit(operation: string, identifier: string): Promise<boolean> {
    // This is a basic implementation - in production, you'd use Redis or similar
    // For now, we'll just log and return true
    logger.debug(`Rate limit check for ${operation}:${identifier}`);
    return true;
  }

  // Cache key generator
  protected generateCacheKey(prefix: string, ...parts: string[]): string {
    return `${prefix}:${parts.join(':')}`;
  }

  // Abstract health check method
  abstract healthCheck(): Promise<boolean>;

  // Get service metrics
  public getMetrics(): {
    serviceName: string;
    config: typeof SERVER_CONFIG;
    timestamp: string;
  } {
    return {
      serviceName: this.serviceName,
      config: SERVER_CONFIG,
      timestamp: new Date().toISOString()
    };
  }
}

// Service factory interface
export interface ServiceFactory {
  createAuthService(): any;
  createDatabaseService(): any;
  createStorageService(): any;
  createFunctionsService(): any;
}

// Connection pool manager (basic implementation)
export class ConnectionPoolManager {
  private static instance: ConnectionPoolManager;
  private activeConnections: number = 0;
  private readonly maxConnections: number;

  private constructor() {
    this.maxConnections = SERVER_CONFIG.connectionPoolSize;
  }

  public static getInstance(): ConnectionPoolManager {
    if (!ConnectionPoolManager.instance) {
      ConnectionPoolManager.instance = new ConnectionPoolManager();
    }
    return ConnectionPoolManager.instance;
  }

  public async acquireConnection(): Promise<boolean> {
    if (this.activeConnections >= this.maxConnections) {
      logger.warn('Connection pool exhausted, waiting...');
      // In a real implementation, you'd queue requests
      await new Promise(resolve => setTimeout(resolve, 100));
      return this.acquireConnection();
    }

    this.activeConnections++;
    logger.debug(`Connection acquired. Active: ${this.activeConnections}/${this.maxConnections}`);
    return true;
  }

  public releaseConnection(): void {
    if (this.activeConnections > 0) {
      this.activeConnections--;
      logger.debug(`Connection released. Active: ${this.activeConnections}/${this.maxConnections}`);
    }
  }

  public getStats(): { active: number; max: number; utilization: number } {
    return {
      active: this.activeConnections,
      max: this.maxConnections,
      utilization: (this.activeConnections / this.maxConnections) * 100
    };
  }
}

// Export connection pool instance
export const connectionPool = ConnectionPoolManager.getInstance();
