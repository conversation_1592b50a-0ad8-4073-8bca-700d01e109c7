/**
 * Workspace File Management Service
 * Handles file operations within workspaces using Appwrite
 */

import {
  adminDatabases,
  createSessionServices,
  logger,
  ID
} from '@/lib/appwrite-server';
import { BaseAppwriteService, ServiceResult, PaginationParams, PaginatedResult } from './base';
import {
  WorkspaceFile,
  FilePermission,
  FileVersion
} from '@/types/workspace';
import { WORKSPACE_DATABASE_ID, WORKSPACE_COLLECTIONS } from './workspace';

// File document interface for Appwrite
export interface WorkspaceFileDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  // File identification
  workspaceId: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  mimeType?: string;
  size: number;
  encoding?: string;
  
  // Content (for small files, larger files stored in storage)
  content?: string;
  storageFileId?: string; // Reference to Appwrite storage file
  
  // Metadata
  createdBy: string;
  updatedBy: string;
  
  // Permissions (JSON string)
  filePermissions: string;
  
  // Version control
  version: number;
  
  // Collaboration
  isLocked: boolean;
  lockedBy?: string;
  lockedAt?: string;
  
  // Editor state
  language?: string;
  isOpen: boolean;
  isDirty: boolean;
  
  // AI features
  aiSuggestions?: string; // JSON string
}

export interface FileVersionDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  
  fileId: string;
  version: number;
  content: string;
  createdBy: string;
  message?: string;
  size: number;
  storageFileId?: string; // For large file versions
}

// Service parameters
export interface CreateFileParams {
  workspaceId: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  content?: string;
  mimeType?: string;
  encoding?: string;
  permissions?: FilePermission[];
}

export interface UpdateFileParams {
  fileId: string;
  content?: string;
  name?: string;
  path?: string;
  permissions?: FilePermission[];
  versionMessage?: string;
}

export interface FileQueryParams {
  workspaceId: string;
  path?: string;
  type?: 'file' | 'directory';
  search?: string;
  pagination?: PaginationParams;
}

// Workspace File Service
export class WorkspaceFileService extends BaseAppwriteService {
  private readonly MAX_INLINE_FILE_SIZE = 1024 * 1024; // 1MB

  constructor() {
    super('WorkspaceFileService');
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await adminDatabases.get(WORKSPACE_DATABASE_ID);
      return true;
    } catch (error) {
      logger.error('Workspace file service health check failed', { error });
      return false;
    }
  }

  // ============================================================================
  // File CRUD Operations
  // ============================================================================

  /**
   * Create a new file or directory
   */
  async createFile(params: CreateFileParams, sessionId?: string): Promise<ServiceResult<WorkspaceFile>> {
    return this.executeOperation('createFile', async () => {
      this.validateRequired(params, ['workspaceId', 'name', 'path', 'type']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };
      const fileId = ID.unique();

      // Determine if content should be stored inline or in storage
      const contentSize = params.content ? Buffer.byteLength(params.content, 'utf8') : 0;
      const useStorage = contentSize > this.MAX_INLINE_FILE_SIZE;

      let storageFileId: string | undefined;
      let inlineContent: string | undefined;

      if (params.content) {
        if (useStorage) {
          // TODO: Store large files in Appwrite Storage
          // storageFileId = await this.storeFileInStorage(params.content, params.mimeType);
          // For now, we'll store inline with a warning
          logger.warn(`File ${params.name} is large (${contentSize} bytes) but storing inline`);
          inlineContent = params.content;
        } else {
          inlineContent = params.content;
        }
      }

      // Create file document
      const fileDoc: Omit<WorkspaceFileDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
        workspaceId: params.workspaceId,
        name: params.name,
        path: params.path,
        type: params.type,
        mimeType: params.mimeType || this.getMimeTypeFromExtension(params.name),
        size: contentSize,
        encoding: params.encoding || 'utf8',
        content: inlineContent,
        storageFileId,
        createdBy: 'system', // Would be set from session
        updatedBy: 'system',
        filePermissions: JSON.stringify(params.permissions || []),
        version: 1,
        isLocked: false,
        language: this.getLanguageFromExtension(params.name),
        isOpen: false,
        isDirty: false
      };

      const document = await services.databases.createDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        fileId,
        fileDoc,
        [
          `read("user:*")`, // Adjust based on workspace permissions
          `write("user:*")`
        ]
      );

      // Create initial version if content exists
      if (params.content) {
        await this.createFileVersion({
          fileId,
          version: 1,
          content: params.content,
          createdBy: 'system',
          message: 'Initial version',
          size: contentSize
        }, sessionId);
      }

      logger.info(`File created: ${fileId} in workspace ${params.workspaceId}`);
      return this.documentToWorkspaceFile(document as unknown as WorkspaceFileDocument);
    });
  }

  /**
   * Get file by ID
   */
  async getFile(fileId: string, sessionId?: string): Promise<ServiceResult<WorkspaceFile>> {
    return this.executeOperation('getFile', async () => {
      this.validateRequired({ fileId }, ['fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      const document = await services.databases.getDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        fileId
      );

      return this.documentToWorkspaceFile(document as unknown as WorkspaceFileDocument);
    });
  }

  /**
   * List files in workspace with filtering
   */
  async listFiles(
    queryParams: FileQueryParams,
    sessionId?: string
  ): Promise<ServiceResult<PaginatedResult<WorkspaceFile>>> {
    return this.executeOperation('listFiles', async () => {
      this.validateRequired(queryParams, ['workspaceId']);

      const validatedPagination = this.validatePagination(queryParams.pagination);
      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Build queries
      const queries: string[] = [
        `equal("workspaceId", "${queryParams.workspaceId}")`
      ];

      if (queryParams.path) {
        queries.push(`startsWith("path", "${queryParams.path}")`);
      }

      if (queryParams.type) {
        queries.push(`equal("type", "${queryParams.type}")`);
      }

      if (queryParams.search) {
        queries.push(`search("name", "${queryParams.search}")`);
      }

      // Add pagination
      queries.push(`limit(${validatedPagination.limit})`);
      queries.push(`offset(${validatedPagination.offset})`);
      queries.push('orderAsc("path")');

      const result = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        queries
      );

      const files = await Promise.all(
        result.documents.map(doc => this.documentToWorkspaceFile(doc as unknown as WorkspaceFileDocument))
      );

      return this.formatPaginatedResult(files, result.total, validatedPagination);
    });
  }

  /**
   * Update file content and metadata
   */
  async updateFile(params: UpdateFileParams, sessionId?: string): Promise<ServiceResult<WorkspaceFile>> {
    return this.executeOperation('updateFile', async () => {
      this.validateRequired(params, ['fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Get current file
      const currentDoc = await services.databases.getDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        params.fileId
      ) as unknown as WorkspaceFileDocument;

      // Prepare update data
      const updateData: Partial<WorkspaceFileDocument> = {
        updatedBy: 'system' // Would be set from session
      };

      if (params.name !== undefined) updateData.name = params.name;
      if (params.path !== undefined) updateData.path = params.path;
      if (params.permissions !== undefined) {
        updateData.filePermissions = JSON.stringify(params.permissions);
      }

      // Handle content update
      if (params.content !== undefined) {
        const contentSize = Buffer.byteLength(params.content, 'utf8');
        const useStorage = contentSize > this.MAX_INLINE_FILE_SIZE;

        if (useStorage) {
          // TODO: Store in Appwrite Storage
          logger.warn(`File ${currentDoc.name} is large (${contentSize} bytes) but storing inline`);
          updateData.content = params.content;
        } else {
          updateData.content = params.content;
        }

        updateData.size = contentSize;
        updateData.version = currentDoc.version + 1;

        // Create new version
        await this.createFileVersion({
          fileId: params.fileId,
          version: currentDoc.version + 1,
          content: params.content,
          createdBy: 'system',
          message: params.versionMessage || 'Updated content',
          size: contentSize
        }, sessionId);
      }

      const document = await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        params.fileId,
        updateData
      );

      logger.info(`File updated: ${params.fileId}`);
      return this.documentToWorkspaceFile(document as unknown as WorkspaceFileDocument);
    });
  }

  /**
   * Delete file
   */
  async deleteFile(fileId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('deleteFile', async () => {
      this.validateRequired({ fileId }, ['fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      // Delete file versions first
      await this.deleteFileVersions(fileId, sessionId);

      // Delete main file document
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        fileId
      );

      logger.info(`File deleted: ${fileId}`);
      return true;
    });
  }

  // ============================================================================
  // File Locking Operations
  // ============================================================================

  /**
   * Lock file for editing
   */
  async lockFile(fileId: string, userId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('lockFile', async () => {
      this.validateRequired({ fileId, userId }, ['fileId', 'userId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        fileId,
        {
          isLocked: true,
          lockedBy: userId,
          lockedAt: new Date().toISOString()
        }
      );

      logger.info(`File locked: ${fileId} by ${userId}`);
      return true;
    });
  }

  /**
   * Unlock file
   */
  async unlockFile(fileId: string, sessionId?: string): Promise<ServiceResult<boolean>> {
    return this.executeOperation('unlockFile', async () => {
      this.validateRequired({ fileId }, ['fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      await services.databases.updateDocument(
        WORKSPACE_DATABASE_ID,
        WORKSPACE_COLLECTIONS.WORKSPACE_FILES,
        fileId,
        {
          isLocked: false,
          lockedBy: null,
          lockedAt: null
        }
      );

      logger.info(`File unlocked: ${fileId}`);
      return true;
    });
  }

  // ============================================================================
  // Version Management
  // ============================================================================

  /**
   * Get file versions
   */
  async getFileVersions(fileId: string, sessionId?: string): Promise<ServiceResult<FileVersion[]>> {
    return this.executeOperation('getFileVersions', async () => {
      this.validateRequired({ fileId }, ['fileId']);

      const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

      const result = await services.databases.listDocuments(
        WORKSPACE_DATABASE_ID,
        'file-versions', // Would need to be added to WORKSPACE_COLLECTIONS
        [
          `equal("fileId", "${fileId}")`,
          'orderDesc("version")'
        ]
      );

      const versions = result.documents.map((doc) => {
        const versionDoc = doc as unknown as FileVersionDocument;
        return {
          id: versionDoc.$id,
          version: versionDoc.version,
          content: versionDoc.content,
          createdAt: new Date(versionDoc.$createdAt),
          createdBy: versionDoc.createdBy,
          message: versionDoc.message,
          size: versionDoc.size
        };
      });

      return versions;
    });
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private async createFileVersion(
    params: {
      fileId: string;
      version: number;
      content: string;
      createdBy: string;
      message?: string;
      size: number;
    },
    sessionId?: string
  ): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const versionDoc: Omit<FileVersionDocument, '$id' | '$createdAt' | '$updatedAt' | '$permissions'> = {
      fileId: params.fileId,
      version: params.version,
      content: params.content,
      createdBy: params.createdBy,
      message: params.message,
      size: params.size
    };

    await services.databases.createDocument(
      WORKSPACE_DATABASE_ID,
      'file-versions', // Would need to be added to WORKSPACE_COLLECTIONS
      ID.unique(),
      versionDoc,
      [`read("user:*")`, `write("user:*")`]
    );
  }

  private async deleteFileVersions(fileId: string, sessionId?: string): Promise<void> {
    const services = sessionId ? createSessionServices(sessionId) : { databases: adminDatabases };

    const versions = await services.databases.listDocuments(
      WORKSPACE_DATABASE_ID,
      'file-versions',
      [`equal("fileId", "${fileId}")`]
    );

    for (const version of versions.documents) {
      await services.databases.deleteDocument(
        WORKSPACE_DATABASE_ID,
        'file-versions',
        version.$id
      );
    }
  }

  private async documentToWorkspaceFile(doc: WorkspaceFileDocument): Promise<WorkspaceFile> {
    // Get file versions
    const versions = await this.getFileVersions(doc.$id);

    return {
      id: doc.$id,
      name: doc.name,
      path: doc.path,
      type: doc.type,
      mimeType: doc.mimeType,
      size: doc.size,
      content: doc.content,
      encoding: doc.encoding,
      createdAt: new Date(doc.$createdAt),
      updatedAt: new Date(doc.$updatedAt),
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      permissions: JSON.parse(doc.filePermissions || '[]'),
      version: doc.version,
      versions: versions.success ? versions.data || [] : [],
      isLocked: doc.isLocked,
      lockedBy: doc.lockedBy,
      lockedAt: doc.lockedAt ? new Date(doc.lockedAt) : undefined,
      language: doc.language,
      isOpen: doc.isOpen,
      isDirty: doc.isDirty,
      aiSuggestions: doc.aiSuggestions ? JSON.parse(doc.aiSuggestions) : undefined
    };
  }

  private getMimeTypeFromExtension(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'js': 'application/javascript',
      'ts': 'application/typescript',
      'py': 'text/x-python',
      'html': 'text/html',
      'css': 'text/css',
      'json': 'application/json',
      'md': 'text/markdown',
      'txt': 'text/plain',
      'yml': 'application/x-yaml',
      'yaml': 'application/x-yaml'
    };
    return mimeTypes[ext || ''] || 'text/plain';
  }

  private getLanguageFromExtension(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languages: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'html': 'html',
      'css': 'css',
      'json': 'json',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml',
      'sh': 'bash',
      'sql': 'sql'
    };
    return languages[ext || ''] || 'text';
  }
}
