import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { AuthService } from '../auth';
import { AppwriteServerError } from '@/lib/appwrite-server';

// Mock the Appwrite server dependencies
jest.mock('@/lib/appwrite-server', () => ({
  adminAccount: {
    createRecovery: jest.fn(),
  },
  adminUsers: {
    create: jest.fn(),
    list: jest.fn(),
  },
  createSessionServices: jest.fn(),
  AppwriteServerError: class extends Error {
    constructor(message: string, public code?: string, public type?: string, public statusCode?: number) {
      super(message);
      this.name = 'AppwriteServerError';
    }
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
  ID: {
    unique: jest.fn(() => 'test-id-123'),
  },
}));

describe('AuthService', () => {
  let authService: AuthService;
  let mockAdminUsers: any;
  let mockAdminAccount: any;
  let mockCreateSessionServices: any;

  beforeEach(() => {
    authService = new AuthService();
    
    // Get mocked dependencies
    const appwriteServer = require('@/lib/appwrite-server');
    mockAdminUsers = appwriteServer.adminUsers;
    mockAdminAccount = appwriteServer.adminAccount;
    mockCreateSessionServices = appwriteServer.createSessionServices;

    // Reset all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('createUser', () => {
    it('should create a user successfully', async () => {
      const mockUser = {
        $id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      mockAdminUsers.create.mockResolvedValue(mockUser);

      const params = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const result = await authService.createUser(params);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.userId).toBe('user-123');
      expect(result.data?.email).toBe('<EMAIL>');
      expect(result.data?.name).toBe('Test User');

      expect(mockAdminUsers.create).toHaveBeenCalledWith(
        'test-id-123',
        '<EMAIL>',
        undefined,
        'password123',
        'Test User'
      );
    });

    it('should handle validation errors', async () => {
      const params = {
        email: '',
        password: 'password123',
        name: 'Test User',
      };

      const result = await authService.createUser(params);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('MISSING_PARAMETERS');
    });

    it('should handle Appwrite errors', async () => {
      const mockError = new Error('User already exists');
      mockAdminUsers.create.mockRejectedValue(mockError);

      const params = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      const result = await authService.createUser(params);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('User already exists');
    });
  });

  describe('loginUser', () => {
    it('should login user successfully', async () => {
      const mockSession = {
        $id: 'session-123',
        userId: 'user-123',
        expire: '2024-12-31T23:59:59.000Z',
      };

      const mockUser = {
        $id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockSessionServices = {
        account: {
          createEmailPasswordSession: jest.fn().mockResolvedValue(mockSession),
          get: jest.fn().mockResolvedValue(mockUser),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const params = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await authService.loginUser(params);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.user).toEqual(mockUser);
      expect(result.data?.session.sessionId).toBe('session-123');
      expect(result.data?.session.userId).toBe('user-123');

      expect(mockSessionServices.account.createEmailPasswordSession).toHaveBeenCalledWith(
        '<EMAIL>',
        'password123'
      );
    });

    it('should handle invalid credentials', async () => {
      const mockError = new Error('Invalid credentials');
      
      const mockSessionServices = {
        account: {
          createEmailPasswordSession: jest.fn().mockRejectedValue(mockError),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const params = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const result = await authService.loginUser(params);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Invalid credentials');
    });
  });

  describe('logoutUser', () => {
    it('should logout user successfully', async () => {
      const mockSessionServices = {
        account: {
          deleteSession: jest.fn().mockResolvedValue({}),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const result = await authService.logoutUser('session-123');

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);

      expect(mockSessionServices.account.deleteSession).toHaveBeenCalledWith('current');
    });

    it('should handle logout errors', async () => {
      const mockError = new Error('Session not found');
      
      const mockSessionServices = {
        account: {
          deleteSession: jest.fn().mockRejectedValue(mockError),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const result = await authService.logoutUser('invalid-session');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Session not found');
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user successfully', async () => {
      const mockUser = {
        $id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockSessionServices = {
        account: {
          get: jest.fn().mockResolvedValue(mockUser),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const result = await authService.getCurrentUser('session-123');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUser);

      expect(mockSessionServices.account.get).toHaveBeenCalled();
    });

    it('should handle invalid session', async () => {
      const mockError = new Error('Invalid session');
      
      const mockSessionServices = {
        account: {
          get: jest.fn().mockRejectedValue(mockError),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const result = await authService.getCurrentUser('invalid-session');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Invalid session');
    });
  });

  describe('sendPasswordReset', () => {
    it('should send password reset email successfully', async () => {
      mockAdminAccount.createRecovery.mockResolvedValue({});

      const params = {
        email: '<EMAIL>',
        url: 'https://example.com/reset',
      };

      const result = await authService.sendPasswordReset(params);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);

      expect(mockAdminAccount.createRecovery).toHaveBeenCalledWith(
        '<EMAIL>',
        'https://example.com/reset'
      );
    });

    it('should handle password reset errors', async () => {
      const mockError = new Error('User not found');
      mockAdminAccount.createRecovery.mockRejectedValue(mockError);

      const params = {
        email: '<EMAIL>',
        url: 'https://example.com/reset',
      };

      const result = await authService.sendPasswordReset(params);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('User not found');
    });
  });

  describe('healthCheck', () => {
    it('should return true when service is healthy', async () => {
      mockAdminUsers.list.mockResolvedValue({ users: [] });

      const result = await authService.healthCheck();

      expect(result).toBe(true);
      expect(mockAdminUsers.list).toHaveBeenCalled();
    });

    it('should return false when service is unhealthy', async () => {
      mockAdminUsers.list.mockRejectedValue(new Error('Connection failed'));

      const result = await authService.healthCheck();

      expect(result).toBe(false);
    });
  });

  describe('updateUser', () => {
    it('should update user profile successfully', async () => {
      const mockUpdatedUser = {
        $id: 'user-123',
        email: '<EMAIL>',
        name: 'Updated Name',
      };

      const mockSessionServices = {
        account: {
          updateName: jest.fn().mockResolvedValue({}),
          get: jest.fn().mockResolvedValue(mockUpdatedUser),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const params = {
        name: 'Updated Name',
      };

      const result = await authService.updateUser('session-123', params);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUpdatedUser);

      expect(mockSessionServices.account.updateName).toHaveBeenCalledWith('Updated Name');
      expect(mockSessionServices.account.get).toHaveBeenCalled();
    });
  });

  describe('getUserSessions', () => {
    it('should get user sessions successfully', async () => {
      const mockSessions = {
        sessions: [
          {
            $id: 'session-1',
            userId: 'user-123',
            clientName: 'Chrome',
            ip: '***********',
            countryName: 'United States',
            expire: '2024-12-31T23:59:59.000Z',
            current: true,
          },
          {
            $id: 'session-2',
            userId: 'user-123',
            clientName: 'Firefox',
            ip: '***********',
            countryName: 'United States',
            expire: '2024-12-30T23:59:59.000Z',
            current: false,
          },
        ],
      };

      const mockSessionServices = {
        account: {
          listSessions: jest.fn().mockResolvedValue(mockSessions),
        },
      };

      mockCreateSessionServices.mockReturnValue(mockSessionServices);

      const result = await authService.getUserSessions('session-123');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.length).toBe(2);
      expect(result.data?.[0].sessionId).toBe('session-1');
      expect(result.data?.[0].isActive).toBe(true);
      expect(result.data?.[1].sessionId).toBe('session-2');
      expect(result.data?.[1].isActive).toBe(false);
    });
  });
});
