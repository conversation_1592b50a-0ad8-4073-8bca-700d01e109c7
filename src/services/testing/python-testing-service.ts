/**
 * Python Testing Service
 * Handles test execution, debugging, and test management for Python frameworks
 */

import { PythonFramework } from '@/types/python-workspace';
import { dockerService } from '../docker';

export type TestFramework = 'pytest' | 'unittest' | 'nose2' | 'django-test';
export type TestStatus = 'pending' | 'running' | 'passed' | 'failed' | 'skipped' | 'error';

export interface TestSuite {
  id: string;
  name: string;
  framework: TestFramework;
  pythonFramework: PythonFramework;
  path: string;
  tests: TestCase[];
  configuration: TestConfiguration;
  lastRun?: TestRun;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestCase {
  id: string;
  name: string;
  description?: string;
  filePath: string;
  className?: string;
  methodName: string;
  tags: string[];
  status: TestStatus;
  duration?: number;
  error?: TestError;
  coverage?: TestCoverage;
  lastRun?: Date;
}

export interface TestRun {
  id: string;
  suiteId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  results: TestResult[];
  summary: TestSummary;
  coverage?: CoverageReport;
  logs: string[];
}

export interface TestResult {
  testId: string;
  status: TestStatus;
  duration: number;
  output?: string;
  error?: TestError;
  assertions: TestAssertion[];
  coverage?: TestCoverage;
}

export interface TestError {
  type: string;
  message: string;
  traceback: string[];
  file: string;
  line: number;
  column?: number;
}

export interface TestAssertion {
  type: 'assertEqual' | 'assertTrue' | 'assertFalse' | 'assertRaises' | 'custom';
  expected?: any;
  actual?: any;
  message?: string;
  passed: boolean;
}

export interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
  errors: number;
  duration: number;
  coverage?: number;
}

export interface TestConfiguration {
  framework: TestFramework;
  testPaths: string[];
  patterns: string[];
  excludePatterns: string[];
  coverage: {
    enabled: boolean;
    threshold: number;
    include: string[];
    exclude: string[];
    reportFormats: ('html' | 'xml' | 'json' | 'text')[];
  };
  parallel: {
    enabled: boolean;
    workers: number;
  };
  environment: Record<string, string>;
  plugins: string[];
  markers: string[];
}

export interface TestCoverage {
  file: string;
  lines: number;
  covered: number;
  percentage: number;
  missing: number[];
  branches?: {
    total: number;
    covered: number;
    percentage: number;
  };
}

export interface CoverageReport {
  overall: {
    lines: number;
    covered: number;
    percentage: number;
    branches?: {
      total: number;
      covered: number;
      percentage: number;
    };
  };
  files: TestCoverage[];
  timestamp: Date;
}

export interface DebugSession {
  id: string;
  testId?: string;
  filePath: string;
  status: 'starting' | 'running' | 'paused' | 'stopped';
  breakpoints: Breakpoint[];
  currentFrame?: StackFrame;
  variables: Variable[];
  output: string[];
  startTime: Date;
}

export interface Breakpoint {
  id: string;
  filePath: string;
  line: number;
  condition?: string;
  enabled: boolean;
  hitCount: number;
}

export interface StackFrame {
  id: string;
  name: string;
  filePath: string;
  line: number;
  column?: number;
  locals: Variable[];
}

export interface Variable {
  name: string;
  value: any;
  type: string;
  scope: 'local' | 'global' | 'builtin';
  children?: Variable[];
}

class PythonTestingService {
  // In-memory storage for demo - replace with persistent storage in production
  private testSuites: Map<string, TestSuite> = new Map();
  private testRuns: Map<string, TestRun> = new Map();
  private debugSessions: Map<string, DebugSession> = new Map();

  /**
   * Discover tests in workspace
   */
  async discoverTests(
    workspaceId: string,
    framework: TestFramework,
    pythonFramework: PythonFramework,
    paths?: string[]
  ): Promise<TestSuite> {
    try {
      const testPaths = paths || this.getDefaultTestPaths(pythonFramework);
      const discoveryCommand = this.buildDiscoveryCommand(framework, testPaths);
      
      const result = await dockerService.executeCommand(workspaceId, discoveryCommand);
      
      if (!result.success) {
        throw new Error(`Test discovery failed: ${result.error}`);
      }

      const tests = this.parseDiscoveryOutput(result.output || '', framework);
      
      const testSuite: TestSuite = {
        id: this.generateId(),
        name: `${pythonFramework} Test Suite`,
        framework,
        pythonFramework,
        path: testPaths[0],
        tests,
        configuration: this.getDefaultConfiguration(framework),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.testSuites.set(testSuite.id, testSuite);
      return testSuite;

    } catch (error) {
      console.error('Error discovering tests:', error);
      throw error;
    }
  }

  /**
   * Run tests
   */
  async runTests(
    workspaceId: string,
    suiteId: string,
    options: {
      testIds?: string[];
      coverage?: boolean;
      parallel?: boolean;
      verbose?: boolean;
    } = {}
  ): Promise<TestRun> {
    try {
      const testSuite = this.testSuites.get(suiteId);
      if (!testSuite) {
        throw new Error('Test suite not found');
      }

      const testRun: TestRun = {
        id: this.generateId(),
        suiteId,
        status: 'running',
        startTime: new Date(),
        results: [],
        summary: {
          total: 0,
          passed: 0,
          failed: 0,
          skipped: 0,
          errors: 0,
          duration: 0,
        },
        logs: [],
      };

      this.testRuns.set(testRun.id, testRun);

      // Build test command
      const testCommand = this.buildTestCommand(testSuite, options);
      
      // Execute tests
      const result = await dockerService.executeCommand(workspaceId, testCommand);
      
      // Parse results
      const testResults = this.parseTestOutput(result.output || '', testSuite.framework);
      
      // Update test run
      testRun.status = result.success ? 'completed' : 'failed';
      testRun.endTime = new Date();
      testRun.duration = testRun.endTime.getTime() - testRun.startTime.getTime();
      testRun.results = testResults;
      testRun.summary = this.calculateSummary(testResults);
      testRun.logs = result.output?.split('\n') || [];

      // Generate coverage report if requested
      if (options.coverage) {
        testRun.coverage = await this.generateCoverageReport(workspaceId, testSuite);
      }

      // Update test suite
      testSuite.lastRun = testRun;
      testSuite.updatedAt = new Date();

      // Update individual test statuses
      for (const result of testResults) {
        const test = testSuite.tests.find(t => t.id === result.testId);
        if (test) {
          test.status = result.status;
          test.duration = result.duration;
          test.error = result.error;
          test.lastRun = new Date();
        }
      }

      this.testSuites.set(suiteId, testSuite);
      this.testRuns.set(testRun.id, testRun);

      return testRun;

    } catch (error) {
      console.error('Error running tests:', error);
      throw error;
    }
  }

  /**
   * Start debug session
   */
  async startDebugSession(
    workspaceId: string,
    filePath: string,
    testId?: string
  ): Promise<DebugSession> {
    try {
      const debugSession: DebugSession = {
        id: this.generateId(),
        testId,
        filePath,
        status: 'starting',
        breakpoints: [],
        variables: [],
        output: [],
        startTime: new Date(),
      };

      // Start debugger
      const debugCommand = this.buildDebugCommand(filePath, testId);
      
      // This would typically start a debug server and connect to it
      // For now, we'll simulate the debug session
      debugSession.status = 'running';

      this.debugSessions.set(debugSession.id, debugSession);
      return debugSession;

    } catch (error) {
      console.error('Error starting debug session:', error);
      throw error;
    }
  }

  /**
   * Set breakpoint
   */
  async setBreakpoint(
    sessionId: string,
    filePath: string,
    line: number,
    condition?: string
  ): Promise<Breakpoint> {
    const session = this.debugSessions.get(sessionId);
    if (!session) {
      throw new Error('Debug session not found');
    }

    const breakpoint: Breakpoint = {
      id: this.generateId(),
      filePath,
      line,
      condition,
      enabled: true,
      hitCount: 0,
    };

    session.breakpoints.push(breakpoint);
    this.debugSessions.set(sessionId, session);

    return breakpoint;
  }

  /**
   * Generate test template
   */
  generateTestTemplate(
    framework: TestFramework,
    pythonFramework: PythonFramework,
    className: string,
    methods: string[]
  ): string {
    switch (framework) {
      case 'pytest':
        return this.generatePytestTemplate(className, methods);
      case 'unittest':
        return this.generateUnittestTemplate(className, methods);
      case 'django-test':
        return this.generateDjangoTestTemplate(className, methods);
      default:
        return this.generatePytestTemplate(className, methods);
    }
  }

  /**
   * Generate test for existing code
   */
  async generateTestsForCode(
    workspaceId: string,
    filePath: string,
    framework: TestFramework
  ): Promise<string> {
    try {
      // Read the source file
      const result = await dockerService.executeCommand(workspaceId, `cat "${filePath}"`);
      
      if (!result.success || !result.output) {
        throw new Error('Failed to read source file');
      }

      const sourceCode = result.output;
      
      // Parse the code to extract classes and functions
      const codeStructure = this.parseCodeStructure(sourceCode);
      
      // Generate tests based on the structure
      return this.generateTestsFromStructure(codeStructure, framework);

    } catch (error) {
      console.error('Error generating tests for code:', error);
      throw error;
    }
  }

  // Private helper methods

  private getDefaultTestPaths(framework: PythonFramework): string[] {
    switch (framework) {
      case 'django':
        return ['/home/<USER>/tests/', '/home/<USER>/*/tests.py'];
      case 'flask':
      case 'fastapi':
        return ['/home/<USER>/tests/', '/home/<USER>/test_*.py'];
      default:
        return ['/home/<USER>/tests/', '/home/<USER>/test_*.py'];
    }
  }

  private buildDiscoveryCommand(framework: TestFramework, paths: string[]): string {
    const pathsStr = paths.join(' ');
    
    switch (framework) {
      case 'pytest':
        return `cd /home/<USER>
      case 'unittest':
        return `cd /home/<USER>"test_*.py" -v`;
      case 'django-test':
        return `cd /home/<USER>
      default:
        return `cd /home/<USER>
    }
  }

  private buildTestCommand(
    testSuite: TestSuite,
    options: {
      testIds?: string[];
      coverage?: boolean;
      parallel?: boolean;
      verbose?: boolean;
    }
  ): string {
    const { framework } = testSuite;
    let command = 'cd /home/<USER>';

    // Add coverage if requested
    if (options.coverage) {
      command += 'coverage run -m ';
    }

    switch (framework) {
      case 'pytest':
        command += 'pytest';
        if (options.verbose) command += ' -v';
        if (options.parallel) command += ` -n ${testSuite.configuration.parallel.workers}`;
        if (options.testIds) {
          // Convert test IDs to pytest node IDs
          const nodeIds = options.testIds.map(id => {
            const test = testSuite.tests.find(t => t.id === id);
            return test ? `${test.filePath}::${test.className}::${test.methodName}` : '';
          }).filter(Boolean);
          command += ` ${nodeIds.join(' ')}`;
        }
        break;
      case 'unittest':
        command += 'unittest';
        if (options.verbose) command += ' -v';
        break;
      case 'django-test':
        command += 'python manage.py test';
        if (options.verbose) command += ' -v 2';
        break;
    }

    return command;
  }

  private buildDebugCommand(filePath: string, testId?: string): string {
    if (testId) {
      return `cd /home/<USER>
    } else {
      return `cd /home/<USER>
    }
  }

  private parseDiscoveryOutput(output: string, framework: TestFramework): TestCase[] {
    const tests: TestCase[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      try {
        if (framework === 'pytest') {
          // Parse pytest collection output
          if (line.includes('::')) {
            const parts = line.split('::');
            if (parts.length >= 2) {
              const filePath = parts[0].replace('<Module ', '').replace('>', '');
              const testName = parts[parts.length - 1].replace('<Function ', '').replace('>', '');
              const className = parts.length > 2 ? parts[1].replace('<Class ', '').replace('>', '') : undefined;

              const test: TestCase = {
                id: this.generateId(),
                name: testName,
                filePath: filePath.startsWith('/') ? filePath : `/home/<USER>/${filePath}`,
                methodName: testName,
                className,
                tags: this.extractPytestTags(line),
                status: 'pending',
              };
              tests.push(test);
            }
          }
        } else if (framework === 'unittest') {
          // Parse unittest discovery output
          if (line.includes(' (') && line.includes(')')) {
            const match = line.match(/(\w+)\s+\(([^)]+)\)/);
            if (match) {
              const [, methodName, className] = match;
              const test: TestCase = {
                id: this.generateId(),
                name: methodName,
                filePath: `/home/<USER>/test_${className.toLowerCase()}.py`,
                methodName,
                className,
                tags: [],
                status: 'pending',
              };
              tests.push(test);
            }
          }
        } else if (framework === 'django-test') {
          // Parse Django test discovery output
          if (line.includes('.')) {
            const parts = line.split('.');
            if (parts.length >= 2) {
              const test: TestCase = {
                id: this.generateId(),
                name: parts[parts.length - 1],
                filePath: `/home/<USER>/${parts[0]}/tests.py`,
                methodName: parts[parts.length - 1],
                className: parts.length > 2 ? parts[parts.length - 2] : undefined,
                tags: [],
                status: 'pending',
              };
              tests.push(test);
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to parse test discovery line: ${line}`, error);
      }
    }

    return tests;
  }

  private extractPytestTags(line: string): string[] {
    const tags: string[] = [];
    // Extract pytest markers
    const markerMatch = line.match(/@pytest\.mark\.(\w+)/g);
    if (markerMatch) {
      tags.push(...markerMatch.map(m => m.replace('@pytest.mark.', '')));
    }
    return tags;
  }

  private parseTestOutput(output: string, framework: TestFramework): TestResult[] {
    const results: TestResult[] = [];
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      try {
        if (framework === 'pytest') {
          // Parse pytest output format
          const pytestMatch = line.match(/^(.+?)::(.*?)\s+(PASSED|FAILED|SKIPPED|ERROR)(?:\s+\[(\d+)%\])?(?:\s+\((.+?)s\))?/);
          if (pytestMatch) {
            const [, filePath, testName, status, , duration] = pytestMatch;
            const result: TestResult = {
              testId: this.generateTestId(filePath, testName),
              status: status.toLowerCase() as TestStatus,
              duration: duration ? parseFloat(duration) * 1000 : 0, // Convert to milliseconds
              assertions: this.extractAssertions(line),
            };

            // Extract error information if failed
            if (status === 'FAILED' || status === 'ERROR') {
              result.error = this.extractErrorFromOutput(output, testName);
            }

            results.push(result);
          }
        } else if (framework === 'unittest') {
          // Parse unittest output format
          const unittestMatch = line.match(/^(\w+)\s+\(([^)]+)\)\s+\.\.\.\s+(ok|FAIL|ERROR|SKIP)/);
          if (unittestMatch) {
            const [, methodName, className, status] = unittestMatch;
            const result: TestResult = {
              testId: this.generateTestId(className, methodName),
              status: this.mapUnittestStatus(status),
              duration: 0, // unittest doesn't provide timing by default
              assertions: [],
            };

            if (status === 'FAIL' || status === 'ERROR') {
              result.error = this.extractErrorFromOutput(output, methodName);
            }

            results.push(result);
          }
        } else if (framework === 'django-test') {
          // Parse Django test output format
          const djangoMatch = line.match(/^(\w+(?:\.\w+)*)\s+\.\.\.\s+(ok|FAIL|ERROR|SKIP)/);
          if (djangoMatch) {
            const [, testPath, status] = djangoMatch;
            const result: TestResult = {
              testId: this.generateTestId('django', testPath),
              status: this.mapUnittestStatus(status), // Django uses unittest format
              duration: 0,
              assertions: [],
            };

            if (status === 'FAIL' || status === 'ERROR') {
              result.error = this.extractErrorFromOutput(output, testPath);
            }

            results.push(result);
          }
        }
      } catch (error) {
        console.warn(`Failed to parse test output line: ${line}`, error);
      }
    }

    return results;
  }

  private generateTestId(filePath: string, testName: string): string {
    return `${filePath}::${testName}`.replace(/[^a-zA-Z0-9:]/g, '_');
  }

  private mapUnittestStatus(status: string): TestStatus {
    switch (status.toLowerCase()) {
      case 'ok': return 'passed';
      case 'fail': return 'failed';
      case 'error': return 'error';
      case 'skip': return 'skipped';
      default: return 'pending';
    }
  }

  private extractAssertions(line: string): TestAssertion[] {
    const assertions: TestAssertion[] = [];
    // Extract assertion information from test output
    const assertMatch = line.match(/assert\s+(.+)/);
    if (assertMatch) {
      assertions.push({
        type: 'custom',
        message: assertMatch[1],
        passed: !line.includes('FAILED'),
      });
    }
    return assertions;
  }

  private extractErrorFromOutput(output: string, testName: string): TestError | undefined {
    const lines = output.split('\n');
    let errorStart = -1;
    let errorEnd = -1;

    // Find the error section for this test
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(testName) && lines[i].includes('FAILED')) {
        errorStart = i + 1;
        break;
      }
    }

    if (errorStart === -1) return undefined;

    // Find the end of the error section
    for (let i = errorStart; i < lines.length; i++) {
      if (lines[i].trim() === '' && lines[i + 1]?.includes('::')) {
        errorEnd = i;
        break;
      }
    }

    if (errorEnd === -1) errorEnd = lines.length;

    const errorLines = lines.slice(errorStart, errorEnd);
    const traceback = errorLines.filter(line => line.trim());

    if (traceback.length === 0) return undefined;

    // Extract error type and message
    const lastLine = traceback[traceback.length - 1];
    const errorMatch = lastLine.match(/^(\w+Error): (.+)$/);

    return {
      type: errorMatch ? errorMatch[1] : 'Error',
      message: errorMatch ? errorMatch[2] : lastLine,
      traceback,
      file: testName,
      line: 0, // Would need more sophisticated parsing to get line number
    };
  }

  private calculateSummary(results: TestResult[]): TestSummary {
    return {
      total: results.length,
      passed: results.filter(r => r.status === 'passed').length,
      failed: results.filter(r => r.status === 'failed').length,
      skipped: results.filter(r => r.status === 'skipped').length,
      errors: results.filter(r => r.status === 'error').length,
      duration: results.reduce((sum, r) => sum + r.duration, 0),
    };
  }

  private async generateCoverageReport(
    workspaceId: string,
    testSuite: TestSuite
  ): Promise<CoverageReport> {
    try {
      // Generate coverage report
      const result = await dockerService.executeCommand(
        workspaceId,
        'cd /home/<USER>'
      );

      if (result.success && result.output) {
        const coverageData = JSON.parse(result.output);
        
        return {
          overall: {
            lines: coverageData.totals.num_statements,
            covered: coverageData.totals.covered_lines,
            percentage: coverageData.totals.percent_covered,
          },
          files: Object.entries(coverageData.files).map(([file, data]: [string, any]) => ({
            file,
            lines: data.summary.num_statements,
            covered: data.summary.covered_lines,
            percentage: data.summary.percent_covered,
            missing: data.missing_lines,
          })),
          timestamp: new Date(),
        };
      }
    } catch (error) {
      console.error('Error generating coverage report:', error);
    }

    // Return empty report if generation fails
    return {
      overall: { lines: 0, covered: 0, percentage: 0 },
      files: [],
      timestamp: new Date(),
    };
  }

  private getDefaultConfiguration(framework: TestFramework): TestConfiguration {
    return {
      framework,
      testPaths: ['/home/<USER>/tests/'],
      patterns: ['test_*.py', '*_test.py'],
      excludePatterns: ['__pycache__', '*.pyc'],
      coverage: {
        enabled: true,
        threshold: 80,
        include: ['*.py'],
        exclude: ['tests/*', 'venv/*'],
        reportFormats: ['html', 'text'],
      },
      parallel: {
        enabled: false,
        workers: 2,
      },
      environment: {},
      plugins: [],
      markers: [],
    };
  }

  private generatePytestTemplate(className: string, methods: string[]): string {
    const testMethods = methods.map(method => `
def test_${method}():
    """Test ${method} functionality."""
    # Arrange
    
    # Act
    
    # Assert
    assert True  # Replace with actual assertions`).join('\n');

    return `import pytest
from ${className.toLowerCase()} import ${className}


class Test${className}:
    """Test cases for ${className}."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        pass
    
    def teardown_method(self):
        """Clean up after each test method."""
        pass
${testMethods}`;
  }

  private generateUnittestTemplate(className: string, methods: string[]): string {
    const testMethods = methods.map(method => `
    def test_${method}(self):
        """Test ${method} functionality."""
        # Arrange
        
        # Act
        
        # Assert
        self.assertTrue(True)  # Replace with actual assertions`).join('\n');

    return `import unittest
from ${className.toLowerCase()} import ${className}


class Test${className}(unittest.TestCase):
    """Test cases for ${className}."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        pass
    
    def tearDown(self):
        """Clean up after each test method."""
        pass
${testMethods}


if __name__ == '__main__':
    unittest.main()`;
  }

  private generateDjangoTestTemplate(className: string, methods: string[]): string {
    const testMethods = methods.map(method => `
    def test_${method}(self):
        """Test ${method} functionality."""
        # Arrange
        
        # Act
        
        # Assert
        self.assertTrue(True)  # Replace with actual assertions`).join('\n');

    return `from django.test import TestCase
from django.contrib.auth.models import User
from .models import ${className}


class ${className}TestCase(TestCase):
    """Test cases for ${className}."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def tearDown(self):
        """Clean up after each test method."""
        pass
${testMethods}`;
  }

  private parseCodeStructure(sourceCode: string): any {
    const structure = {
      classes: [] as string[],
      functions: [] as string[],
      imports: [] as string[],
      decorators: [] as string[],
    };

    const lines = sourceCode.split('\n');
    let currentIndent = 0;
    let inClass = false;
    let currentClass = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      const indent = line.length - line.trimStart().length;

      // Skip empty lines and comments
      if (!trimmed || trimmed.startsWith('#')) continue;

      // Parse imports
      if (trimmed.startsWith('import ') || trimmed.startsWith('from ')) {
        const importMatch = trimmed.match(/(?:from\s+(\S+)\s+)?import\s+(.+)/);
        if (importMatch) {
          const [, fromModule, imports] = importMatch;
          const importList = imports.split(',').map(imp => imp.trim().split(' as ')[0]);
          structure.imports.push(...importList);
        }
      }

      // Parse decorators
      if (trimmed.startsWith('@')) {
        const decorator = trimmed.substring(1).split('(')[0];
        structure.decorators.push(decorator);
      }

      // Parse class definitions
      if (trimmed.startsWith('class ')) {
        const classMatch = trimmed.match(/class\s+(\w+)(?:\([^)]*\))?:/);
        if (classMatch) {
          const className = classMatch[1];
          structure.classes.push(className);
          inClass = true;
          currentClass = className;
          currentIndent = indent;
        }
      }

      // Parse function/method definitions
      if (trimmed.startsWith('def ')) {
        const funcMatch = trimmed.match(/def\s+(\w+)\s*\([^)]*\):/);
        if (funcMatch) {
          const funcName = funcMatch[1];

          // Skip private methods and special methods for public API
          if (!funcName.startsWith('_') || funcName.startsWith('__') && funcName.endsWith('__')) {
            if (inClass && indent > currentIndent) {
              // This is a method
              structure.functions.push(`${currentClass}.${funcName}`);
            } else {
              // This is a standalone function
              structure.functions.push(funcName);
              inClass = false;
              currentClass = '';
            }
          }
        }
      }

      // Reset class context when we exit the class
      if (inClass && indent <= currentIndent && trimmed && !trimmed.startsWith('class ')) {
        // Check if we're still in the class by looking for method definitions
        if (!trimmed.startsWith('def ') && !trimmed.startsWith('@')) {
          inClass = false;
          currentClass = '';
        }
      }
    }

    return structure;
  }

  private generateTestsFromStructure(structure: any, framework: TestFramework): string {
    if (structure.classes.length > 0) {
      return this.generateTestTemplate(framework, 'pytest', structure.classes[0], structure.functions);
    } else if (structure.functions.length > 0) {
      return this.generateTestTemplate(framework, 'pytest', 'Functions', structure.functions);
    } else {
      return this.generateTestTemplate(framework, 'pytest', 'Module', ['example']);
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // Public getter methods
  getTestSuites(): TestSuite[] {
    return Array.from(this.testSuites.values());
  }

  getTestSuite(id: string): TestSuite | undefined {
    return this.testSuites.get(id);
  }

  getTestRun(id: string): TestRun | undefined {
    return this.testRuns.get(id);
  }

  getDebugSession(id: string): DebugSession | undefined {
    return this.debugSessions.get(id);
  }
}

// Export singleton instance
export const pythonTestingService = new PythonTestingService();
