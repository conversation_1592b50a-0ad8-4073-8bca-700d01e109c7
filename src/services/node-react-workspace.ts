/**
 * Node/React Workspace Service
 * Handles Node.js and React project creation, management, and framework-specific operations
 */

import { 
  NodeProjectTemplate, 
  NodeWorkspaceConfig, 
  NodeEnvironment,
  NodeLivePreview,
  CreateNodeProjectRequest,
  CreateNodeProjectResponse,
  NodeWorkspaceStatus,
  NodeFramework
} from '@/types/node-react-workspace';
import { NODE_REACT_PROJECT_TEMPLATES, getNodeTemplate } from '@/data/node-react-templates';

export class NodeReactWorkspaceService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/node-react-workspace') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get all available Node/React project templates
   */
  async getTemplates(): Promise<NodeProjectTemplate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/templates`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching templates:', error);
      // Fallback to local templates
      return NODE_REACT_PROJECT_TEMPLATES;
    }
  }

  /**
   * Get template by ID
   */
  async getTemplate(id: string): Promise<NodeProjectTemplate | null> {
    try {
      const response = await fetch(`${this.baseUrl}/templates/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch template: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching template:', error);
      // Fallback to local template
      return getNodeTemplate(id) || null;
    }
  }

  /**
   * Create a new Node/React project
   */
  async createProject(request: CreateNodeProjectRequest): Promise<CreateNodeProjectResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/projects`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to create project: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error creating project:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get workspace status
   */
  async getWorkspaceStatus(workspaceId: string): Promise<NodeWorkspaceStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/status`);
      
      if (!response.ok) {
        throw new Error(`Failed to get workspace status: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error getting workspace status:', error);
      throw error;
    }
  }

  /**
   * Install Node package
   */
  async installPackage(
    workspaceId: string, 
    packageName: string, 
    version?: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm',
    isDev: boolean = false
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages/install`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageName,
          version,
          packageManager,
          isDev,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to install package: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error installing package:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to install package',
      };
    }
  }

  /**
   * Uninstall Node package
   */
  async uninstallPackage(
    workspaceId: string, 
    packageName: string,
    packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm'
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages/uninstall`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageName,
          packageManager,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to uninstall package: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error uninstalling package:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to uninstall package',
      };
    }
  }

  /**
   * Get installed packages
   */
  async getInstalledPackages(workspaceId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/packages`);
      
      if (!response.ok) {
        throw new Error(`Failed to get packages: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error getting packages:', error);
      return [];
    }
  }

  /**
   * Search for packages
   */
  async searchPackages(query: string, limit: number = 20): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/packages/search?q=${encodeURIComponent(query)}&limit=${limit}`);
      
      if (!response.ok) {
        throw new Error(`Failed to search packages: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error searching packages:', error);
      return [];
    }
  }

  /**
   * Execute Node command in workspace
   */
  async executeCommand(
    workspaceId: string,
    command: string,
    workingDirectory?: string
  ): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command,
          workingDirectory,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to execute command: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error executing command:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get live preview status
   */
  async getLivePreview(workspaceId: string): Promise<NodeLivePreview | null> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to get live preview: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error getting live preview:', error);
      return null;
    }
  }

  /**
   * Start live preview
   */
  async startLivePreview(
    workspaceId: string,
    projectPath: string,
    framework: NodeFramework,
    port?: number,
    command?: string
  ): Promise<NodeLivePreview> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectPath,
          framework,
          port,
          command,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start live preview: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error starting live preview:', error);
      throw error;
    }
  }

  /**
   * Stop live preview
   */
  async stopLivePreview(workspaceId: string): Promise<{ success: boolean }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/preview/stop`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Failed to stop live preview: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error stopping live preview:', error);
      return { success: false };
    }
  }

  /**
   * Generate framework-specific code using AI
   */
  async generateCode(
    workspaceId: string,
    framework: NodeFramework,
    prompt: string,
    context?: string
  ): Promise<{ success: boolean; code?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/workspaces/${workspaceId}/generate-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          framework,
          prompt,
          context,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `Failed to generate code: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      console.error('Error generating code:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate code',
      };
    }
  }
}

// Export singleton instance
export const nodeReactWorkspaceService = new NodeReactWorkspaceService();
