'use client';

import React from 'react';
import { AdminRoute, AdminSection, AdminCard } from '@/components/auth/admin-route';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Building2, 
  Monitor, 
  Server, 
  BarChart3, 
  Settings, 
  Shield, 
  Activity,
  Database,
  HardDrive,
  Cpu,
  MemoryStick,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

/**
 * Admin Dashboard Page
 * 
 * Main dashboard for system administrators with overview of all platform resources
 */
export default function AdminDashboard() {
  const { profile, user } = useAuth();

  // Mock data for demonstration
  const stats = {
    users: { total: 4, active: 3, new: 1 },
    organizations: { total: 3, active: 3 },
    workspaces: { total: 7, running: 3, stopped: 2, paused: 2 },
    vms: { total: 4, running: 2, stopped: 1, paused: 1 },
    resources: {
      cpu: { used: 24, total: 64, percentage: 37.5 },
      memory: { used: 48, total: 128, percentage: 37.5 },
      storage: { used: 850, total: 2000, percentage: 42.5 }
    }
  };

  const recentActivity = [
    { id: 1, action: 'User Login', user: '<EMAIL>', time: '2 minutes ago', type: 'success' },
    { id: 2, action: 'Workspace Created', user: '<EMAIL>', time: '15 minutes ago', type: 'info' },
    { id: 3, action: 'VM Started', user: '<EMAIL>', time: '1 hour ago', type: 'success' },
    { id: 4, action: 'Organization Updated', user: '<EMAIL>', time: '2 hours ago', type: 'info' },
  ];

  return (
    <AdminRoute>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          {/* Welcome Section */}
          <AdminSection
            title="System Administration Dashboard"
            description="Monitor and manage all aspects of the Omnispace platform"
            icon={Shield}
          >
            <div className="mb-8">
              <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                        Welcome back, {profile?.name || user?.name}!
                      </h3>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        You have full administrative access to the Omnispace platform
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="default" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                        <Shield className="h-3 w-3 mr-1" />
                        Administrator
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.users.total}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+{stats.users.new}</span> new this week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Organizations</CardTitle>
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.organizations.total}</div>
                  <p className="text-xs text-muted-foreground">
                    All active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Workspaces</CardTitle>
                  <Monitor className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.workspaces.total}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">{stats.workspaces.running}</span> running
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Virtual Machines</CardTitle>
                  <Server className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.vms.total}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">{stats.vms.running}</span> running
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Resource Usage */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
              <AdminCard title="CPU Usage" variant="default">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Cpu className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">CPU Cores</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {stats.resources.cpu.used}/{stats.resources.cpu.total}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${stats.resources.cpu.percentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {stats.resources.cpu.percentage}% utilized
                  </p>
                </div>
              </AdminCard>

              <AdminCard title="Memory Usage" variant="default">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MemoryStick className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">RAM</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {stats.resources.memory.used}/{stats.resources.memory.total} GB
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${stats.resources.memory.percentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {stats.resources.memory.percentage}% utilized
                  </p>
                </div>
              </AdminCard>

              <AdminCard title="Storage Usage" variant="default">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <HardDrive className="h-4 w-4 text-purple-500" />
                      <span className="text-sm font-medium">Storage</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {stats.resources.storage.used}/{stats.resources.storage.total} GB
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{ width: `${stats.resources.storage.percentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {stats.resources.storage.percentage}% utilized
                  </p>
                </div>
              </AdminCard>
            </div>

            {/* Quick Actions & Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AdminCard title="Quick Actions" description="Common administrative tasks">
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                    <Users className="h-5 w-5" />
                    <span className="text-sm">Manage Users</span>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    <span className="text-sm">Organizations</span>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                    <Monitor className="h-5 w-5" />
                    <span className="text-sm">Workspaces</span>
                  </Button>
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                    <Settings className="h-5 w-5" />
                    <span className="text-sm">System Settings</span>
                  </Button>
                </div>
              </AdminCard>

              <AdminCard title="Recent Activity" description="Latest system events">
                <div className="space-y-3">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
                      <div className={`h-2 w-2 rounded-full ${
                        activity.type === 'success' ? 'bg-green-500' : 
                        activity.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                      }`} />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{activity.action}</p>
                        <p className="text-xs text-muted-foreground truncate">{activity.user}</p>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {activity.time}
                      </div>
                    </div>
                  ))}
                </div>
              </AdminCard>
            </div>
          </AdminSection>
        </div>
      </div>
    </AdminRoute>
  );
}
