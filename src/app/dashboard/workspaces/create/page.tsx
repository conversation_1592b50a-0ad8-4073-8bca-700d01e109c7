'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

import { WorkspaceSelector } from '@/components/workspace/WorkspaceSelector';
import { WORKSPACE_LAYOUTS } from '@/components/workspace/components/WorkspaceLayout';
import { ProtectedRoute } from '@/components/auth/protected-route';

interface WorkspaceConfig {
  name: string;
  description: string;
  type: keyof typeof WORKSPACE_LAYOUTS;
  tags: string[];
}

export default function CreateWorkspacePage() {
  const router = useRouter();
  const [step, setStep] = useState<'select' | 'configure' | 'creating' | 'success'>('select');
  const [selectedType, setSelectedType] = useState<keyof typeof WORKSPACE_LAYOUTS | null>(null);
  const [config, setConfig] = useState<WorkspaceConfig>({
    name: '',
    description: '',
    type: 'default',
    tags: [],
  });
  const [newTag, setNewTag] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [createdWorkspaceId, setCreatedWorkspaceId] = useState<string | null>(null);

  const handleWorkspaceSelect = (workspaceType: keyof typeof WORKSPACE_LAYOUTS) => {
    setSelectedType(workspaceType);
    setConfig(prev => ({
      ...prev,
      type: workspaceType,
      name: prev.name || `My ${WORKSPACE_LAYOUTS[workspaceType].name}`,
    }));
    setStep('configure');
  };

  const handleAddTag = () => {
    if (newTag.trim() && !config.tags.includes(newTag.trim())) {
      setConfig(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setConfig(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleCreateWorkspace = async () => {
    if (!config.name.trim()) return;

    setIsCreating(true);
    setStep('creating');

    try {
      // Simulate workspace creation API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock workspace ID
      const workspaceId = `ws-${Date.now()}`;
      setCreatedWorkspaceId(workspaceId);
      setStep('success');
    } catch (error) {
      console.error('Failed to create workspace:', error);
      setStep('configure');
    } finally {
      setIsCreating(false);
    }
  };

  const handleBackToSelect = () => {
    setStep('select');
    setSelectedType(null);
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard/workspaces');
  };

  const handleOpenWorkspace = () => {
    if (createdWorkspaceId) {
      router.push(`/workspace/${createdWorkspaceId}`);
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={step === 'select' ? handleBackToDashboard : handleBackToSelect}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {step === 'select' ? 'Back to Dashboard' : 'Back to Selection'}
            </Button>
            
            <div>
              <h1 className="text-2xl font-bold">Create New Workspace</h1>
              <p className="text-muted-foreground">
                Set up your development environment in minutes
              </p>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="flex items-center gap-4 mb-8">
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step === 'select' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                1
              </div>
              <span className="text-sm font-medium">Select Template</span>
            </div>
            
            <div className="flex-1 h-px bg-border" />
            
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                ['configure', 'creating', 'success'].includes(step) ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                2
              </div>
              <span className="text-sm font-medium">Configure</span>
            </div>
            
            <div className="flex-1 h-px bg-border" />
            
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                ['creating', 'success'].includes(step) ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
              }`}>
                {step === 'success' ? <CheckCircle className="h-4 w-4" /> : '3'}
              </div>
              <span className="text-sm font-medium">Create</span>
            </div>
          </div>

          {/* Step Content */}
          <motion.div
            key={step}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {step === 'select' && (
              <WorkspaceSelector onSelect={handleWorkspaceSelect} />
            )}

            {step === 'configure' && selectedType && (
              <div className="max-w-2xl mx-auto space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Configure Your Workspace</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Customize your {WORKSPACE_LAYOUTS[selectedType].name} workspace
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Workspace Name */}
                    <div className="space-y-2">
                      <Label htmlFor="workspace-name">Workspace Name</Label>
                      <Input
                        id="workspace-name"
                        placeholder="Enter workspace name"
                        value={config.name}
                        onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>

                    {/* Description */}
                    <div className="space-y-2">
                      <Label htmlFor="workspace-description">Description (Optional)</Label>
                      <Textarea
                        id="workspace-description"
                        placeholder="Describe what you'll use this workspace for"
                        value={config.description}
                        onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>

                    {/* Tags */}
                    <div className="space-y-2">
                      <Label>Tags (Optional)</Label>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add a tag"
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                        />
                        <Button onClick={handleAddTag} size="sm">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {config.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {config.tags.map(tag => (
                            <Badge
                              key={tag}
                              variant="secondary"
                              className="cursor-pointer"
                              onClick={() => handleRemoveTag(tag)}
                            >
                              {tag} ×
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Workspace Preview */}
                    <div className="space-y-2">
                      <Label>Workspace Configuration</Label>
                      <div className="p-4 bg-muted rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Template:</span>
                          <span>{WORKSPACE_LAYOUTS[selectedType].name}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Panels:</span>
                          <span>{WORKSPACE_LAYOUTS[selectedType].panels.length} configured</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {WORKSPACE_LAYOUTS[selectedType].panels.map(panel => panel.title).join(', ')}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        onClick={handleCreateWorkspace}
                        disabled={!config.name.trim()}
                        className="flex-1"
                      >
                        Create Workspace
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {step === 'creating' && (
              <div className="max-w-md mx-auto">
                <Card>
                  <CardContent className="flex flex-col items-center justify-center p-8 text-center">
                    <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Creating Your Workspace</h3>
                    <p className="text-muted-foreground">
                      Setting up your {selectedType && WORKSPACE_LAYOUTS[selectedType].name} environment...
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {step === 'success' && (
              <div className="max-w-md mx-auto">
                <Card>
                  <CardContent className="flex flex-col items-center justify-center p-8 text-center">
                    <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Workspace Created!</h3>
                    <p className="text-muted-foreground mb-6">
                      Your {config.name} workspace is ready to use.
                    </p>
                    <div className="flex gap-3 w-full">
                      <Button variant="outline" onClick={handleBackToDashboard} className="flex-1">
                        Back to Dashboard
                      </Button>
                      <Button onClick={handleOpenWorkspace} className="flex-1">
                        Open Workspace
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
