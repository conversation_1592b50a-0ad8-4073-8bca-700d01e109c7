"use client";

import React from 'react';
import { ContainerManager } from '@/components/containers';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';

export default function ContainersPage() {
  return (
    <ProtectedRoute>
      <div className="flex flex-1 flex-col gap-4 p-4">
        {/* Header */}
        <header className="bg-background sticky top-0 flex shrink-0 items-center gap-2 border-b p-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
          <div>
            <h1 className="text-2xl font-bold">Container Management</h1>
            <p className="text-gray-600">Manage Docker containers and images</p>
          </div>
        </header>

        {/* Content */}
        <div className="flex-1">
          <ContainerManager />
        </div>
      </div>
    </ProtectedRoute>
  );
}
