import { NextRequest, NextResponse } from 'next/server';
import { getPythonTemplate } from '@/data/python-templates';
import { dockerService } from '@/services/server/docker';
import { CreatePythonProjectRequest } from '@/types/python-workspace';

// POST /api/python-workspace/projects - Create new Python project
export async function POST(request: NextRequest) {
  try {
    const body: CreatePythonProjectRequest = await request.json();
    const { template: templateId, projectName, config, workspaceId, userId } = body;

    // Validate required fields
    if (!templateId || !projectName || !workspaceId || !userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'template, projectName, workspaceId, and userId are required',
        },
        { status: 400 }
      );
    }

    // Get template
    const template = getPythonTemplate(templateId);
    if (!template) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
          message: `Template '${templateId}' does not exist`,
        },
        { status: 404 }
      );
    }

    // Validate project name
    if (!/^[a-zA-Z0-9_-]+$/.test(projectName)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid project name',
          message: 'Project name can only contain letters, numbers, hyphens, and underscores',
        },
        { status: 400 }
      );
    }

    // Check if workspace exists and is running
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Create project directory structure
    const projectPath = `/home/<USER>/projects/${projectName}`;
    
    // Execute project creation commands in the workspace container
    const commands = [
      `mkdir -p ${projectPath}`,
      `cd ${projectPath}`,
    ];

    // Create template files
    for (const file of template.files) {
      const filePath = `${projectPath}/${file.path}`;
      const fileDir = filePath.substring(0, filePath.lastIndexOf('/'));
      
      // Create directory if needed
      if (fileDir !== projectPath) {
        commands.push(`mkdir -p ${fileDir}`);
      }
      
      // Create file with content
      const escapedContent = file.content.replace(/'/g, "'\"'\"'");
      commands.push(`echo '${escapedContent}' > ${filePath}`);
      
      // Make executable if needed
      if (file.executable) {
        commands.push(`chmod +x ${filePath}`);
      }
    }

    // Execute all commands
    const commandString = commands.join(' && ');
    
    try {
      // Execute the command in the workspace container
      const result = await dockerService.executeCommand(workspaceId, commandString);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create project files');
      }
    } catch (error) {
      console.error('Error creating project files:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create project',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        },
        { status: 500 }
      );
    }

    // Create project metadata
    const project = {
      id: `${workspaceId}-${projectName}`,
      name: projectName,
      path: projectPath,
      framework: template.framework,
      config: {
        ...config,
        framework: template.framework,
        projectName,
      },
      template: templateId,
      createdAt: new Date().toISOString(),
      userId,
      workspaceId,
    };

    // TODO: Store project metadata in database
    // For now, we'll just return the project info

    return NextResponse.json({
      success: true,
      project,
      message: `Python ${template.framework} project '${projectName}' created successfully`,
    });

  } catch (error) {
    console.error('Error creating Python project:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create Python project',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/python-workspace/projects - List projects (future feature)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const userId = searchParams.get('userId');

    if (!workspaceId || !userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'workspaceId and userId are required',
        },
        { status: 400 }
      );
    }

    // TODO: Implement project listing from database
    // For now, return empty list
    
    return NextResponse.json({
      success: true,
      projects: [],
      message: 'Project listing will be implemented in a future update',
    });

  } catch (error) {
    console.error('Error listing Python projects:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list Python projects',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
