import { NextRequest, NextResponse } from 'next/server';
import { PYTHON_PROJECT_TEMPLATES } from '@/data/python-templates';

// GET /api/python-workspace/templates - Get all Python project templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework');
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    let templates = [...PYTHON_PROJECT_TEMPLATES];

    // Filter by framework
    if (framework) {
      templates = templates.filter(template => template.framework === framework);
    }

    // Filter by category (using tags)
    if (category) {
      templates = templates.filter(template => 
        template.tags.some(tag => tag.toLowerCase().includes(category.toLowerCase()))
      );
    }

    // Search in name, description, and tags
    if (search) {
      const searchLower = search.toLowerCase();
      templates = templates.filter(template =>
        template.name.toLowerCase().includes(searchLower) ||
        template.description.toLowerCase().includes(searchLower) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return NextResponse.json({
      success: true,
      data: templates,
      total: templates.length,
    });
  } catch (error) {
    console.error('Error fetching Python templates:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch Python templates',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/python-workspace/templates - Create custom template (future feature)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // TODO: Implement custom template creation
    // This would allow users to create and save their own templates
    
    return NextResponse.json(
      {
        success: false,
        error: 'Custom template creation not yet implemented',
        message: 'This feature will be available in a future update',
      },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error creating custom template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create custom template',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
