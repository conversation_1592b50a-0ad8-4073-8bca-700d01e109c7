import { NextRequest, NextResponse } from 'next/server';
import { pythonAIService } from '@/services/ai-code/python-ai-service';
import { PythonAnalysisRequest } from '@/types/python-ai-editor';

// POST /api/python-workspace/ai/analysis - Analyze Python code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      code,
      language,
      fileName,
      framework,
      pythonVersion,
      projectStructure,
      installedPackages,
      checkFrameworkBestPractices,
      checkSecurityVulnerabilities,
      checkPerformanceIssues,
      enableLinting,
      userId,
      workspaceId,
    } = body;

    // Validate required fields
    if (!code || !language) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'code and language are required',
        },
        { status: 400 }
      );
    }

    // Validate language is Python
    if (language !== 'python') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid language',
          message: 'This endpoint only supports Python code',
        },
        { status: 400 }
      );
    }

    // Create analysis request
    const analysisRequest: PythonAnalysisRequest = {
      code,
      language,
      fileName: fileName || 'untitled.py',
      framework,
      pythonVersion: pythonVersion || '3.11',
      projectStructure,
      installedPackages: installedPackages || [],
      checkFrameworkBestPractices: checkFrameworkBestPractices !== false,
      checkSecurityVulnerabilities: checkSecurityVulnerabilities !== false,
      checkPerformanceIssues: checkPerformanceIssues !== false,
    };

    // Get analysis from AI service
    const result = await pythonAIService.analyzePythonCode(
      analysisRequest,
      {
        userId: userId || 'anonymous',
        workspaceId: workspaceId || 'default',
        timestamp: new Date(),
      }
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'AI service error',
          message: result.error || 'Failed to analyze code',
        },
        { status: 500 }
      );
    }

    // Filter results based on preferences
    let { errors, suggestions } = result.data;

    // Filter linting errors if disabled
    if (!enableLinting) {
      errors = errors.filter(e => e.category !== 'style');
    }

    // Categorize errors and suggestions
    const categorizedErrors = {
      syntax: errors.filter(e => e.category === 'syntax'),
      import: errors.filter(e => e.category === 'import'),
      framework: errors.filter(e => e.category === 'framework'),
      security: errors.filter(e => e.category === 'security'),
      performance: errors.filter(e => e.category === 'performance'),
      style: errors.filter(e => e.category === 'style'),
      type: errors.filter(e => e.category === 'type'),
    };

    const categorizedSuggestions = {
      'framework-pattern': suggestions.filter(s => s.category === 'framework-pattern'),
      performance: suggestions.filter(s => s.category === 'performance'),
      security: suggestions.filter(s => s.category === 'security'),
      style: suggestions.filter(s => s.category === 'style'),
      structure: suggestions.filter(s => s.category === 'structure'),
    };

    // Calculate metrics
    const metrics = {
      totalErrors: errors.length,
      totalSuggestions: suggestions.length,
      criticalIssues: errors.filter(e => e.severity === 'error').length,
      warnings: errors.filter(e => e.severity === 'warning').length,
      frameworkSpecificIssues: errors.filter(e => e.frameworkSpecific).length,
      securityIssues: errors.filter(e => e.category === 'security').length,
      performanceIssues: errors.filter(e => e.category === 'performance').length,
    };

    return NextResponse.json({
      success: true,
      data: {
        errors,
        suggestions,
        categorized: {
          errors: categorizedErrors,
          suggestions: categorizedSuggestions,
        },
        metrics,
        framework,
        pythonVersion: analysisRequest.pythonVersion,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Python analysis error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/python-workspace/ai/analysis - Get analysis configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework');

    // Return configuration for Python analysis
    const config = {
      supportedFrameworks: ['django', 'flask', 'fastapi', 'streamlit', 'gradio'],
      supportedPythonVersions: ['3.8', '3.9', '3.10', '3.11', '3.12'],
      analysisTypes: [
        'syntax',
        'import',
        'framework',
        'security',
        'performance',
        'style',
        'type',
      ],
      suggestionCategories: [
        'framework-pattern',
        'performance',
        'security',
        'style',
        'structure',
      ],
      frameworkSpecific: framework ? {
        framework,
        bestPractices: getFrameworkBestPractices(framework),
        commonIssues: getFrameworkCommonIssues(framework),
        securityChecks: getFrameworkSecurityChecks(framework),
      } : null,
      features: {
        frameworkBestPractices: true,
        securityVulnerabilities: true,
        performanceIssues: true,
        linting: true,
        autoFix: true,
      },
    };

    return NextResponse.json({
      success: true,
      data: config,
    });

  } catch (error) {
    console.error('Error getting analysis config:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get analysis configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getFrameworkBestPractices(framework: string): string[] {
  switch (framework) {
    case 'django':
      return [
        'Use Django ORM instead of raw SQL',
        'Follow Django naming conventions',
        'Use Django forms for validation',
        'Implement proper URL patterns',
        'Use Django templates correctly',
      ];
    case 'flask':
      return [
        'Use blueprints for organization',
        'Implement proper error handling',
        'Use Flask-WTF for forms',
        'Follow Flask application factory pattern',
        'Use proper configuration management',
      ];
    case 'fastapi':
      return [
        'Use Pydantic models for validation',
        'Implement proper dependency injection',
        'Use async/await correctly',
        'Follow OpenAPI documentation standards',
        'Implement proper error handling',
      ];
    case 'streamlit':
      return [
        'Use session state for data persistence',
        'Implement proper caching',
        'Organize code with functions',
        'Use proper widget organization',
        'Handle user input validation',
      ];
    case 'gradio':
      return [
        'Use proper interface design',
        'Implement error handling in functions',
        'Use appropriate component types',
        'Handle file uploads securely',
        'Implement proper sharing settings',
      ];
    default:
      return [];
  }
}

function getFrameworkCommonIssues(framework: string): string[] {
  switch (framework) {
    case 'django':
      return [
        'N+1 query problems',
        'Missing CSRF protection',
        'Improper model relationships',
        'Incorrect URL patterns',
        'Template injection vulnerabilities',
      ];
    case 'flask':
      return [
        'Missing CSRF protection',
        'Improper session handling',
        'SQL injection vulnerabilities',
        'Missing input validation',
        'Incorrect error handling',
      ];
    case 'fastapi':
      return [
        'Missing input validation',
        'Improper async usage',
        'Missing dependency injection',
        'Incorrect response models',
        'Security vulnerabilities',
      ];
    case 'streamlit':
      return [
        'Missing session state',
        'Performance issues with large data',
        'Improper caching',
        'UI/UX problems',
        'Memory leaks',
      ];
    case 'gradio':
      return [
        'Improper interface design',
        'Missing error handling',
        'Security issues with file uploads',
        'Performance problems',
        'Incorrect component usage',
      ];
    default:
      return [];
  }
}

function getFrameworkSecurityChecks(framework: string): string[] {
  switch (framework) {
    case 'django':
      return [
        'CSRF protection',
        'SQL injection prevention',
        'XSS protection',
        'Authentication and authorization',
        'Secure settings configuration',
      ];
    case 'flask':
      return [
        'CSRF protection',
        'SQL injection prevention',
        'XSS protection',
        'Session security',
        'Input validation',
      ];
    case 'fastapi':
      return [
        'Input validation',
        'Authentication and authorization',
        'CORS configuration',
        'SQL injection prevention',
        'Rate limiting',
      ];
    case 'streamlit':
      return [
        'Input validation',
        'File upload security',
        'Data privacy',
        'Authentication',
        'Secure deployment',
      ];
    case 'gradio':
      return [
        'File upload security',
        'Input validation',
        'Authentication',
        'Data privacy',
        'Secure sharing',
      ];
    default:
      return [];
  }
}
