import { NextRequest, NextResponse } from 'next/server';
import { pythonAIService } from '@/services/ai-code/python-ai-service';
import { PythonCompletionRequest } from '@/types/python-ai-editor';

// POST /api/python-workspace/ai/completion - Get Python code completions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      code,
      position,
      language,
      framework,
      pythonVersion,
      projectStructure,
      installedPackages,
      enableFrameworkCompletion,
      enableImportSuggestions,
      userId,
      workspaceId,
      context,
    } = body;

    // Validate required fields
    if (!code || !position || !language) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'code, position, and language are required',
        },
        { status: 400 }
      );
    }

    // Validate language is Python
    if (language !== 'python') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid language',
          message: 'This endpoint only supports Python code',
        },
        { status: 400 }
      );
    }

    // Create completion request
    const completionRequest: PythonCompletionRequest = {
      code,
      position,
      language,
      framework,
      pythonVersion: pythonVersion || '3.11',
      projectStructure,
      installedPackages: installedPackages || [],
      context: context || {},
    };

    // Get completions from AI service
    const result = await pythonAIService.getPythonCompletions(
      completionRequest,
      {
        userId: userId || 'anonymous',
        workspaceId: workspaceId || 'default',
        timestamp: new Date(),
      }
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'AI service error',
          message: result.error || 'Failed to get completions',
        },
        { status: 500 }
      );
    }

    // Filter suggestions based on preferences
    let suggestions = result.data.suggestions;

    // Filter framework-specific suggestions if disabled
    if (!enableFrameworkCompletion && framework) {
      suggestions = suggestions.filter(s => !s.isFrameworkSpecific);
    }

    // Filter import suggestions if disabled
    if (!enableImportSuggestions) {
      suggestions = suggestions.filter(s => s.type !== 'import');
    }

    return NextResponse.json({
      success: true,
      data: {
        suggestions,
        framework,
        pythonVersion: completionRequest.pythonVersion,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Python completion error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/python-workspace/ai/completion - Get completion configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework');
    const pythonVersion = searchParams.get('pythonVersion') || '3.11';

    // Return configuration for Python completions
    const config = {
      supportedFrameworks: ['django', 'flask', 'fastapi', 'streamlit', 'gradio'],
      supportedPythonVersions: ['3.8', '3.9', '3.10', '3.11', '3.12'],
      defaultTriggerCharacters: ['.', '(', ' ', '\n', ':', '='],
      frameworkSpecific: framework ? {
        framework,
        triggerCharacters: getFrameworkTriggerCharacters(framework),
        completionTypes: getFrameworkCompletionTypes(framework),
      } : null,
      features: {
        frameworkCompletion: true,
        importSuggestions: true,
        packageInstallation: true,
        contextAware: true,
      },
    };

    return NextResponse.json({
      success: true,
      data: config,
    });

  } catch (error) {
    console.error('Error getting completion config:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get completion configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getFrameworkTriggerCharacters(framework: string): string[] {
  const base = ['.', '(', ' ', '\n', ':', '='];
  
  switch (framework) {
    case 'django':
      return [...base, '@', '"', "'"];
    case 'flask':
      return [...base, '@', '"', "'"];
    case 'fastapi':
      return [...base, '@', '"', "'", '[', ']'];
    case 'streamlit':
      return [...base, '"', "'"];
    case 'gradio':
      return [...base, '"', "'"];
    default:
      return base;
  }
}

function getFrameworkCompletionTypes(framework: string): string[] {
  const base = ['completion', 'snippet', 'import', 'function', 'variable', 'class', 'method'];
  
  switch (framework) {
    case 'django':
      return [...base, 'model', 'view', 'url', 'template', 'form'];
    case 'flask':
      return [...base, 'route', 'blueprint', 'template'];
    case 'fastapi':
      return [...base, 'route', 'dependency', 'schema', 'model'];
    case 'streamlit':
      return [...base, 'widget', 'chart', 'layout'];
    case 'gradio':
      return [...base, 'interface', 'component', 'block'];
    default:
      return base;
  }
}
