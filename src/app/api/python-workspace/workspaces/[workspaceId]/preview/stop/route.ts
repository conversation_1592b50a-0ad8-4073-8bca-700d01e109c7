import { NextRequest, NextResponse } from 'next/server';
import { pythonLivePreviewService } from '@/services/python-live-preview';
import { dockerService } from '@/services/docker';
import { PythonFramework } from '@/types/python-workspace';

// POST /api/python-workspace/workspaces/[workspaceId]/preview/stop - Stop live preview
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { framework } = body;

    // Validate required fields
    if (!framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'framework is required',
        },
        { status: 400 }
      );
    }

    // Validate framework
    const validFrameworks: PythonFramework[] = ['django', 'flask', 'fastapi', 'streamlit', 'gradio'];
    if (!validFrameworks.includes(framework)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid framework',
          message: `Framework must be one of: ${validFrameworks.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Check if preview exists
    const existingPreview = pythonLivePreviewService.getPreviewStatus(workspaceId, framework);
    if (!existingPreview) {
      return NextResponse.json(
        {
          success: false,
          error: 'Preview not found',
          message: `No active preview found for ${framework} in workspace '${workspaceId}'`,
        },
        { status: 404 }
      );
    }

    // Stop the live preview
    await pythonLivePreviewService.stopPreview(workspaceId, framework);

    return NextResponse.json({
      success: true,
      message: `Live preview stopped for ${framework} project`,
      data: {
        framework,
        previousStatus: existingPreview.status,
        stoppedAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Error stopping live preview:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to stop live preview',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/python-workspace/workspaces/[workspaceId]/preview/stop - Stop all previews
export async function DELETE(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get all active previews
    const activePreviews = pythonLivePreviewService.getWorkspacePreviews(workspaceId);

    if (activePreviews.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No active previews to stop',
        data: {
          stoppedPreviews: [],
        },
      });
    }

    // Stop all previews
    const stoppedPreviews = [];
    for (const preview of activePreviews) {
      try {
        await pythonLivePreviewService.stopPreview(workspaceId, preview.framework);
        stoppedPreviews.push({
          framework: preview.framework,
          port: preview.port,
          previousStatus: preview.status,
        });
      } catch (error) {
        console.error(`Error stopping ${preview.framework} preview:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Stopped ${stoppedPreviews.length} live preview(s)`,
      data: {
        stoppedPreviews,
        stoppedAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Error stopping all previews:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to stop all previews',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
