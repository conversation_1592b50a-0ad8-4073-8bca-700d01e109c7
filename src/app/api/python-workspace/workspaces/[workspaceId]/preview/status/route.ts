import { NextRequest, NextResponse } from 'next/server';
import { pythonLivePreviewService } from '@/services/python-live-preview';
import { dockerService } from '@/services/docker';
import { PythonFramework } from '@/types/python-workspace';

// GET /api/python-workspace/workspaces/[workspaceId]/preview/status - Get preview status
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework') as PythonFramework;
    const includeLogs = searchParams.get('includeLogs') === 'true';

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    if (framework) {
      // Get status for specific framework
      const preview = pythonLivePreviewService.getPreviewStatus(workspaceId, framework);
      
      if (!preview) {
        return NextResponse.json(
          {
            success: false,
            error: 'Preview not found',
            message: `No preview found for ${framework} in workspace '${workspaceId}'`,
          },
          { status: 404 }
        );
      }

      // Include logs if requested
      let logs = preview.logs;
      if (includeLogs) {
        logs = pythonLivePreviewService.getPreviewLogs(workspaceId, framework);
      }

      // Check if the preview is actually running by testing the port
      let actualStatus = preview.status;
      if (preview.status === 'running' && preview.port) {
        try {
          const healthCheck = await dockerService.executeCommand(
            workspaceId,
            `curl -s -o /dev/null -w "%{http_code}" http://localhost:${preview.port} --connect-timeout 5 || echo "000"`
          );
          
          const isHealthy = healthCheck.success && healthCheck.output?.trim() !== '000';
          if (!isHealthy) {
            actualStatus = 'error';
          }
        } catch (error) {
          actualStatus = 'error';
        }
      }

      const response = {
        ...preview,
        status: actualStatus,
        logs,
        lastChecked: new Date().toISOString(),
      };

      return NextResponse.json({
        success: true,
        data: response,
      });

    } else {
      // Get status for all previews in workspace
      const previews = pythonLivePreviewService.getWorkspacePreviews(workspaceId);
      
      // Enhance with real-time status checks
      const enhancedPreviews = await Promise.all(
        previews.map(async (preview) => {
          let actualStatus = preview.status;
          
          if (preview.status === 'running' && preview.port) {
            try {
              const healthCheck = await dockerService.executeCommand(
                workspaceId,
                `curl -s -o /dev/null -w "%{http_code}" http://localhost:${preview.port} --connect-timeout 5 || echo "000"`
              );
              
              const isHealthy = healthCheck.success && healthCheck.output?.trim() !== '000';
              if (!isHealthy) {
                actualStatus = 'error';
              }
            } catch (error) {
              actualStatus = 'error';
            }
          }

          return {
            ...preview,
            status: actualStatus,
            logs: includeLogs ? pythonLivePreviewService.getPreviewLogs(workspaceId, preview.framework) : preview.logs,
          };
        })
      );

      // Get workspace resource usage
      const resourceUsage = await getWorkspaceResourceUsage(workspaceId);

      return NextResponse.json({
        success: true,
        data: {
          workspaceId,
          previews: enhancedPreviews,
          summary: {
            total: enhancedPreviews.length,
            running: enhancedPreviews.filter(p => p.status === 'running').length,
            starting: enhancedPreviews.filter(p => p.status === 'starting').length,
            stopped: enhancedPreviews.filter(p => p.status === 'stopped').length,
            error: enhancedPreviews.filter(p => p.status === 'error').length,
          },
          resourceUsage,
          lastChecked: new Date().toISOString(),
        },
      });
    }

  } catch (error) {
    console.error('Error getting preview status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get preview status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/python-workspace/workspaces/[workspaceId]/preview/status - Restart preview
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { framework, projectPath, port, autoReload, environment, command } = body;

    // Validate required fields
    if (!framework || !projectPath) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'framework and projectPath are required',
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Restart the preview
    const preview = await pythonLivePreviewService.restartPreview(workspaceId, {
      framework,
      projectPath,
      port,
      autoReload: autoReload !== false,
      environment,
      command,
    });

    return NextResponse.json({
      success: true,
      data: preview,
      message: `Live preview restarted for ${framework} project`,
    });

  } catch (error) {
    console.error('Error restarting preview:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to restart preview',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper function to get workspace resource usage
async function getWorkspaceResourceUsage(workspaceId: string) {
  try {
    // Get CPU and memory usage
    const statsResult = await dockerService.executeCommand(
      workspaceId,
      `top -bn1 | grep "Cpu(s)" && free -m | grep "Mem:"`
    );

    // Get network usage for preview ports
    const networkResult = await dockerService.executeCommand(
      workspaceId,
      `netstat -tuln | grep -E ":(8000|5000|8501|7860|3000)" | wc -l`
    );

    // Get disk usage
    const diskResult = await dockerService.executeCommand(
      workspaceId,
      `df -h /home/<USER>
    );

    return {
      cpu: statsResult.output?.includes('Cpu(s)') ? 'Available' : 'Unknown',
      memory: statsResult.output?.includes('Mem:') ? 'Available' : 'Unknown',
      activePorts: parseInt(networkResult.output?.trim() || '0'),
      disk: diskResult.output?.trim() || 'Unknown',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Error getting resource usage:', error);
    return {
      cpu: 'Error',
      memory: 'Error',
      activePorts: 0,
      disk: 'Error',
      timestamp: new Date().toISOString(),
    };
  }
}
