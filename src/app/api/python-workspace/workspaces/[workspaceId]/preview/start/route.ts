import { NextRequest, NextResponse } from 'next/server';
import { pythonLivePreviewService } from '@/services/python-live-preview';
import { dockerService } from '@/services/server/docker';
import { PythonFramework } from '@/types/python-workspace';

// POST /api/python-workspace/workspaces/[workspaceId]/preview/start - Start live preview
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { projectPath, framework, port, autoReload, environment, command } = body;

    // Validate required fields
    if (!projectPath || !framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'projectPath and framework are required',
        },
        { status: 400 }
      );
    }

    // Validate framework
    const validFrameworks: PythonFramework[] = ['django', 'flask', 'fastapi', 'streamlit', 'gradio'];
    if (!validFrameworks.includes(framework)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid framework',
          message: `Framework must be one of: ${validFrameworks.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Check if workspace exists and is running
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Check if project path exists in workspace
    const pathCheckResult = await dockerService.executeCommand(
      workspaceId,
      `test -d ${projectPath} && echo "exists" || echo "not found"`
    );

    if (pathCheckResult.exitCode !== 0 || !pathCheckResult.stdout.includes('exists')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Project path not found',
          message: `Project path '${projectPath}' does not exist in workspace`,
        },
        { status: 404 }
      );
    }

    // Start the live preview
    const preview = await pythonLivePreviewService.startPreview(workspaceId, {
      framework,
      projectPath,
      port,
      autoReload: autoReload !== false,
      environment,
      command,
    });

    return NextResponse.json({
      success: true,
      data: preview,
      message: `Live preview started for ${framework} project`,
    });

  } catch (error) {
    console.error('Error starting live preview:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to start live preview',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/python-workspace/workspaces/[workspaceId]/preview/start - Get preview configuration
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework') as PythonFramework;

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get available ports
    const availablePorts = await getAvailablePorts(workspaceId);

    // Get framework-specific configuration
    const frameworkConfig = framework ? getFrameworkConfig(framework) : null;

    // Get existing previews
    const existingPreviews = pythonLivePreviewService.getWorkspacePreviews(workspaceId);

    const config = {
      workspaceId,
      supportedFrameworks: ['django', 'flask', 'fastapi', 'streamlit', 'gradio'],
      availablePorts,
      existingPreviews: existingPreviews.map(p => ({
        framework: p.framework,
        port: p.port,
        status: p.status,
        url: p.url,
      })),
      frameworkConfig,
      defaultSettings: {
        autoReload: true,
        environment: {
          PYTHONUNBUFFERED: '1',
          PYTHONDONTWRITEBYTECODE: '1',
        },
      },
    };

    return NextResponse.json({
      success: true,
      data: config,
    });

  } catch (error) {
    console.error('Error getting preview configuration:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get preview configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions
async function getAvailablePorts(workspaceId: string): Promise<number[]> {
  const basePorts = [8000, 5000, 8001, 8501, 7860, 3000, 4000, 9000];
  const availablePorts: number[] = [];

  for (const port of basePorts) {
    try {
      const result = await dockerService.executeCommand(
        workspaceId,
        `netstat -tuln | grep :${port} || echo "available"`
      );

      if (result.exitCode === 0 && result.stdout.includes('available')) {
        availablePorts.push(port);
      }
    } catch (error) {
      // If there's an error, assume port is available
      availablePorts.push(port);
    }
  }

  return availablePorts;
}

function getFrameworkConfig(framework: PythonFramework) {
  const configs = {
    django: {
      defaultPort: 8000,
      defaultCommand: 'python manage.py runserver 0.0.0.0:{{port}}',
      requiredFiles: ['manage.py'],
      environment: {
        DJANGO_SETTINGS_MODULE: 'settings',
        DJANGO_DEBUG: 'True',
      },
      healthCheckPath: '/',
      documentation: 'https://docs.djangoproject.com/en/stable/ref/django-admin/#runserver',
    },
    flask: {
      defaultPort: 5000,
      defaultCommand: 'flask run --host=0.0.0.0 --port={{port}}',
      requiredFiles: ['app.py'],
      environment: {
        FLASK_ENV: 'development',
        FLASK_APP: 'app.py',
        FLASK_DEBUG: '1',
      },
      healthCheckPath: '/',
      documentation: 'https://flask.palletsprojects.com/en/2.3.x/quickstart/',
    },
    fastapi: {
      defaultPort: 8000,
      defaultCommand: 'uvicorn main:app --host 0.0.0.0 --port {{port}} --reload',
      requiredFiles: ['main.py'],
      environment: {
        PYTHONPATH: '.',
      },
      healthCheckPath: '/docs',
      documentation: 'https://fastapi.tiangolo.com/tutorial/',
    },
    streamlit: {
      defaultPort: 8501,
      defaultCommand: 'streamlit run app.py --server.address 0.0.0.0 --server.port {{port}}',
      requiredFiles: ['app.py'],
      environment: {
        STREAMLIT_SERVER_HEADLESS: 'true',
        STREAMLIT_SERVER_ENABLE_CORS: 'false',
      },
      healthCheckPath: '/',
      documentation: 'https://docs.streamlit.io/',
    },
    gradio: {
      defaultPort: 7860,
      defaultCommand: 'python app.py',
      requiredFiles: ['app.py'],
      environment: {
        GRADIO_SERVER_NAME: '0.0.0.0',
        GRADIO_SERVER_PORT: '{{port}}',
      },
      healthCheckPath: '/',
      documentation: 'https://gradio.app/docs/',
    },
  };

  return configs[framework];
}
