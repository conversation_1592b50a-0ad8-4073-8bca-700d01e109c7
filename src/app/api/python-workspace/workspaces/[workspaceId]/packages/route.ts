import { NextRequest, NextResponse } from 'next/server';
import { pythonPackageManagerService } from '@/services/python-package-manager';
import { dockerService } from '@/services/docker';

// GET /api/python-workspace/workspaces/[workspaceId]/packages - List installed packages
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const packageManager = searchParams.get('packageManager') || 'pip';
    const environment = searchParams.get('environment');
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    // Validate package manager
    const validManagers = ['pip', 'conda', 'poetry'];
    if (!validManagers.includes(packageManager)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid package manager',
          message: `Package manager must be one of: ${validManagers.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get installed packages
    let packages = await pythonPackageManagerService.listPackages(
      workspaceId,
      packageManager as 'pip' | 'conda' | 'poetry',
      environment || undefined
    );

    // Filter by category if specified
    if (category) {
      packages = packages.filter(pkg => pkg.category === category);
    }

    // Filter by search term if specified
    if (search) {
      const searchLower = search.toLowerCase();
      packages = packages.filter(pkg =>
        pkg.name.toLowerCase().includes(searchLower) ||
        (pkg.description && pkg.description.toLowerCase().includes(searchLower))
      );
    }

    // Group packages by category
    const packagesByCategory = packages.reduce((acc, pkg) => {
      if (!acc[pkg.category]) {
        acc[pkg.category] = [];
      }
      acc[pkg.category].push(pkg);
      return acc;
    }, {} as Record<string, typeof packages>);

    // Get package statistics
    const stats = {
      total: packages.length,
      byCategory: Object.keys(packagesByCategory).reduce((acc, cat) => {
        acc[cat] = packagesByCategory[cat].length;
        return acc;
      }, {} as Record<string, number>),
      required: packages.filter(p => p.required).length,
      development: packages.filter(p => p.category === 'development').length,
    };

    return NextResponse.json({
      success: true,
      data: {
        packages,
        packagesByCategory,
        stats,
        filters: {
          packageManager,
          environment,
          category,
          search,
        },
        metadata: {
          workspaceId,
          timestamp: new Date().toISOString(),
        },
      },
    });

  } catch (error) {
    console.error('Error listing packages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list packages',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/python-workspace/workspaces/[workspaceId]/packages - Uninstall package
export async function DELETE(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { packageName, packageManager = 'pip' } = body;

    // Validate required fields
    if (!packageName) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'packageName is required',
        },
        { status: 400 }
      );
    }

    // Validate package manager
    const validManagers = ['pip', 'conda', 'poetry'];
    if (!validManagers.includes(packageManager)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid package manager',
          message: `Package manager must be one of: ${validManagers.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Uninstall the package
    const result = await pythonPackageManagerService.uninstallPackage(
      workspaceId,
      packageName,
      packageManager
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Package uninstallation failed',
          message: result.message || 'Unknown error occurred',
        },
        { status: 500 }
      );
    }

    // Get updated package list
    const packages = await pythonPackageManagerService.listPackages(
      workspaceId,
      packageManager
    );

    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        packageName,
        packageManager,
        remainingPackages: packages,
      },
    });

  } catch (error) {
    console.error('Error uninstalling package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to uninstall package',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
