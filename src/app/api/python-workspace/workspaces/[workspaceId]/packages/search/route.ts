import { NextRequest, NextResponse } from 'next/server';
import { pythonPackageManagerService } from '@/services/python-package-manager';
import { dockerService } from '@/services/docker';

// GET /api/python-workspace/workspaces/[workspaceId]/packages/search - Search for packages
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || searchParams.get('query');
    const packageManager = searchParams.get('packageManager') || 'pip';
    const limit = parseInt(searchParams.get('limit') || '20');

    // Validate required fields
    if (!query) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'query parameter is required',
        },
        { status: 400 }
      );
    }

    // Validate package manager
    const validManagers = ['pip', 'conda', 'poetry'];
    if (!validManagers.includes(packageManager)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid package manager',
          message: `Package manager must be one of: ${validManagers.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Validate limit
    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid limit',
          message: 'Limit must be between 1 and 100',
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Search for packages
    const searchResults = await pythonPackageManagerService.searchPackages(
      workspaceId,
      query,
      packageManager as 'pip' | 'conda' | 'poetry',
      limit
    );

    // Get currently installed packages to mark them
    const installedPackages = await pythonPackageManagerService.listPackages(
      workspaceId,
      packageManager as 'pip' | 'conda' | 'poetry'
    );

    const installedPackageNames = new Set(installedPackages.map(p => p.name.toLowerCase()));

    // Enhance search results with installation status
    const enhancedResults = searchResults.map(result => ({
      ...result,
      isInstalled: installedPackageNames.has(result.name.toLowerCase()),
      installedVersion: installedPackages.find(
        p => p.name.toLowerCase() === result.name.toLowerCase()
      )?.version,
    }));

    // Categorize results
    const categorizedResults = {
      exact: enhancedResults.filter(r => 
        r.name.toLowerCase() === query.toLowerCase()
      ),
      startsWith: enhancedResults.filter(r => 
        r.name.toLowerCase().startsWith(query.toLowerCase()) &&
        r.name.toLowerCase() !== query.toLowerCase()
      ),
      contains: enhancedResults.filter(r => 
        r.name.toLowerCase().includes(query.toLowerCase()) &&
        !r.name.toLowerCase().startsWith(query.toLowerCase())
      ),
      description: enhancedResults.filter(r => 
        !r.name.toLowerCase().includes(query.toLowerCase()) &&
        r.description.toLowerCase().includes(query.toLowerCase())
      ),
    };

    // Get popular packages for suggestions
    const popularPackages = getPopularPackages(packageManager).filter(pkg =>
      pkg.toLowerCase().includes(query.toLowerCase())
    );

    return NextResponse.json({
      success: true,
      data: {
        query,
        results: enhancedResults,
        categorized: categorizedResults,
        popular: popularPackages,
        stats: {
          total: enhancedResults.length,
          installed: enhancedResults.filter(r => r.isInstalled).length,
          exact: categorizedResults.exact.length,
          startsWith: categorizedResults.startsWith.length,
          contains: categorizedResults.contains.length,
          description: categorizedResults.description.length,
        },
        metadata: {
          packageManager,
          limit,
          workspaceId,
          timestamp: new Date().toISOString(),
        },
      },
    });

  } catch (error) {
    console.error('Error searching packages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to search packages',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/python-workspace/workspaces/[workspaceId]/packages/search - Advanced package search
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { 
      query, 
      packageManager = 'pip',
      limit = 20,
      categories = [],
      includePrerelease = false,
      sortBy = 'relevance'
    } = body;

    // Validate required fields
    if (!query) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'query is required',
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Perform advanced search
    let searchResults = await pythonPackageManagerService.searchPackages(
      workspaceId,
      query,
      packageManager,
      limit
    );

    // Filter by categories if specified
    if (categories.length > 0) {
      searchResults = searchResults.filter(result =>
        categories.some(category =>
          result.keywords.some(keyword =>
            keyword.toLowerCase().includes(category.toLowerCase())
          ) ||
          result.description.toLowerCase().includes(category.toLowerCase())
        )
      );
    }

    // Sort results
    searchResults = sortSearchResults(searchResults, sortBy, query);

    // Get framework-specific recommendations
    const frameworkRecommendations = getFrameworkRecommendations(query, categories);

    return NextResponse.json({
      success: true,
      data: {
        query,
        results: searchResults,
        recommendations: frameworkRecommendations,
        filters: {
          packageManager,
          categories,
          includePrerelease,
          sortBy,
        },
        metadata: {
          total: searchResults.length,
          workspaceId,
          timestamp: new Date().toISOString(),
        },
      },
    });

  } catch (error) {
    console.error('Error in advanced package search:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to perform advanced search',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getPopularPackages(packageManager: string): string[] {
  const popularByManager = {
    pip: [
      'requests', 'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
      'django', 'flask', 'fastapi', 'streamlit', 'gradio',
      'pytest', 'black', 'flake8', 'mypy', 'isort',
      'sqlalchemy', 'psycopg2-binary', 'redis', 'celery',
      'pillow', 'opencv-python', 'scikit-learn', 'tensorflow', 'torch'
    ],
    conda: [
      'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
      'scikit-learn', 'tensorflow', 'pytorch', 'jupyter',
      'requests', 'flask', 'django', 'fastapi',
      'pytest', 'black', 'flake8', 'mypy'
    ],
    poetry: [
      'requests', 'fastapi', 'django', 'flask', 'streamlit',
      'pytest', 'black', 'flake8', 'mypy', 'isort',
      'sqlalchemy', 'pydantic', 'typer', 'rich'
    ],
  };

  return popularByManager[packageManager as keyof typeof popularByManager] || popularByManager.pip;
}

function sortSearchResults(results: any[], sortBy: string, query: string) {
  switch (sortBy) {
    case 'name':
      return results.sort((a, b) => a.name.localeCompare(b.name));
    case 'relevance':
      return results.sort((a, b) => {
        // Exact match first
        if (a.name.toLowerCase() === query.toLowerCase()) return -1;
        if (b.name.toLowerCase() === query.toLowerCase()) return 1;
        
        // Then starts with query
        const aStartsWith = a.name.toLowerCase().startsWith(query.toLowerCase());
        const bStartsWith = b.name.toLowerCase().startsWith(query.toLowerCase());
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;
        
        // Then alphabetical
        return a.name.localeCompare(b.name);
      });
    case 'updated':
      return results.sort((a, b) => 
        new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
      );
    default:
      return results;
  }
}

function getFrameworkRecommendations(query: string, categories: string[]) {
  const recommendations = {
    django: ['django', 'djangorestframework', 'django-cors-headers', 'celery', 'gunicorn'],
    flask: ['flask', 'flask-restful', 'flask-cors', 'flask-sqlalchemy', 'gunicorn'],
    fastapi: ['fastapi', 'uvicorn', 'pydantic', 'sqlalchemy', 'alembic'],
    streamlit: ['streamlit', 'pandas', 'numpy', 'matplotlib', 'plotly'],
    gradio: ['gradio', 'numpy', 'pandas', 'pillow', 'matplotlib'],
    ml: ['numpy', 'pandas', 'scikit-learn', 'matplotlib', 'seaborn'],
    web: ['requests', 'httpx', 'aiohttp', 'beautifulsoup4', 'selenium'],
    testing: ['pytest', 'pytest-cov', 'pytest-mock', 'coverage', 'tox'],
    development: ['black', 'flake8', 'mypy', 'isort', 'pre-commit'],
  };

  const queryLower = query.toLowerCase();
  const relevantFrameworks = Object.keys(recommendations).filter(framework =>
    queryLower.includes(framework) || 
    categories.some(cat => cat.toLowerCase().includes(framework))
  );

  if (relevantFrameworks.length === 0) {
    return [];
  }

  const suggested = new Set<string>();
  relevantFrameworks.forEach(framework => {
    recommendations[framework as keyof typeof recommendations].forEach(pkg => {
      suggested.add(pkg);
    });
  });

  return Array.from(suggested);
}
