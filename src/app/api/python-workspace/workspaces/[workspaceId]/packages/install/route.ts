import { NextRequest, NextResponse } from 'next/server';
import { pythonPackageManagerService } from '@/services/python-package-manager';
import { dockerService } from '@/services/server/docker';

// POST /api/python-workspace/workspaces/[workspaceId]/packages/install - Install Python package
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { 
      packageName, 
      version, 
      packageManager = 'pip',
      dev = false,
      upgrade = false,
      force = false,
      extraIndex 
    } = body;

    // Validate required fields
    if (!packageName) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'packageName is required',
        },
        { status: 400 }
      );
    }

    // Validate package manager
    const validManagers = ['pip', 'conda', 'poetry'];
    if (!validManagers.includes(packageManager)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid package manager',
          message: `Package manager must be one of: ${validManagers.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Check if workspace exists and is running
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Install the package
    const result = await pythonPackageManagerService.installPackage(
      workspaceId,
      packageName,
      packageManager,
      {
        version,
        dev,
        upgrade,
        force,
        extraIndex,
      }
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Package installation failed',
          message: result.message || 'Unknown error occurred',
          output: result.output,
        },
        { status: 500 }
      );
    }

    // Get updated package list
    const packages = await pythonPackageManagerService.listPackages(
      workspaceId,
      packageManager
    );

    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        packageName,
        version,
        packageManager,
        installedPackages: packages,
        output: result.output,
      },
    });

  } catch (error) {
    console.error('Error installing package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to install package',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/python-workspace/workspaces/[workspaceId]/packages/install - Get installation info
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const packageManager = searchParams.get('packageManager') || 'pip';

    // Check if workspace exists
    const workspace = await dockerService.getWorkspace(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get available package managers
    const availableManagers = pythonPackageManagerService.getPackageManagers();

    // Get current package manager info
    const currentManager = pythonPackageManagerService.getPackageManager(packageManager);

    // Get installed packages
    const installedPackages = await pythonPackageManagerService.listPackages(
      workspaceId,
      packageManager as 'pip' | 'conda' | 'poetry'
    );

    // Get environments
    const environments = await pythonPackageManagerService.listEnvironments(
      workspaceId,
      packageManager as 'pip' | 'conda' | 'poetry'
    );

    const info = {
      workspaceId,
      availableManagers,
      currentManager,
      installedPackages: {
        count: installedPackages.length,
        packages: installedPackages,
        categories: {
          framework: installedPackages.filter(p => p.category === 'framework').length,
          database: installedPackages.filter(p => p.category === 'database').length,
          testing: installedPackages.filter(p => p.category === 'testing').length,
          development: installedPackages.filter(p => p.category === 'development').length,
          ml: installedPackages.filter(p => p.category === 'ml').length,
          web: installedPackages.filter(p => p.category === 'web').length,
          utility: installedPackages.filter(p => p.category === 'utility').length,
        },
      },
      environments: {
        count: environments.length,
        list: environments,
      },
      features: {
        multipleManagers: true,
        virtualEnvironments: true,
        requirementsGeneration: true,
        packageSearch: true,
        dependencyResolution: true,
      },
    };

    return NextResponse.json({
      success: true,
      data: info,
    });

  } catch (error) {
    console.error('Error getting installation info:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get installation info',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
