import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';
import { CreateWorkspaceOptions } from '@/types/docker';
import { guacamoleService } from '@/services/guacamole';

// GET /api/workspaces - List all workspace containers
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    const workspaces = await dockerService.listWorkspaces();
    
    // Filter by user if specified
    const filteredWorkspaces = userId 
      ? workspaces.filter(workspace => workspace.userId === userId)
      : workspaces;
    
    return NextResponse.json({
      success: true,
      data: filteredWorkspaces,
      count: filteredWorkspaces.length,
    });
  } catch (error) {
    console.error('Error listing workspaces:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list workspaces',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/workspaces - Create a new workspace container
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.workspaceType || !body.userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'Workspace type and user ID are required',
        },
        { status: 400 }
      );
    }

    // Validate workspace type
    const validTypes = ['ubuntu-desktop', 'development-env', 'minimal-desktop'];
    if (!validTypes.includes(body.workspaceType)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid workspace type',
          message: `Workspace type must be one of: ${validTypes.join(', ')}`,
        },
        { status: 400 }
      );
    }

    const options: CreateWorkspaceOptions = {
      workspaceType: body.workspaceType,
      userId: body.userId,
      name: body.name,
      vncPassword: body.vncPassword,
      displayWidth: body.displayWidth,
      displayHeight: body.displayHeight,
      resources: body.resources,
      environment: body.environment,
    };

    // Create the workspace container
    const containerId = await dockerService.createWorkspace(options);
    
    // Start the container
    await dockerService.startContainer(containerId);
    
    // Get workspace info
    const workspaceInfo = await dockerService.getWorkspaceInfo(containerId);
    if (!workspaceInfo) {
      throw new Error('Failed to get workspace information after creation');
    }

    // Create Guacamole connection
    let guacamoleConnectionId: string | undefined;
    try {
      // Ensure user exists in Guacamole
      await guacamoleService.createUser(body.userId, body.userPassword || 'defaultpass');
      
      // Create connection in Guacamole
      guacamoleConnectionId = await guacamoleService.createWorkspaceConnection(
        containerId,
        workspaceInfo.name,
        'localhost', // Container hostname - adjust based on your network setup
        workspaceInfo.vncPort,
        workspaceInfo.vncPassword || '',
        body.userId
      );

      // Update container with Guacamole connection ID
      const container = dockerService.docker.getContainer(containerId);
      await container.update({
        Labels: {
          'omnispace.guacamole.connection': guacamoleConnectionId,
        },
      });
    } catch (guacamoleError) {
      console.warn('Failed to create Guacamole connection:', guacamoleError);
      // Continue without Guacamole integration
    }
    
    return NextResponse.json({
      success: true,
      data: {
        id: containerId,
        workspace: workspaceInfo,
        guacamoleConnectionId,
        message: 'Workspace created successfully',
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating workspace:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create workspace',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
