import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';
import { guacamoleService } from '@/services/guacamole';

// GET /api/workspaces/[id] - Get workspace details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const workspaceInfo = await dockerService.getWorkspaceInfo(params.id);
    
    if (!workspaceInfo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace with ID ${params.id} not found`,
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: workspaceInfo,
    });
  } catch (error) {
    console.error('Error getting workspace:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get workspace',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/workspaces/[id] - Delete workspace
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get workspace info before deletion
    const workspaceInfo = await dockerService.getWorkspaceInfo(params.id);
    
    if (!workspaceInfo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace with ID ${params.id} not found`,
        },
        { status: 404 }
      );
    }

    // Delete Guacamole connection if it exists
    if (workspaceInfo.guacamoleConnectionId) {
      try {
        await guacamoleService.deleteConnection(workspaceInfo.guacamoleConnectionId);
      } catch (guacamoleError) {
        console.warn('Failed to delete Guacamole connection:', guacamoleError);
        // Continue with container deletion
      }
    }

    // Stop and remove the container
    await dockerService.stopContainer(params.id);
    await dockerService.removeContainer(params.id);
    
    return NextResponse.json({
      success: true,
      data: {
        id: params.id,
        message: 'Workspace deleted successfully',
      },
    });
  } catch (error) {
    console.error('Error deleting workspace:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete workspace',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/workspaces/[id]/start - Start workspace
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'start':
        await dockerService.startContainer(params.id);
        return NextResponse.json({
          success: true,
          data: { id: params.id, message: 'Workspace started successfully' },
        });

      case 'stop':
        await dockerService.stopContainer(params.id);
        return NextResponse.json({
          success: true,
          data: { id: params.id, message: 'Workspace stopped successfully' },
        });

      case 'restart':
        await dockerService.restartContainer(params.id);
        return NextResponse.json({
          success: true,
          data: { id: params.id, message: 'Workspace restarted successfully' },
        });

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action',
            message: 'Action must be one of: start, stop, restart',
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error managing workspace:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to manage workspace',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
