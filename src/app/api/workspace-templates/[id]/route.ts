import { NextRequest, NextResponse } from 'next/server';
import { WorkspaceTemplate } from '../route';

// Import templates from parent route (in a real app, this would be from a database)
const WORKSPACE_TEMPLATES: WorkspaceTemplate[] = [
  {
    id: 'ubuntu-desktop',
    name: 'Ubuntu Desktop',
    description: 'Full Ubuntu 22.04 LTS desktop environment with GNOME and essential applications',
    image: 'omnispace/ubuntu-desktop:latest',
    category: 'desktop',
    defaultResources: { cpu: 2, memory: 2048, storage: 20 },
    features: ['GNOME Desktop Environment', 'Firefox Web Browser', 'LibreOffice Suite', 'File Manager (Nautilus)', 'Terminal Access', 'Basic Development Tools', 'VNC Remote Access'],
    ports: { vnc: 5901 },
    environment: { DISPLAY_WIDTH: '1920', DISPLAY_HEIGHT: '1080', VNC_DEPTH: '24' },
    icon: '🖥️',
    tags: ['desktop', 'ubuntu', 'gnome', 'office'],
    estimatedStartTime: 45,
    popularity: 85,
    lastUpdated: '2025-01-29',
  },
  {
    id: 'development-env',
    name: 'Development Environment',
    description: 'Complete development setup with VS Code, Docker, Node.js, Python, and Git',
    image: 'omnispace/development-env:latest',
    category: 'development',
    defaultResources: { cpu: 4, memory: 4096, storage: 40 },
    features: ['Visual Studio Code', 'Docker-in-Docker Support', 'Node.js (LTS) & npm/pnpm', 'Python 3 with pip', 'Git with common configurations', 'Java OpenJDK', 'PostgreSQL & Redis clients', 'Development databases', 'Terminal with dev tools'],
    ports: { vnc: 5901 },
    environment: { DISPLAY_WIDTH: '1920', DISPLAY_HEIGHT: '1080', VNC_DEPTH: '24' },
    icon: '💻',
    tags: ['development', 'vscode', 'docker', 'nodejs', 'python', 'git'],
    estimatedStartTime: 60,
    popularity: 95,
    lastUpdated: '2025-01-29',
  },
  {
    id: 'minimal-desktop',
    name: 'Minimal Desktop',
    description: 'Lightweight Alpine Linux desktop with XFCE for basic computing needs',
    image: 'omnispace/minimal-desktop:latest',
    category: 'minimal',
    defaultResources: { cpu: 1, memory: 1024, storage: 10 },
    features: ['XFCE4 Lightweight Desktop', 'Firefox Web Browser', 'File Manager (Thunar)', 'Text Editor (Mousepad)', 'Terminal Access', 'Minimal Resource Usage', 'Fast Startup Time'],
    ports: { vnc: 5901 },
    environment: { DISPLAY_WIDTH: '1280', DISPLAY_HEIGHT: '720', VNC_DEPTH: '24' },
    icon: '⚡',
    tags: ['minimal', 'alpine', 'xfce', 'lightweight'],
    estimatedStartTime: 20,
    popularity: 70,
    lastUpdated: '2025-01-29',
  },
  {
    id: 'python-dev',
    name: 'Python Development',
    description: 'Specialized Python development environment with Django, Flask, FastAPI, Streamlit, and Gradio support',
    image: 'omnispace/python-dev:latest',
    category: 'development',
    defaultResources: { cpu: 4, memory: 6144, storage: 50 },
    features: ['Python 3.11 with pip, conda, poetry', 'Django, Flask, FastAPI frameworks', 'Streamlit & Gradio for ML apps', 'VS Code with Python extensions', 'Jupyter Lab environment', 'Database clients (PostgreSQL, MySQL, Redis)', 'Docker-in-Docker support', 'Pre-configured project templates', 'AI-powered code assistance'],
    ports: { vnc: 5901, django: 8000, flask: 5000, fastapi: 8000, streamlit: 8501, gradio: 7860, jupyter: 8888 },
    environment: { DISPLAY_WIDTH: '1920', DISPLAY_HEIGHT: '1080', VNC_DEPTH: '24', START_JUPYTER: 'false', PYTHONUNBUFFERED: '1' },
    icon: '🐍',
    tags: ['python', 'django', 'flask', 'fastapi', 'streamlit', 'gradio', 'jupyter', 'ai'],
    estimatedStartTime: 75,
    popularity: 90,
    lastUpdated: '2025-01-29',
  },
];

// GET /api/workspace-templates/[id] - Get specific template details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const template = WORKSPACE_TEMPLATES.find(t => t.id === params.id);
    
    if (!template) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
          message: `Workspace template with ID ${params.id} not found`,
        },
        { status: 404 }
      );
    }
    
    // Add additional details for individual template view
    const detailedTemplate = {
      ...template,
      systemRequirements: {
        minimumCpu: Math.max(1, template.defaultResources.cpu - 1),
        minimumMemory: Math.max(512, template.defaultResources.memory - 512),
        minimumStorage: Math.max(5, template.defaultResources.storage - 5),
        recommendedCpu: template.defaultResources.cpu,
        recommendedMemory: template.defaultResources.memory,
        recommendedStorage: template.defaultResources.storage,
      },
      compatibility: {
        browsers: ['Chrome', 'Firefox', 'Safari', 'Edge'],
        platforms: ['Windows', 'macOS', 'Linux'],
        mobileSupport: true,
      },
      usage: {
        averageSessionTime: template.category === 'development' ? 180 : 60, // minutes
        concurrentUsers: template.category === 'minimal' ? 10 : 5,
        resourceEfficiency: template.category === 'minimal' ? 'high' : template.category === 'desktop' ? 'medium' : 'low',
      },
      documentation: {
        quickStart: `/docs/templates/${template.id}/quick-start`,
        userGuide: `/docs/templates/${template.id}/user-guide`,
        troubleshooting: `/docs/templates/${template.id}/troubleshooting`,
        changelog: `/docs/templates/${template.id}/changelog`,
      },
    };
    
    return NextResponse.json({
      success: true,
      data: detailedTemplate,
    });
  } catch (error) {
    console.error('Error getting workspace template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get workspace template',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
