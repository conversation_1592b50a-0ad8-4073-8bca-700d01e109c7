/**
 * Workspace API Routes
 * Handles workspace CRUD operations with authentication and validation
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { workspaceService } from '@/services/appwrite';
import { authMiddleware } from '@/lib/middleware/auth';
import { rateLimitMiddleware } from '@/lib/middleware/rate-limit';
import {
  CreateWorkspaceRequest,
  UpdateWorkspaceRequest,
  WorkspaceType,
  WorkspaceVisibility,
  FilterParams
} from '@/types/workspace';

// Validation schemas
const createWorkspaceSchema = z.object({
  name: z.string().min(1, 'Workspace name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  type: z.enum(['python', 'nodejs', 'general', 'collaborative'] as const),
  templateId: z.string().optional(),
  visibility: z.enum(['private', 'team', 'public'] as const).optional(),
  tags: z.array(z.string()).max(10, 'Too many tags').optional(),
  configuration: z.object({
    runtime: z.object({
      version: z.string().optional(),
      environment: z.record(z.string()).optional(),
      dependencies: z.array(z.string()).optional()
    }).optional(),
    resources: z.object({
      cpu: z.number().min(1).max(16).optional(),
      memory: z.number().min(512).max(32768).optional(),
      storage: z.number().min(1).max(1000).optional()
    }).optional(),
    editor: z.object({
      theme: z.string().optional(),
      fontSize: z.number().min(8).max(32).optional(),
      tabSize: z.number().min(1).max(8).optional()
    }).optional(),
    collaboration: z.object({
      enabled: z.boolean().optional(),
      maxCollaborators: z.number().min(1).max(50).optional()
    }).optional()
  }).optional()
});

const updateWorkspaceSchema = createWorkspaceSchema.partial().extend({
  workspaceId: z.string().min(1, 'Workspace ID is required')
});

const shareWorkspaceSchema = z.object({
  userIds: z.array(z.string().min(1)).min(1, 'At least one user ID required'),
  role: z.enum(['admin', 'editor', 'viewer'] as const),
  message: z.string().max(500).optional(),
  expiresAt: z.string().datetime().optional()
});

// Helper function to get session ID from request
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

// Helper function to get user info from request headers
function getUserInfo(request: NextRequest): { userId: string; userEmail: string } | null {
  const userId = request.headers.get('x-user-id');
  const userEmail = request.headers.get('x-user-email');
  
  if (!userId || !userEmail) return null;
  
  return { userId, userEmail };
}

// Helper function to format API response
function handleResponse(result: any) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });
  } else {
    const status = result.error?.code === 'AUTHENTICATION_REQUIRED' ? 401 :
                  result.error?.code === 'INSUFFICIENT_PERMISSIONS' ? 403 :
                  result.error?.code === 'WORKSPACE_NOT_FOUND' ? 404 :
                  result.error?.code === 'VALIDATION_ERROR' ? 400 : 500;
    
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Unknown error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        details: result.error?.details
      }
    }, { status });
  }
}

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per window
  message: 'Too many workspace requests'
};

// POST /api/workspace - Create a new workspace
export async function POST(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const body = await authReq.json();
        const validatedData = createWorkspaceSchema.parse(body);
        
        const userInfo = getUserInfo(authReq);
        if (!userInfo) {
          return NextResponse.json({
            success: false,
            error: { message: 'User information not found', code: 'USER_INFO_MISSING' }
          }, { status: 400 });
        }

        const sessionId = getSessionId(authReq);
        
        // Create workspace with user information
        const createParams = {
          ...validatedData,
          ownerId: userInfo.userId,
          ownerName: userInfo.userEmail.split('@')[0] // Use email prefix as name
        };

        const result = await workspaceService.createWorkspace(createParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('Workspace POST API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// GET /api/workspace - List workspaces with filtering and pagination
export async function GET(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { searchParams } = new URL(authReq.url);
        const sessionId = getSessionId(authReq);
        const userInfo = getUserInfo(authReq);

        // Parse query parameters
        const page = parseInt(searchParams.get('page') || '1');
        const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
        const search = searchParams.get('search') || undefined;
        const type = searchParams.get('type') as WorkspaceType | undefined;
        const visibility = searchParams.get('visibility') as WorkspaceVisibility | undefined;
        const tags = searchParams.get('tags')?.split(',').filter(Boolean) || undefined;
        const ownerId = searchParams.get('ownerId') || undefined;
        const sortBy = searchParams.get('sortBy') || '$updatedAt';
        const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';

        // Build filter parameters
        const filters: FilterParams = {};
        if (type) filters.type = [type];
        if (visibility) filters.visibility = [visibility];
        if (tags) filters.tags = tags;
        if (search) filters.search = search;
        if (ownerId) filters.ownerId = ownerId;

        // If no specific owner filter and user wants their own workspaces
        const showMyWorkspaces = searchParams.get('my') === 'true';
        if (showMyWorkspaces && userInfo && !ownerId) {
          filters.ownerId = userInfo.userId;
        }

        const queryParams = {
          filters,
          pagination: {
            limit,
            offset: (page - 1) * limit,
            orderBy: sortBy,
            orderDirection: sortOrder
          }
        };

        const result = await workspaceService.listWorkspaces(queryParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('Workspace GET API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// PUT /api/workspace - Update workspace
export async function PUT(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const body = await authReq.json();
        const validatedData = updateWorkspaceSchema.parse(body);
        const sessionId = getSessionId(authReq);

        const { workspaceId, ...updateData } = validatedData;
        const updateParams = {
          workspaceId,
          ...updateData
        };

        const result = await workspaceService.updateWorkspace(updateParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('Workspace PUT API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// DELETE /api/workspace - Delete workspace
export async function DELETE(request: NextRequest) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { searchParams } = new URL(authReq.url);
        const workspaceId = searchParams.get('id');

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        const sessionId = getSessionId(authReq);
        const result = await workspaceService.deleteWorkspace(workspaceId, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('Workspace DELETE API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}
