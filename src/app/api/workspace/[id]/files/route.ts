/**
 * Workspace Files API Routes
 * Handles file operations within workspaces
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { workspaceFileService } from '@/services/appwrite';
import { authMiddleware } from '@/lib/middleware/auth';
import { rateLimitMiddleware } from '@/lib/middleware/rate-limit';

// Validation schemas
const createFileSchema = z.object({
  name: z.string().min(1, 'File name is required').max(255, 'Name too long'),
  path: z.string().min(1, 'File path is required'),
  type: z.enum(['file', 'directory'] as const),
  content: z.string().optional(),
  mimeType: z.string().optional(),
  encoding: z.string().optional(),
  permissions: z.array(z.object({
    userId: z.string(),
    permissions: z.array(z.enum(['read', 'write', 'execute', 'delete'] as const))
  })).optional()
});

const updateFileSchema = z.object({
  fileId: z.string().min(1, 'File ID is required'),
  content: z.string().optional(),
  name: z.string().min(1).max(255).optional(),
  path: z.string().min(1).optional(),
  versionMessage: z.string().max(500).optional(),
  permissions: z.array(z.object({
    userId: z.string(),
    permissions: z.array(z.enum(['read', 'write', 'execute', 'delete'] as const))
  })).optional()
});

const fileLockSchema = z.object({
  fileId: z.string().min(1, 'File ID is required')
});

// Helper functions
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

function getUserInfo(request: NextRequest): { userId: string; userEmail: string } | null {
  const userId = request.headers.get('x-user-id');
  const userEmail = request.headers.get('x-user-email');
  
  if (!userId || !userEmail) return null;
  
  return { userId, userEmail };
}

function handleResponse(result: any) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });
  } else {
    const status = result.error?.code === 'AUTHENTICATION_REQUIRED' ? 401 :
                  result.error?.code === 'INSUFFICIENT_PERMISSIONS' ? 403 :
                  result.error?.code === 'FILE_NOT_FOUND' ? 404 :
                  result.error?.code === 'WORKSPACE_NOT_FOUND' ? 404 :
                  result.error?.code === 'VALIDATION_ERROR' ? 400 : 500;
    
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Unknown error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        details: result.error?.details
      }
    }, { status });
  }
}

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 500, // Higher limit for file operations
  message: 'Too many file operation requests'
};

// POST /api/workspace/[id]/files - Create a new file or directory
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const body = await authReq.json();
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        const validatedData = createFileSchema.parse(body);
        
        const createParams = {
          workspaceId,
          ...validatedData
        };

        const result = await workspaceFileService.createFile(createParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('File POST API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// GET /api/workspace/[id]/files - List files in workspace
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const { searchParams } = new URL(authReq.url);
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        // Parse query parameters
        const page = parseInt(searchParams.get('page') || '1');
        const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 200);
        const path = searchParams.get('path') || undefined;
        const type = searchParams.get('type') as 'file' | 'directory' | undefined;
        const search = searchParams.get('search') || undefined;

        const queryParams = {
          workspaceId,
          path,
          type,
          search,
          pagination: {
            limit,
            offset: (page - 1) * limit
          }
        };

        const result = await workspaceFileService.listFiles(queryParams, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('File GET API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// PUT /api/workspace/[id]/files - Update file content or metadata
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const body = await authReq.json();
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        const validatedData = updateFileSchema.parse(body);
        const result = await workspaceFileService.updateFile(validatedData, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('File PUT API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// DELETE /api/workspace/[id]/files - Delete file
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const { searchParams } = new URL(authReq.url);
        const fileId = searchParams.get('fileId');
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        if (!fileId) {
          return NextResponse.json({
            success: false,
            error: { message: 'File ID is required', code: 'MISSING_FILE_ID' }
          }, { status: 400 });
        }

        const result = await workspaceFileService.deleteFile(fileId, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('File DELETE API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// PATCH /api/workspace/[id]/files - File operations (lock, unlock, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const body = await authReq.json();
        const { searchParams } = new URL(authReq.url);
        const action = searchParams.get('action');
        const sessionId = getSessionId(authReq);
        const userInfo = getUserInfo(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        if (!userInfo) {
          return NextResponse.json({
            success: false,
            error: { message: 'User information not found', code: 'USER_INFO_MISSING' }
          }, { status: 400 });
        }

        switch (action) {
          case 'lock': {
            const validatedData = fileLockSchema.parse(body);
            const result = await workspaceFileService.lockFile(
              validatedData.fileId,
              userInfo.userId,
              sessionId || undefined
            );
            return handleResponse(result);
          }

          case 'unlock': {
            const validatedData = fileLockSchema.parse(body);
            const result = await workspaceFileService.unlockFile(
              validatedData.fileId,
              sessionId || undefined
            );
            return handleResponse(result);
          }

          default:
            return NextResponse.json({
              success: false,
              error: { message: 'Invalid action', code: 'INVALID_ACTION' }
            }, { status: 400 });
        }

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('File PATCH API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}
