/**
 * Workspace Detail API Routes
 * Handles individual workspace operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { workspaceService } from '@/services/appwrite';
import { authMiddleware } from '@/lib/middleware/auth';
import { rateLimitMiddleware } from '@/lib/middleware/rate-limit';
import { WorkspaceStatus, ShareWorkspaceRequest } from '@/types/workspace';

// Validation schemas
const shareWorkspaceSchema = z.object({
  userIds: z.array(z.string().min(1)).min(1, 'At least one user ID required'),
  role: z.enum(['admin', 'editor', 'viewer'] as const),
  message: z.string().max(500).optional(),
  expiresAt: z.string().datetime().optional()
});

const updateStatusSchema = z.object({
  status: z.enum(['creating', 'active', 'stopped', 'error', 'archived'] as const)
});

// Helper functions
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

function getUserInfo(request: NextRequest): { userId: string; userEmail: string } | null {
  const userId = request.headers.get('x-user-id');
  const userEmail = request.headers.get('x-user-email');
  
  if (!userId || !userEmail) return null;
  
  return { userId, userEmail };
}

function handleResponse(result: any) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });
  } else {
    const status = result.error?.code === 'AUTHENTICATION_REQUIRED' ? 401 :
                  result.error?.code === 'INSUFFICIENT_PERMISSIONS' ? 403 :
                  result.error?.code === 'WORKSPACE_NOT_FOUND' ? 404 :
                  result.error?.code === 'VALIDATION_ERROR' ? 400 : 500;
    
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Unknown error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        details: result.error?.details
      }
    }, { status });
  }
}

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 200, // Higher limit for individual workspace operations
  message: 'Too many workspace detail requests'
};

// GET /api/workspace/[id] - Get workspace by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        const result = await workspaceService.getWorkspace(workspaceId, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('Workspace GET by ID API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// PUT /api/workspace/[id] - Update workspace by ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const body = await authReq.json();
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        // Add workspaceId to the body for validation
        const updateData = { ...body, workspaceId };
        
        // Use the same validation schema from the main route
        const updateWorkspaceSchema = z.object({
          workspaceId: z.string().min(1),
          name: z.string().min(1).max(100).optional(),
          description: z.string().max(500).optional(),
          visibility: z.enum(['private', 'team', 'public'] as const).optional(),
          tags: z.array(z.string()).max(10).optional(),
          configuration: z.object({
            runtime: z.object({
              version: z.string().optional(),
              environment: z.record(z.string()).optional(),
              dependencies: z.array(z.string()).optional()
            }).optional(),
            resources: z.object({
              cpu: z.number().min(1).max(16).optional(),
              memory: z.number().min(512).max(32768).optional(),
              storage: z.number().min(1).max(1000).optional()
            }).optional(),
            editor: z.object({
              theme: z.string().optional(),
              fontSize: z.number().min(8).max(32).optional(),
              tabSize: z.number().min(1).max(8).optional()
            }).optional(),
            collaboration: z.object({
              enabled: z.boolean().optional(),
              maxCollaborators: z.number().min(1).max(50).optional()
            }).optional()
          }).optional()
        });

        const validatedData = updateWorkspaceSchema.parse(updateData);
        const result = await workspaceService.updateWorkspace(validatedData, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('Workspace PUT by ID API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// DELETE /api/workspace/[id] - Delete workspace by ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const sessionId = getSessionId(authReq);

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        const result = await workspaceService.deleteWorkspace(workspaceId, sessionId || undefined);
        return handleResponse(result);

      } catch (error) {
        console.error('Workspace DELETE by ID API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}

// PATCH /api/workspace/[id] - Partial updates (status, sharing, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return rateLimitMiddleware(rateLimitConfig)(request, async (req) => {
    return authMiddleware(req, async (authReq) => {
      try {
        const { id: workspaceId } = params;
        const body = await authReq.json();
        const sessionId = getSessionId(authReq);
        const { searchParams } = new URL(authReq.url);
        const action = searchParams.get('action');

        if (!workspaceId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Workspace ID is required', code: 'MISSING_WORKSPACE_ID' }
          }, { status: 400 });
        }

        switch (action) {
          case 'status': {
            const validatedData = updateStatusSchema.parse(body);
            const result = await workspaceService.updateWorkspaceStatus(
              workspaceId,
              validatedData.status,
              sessionId || undefined
            );
            return handleResponse(result);
          }

          case 'share': {
            const validatedData = shareWorkspaceSchema.parse(body);
            const shareParams: ShareWorkspaceRequest = {
              userIds: validatedData.userIds,
              role: validatedData.role,
              message: validatedData.message,
              expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined
            };

            const result = await workspaceService.shareWorkspace(
              workspaceId,
              shareParams,
              sessionId || undefined
            );
            return handleResponse(result);
          }

          case 'remove-collaborator': {
            const { userId } = body;
            if (!userId) {
              return NextResponse.json({
                success: false,
                error: { message: 'User ID is required', code: 'MISSING_USER_ID' }
              }, { status: 400 });
            }

            const result = await workspaceService.removeCollaborator(
              workspaceId,
              userId,
              sessionId || undefined
            );
            return handleResponse(result);
          }

          default:
            return NextResponse.json({
              success: false,
              error: { message: 'Invalid action', code: 'INVALID_ACTION' }
            }, { status: 400 });
        }

      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            success: false,
            error: {
              message: 'Validation error',
              code: 'VALIDATION_ERROR',
              details: error.issues
            }
          }, { status: 400 });
        }

        console.error('Workspace PATCH by ID API error:', error);
        return NextResponse.json({
          success: false,
          error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
        }, { status: 500 });
      }
    });
  });
}
