import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// GET /api/containers/[id]/logs - Get container logs
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const tail = parseInt(searchParams.get('tail') || '100');
    
    const logs = await dockerService.getContainerLogs(params.id, tail);
    
    return NextResponse.json({
      success: true,
      data: {
        logs,
        containerId: params.id,
        tail,
      },
    });
  } catch (error) {
    console.error(`Error getting logs for container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get container logs',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
