import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// POST /api/containers/[id]/start - Start container
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dockerService.startContainer(params.id);
    
    return NextResponse.json({
      success: true,
      message: 'Container started successfully',
    });
  } catch (error) {
    console.error(`Error starting container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to start container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
