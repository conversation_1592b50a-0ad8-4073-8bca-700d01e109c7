import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// POST /api/containers/[id]/execute - Execute shell command in container with better interface
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    if (!body.command || typeof body.command !== 'string') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid command',
          message: 'Command must be provided as a string',
        },
        { status: 400 }
      );
    }
    
    const result = await dockerService.executeCommand(
      params.id, 
      body.command, 
      body.workingDir
    );
    
    return NextResponse.json({
      success: true,
      data: {
        containerId: params.id,
        command: body.command,
        workingDir: body.workingDir,
        exitCode: result.exitCode,
        stdout: result.stdout,
        stderr: result.stderr,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error(`<PERSON>rror executing command in container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to execute command',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
