import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// GET /api/containers/[id] - Get container details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const container = await dockerService.getContainer(params.id);
    
    if (!container) {
      return NextResponse.json(
        {
          success: false,
          error: 'Container not found',
          message: `Container with ID ${params.id} does not exist`,
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: container,
    });
  } catch (error) {
    console.error(`Error getting container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get container details',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/containers/[id] - Remove container
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const force = searchParams.get('force') === 'true';
    
    await dockerService.removeContainer(params.id, force);
    
    return NextResponse.json({
      success: true,
      message: 'Container removed successfully',
    });
  } catch (error) {
    console.error(`Error removing container ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to remove container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
