import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';
import { CreateContainerOptions } from '@/types/docker';

// GET /api/containers - List all containers
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const all = searchParams.get('all') !== 'false'; // Default to true
    
    const containers = await dockerService.listContainers(all);
    
    return NextResponse.json({
      success: true,
      data: containers,
      count: containers.length,
    });
  } catch (error) {
    console.error('Error listing containers:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list containers',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/containers - Create a new container
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.image) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'Container name and image are required',
        },
        { status: 400 }
      );
    }

    const options: CreateContainerOptions = {
      name: body.name,
      image: body.image,
      cpu: body.cpu,
      memory: body.memory,
      ports: body.ports,
      environment: body.environment,
      volumes: body.volumes,
      networkMode: body.networkMode,
      cmd: body.cmd,
      workingDir: body.workingDir,
      user: body.user,
      privileged: body.privileged,
      autoRemove: body.autoRemove,
    };

    const containerId = await dockerService.createContainer(options);
    
    return NextResponse.json({
      success: true,
      data: {
        id: containerId,
        message: 'Container created successfully',
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating container:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create container',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
