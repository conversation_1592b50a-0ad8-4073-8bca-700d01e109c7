import { NextRequest, NextResponse } from 'next/server';
import { pythonTestingService } from '@/services/testing/python-testing-service';

// POST /api/testing/run - Run tests
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      workspaceId, 
      suiteId, 
      testIds, 
      options = {} 
    } = body;

    // Validate required fields
    if (!workspaceId || !suiteId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId and suiteId are required',
        },
        { status: 400 }
      );
    }

    // Validate test suite exists
    const testSuite = pythonTestingService.getTestSuite(suiteId);
    if (!testSuite) {
      return NextResponse.json(
        {
          success: false,
          error: 'Test suite not found',
          message: `Test suite '${suiteId}' not found`,
        },
        { status: 404 }
      );
    }

    // Validate test IDs if provided
    if (testIds && Array.isArray(testIds)) {
      const invalidTestIds = testIds.filter(id => 
        !testSuite.tests.some(test => test.id === id)
      );
      
      if (invalidTestIds.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid test IDs',
            message: `Test IDs not found: ${invalidTestIds.join(', ')}`,
          },
          { status: 400 }
        );
      }
    }

    // Run tests
    const testRun = await pythonTestingService.runTests(workspaceId, suiteId, {
      testIds,
      coverage: options.coverage !== false,
      parallel: options.parallel === true,
      verbose: options.verbose === true,
    });

    // Prepare response data
    const responseData = {
      testRun: {
        ...testRun,
        results: testRun.results.map(result => ({
          ...result,
          test: testSuite.tests.find(t => t.id === result.testId),
        })),
        performance: {
          averageTestDuration: testRun.results.length > 0 
            ? testRun.results.reduce((sum, r) => sum + r.duration, 0) / testRun.results.length
            : 0,
          slowestTests: testRun.results
            .sort((a, b) => b.duration - a.duration)
            .slice(0, 5)
            .map(result => ({
              ...result,
              test: testSuite.tests.find(t => t.id === result.testId),
            })),
        },
        insights: generateTestInsights(testRun, testSuite),
      },
    };

    return NextResponse.json({
      success: true,
      data: responseData,
      message: `Test run completed: ${testRun.summary.passed}/${testRun.summary.total} tests passed`,
    });

  } catch (error) {
    console.error('Error running tests:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to run tests',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/testing/run - Get test run status and history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const runId = searchParams.get('runId');
    const suiteId = searchParams.get('suiteId');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (runId) {
      // Get specific test run
      const testRun = pythonTestingService.getTestRun(runId);
      if (!testRun) {
        return NextResponse.json(
          {
            success: false,
            error: 'Test run not found',
            message: `Test run '${runId}' not found`,
          },
          { status: 404 }
        );
      }

      const testSuite = pythonTestingService.getTestSuite(testRun.suiteId);
      
      return NextResponse.json({
        success: true,
        data: {
          testRun: {
            ...testRun,
            testSuite: testSuite ? {
              id: testSuite.id,
              name: testSuite.name,
              framework: testSuite.framework,
              pythonFramework: testSuite.pythonFramework,
            } : null,
          },
        },
      });

    } else if (suiteId) {
      // Get test runs for specific suite
      const testSuite = pythonTestingService.getTestSuite(suiteId);
      if (!testSuite) {
        return NextResponse.json(
          {
            success: false,
            error: 'Test suite not found',
            message: `Test suite '${suiteId}' not found`,
          },
          { status: 404 }
        );
      }

      // In a real implementation, you'd query the database for test runs
      // For now, we'll return the last run if it exists
      const testRuns = testSuite.lastRun ? [testSuite.lastRun] : [];

      return NextResponse.json({
        success: true,
        data: {
          testRuns: testRuns.slice(0, limit),
          testSuite: {
            id: testSuite.id,
            name: testSuite.name,
            framework: testSuite.framework,
            pythonFramework: testSuite.pythonFramework,
            testsCount: testSuite.tests.length,
          },
        },
      });

    } else {
      // Get all test suites with their latest runs
      const testSuites = pythonTestingService.getTestSuites();
      
      const suitesWithRuns = testSuites.map(suite => ({
        id: suite.id,
        name: suite.name,
        framework: suite.framework,
        pythonFramework: suite.pythonFramework,
        testsCount: suite.tests.length,
        lastRun: suite.lastRun ? {
          id: suite.lastRun.id,
          status: suite.lastRun.status,
          startTime: suite.lastRun.startTime,
          duration: suite.lastRun.duration,
          summary: suite.lastRun.summary,
        } : null,
        testsByStatus: {
          pending: suite.tests.filter(t => t.status === 'pending').length,
          passed: suite.tests.filter(t => t.status === 'passed').length,
          failed: suite.tests.filter(t => t.status === 'failed').length,
          skipped: suite.tests.filter(t => t.status === 'skipped').length,
          error: suite.tests.filter(t => t.status === 'error').length,
        },
      }));

      return NextResponse.json({
        success: true,
        data: {
          testSuites: suitesWithRuns,
          summary: {
            totalSuites: testSuites.length,
            totalTests: testSuites.reduce((sum, suite) => sum + suite.tests.length, 0),
            suitesWithRuns: testSuites.filter(suite => suite.lastRun).length,
          },
        },
      });
    }

  } catch (error) {
    console.error('Error getting test runs:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get test runs',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/testing/run - Cancel running test
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const runId = searchParams.get('runId');

    if (!runId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'runId is required',
        },
        { status: 400 }
      );
    }

    const testRun = pythonTestingService.getTestRun(runId);
    if (!testRun) {
      return NextResponse.json(
        {
          success: false,
          error: 'Test run not found',
          message: `Test run '${runId}' not found`,
        },
        { status: 404 }
      );
    }

    if (testRun.status !== 'running') {
      return NextResponse.json(
        {
          success: false,
          error: 'Test run not running',
          message: `Test run '${runId}' is not currently running`,
        },
        { status: 400 }
      );
    }

    // Cancel the test run (simplified - in reality, you'd need to kill the process)
    testRun.status = 'cancelled';
    testRun.endTime = new Date();
    testRun.duration = testRun.endTime.getTime() - testRun.startTime.getTime();

    return NextResponse.json({
      success: true,
      message: `Test run '${runId}' cancelled successfully`,
      data: {
        testRun: {
          id: testRun.id,
          status: testRun.status,
          duration: testRun.duration,
        },
      },
    });

  } catch (error) {
    console.error('Error cancelling test run:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to cancel test run',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper function to generate test insights
function generateTestInsights(testRun: any, testSuite: any): string[] {
  const insights: string[] = [];
  const { summary, results } = testRun;

  // Success rate insights
  const successRate = (summary.passed / summary.total) * 100;
  if (successRate === 100) {
    insights.push('🎉 All tests passed! Great job!');
  } else if (successRate >= 90) {
    insights.push('✅ Excellent test coverage with high success rate');
  } else if (successRate >= 70) {
    insights.push('⚠️ Good progress, but some tests need attention');
  } else {
    insights.push('🔴 Many tests are failing - consider reviewing your code');
  }

  // Performance insights
  const avgDuration = results.length > 0 
    ? results.reduce((sum: number, r: any) => sum + r.duration, 0) / results.length
    : 0;
  
  if (avgDuration > 5000) { // 5 seconds
    insights.push('🐌 Tests are running slowly - consider optimizing or mocking external dependencies');
  } else if (avgDuration < 100) { // 100ms
    insights.push('⚡ Tests are running very fast - excellent performance!');
  }

  // Coverage insights
  if (testRun.coverage) {
    const coveragePercentage = testRun.coverage.overall.percentage;
    if (coveragePercentage >= 90) {
      insights.push('📊 Excellent code coverage!');
    } else if (coveragePercentage >= 70) {
      insights.push('📈 Good code coverage, consider adding more tests for critical paths');
    } else {
      insights.push('📉 Low code coverage - add more tests to improve reliability');
    }
  }

  // Framework-specific insights
  switch (testSuite.pythonFramework) {
    case 'django':
      if (summary.failed > 0) {
        insights.push('🔧 For Django tests, check database fixtures and migrations');
      }
      break;
    case 'flask':
      if (summary.failed > 0) {
        insights.push('🔧 For Flask tests, verify application context and configuration');
      }
      break;
    case 'fastapi':
      if (summary.failed > 0) {
        insights.push('🔧 For FastAPI tests, check async/await usage and dependency overrides');
      }
      break;
  }

  // Test organization insights
  const fileCount = new Set(testSuite.tests.map((t: any) => t.filePath)).size;
  if (fileCount === 1 && testSuite.tests.length > 20) {
    insights.push('📁 Consider splitting tests into multiple files for better organization');
  }

  return insights;
}
