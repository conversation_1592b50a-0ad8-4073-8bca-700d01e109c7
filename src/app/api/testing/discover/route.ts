import { NextRequest, NextResponse } from 'next/server';
import { pythonTestingService } from '@/services/testing/python-testing-service';
import { PythonFramework } from '@/types/python-workspace';

// POST /api/testing/discover - Discover tests in workspace
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workspaceId, framework, pythonFramework, paths } = body;

    // Validate required fields
    if (!workspaceId || !framework || !pythonFramework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId, framework, and pythonFramework are required',
        },
        { status: 400 }
      );
    }

    // Validate frameworks
    const supportedTestFrameworks = ['pytest', 'unittest', 'nose2', 'django-test'];
    const supportedPythonFrameworks: PythonFramework[] = ['django', 'flask', 'fastapi', 'streamlit', 'gradio'];

    if (!supportedTestFrameworks.includes(framework)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid test framework',
          message: `Test framework must be one of: ${supportedTestFrameworks.join(', ')}`,
        },
        { status: 400 }
      );
    }

    if (!supportedPythonFrameworks.includes(pythonFramework)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Python framework',
          message: `Python framework must be one of: ${supportedPythonFrameworks.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Discover tests
    const testSuite = await pythonTestingService.discoverTests(
      workspaceId,
      framework,
      pythonFramework,
      paths
    );

    // Prepare response data
    const responseData = {
      testSuite: {
        ...testSuite,
        testsCount: testSuite.tests.length,
        testsByStatus: {
          pending: testSuite.tests.filter(t => t.status === 'pending').length,
          passed: testSuite.tests.filter(t => t.status === 'passed').length,
          failed: testSuite.tests.filter(t => t.status === 'failed').length,
          skipped: testSuite.tests.filter(t => t.status === 'skipped').length,
          error: testSuite.tests.filter(t => t.status === 'error').length,
        },
        testsByFile: testSuite.tests.reduce((acc, test) => {
          if (!acc[test.filePath]) {
            acc[test.filePath] = [];
          }
          acc[test.filePath].push(test);
          return acc;
        }, {} as Record<string, any[]>),
      },
      recommendations: getTestingRecommendations(pythonFramework, framework),
    };

    return NextResponse.json({
      success: true,
      data: responseData,
      message: `Discovered ${testSuite.tests.length} tests using ${framework}`,
    });

  } catch (error) {
    console.error('Error discovering tests:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to discover tests',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/testing/discover - Get test discovery configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const pythonFramework = searchParams.get('pythonFramework') as PythonFramework;

    const config = {
      supportedTestFrameworks: [
        {
          name: 'pytest',
          displayName: 'pytest',
          description: 'Modern, feature-rich testing framework',
          features: ['fixtures', 'parametrization', 'plugins', 'parallel execution'],
          recommended: true,
        },
        {
          name: 'unittest',
          displayName: 'unittest',
          description: 'Built-in Python testing framework',
          features: ['test discovery', 'test fixtures', 'assertions'],
          recommended: false,
        },
        {
          name: 'nose2',
          displayName: 'nose2',
          description: 'Successor to nose testing framework',
          features: ['test discovery', 'plugins', 'configuration'],
          recommended: false,
        },
        {
          name: 'django-test',
          displayName: 'Django Test',
          description: 'Django\'s built-in testing framework',
          features: ['database fixtures', 'client testing', 'assertions'],
          recommended: pythonFramework === 'django',
        },
      ],
      defaultPaths: getDefaultTestPaths(pythonFramework),
      testPatterns: getTestPatterns(pythonFramework),
      bestPractices: getTestingBestPractices(pythonFramework),
      setupInstructions: getSetupInstructions(pythonFramework),
    };

    return NextResponse.json({
      success: true,
      data: config,
    });

  } catch (error) {
    console.error('Error getting test discovery config:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getTestingRecommendations(pythonFramework: PythonFramework, testFramework: string): string[] {
  const recommendations: string[] = [];

  switch (pythonFramework) {
    case 'django':
      recommendations.push(
        'Use Django\'s TestCase for database-related tests',
        'Use SimpleTestCase for tests that don\'t need database access',
        'Consider using factory_boy for test data generation',
        'Use Django\'s Client for testing views',
        'Test your models, views, forms, and URLs separately'
      );
      break;
    case 'flask':
      recommendations.push(
        'Use Flask\'s test client for testing routes',
        'Test your application factory if using one',
        'Use pytest fixtures for database setup',
        'Test authentication and authorization separately',
        'Mock external services and APIs'
      );
      break;
    case 'fastapi':
      recommendations.push(
        'Use FastAPI\'s TestClient for testing endpoints',
        'Test your Pydantic models separately',
        'Use dependency overrides for testing',
        'Test authentication and authorization',
        'Use async test functions for async endpoints'
      );
      break;
    case 'streamlit':
      recommendations.push(
        'Test your data processing functions separately',
        'Use pytest for unit tests',
        'Test your Streamlit components with st.testing',
        'Mock data sources for consistent testing',
        'Test user interactions and state management'
      );
      break;
    case 'gradio':
      recommendations.push(
        'Test your interface functions separately',
        'Use pytest for unit tests',
        'Test input validation and error handling',
        'Mock ML models for faster testing',
        'Test different input types and edge cases'
      );
      break;
  }

  if (testFramework === 'pytest') {
    recommendations.push(
      'Use pytest fixtures for setup and teardown',
      'Parametrize tests for multiple input scenarios',
      'Use pytest markers to organize tests',
      'Consider using pytest-cov for coverage reporting'
    );
  }

  return recommendations;
}

function getDefaultTestPaths(pythonFramework?: PythonFramework): string[] {
  switch (pythonFramework) {
    case 'django':
      return ['tests/', '*/tests.py', '*/test_*.py'];
    case 'flask':
    case 'fastapi':
      return ['tests/', 'test_*.py', '*_test.py'];
    default:
      return ['tests/', 'test_*.py', '*_test.py'];
  }
}

function getTestPatterns(pythonFramework?: PythonFramework): string[] {
  switch (pythonFramework) {
    case 'django':
      return ['test_*.py', '*_test.py', 'tests.py'];
    default:
      return ['test_*.py', '*_test.py'];
  }
}

function getTestingBestPractices(pythonFramework?: PythonFramework): string[] {
  const general = [
    'Write tests before or alongside your code (TDD/BDD)',
    'Keep tests simple and focused on one thing',
    'Use descriptive test names that explain what is being tested',
    'Arrange-Act-Assert pattern for test structure',
    'Mock external dependencies and services',
    'Use fixtures for common test setup',
    'Aim for high test coverage but focus on critical paths',
    'Run tests frequently during development',
  ];

  const frameworkSpecific: Record<PythonFramework, string[]> = {
    django: [
      'Use Django\'s transaction rollback for test isolation',
      'Test your models\' methods and properties',
      'Test form validation and cleaning',
      'Use Django\'s assertContains for template testing',
    ],
    flask: [
      'Use application context in tests',
      'Test your blueprints separately',
      'Use Flask-Testing for additional test utilities',
      'Test error handlers and custom exceptions',
    ],
    fastapi: [
      'Test both sync and async endpoints appropriately',
      'Use dependency overrides for testing',
      'Test request/response models with Pydantic',
      'Test middleware and exception handlers',
    ],
    streamlit: [
      'Test data processing logic separately from UI',
      'Use session state carefully in tests',
      'Test caching behavior',
      'Mock file uploads and downloads',
    ],
    gradio: [
      'Test interface functions independently',
      'Test different input combinations',
      'Mock heavy computations',
      'Test error handling for invalid inputs',
    ],
  };

  return pythonFramework ? [...general, ...frameworkSpecific[pythonFramework]] : general;
}

function getSetupInstructions(pythonFramework?: PythonFramework): Record<string, string[]> {
  const instructions: Record<string, string[]> = {
    pytest: [
      'Install pytest: pip install pytest',
      'Install pytest-cov for coverage: pip install pytest-cov',
      'Create pytest.ini or pyproject.toml for configuration',
      'Run tests with: pytest',
      'Generate coverage report: pytest --cov=your_package',
    ],
    unittest: [
      'unittest is built into Python, no installation needed',
      'Create test files with test_ prefix',
      'Run tests with: python -m unittest discover',
      'Use python -m unittest -v for verbose output',
    ],
    'django-test': [
      'Django testing is built-in, no additional installation needed',
      'Create tests in tests.py or tests/ directory',
      'Run tests with: python manage.py test',
      'Use --keepdb to speed up test runs',
      'Use --parallel for parallel test execution',
    ],
  };

  if (pythonFramework === 'flask') {
    instructions.flask = [
      'Install Flask-Testing: pip install Flask-Testing',
      'Create test configuration',
      'Use app.test_client() for testing routes',
      'Set up test database if needed',
    ];
  }

  if (pythonFramework === 'fastapi') {
    instructions.fastapi = [
      'Install httpx for async testing: pip install httpx',
      'Use TestClient from fastapi.testclient',
      'Create test database configuration',
      'Use dependency overrides for testing',
    ];
  }

  return instructions;
}
