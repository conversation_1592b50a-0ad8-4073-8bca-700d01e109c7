import { NextRequest, NextResponse } from 'next/server';
import { fileManagerService } from '@/services/file-management/file-manager';

// GET /api/files - List directory contents or get file info
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const path = searchParams.get('path') || '/home/<USER>';
    const action = searchParams.get('action') || 'list';
    const showHidden = searchParams.get('showHidden') === 'true';
    const recursive = searchParams.get('recursive') === 'true';
    const maxDepth = parseInt(searchParams.get('maxDepth') || '1');

    if (!workspaceId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'workspaceId is required',
        },
        { status: 400 }
      );
    }

    if (action === 'list') {
      // List directory contents
      const files = await fileManagerService.listDirectory(workspaceId, path, {
        showHidden,
        recursive,
        maxDepth,
      });

      // Group files by type
      const directories = files.filter(f => f.type === 'directory');
      const regularFiles = files.filter(f => f.type === 'file');

      // Calculate directory statistics
      const stats = {
        totalItems: files.length,
        directories: directories.length,
        files: regularFiles.length,
        totalSize: regularFiles.reduce((sum, file) => sum + file.size, 0),
        hiddenItems: files.filter(f => f.isHidden).length,
      };

      return NextResponse.json({
        success: true,
        data: {
          path,
          files: files.sort((a, b) => {
            // Sort directories first, then by name
            if (a.type !== b.type) {
              return a.type === 'directory' ? -1 : 1;
            }
            return a.name.localeCompare(b.name);
          }),
          directories,
          regularFiles,
          stats,
        },
      });

    } else if (action === 'info') {
      // Get file/directory info
      const itemInfo = await fileManagerService.getItemInfo(workspaceId, path);
      
      return NextResponse.json({
        success: true,
        data: itemInfo,
      });

    } else if (action === 'read') {
      // Read file content
      const fileContent = await fileManagerService.readFile(workspaceId, path);
      
      return NextResponse.json({
        success: true,
        data: {
          path,
          ...fileContent,
        },
      });

    } else if (action === 'tree') {
      // Get directory tree
      const tree = await fileManagerService.getDirectoryTree(workspaceId, path, maxDepth);
      
      return NextResponse.json({
        success: true,
        data: tree,
      });

    } else if (action === 'search') {
      // Search files
      const query = searchParams.get('query');
      if (!query) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing search query',
            message: 'query parameter is required for search action',
          },
          { status: 400 }
        );
      }

      const fileTypes = searchParams.get('fileTypes')?.split(',') || [];
      const caseSensitive = searchParams.get('caseSensitive') === 'true';
      const regex = searchParams.get('regex') === 'true';
      const maxResults = parseInt(searchParams.get('maxResults') || '100');

      const searchResults = await fileManagerService.searchFiles(workspaceId, query, {
        path,
        fileTypes,
        caseSensitive,
        regex,
        maxResults,
      });

      return NextResponse.json({
        success: true,
        data: {
          query,
          results: searchResults,
          totalMatches: searchResults.reduce((sum, result) => sum + result.matches.length, 0),
          filesWithMatches: searchResults.length,
        },
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: list, info, read, tree, search',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error handling file request:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'File operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/files - Create file/directory or write file content
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workspaceId, action, path, content, type = 'file', options = {} } = body;

    if (!workspaceId || !action || !path) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId, action, and path are required',
        },
        { status: 400 }
      );
    }

    if (action === 'create') {
      if (type === 'directory') {
        // Create directory
        await fileManagerService.createDirectory(workspaceId, path, options);
        
        return NextResponse.json({
          success: true,
          message: `Directory '${path}' created successfully`,
        });

      } else {
        // Create file
        await fileManagerService.writeFile(workspaceId, path, content || '', options);
        
        return NextResponse.json({
          success: true,
          message: `File '${path}' created successfully`,
        });
      }

    } else if (action === 'write') {
      // Write file content
      if (content === undefined) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing content',
            message: 'content is required for write action',
          },
          { status: 400 }
        );
      }

      await fileManagerService.writeFile(workspaceId, path, content, options);
      
      return NextResponse.json({
        success: true,
        message: `File '${path}' updated successfully`,
      });

    } else if (action === 'copy') {
      // Copy file/directory
      const { destination } = body;
      if (!destination) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing destination',
            message: 'destination is required for copy action',
          },
          { status: 400 }
        );
      }

      await fileManagerService.copyItem(workspaceId, path, destination, options);
      
      return NextResponse.json({
        success: true,
        message: `'${path}' copied to '${destination}' successfully`,
      });

    } else if (action === 'move') {
      // Move/rename file/directory
      const { destination } = body;
      if (!destination) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing destination',
            message: 'destination is required for move action',
          },
          { status: 400 }
        );
      }

      await fileManagerService.moveItem(workspaceId, path, destination);
      
      return NextResponse.json({
        success: true,
        message: `'${path}' moved to '${destination}' successfully`,
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: create, write, copy, move',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error handling file operation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'File operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/files - Delete file or directory
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const path = searchParams.get('path');
    const recursive = searchParams.get('recursive') === 'true';
    const force = searchParams.get('force') === 'true';

    if (!workspaceId || !path) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'workspaceId and path are required',
        },
        { status: 400 }
      );
    }

    // Safety check - prevent deletion of important directories
    const dangerousPaths = ['/home/<USER>', '/home', '/', '/usr', '/etc', '/var'];
    if (dangerousPaths.includes(path)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Operation not allowed',
          message: `Cannot delete system directory: ${path}`,
        },
        { status: 403 }
      );
    }

    await fileManagerService.deleteItem(workspaceId, path, { recursive, force });
    
    return NextResponse.json({
      success: true,
      message: `'${path}' deleted successfully`,
    });

  } catch (error) {
    console.error('Error deleting file:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Delete operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT /api/files - Update file content or rename
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { workspaceId, path, action = 'update', content, newPath, options = {} } = body;

    if (!workspaceId || !path) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId and path are required',
        },
        { status: 400 }
      );
    }

    if (action === 'update') {
      // Update file content
      if (content === undefined) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing content',
            message: 'content is required for update action',
          },
          { status: 400 }
        );
      }

      await fileManagerService.writeFile(workspaceId, path, content, options);
      
      return NextResponse.json({
        success: true,
        message: `File '${path}' updated successfully`,
      });

    } else if (action === 'rename') {
      // Rename file/directory
      if (!newPath) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing new path',
            message: 'newPath is required for rename action',
          },
          { status: 400 }
        );
      }

      await fileManagerService.moveItem(workspaceId, path, newPath);
      
      return NextResponse.json({
        success: true,
        message: `'${path}' renamed to '${newPath}' successfully`,
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: update, rename',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error updating file:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Update operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
