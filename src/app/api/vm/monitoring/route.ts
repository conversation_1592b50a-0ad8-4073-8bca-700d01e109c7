/**
 * VM Monitoring API Routes
 * Infrastructure monitoring endpoints for VM resource tracking, health checks, and alerts
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { VMMonitoringService } from '@/services/vm/monitoring';
import { vmLogger } from '@/lib/vm-server';
import { VMConnectionConfig } from '@/types/vm';

// Validation schemas
const vmConnectionConfigSchema = z.object({
  host: z.string().min(1),
  port: z.number().int().min(1).max(65535),
  username: z.string().min(1),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  passphrase: z.string().optional(),
  timeout: z.number().int().positive().optional(),
  keepAlive: z.boolean().optional(),
  maxRetries: z.number().int().positive().optional(),
  retryDelay: z.number().int().positive().optional(),
}).refine(
  (data) => data.password || data.privateKey,
  { message: "Either password or privateKey must be provided" }
);

const processListSchema = z.object({
  options: z.object({
    sortBy: z.enum(['cpu', 'memory', 'name']).optional(),
    limit: z.number().int().positive().optional(),
    filter: z.string().optional(),
  }).optional(),
});

const serviceStatusSchema = z.object({
  serviceNames: z.array(z.string()).optional(),
});

const metricsHistorySchema = z.object({
  limit: z.number().int().positive().max(1000).optional(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
});

// Helper functions
function handleResponse(result: any, successStatus: number = 200) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata,
    }, { status: successStatus });
  } else {
    const statusCode = result.error?.details?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        code: result.error?.code || 'UNKNOWN_ERROR',
        message: result.error?.message || 'An unknown error occurred',
        details: result.error?.details,
      },
      metadata: result.metadata,
    }, { status: statusCode });
  }
}

function getRequiredParam(request: NextRequest, paramName: string): string {
  const value = request.nextUrl.searchParams.get(paramName);
  if (!value) {
    throw new Error(`${paramName} is required`);
  }
  return value;
}

function getVMConnectionConfig(request: NextRequest): VMConnectionConfig {
  const configParam = request.nextUrl.searchParams.get('config');
  if (!configParam) {
    throw new Error('VM connection configuration is required');
  }
  
  const config = JSON.parse(configParam);
  return vmConnectionConfigSchema.parse(config);
}

// GET /api/vm/monitoring - Get monitoring data, metrics, and health information
export async function GET(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');
    const connectionId = getRequiredParam(request, 'connectionId');

    vmLogger.info(`VM Monitoring API GET request`, { action, vmId, connectionId });

    const config = getVMConnectionConfig(request);
    const monitoringService = new VMMonitoringService(vmId, config);

    switch (action) {
      case 'system-info': {
        const result = await monitoringService.getSystemInfo(connectionId);
        return handleResponse(result);
      }

      case 'metrics': {
        const result = await monitoringService.getResourceMetrics(connectionId);
        return handleResponse(result);
      }

      case 'processes': {
        const sortBy = request.nextUrl.searchParams.get('sortBy') as 'cpu' | 'memory' | 'name' | null;
        const limit = request.nextUrl.searchParams.get('limit');
        const filter = request.nextUrl.searchParams.get('filter');

        const options: any = {};
        if (sortBy) options.sortBy = sortBy;
        if (limit) options.limit = parseInt(limit);
        if (filter) options.filter = filter;

        const result = await monitoringService.getProcesses(connectionId, options);
        return handleResponse(result);
      }

      case 'services': {
        const serviceNamesParam = request.nextUrl.searchParams.get('serviceNames');
        const serviceNames = serviceNamesParam ? JSON.parse(serviceNamesParam) : undefined;

        const result = await monitoringService.getServiceStatuses(connectionId, serviceNames);
        return handleResponse(result);
      }

      case 'health': {
        const result = await monitoringService.performHealthCheck(connectionId);
        return handleResponse(result);
      }

      case 'metrics-history': {
        const limit = request.nextUrl.searchParams.get('limit');
        const startTime = request.nextUrl.searchParams.get('startTime');
        const endTime = request.nextUrl.searchParams.get('endTime');

        const limitNum = limit ? parseInt(limit) : 50;
        const startDate = startTime ? new Date(startTime) : undefined;
        const endDate = endTime ? new Date(endTime) : undefined;

        const result = await monitoringService.getMetricsHistory(limitNum, startDate, endDate);
        return handleResponse(result);
      }

      case 'alerts': {
        const result = await monitoringService.getActiveAlerts();
        return handleResponse(result);
      }

      case 'health-simple': {
        const isHealthy = await monitoringService.healthCheck();
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            healthy: isHealthy,
            timestamp: new Date().toISOString(),
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Monitoring API GET error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// POST /api/vm/monitoring - Start monitoring, acknowledge alerts, etc.
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');
    const connectionId = getRequiredParam(request, 'connectionId');

    vmLogger.info(`VM Monitoring API POST request`, { action, vmId, connectionId });

    const validatedConfig = vmConnectionConfigSchema.parse(body.config);
    const monitoringService = new VMMonitoringService(vmId, validatedConfig);

    switch (action) {
      case 'start-monitoring': {
        const result = await monitoringService.startMonitoring(connectionId);
        return handleResponse(result, 201);
      }

      case 'stop-monitoring': {
        const result = await monitoringService.stopMonitoring();
        return handleResponse(result);
      }

      case 'acknowledge-alert': {
        const alertId = getRequiredParam(request, 'alertId');
        const result = await monitoringService.acknowledgeAlert(alertId);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Monitoring API POST error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// PUT /api/vm/monitoring - Update monitoring configuration or settings
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');

    vmLogger.info(`VM Monitoring API PUT request`, { action, vmId });

    switch (action) {
      case 'update-thresholds': {
        // In a real implementation, you'd update alert thresholds
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            message: 'Update thresholds endpoint - implementation needed',
            thresholds: body.thresholds,
          },
        });
      }

      case 'update-monitoring-config': {
        // In a real implementation, you'd update monitoring configuration
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            message: 'Update monitoring config endpoint - implementation needed',
            config: body.config,
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Monitoring API PUT error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// DELETE /api/vm/monitoring - Clean up monitoring resources
export async function DELETE(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');

    vmLogger.info(`VM Monitoring API DELETE request`, { action, vmId });

    switch (action) {
      case 'cleanup': {
        // In a real implementation, you'd clean up monitoring resources
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            message: 'Monitoring cleanup completed',
            timestamp: new Date().toISOString(),
          },
        });
      }

      case 'clear-alerts': {
        // In a real implementation, you'd clear all alerts
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            message: 'All alerts cleared',
            timestamp: new Date().toISOString(),
          },
        });
      }

      case 'clear-metrics-history': {
        // In a real implementation, you'd clear metrics history
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            message: 'Metrics history cleared',
            timestamp: new Date().toISOString(),
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Monitoring API DELETE error', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}
