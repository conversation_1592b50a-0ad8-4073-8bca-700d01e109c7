/**
 * VM API Routes
 * Main VM management endpoints for connection, configuration, and basic operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { VMConnectionService } from '@/services/vm/connection';
import { vmLogger } from '@/lib/vm-server';
import { VMConnectionConfig, VMAuthCredentials } from '@/types/vm';

// Validation schemas
const vmConnectionConfigSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  port: z.number().int().min(1).max(65535),
  username: z.string().min(1, 'Username is required'),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  passphrase: z.string().optional(),
  timeout: z.number().int().positive().optional(),
  keepAlive: z.boolean().optional(),
  maxRetries: z.number().int().positive().optional(),
  retryDelay: z.number().int().positive().optional(),
}).refine(
  (data) => data.password || data.privateKey,
  {
    message: "Either password or privateKey must be provided",
    path: ["password"],
  }
);

const vmAuthCredentialsSchema = z.object({
  type: z.enum(['password', 'key', 'certificate']),
  username: z.string().min(1),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  publicKey: z.string().optional(),
  certificate: z.string().optional(),
  passphrase: z.string().optional(),
  expiresAt: z.string().datetime().optional(),
});

const executeCommandSchema = z.object({
  command: z.string().min(1, 'Command is required'),
  options: z.object({
    timeout: z.number().int().positive().optional(),
    cwd: z.string().optional(),
    env: z.record(z.string()).optional(),
  }).optional(),
});

const createSessionSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  metadata: z.record(z.any()).optional(),
});

// Helper function to handle API responses
function handleResponse(result: any, successStatus: number = 200) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata,
    }, { status: successStatus });
  } else {
    const statusCode = result.error?.details?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        code: result.error?.code || 'UNKNOWN_ERROR',
        message: result.error?.message || 'An unknown error occurred',
        details: result.error?.details,
      },
      metadata: result.metadata,
    }, { status: statusCode });
  }
}

// Helper function to get VM ID from request
function getVMId(request: NextRequest): string {
  const vmId = request.nextUrl.searchParams.get('vmId');
  if (!vmId) {
    throw new Error('VM ID is required');
  }
  return vmId;
}

// Helper function to get connection ID from request
function getConnectionId(request: NextRequest): string {
  const connectionId = request.nextUrl.searchParams.get('connectionId');
  if (!connectionId) {
    throw new Error('Connection ID is required');
  }
  return connectionId;
}

// POST /api/vm - Create VM connection or execute operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getVMId(request);

    vmLogger.info(`VM API POST request`, { action, vmId });

    switch (action) {
      case 'connect': {
        const validatedConfig = vmConnectionConfigSchema.parse(body.config);
        const credentials = body.credentials ? vmAuthCredentialsSchema.parse(body.credentials) : undefined;
        
        const connectionService = new VMConnectionService(vmId, validatedConfig);
        const result = await connectionService.connect(credentials);
        
        return handleResponse(result, 201);
      }

      case 'execute': {
        const connectionId = getConnectionId(request);
        const validatedData = executeCommandSchema.parse(body);
        
        // Get connection config from body (in a real app, this would be stored/retrieved)
        const validatedConfig = vmConnectionConfigSchema.parse(body.config);
        const connectionService = new VMConnectionService(vmId, validatedConfig);
        
        const result = await connectionService.executeCommand(
          connectionId,
          validatedData.command,
          validatedData.options
        );
        
        return handleResponse(result);
      }

      case 'create-session': {
        const connectionId = getConnectionId(request);
        const validatedData = createSessionSchema.parse(body);
        
        const validatedConfig = vmConnectionConfigSchema.parse(body.config);
        const connectionService = new VMConnectionService(vmId, validatedConfig);
        
        const result = await connectionService.createSession(
          connectionId,
          validatedData.userId,
          validatedData.metadata
        );
        
        return handleResponse(result, 201);
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM API POST error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// GET /api/vm - Get VM information or connection status
export async function GET(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getVMId(request);

    vmLogger.info(`VM API GET request`, { action, vmId });

    switch (action) {
      case 'status':
      case 'connection-status': {
        const connectionId = getConnectionId(request);
        
        // In a real implementation, you'd retrieve the config from storage
        // For now, we'll return a basic response
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            connectionId,
            message: 'Connection status endpoint - config retrieval needed',
          },
        });
      }

      case 'connections': {
        // In a real implementation, you'd retrieve the config from storage
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            message: 'List connections endpoint - config retrieval needed',
          },
        });
      }

      case 'health': {
        // Basic health check without requiring full config
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            status: 'healthy',
            timestamp: new Date().toISOString(),
            message: 'VM API is operational',
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM API GET error', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// DELETE /api/vm - Disconnect or cleanup VM resources
export async function DELETE(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getVMId(request);

    vmLogger.info(`VM API DELETE request`, { action, vmId });

    switch (action) {
      case 'disconnect': {
        const connectionId = getConnectionId(request);
        
        // In a real implementation, you'd retrieve the config and disconnect
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            connectionId,
            message: 'Disconnect endpoint - implementation needed',
          },
        });
      }

      case 'close-session': {
        const sessionId = request.nextUrl.searchParams.get('sessionId');
        if (!sessionId) {
          return NextResponse.json({
            success: false,
            error: {
              code: 'MISSING_PARAMETER',
              message: 'Session ID is required',
            },
          }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          data: {
            vmId,
            sessionId,
            message: 'Close session endpoint - implementation needed',
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM API DELETE error', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// PUT /api/vm - Update VM configuration or connection settings
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getVMId(request);

    vmLogger.info(`VM API PUT request`, { action, vmId });

    switch (action) {
      case 'update-config': {
        const validatedConfig = vmConnectionConfigSchema.parse(body.config);
        
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            config: validatedConfig,
            message: 'Update config endpoint - implementation needed',
          },
        });
      }

      case 'acknowledge-alert': {
        const alertId = request.nextUrl.searchParams.get('alertId');
        if (!alertId) {
          return NextResponse.json({
            success: false,
            error: {
              code: 'MISSING_PARAMETER',
              message: 'Alert ID is required',
            },
          }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          data: {
            vmId,
            alertId,
            message: 'Acknowledge alert endpoint - implementation needed',
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM API PUT error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}
