/**
 * VM Health Check API Routes
 * Comprehensive health monitoring endpoints for VM services
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { VMConnectionService } from '@/services/vm/connection';
import { VMDockerService } from '@/services/vm/docker';
import { VMMonitoringService } from '@/services/vm/monitoring';
import { vmLogger, vmHealthCheck } from '@/lib/vm-server';
import { VMConnectionConfig } from '@/types/vm';

// Validation schema
const vmConnectionConfigSchema = z.object({
  host: z.string().min(1),
  port: z.number().int().min(1).max(65535),
  username: z.string().min(1),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  passphrase: z.string().optional(),
  timeout: z.number().int().positive().optional(),
  keepAlive: z.boolean().optional(),
  maxRetries: z.number().int().positive().optional(),
  retryDelay: z.number().int().positive().optional(),
}).refine(
  (data) => data.password || data.privateKey,
  { message: "Either password or privateKey must be provided" }
);

// Health check result interface
interface HealthCheckResult {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    connection: boolean;
    docker: boolean;
    monitoring: boolean;
  };
  details: {
    connection?: string;
    docker?: string;
    monitoring?: string;
  };
  timestamp: string;
  vmId: string;
  responseTime: number;
}

// Helper function to perform comprehensive health check
async function performComprehensiveHealthCheck(
  vmId: string,
  config: VMConnectionConfig
): Promise<HealthCheckResult> {
  const startTime = Date.now();
  const result: HealthCheckResult = {
    overall: 'unhealthy',
    services: {
      connection: false,
      docker: false,
      monitoring: false,
    },
    details: {},
    timestamp: new Date().toISOString(),
    vmId,
    responseTime: 0,
  };

  try {
    // Test connection service
    try {
      const connectionService = new VMConnectionService(vmId, config);
      result.services.connection = await connectionService.healthCheck();
      if (!result.services.connection) {
        result.details.connection = 'Failed to establish VM connection';
      }
    } catch (error) {
      result.services.connection = false;
      result.details.connection = `Connection service error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }

    // Test Docker service (only if connection is working)
    if (result.services.connection) {
      try {
        const dockerService = new VMDockerService(vmId, config);
        result.services.docker = await dockerService.healthCheck();
        if (!result.services.docker) {
          result.details.docker = 'Docker daemon not accessible or not running';
        }
      } catch (error) {
        result.services.docker = false;
        result.details.docker = `Docker service error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
    } else {
      result.services.docker = false;
      result.details.docker = 'Skipped due to connection failure';
    }

    // Test monitoring service (only if connection is working)
    if (result.services.connection) {
      try {
        const monitoringService = new VMMonitoringService(vmId, config);
        result.services.monitoring = await monitoringService.healthCheck();
        if (!result.services.monitoring) {
          result.details.monitoring = 'Monitoring service checks failed';
        }
      } catch (error) {
        result.services.monitoring = false;
        result.details.monitoring = `Monitoring service error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
    } else {
      result.services.monitoring = false;
      result.details.monitoring = 'Skipped due to connection failure';
    }

    // Determine overall health
    const serviceValues = Object.values(result.services);
    const healthyServices = serviceValues.filter(service => service).length;
    const totalServices = serviceValues.length;

    if (healthyServices === totalServices) {
      result.overall = 'healthy';
    } else if (healthyServices >= totalServices * 0.5) {
      result.overall = 'degraded';
    } else {
      result.overall = 'unhealthy';
    }

  } catch (error) {
    vmLogger.error(`Comprehensive health check failed for VM ${vmId}`, error);
    result.details.connection = `Health check error: ${error instanceof Error ? error.message : 'Unknown error'}`;
  }

  result.responseTime = Date.now() - startTime;
  return result;
}

// GET /api/vm/health - Get VM health status
export async function GET(request: NextRequest) {
  try {
    const vmId = request.nextUrl.searchParams.get('vmId');
    const detailed = request.nextUrl.searchParams.get('detailed') === 'true';
    const configParam = request.nextUrl.searchParams.get('config');

    vmLogger.info(`VM Health API GET request`, { vmId, detailed });

    // Basic health check without VM connection
    if (!vmId) {
      return NextResponse.json({
        success: true,
        data: {
          status: 'healthy',
          message: 'VM Health API is operational',
          timestamp: new Date().toISOString(),
          services: {
            api: true,
            logging: true,
            configuration: true,
          },
        },
      });
    }

    // VM-specific health check
    if (!configParam) {
      // Return basic VM health without connection test
      const basicHealth = await vmHealthCheck(vmId);
      return NextResponse.json({
        success: true,
        data: {
          vmId,
          status: basicHealth.status,
          timestamp: basicHealth.timestamp,
          message: 'Basic health check - connection config required for detailed check',
        },
      });
    }

    // Detailed health check with VM connection
    const config = JSON.parse(configParam);
    const validatedConfig = vmConnectionConfigSchema.parse(config);

    if (detailed) {
      const healthResult = await performComprehensiveHealthCheck(vmId, validatedConfig);
      return NextResponse.json({
        success: true,
        data: healthResult,
      });
    } else {
      // Quick connection test only
      const connectionService = new VMConnectionService(vmId, validatedConfig);
      const isHealthy = await connectionService.healthCheck();
      
      return NextResponse.json({
        success: true,
        data: {
          vmId,
          status: isHealthy ? 'healthy' : 'unhealthy',
          connection: isHealthy,
          timestamp: new Date().toISOString(),
          message: isHealthy ? 'VM connection is healthy' : 'VM connection failed',
        },
      });
    }

  } catch (error) {
    vmLogger.error('VM Health API GET error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid configuration data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// POST /api/vm/health - Perform health check with configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const vmId = body.vmId;
    const detailed = body.detailed || false;

    if (!vmId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: 'VM ID is required',
        },
      }, { status: 400 });
    }

    vmLogger.info(`VM Health API POST request`, { vmId, detailed });

    const validatedConfig = vmConnectionConfigSchema.parse(body.config);

    if (detailed) {
      const healthResult = await performComprehensiveHealthCheck(vmId, validatedConfig);
      return NextResponse.json({
        success: true,
        data: healthResult,
      });
    } else {
      // Quick health check
      const connectionService = new VMConnectionService(vmId, validatedConfig);
      const startTime = Date.now();
      const isHealthy = await connectionService.healthCheck();
      const responseTime = Date.now() - startTime;
      
      return NextResponse.json({
        success: true,
        data: {
          vmId,
          status: isHealthy ? 'healthy' : 'unhealthy',
          connection: isHealthy,
          timestamp: new Date().toISOString(),
          responseTime,
          message: isHealthy ? 'VM connection is healthy' : 'VM connection failed',
        },
      });
    }

  } catch (error) {
    vmLogger.error('VM Health API POST error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// PUT /api/vm/health - Update health check configuration
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const vmId = body.vmId;

    if (!vmId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: 'VM ID is required',
        },
      }, { status: 400 });
    }

    vmLogger.info(`VM Health API PUT request`, { vmId });

    // In a real implementation, you'd update health check configuration
    return NextResponse.json({
      success: true,
      data: {
        vmId,
        message: 'Health check configuration updated',
        config: body.healthConfig,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    vmLogger.error('VM Health API PUT error', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}
