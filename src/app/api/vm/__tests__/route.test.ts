/**
 * VM API Routes Tests
 * Unit tests for the VM API endpoints
 */

import { NextRequest } from 'next/server';
import { POST, GET, DELETE, PUT } from '../route';

// Mock the VM services
jest.mock('@/services/vm/connection', () => ({
  VMConnectionService: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    executeCommand: jest.fn(),
    createSession: jest.fn(),
    disconnect: jest.fn(),
    getConnectionStatus: jest.fn(),
    listConnections: jest.fn(),
  })),
}));

// Mock the VM logger
jest.mock('@/lib/vm-server', () => ({
  ...jest.requireActual('@/lib/vm-server'),
  vmLogger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
}));

// Helper function to create mock NextRequest
function createMockRequest(
  method: string,
  url: string,
  body?: any,
  searchParams?: Record<string, string>
): NextRequest {
  const fullUrl = new URL(url, 'http://localhost:3000');
  
  if (searchParams) {
    Object.entries(searchParams).forEach(([key, value]) => {
      fullUrl.searchParams.set(key, value);
    });
  }

  const request = new NextRequest(fullUrl, {
    method,
    body: body ? JSON.stringify(body) : undefined,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return request;
}

describe('VM API Routes', () => {
  const mockVMConfig = {
    host: 'test-vm.example.com',
    port: 22,
    username: 'testuser',
    password: 'testpass',
    timeout: 30000,
    keepAlive: true,
    maxRetries: 3,
    retryDelay: 1000,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/vm', () => {
    describe('connect action', () => {
      it('should connect to VM successfully', async () => {
        const { VMConnectionService } = require('@/services/vm/connection');
        const mockConnect = jest.fn().mockResolvedValue({
          success: true,
          data: {
            isConnected: true,
            connectionId: 'conn-123',
            establishedAt: new Date(),
            lastActivity: new Date(),
          },
        });
        VMConnectionService.mockImplementation(() => ({ connect: mockConnect }));

        const request = createMockRequest(
          'POST',
          '/api/vm',
          { config: mockVMConfig },
          { action: 'connect', vmId: 'test-vm-001' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(201);
        expect(data.success).toBe(true);
        expect(data.data.isConnected).toBe(true);
        expect(data.data.connectionId).toBe('conn-123');
        expect(mockConnect).toHaveBeenCalledWith(undefined);
      });

      it('should connect with custom credentials', async () => {
        const { VMConnectionService } = require('@/services/vm/connection');
        const mockConnect = jest.fn().mockResolvedValue({
          success: true,
          data: { isConnected: true, connectionId: 'conn-123' },
        });
        VMConnectionService.mockImplementation(() => ({ connect: mockConnect }));

        const credentials = {
          type: 'key',
          username: 'customuser',
          privateKey: 'custom-key',
        };

        const request = createMockRequest(
          'POST',
          '/api/vm',
          { config: mockVMConfig, credentials },
          { action: 'connect', vmId: 'test-vm-001' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(201);
        expect(data.success).toBe(true);
        expect(mockConnect).toHaveBeenCalledWith(credentials);
      });

      it('should handle connection failure', async () => {
        const { VMConnectionService } = require('@/services/vm/connection');
        const mockConnect = jest.fn().mockResolvedValue({
          success: false,
          error: {
            code: 'CONNECTION_FAILED',
            message: 'Failed to connect to VM',
            details: { statusCode: 503 },
          },
        });
        VMConnectionService.mockImplementation(() => ({ connect: mockConnect }));

        const request = createMockRequest(
          'POST',
          '/api/vm',
          { config: mockVMConfig },
          { action: 'connect', vmId: 'test-vm-001' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(503);
        expect(data.success).toBe(false);
        expect(data.error.code).toBe('CONNECTION_FAILED');
      });

      it('should validate configuration', async () => {
        const invalidConfig = { ...mockVMConfig, host: '' };

        const request = createMockRequest(
          'POST',
          '/api/vm',
          { config: invalidConfig },
          { action: 'connect', vmId: 'test-vm-001' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.success).toBe(false);
        expect(data.error.code).toBe('VALIDATION_ERROR');
      });
    });

    describe('execute action', () => {
      it('should execute command successfully', async () => {
        const { VMConnectionService } = require('@/services/vm/connection');
        const mockExecuteCommand = jest.fn().mockResolvedValue({
          success: true,
          data: {
            stdout: 'command output',
            stderr: '',
            exitCode: 0,
          },
        });
        VMConnectionService.mockImplementation(() => ({ executeCommand: mockExecuteCommand }));

        const request = createMockRequest(
          'POST',
          '/api/vm',
          {
            config: mockVMConfig,
            command: 'ls -la',
            options: { timeout: 5000 },
          },
          { action: 'execute', vmId: 'test-vm-001', connectionId: 'conn-123' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.stdout).toBe('command output');
        expect(mockExecuteCommand).toHaveBeenCalledWith('conn-123', 'ls -la', { timeout: 5000 });
      });

      it('should validate command', async () => {
        const request = createMockRequest(
          'POST',
          '/api/vm',
          {
            config: mockVMConfig,
            command: '', // Invalid empty command
          },
          { action: 'execute', vmId: 'test-vm-001', connectionId: 'conn-123' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.success).toBe(false);
        expect(data.error.code).toBe('VALIDATION_ERROR');
      });
    });

    describe('create-session action', () => {
      it('should create session successfully', async () => {
        const { VMConnectionService } = require('@/services/vm/connection');
        const mockCreateSession = jest.fn().mockResolvedValue({
          success: true,
          data: {
            id: 'session-123',
            vmId: 'test-vm-001',
            userId: 'user-123',
            connectionId: 'conn-123',
            isActive: true,
          },
        });
        VMConnectionService.mockImplementation(() => ({ createSession: mockCreateSession }));

        const request = createMockRequest(
          'POST',
          '/api/vm',
          {
            config: mockVMConfig,
            userId: 'user-123',
            metadata: { userAgent: 'test-agent' },
          },
          { action: 'create-session', vmId: 'test-vm-001', connectionId: 'conn-123' }
        );

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(201);
        expect(data.success).toBe(true);
        expect(data.data.id).toBe('session-123');
        expect(mockCreateSession).toHaveBeenCalledWith(
          'conn-123',
          'user-123',
          { userAgent: 'test-agent' }
        );
      });
    });

    it('should handle missing VM ID', async () => {
      const request = createMockRequest(
        'POST',
        '/api/vm',
        { config: mockVMConfig },
        { action: 'connect' } // Missing vmId
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INTERNAL_ERROR');
    });

    it('should handle invalid action', async () => {
      const request = createMockRequest(
        'POST',
        '/api/vm',
        { config: mockVMConfig },
        { action: 'invalid-action', vmId: 'test-vm-001' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_ACTION');
    });
  });

  describe('GET /api/vm', () => {
    it('should return health status', async () => {
      const request = createMockRequest(
        'GET',
        '/api/vm',
        undefined,
        { action: 'health', vmId: 'test-vm-001' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe('healthy');
      expect(data.data.vmId).toBe('test-vm-001');
    });

    it('should handle connection status request', async () => {
      const request = createMockRequest(
        'GET',
        '/api/vm',
        undefined,
        { action: 'connection-status', vmId: 'test-vm-001', connectionId: 'conn-123' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.vmId).toBe('test-vm-001');
      expect(data.data.connectionId).toBe('conn-123');
    });

    it('should handle connections list request', async () => {
      const request = createMockRequest(
        'GET',
        '/api/vm',
        undefined,
        { action: 'connections', vmId: 'test-vm-001' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.vmId).toBe('test-vm-001');
    });

    it('should handle missing VM ID', async () => {
      const request = createMockRequest(
        'GET',
        '/api/vm',
        undefined,
        { action: 'health' } // Missing vmId
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INTERNAL_ERROR');
    });

    it('should handle invalid action', async () => {
      const request = createMockRequest(
        'GET',
        '/api/vm',
        undefined,
        { action: 'invalid-action', vmId: 'test-vm-001' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_ACTION');
    });
  });

  describe('DELETE /api/vm', () => {
    it('should handle disconnect request', async () => {
      const request = createMockRequest(
        'DELETE',
        '/api/vm',
        undefined,
        { action: 'disconnect', vmId: 'test-vm-001', connectionId: 'conn-123' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.vmId).toBe('test-vm-001');
      expect(data.data.connectionId).toBe('conn-123');
    });

    it('should handle close session request', async () => {
      const request = createMockRequest(
        'DELETE',
        '/api/vm',
        undefined,
        { action: 'close-session', vmId: 'test-vm-001', sessionId: 'session-123' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.vmId).toBe('test-vm-001');
      expect(data.data.sessionId).toBe('session-123');
    });

    it('should handle missing session ID for close-session', async () => {
      const request = createMockRequest(
        'DELETE',
        '/api/vm',
        undefined,
        { action: 'close-session', vmId: 'test-vm-001' } // Missing sessionId
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('MISSING_PARAMETER');
    });

    it('should handle invalid action', async () => {
      const request = createMockRequest(
        'DELETE',
        '/api/vm',
        undefined,
        { action: 'invalid-action', vmId: 'test-vm-001' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_ACTION');
    });
  });

  describe('PUT /api/vm', () => {
    it('should handle update config request', async () => {
      const request = createMockRequest(
        'PUT',
        '/api/vm',
        { config: mockVMConfig },
        { action: 'update-config', vmId: 'test-vm-001' }
      );

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.vmId).toBe('test-vm-001');
      expect(data.data.config).toEqual(mockVMConfig);
    });

    it('should handle acknowledge alert request', async () => {
      const request = createMockRequest(
        'PUT',
        '/api/vm',
        {},
        { action: 'acknowledge-alert', vmId: 'test-vm-001', alertId: 'alert-123' }
      );

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.vmId).toBe('test-vm-001');
      expect(data.data.alertId).toBe('alert-123');
    });

    it('should handle missing alert ID for acknowledge-alert', async () => {
      const request = createMockRequest(
        'PUT',
        '/api/vm',
        {},
        { action: 'acknowledge-alert', vmId: 'test-vm-001' } // Missing alertId
      );

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('MISSING_PARAMETER');
    });

    it('should validate configuration for update-config', async () => {
      const invalidConfig = { ...mockVMConfig, port: 70000 };

      const request = createMockRequest(
        'PUT',
        '/api/vm',
        { config: invalidConfig },
        { action: 'update-config', vmId: 'test-vm-001' }
      );

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });

    it('should handle invalid action', async () => {
      const request = createMockRequest(
        'PUT',
        '/api/vm',
        {},
        { action: 'invalid-action', vmId: 'test-vm-001' }
      );

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_ACTION');
    });
  });
});
