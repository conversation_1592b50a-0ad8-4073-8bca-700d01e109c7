/**
 * VM Docker API Routes
 * Docker operations endpoints for VM-based container management
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { VMDockerService, ContainerCreateOptions } from '@/services/vm/docker';
import { vmLogger } from '@/lib/vm-server';
import { VMConnectionConfig } from '@/types/vm';

// Validation schemas
const vmConnectionConfigSchema = z.object({
  host: z.string().min(1),
  port: z.number().int().min(1).max(65535),
  username: z.string().min(1),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  passphrase: z.string().optional(),
  timeout: z.number().int().positive().optional(),
  keepAlive: z.boolean().optional(),
  maxRetries: z.number().int().positive().optional(),
  retryDelay: z.number().int().positive().optional(),
}).refine(
  (data) => data.password || data.privateKey,
  { message: "Either password or privateKey must be provided" }
);

const containerCreateSchema = z.object({
  name: z.string().optional(),
  image: z.string().min(1, 'Image is required'),
  command: z.array(z.string()).optional(),
  entrypoint: z.array(z.string()).optional(),
  workingDir: z.string().optional(),
  environment: z.record(z.string()).optional(),
  ports: z.record(z.string()).optional(),
  volumes: z.record(z.string()).optional(),
  networks: z.array(z.string()).optional(),
  labels: z.record(z.string()).optional(),
  restartPolicy: z.enum(['no', 'always', 'unless-stopped', 'on-failure']).optional(),
  memory: z.number().int().positive().optional(),
  cpu: z.number().positive().optional(),
  privileged: z.boolean().optional(),
  user: z.string().optional(),
  hostname: z.string().optional(),
  domainname: z.string().optional(),
  attachStdin: z.boolean().optional(),
  attachStdout: z.boolean().optional(),
  attachStderr: z.boolean().optional(),
  tty: z.boolean().optional(),
  openStdin: z.boolean().optional(),
  stdinOnce: z.boolean().optional(),
  autoRemove: z.boolean().optional(),
});

const containerExecSchema = z.object({
  command: z.array(z.string()).min(1, 'Command is required'),
  options: z.object({
    interactive: z.boolean().optional(),
    tty: z.boolean().optional(),
    user: z.string().optional(),
    workingDir: z.string().optional(),
    environment: z.record(z.string()).optional(),
  }).optional(),
});

const containerLogsSchema = z.object({
  options: z.object({
    follow: z.boolean().optional(),
    tail: z.number().int().positive().optional(),
    since: z.string().optional(),
    until: z.string().optional(),
    timestamps: z.boolean().optional(),
  }).optional(),
});

// Helper functions
function handleResponse(result: any, successStatus: number = 200) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata,
    }, { status: successStatus });
  } else {
    const statusCode = result.error?.details?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        code: result.error?.code || 'UNKNOWN_ERROR',
        message: result.error?.message || 'An unknown error occurred',
        details: result.error?.details,
      },
      metadata: result.metadata,
    }, { status: statusCode });
  }
}

function getRequiredParam(request: NextRequest, paramName: string): string {
  const value = request.nextUrl.searchParams.get(paramName);
  if (!value) {
    throw new Error(`${paramName} is required`);
  }
  return value;
}

// POST /api/vm/docker - Create containers or execute Docker operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');
    const connectionId = getRequiredParam(request, 'connectionId');

    vmLogger.info(`VM Docker API POST request`, { action, vmId, connectionId });

    // Validate connection config
    const validatedConfig = vmConnectionConfigSchema.parse(body.config);
    const dockerService = new VMDockerService(vmId, validatedConfig);

    switch (action) {
      case 'create-container': {
        const validatedData = containerCreateSchema.parse(body.options);
        const result = await dockerService.createContainer(connectionId, validatedData);
        return handleResponse(result, 201);
      }

      case 'start-container': {
        const containerId = getRequiredParam(request, 'containerId');
        const result = await dockerService.startContainer(connectionId, containerId);
        return handleResponse(result);
      }

      case 'stop-container': {
        const containerId = getRequiredParam(request, 'containerId');
        const timeout = parseInt(request.nextUrl.searchParams.get('timeout') || '10');
        const result = await dockerService.stopContainer(connectionId, containerId, timeout);
        return handleResponse(result);
      }

      case 'restart-container': {
        const containerId = getRequiredParam(request, 'containerId');
        const timeout = parseInt(request.nextUrl.searchParams.get('timeout') || '10');
        
        // Stop then start the container
        const stopResult = await dockerService.stopContainer(connectionId, containerId, timeout);
        if (!stopResult.success) {
          return handleResponse(stopResult);
        }
        
        const startResult = await dockerService.startContainer(connectionId, containerId);
        return handleResponse(startResult);
      }

      case 'exec-container': {
        const containerId = getRequiredParam(request, 'containerId');
        const validatedData = containerExecSchema.parse(body);
        const result = await dockerService.execInContainer(
          connectionId,
          containerId,
          validatedData.command,
          validatedData.options
        );
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Docker API POST error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// GET /api/vm/docker - Get Docker information, containers, images, etc.
export async function GET(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');
    const connectionId = getRequiredParam(request, 'connectionId');

    vmLogger.info(`VM Docker API GET request`, { action, vmId, connectionId });

    // For GET requests, we need the config to be passed or retrieved from storage
    // In a real implementation, you'd retrieve this from a database or cache
    const configParam = request.nextUrl.searchParams.get('config');
    if (!configParam) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_CONFIG',
          message: 'VM connection configuration is required',
        },
      }, { status: 400 });
    }

    const config = JSON.parse(configParam);
    const validatedConfig = vmConnectionConfigSchema.parse(config);
    const dockerService = new VMDockerService(vmId, validatedConfig);

    switch (action) {
      case 'info': {
        const result = await dockerService.getDockerInfo(connectionId);
        return handleResponse(result);
      }

      case 'containers': {
        const all = request.nextUrl.searchParams.get('all') === 'true';
        const limit = request.nextUrl.searchParams.get('limit');
        const filters = request.nextUrl.searchParams.get('filters');

        const options: any = { all };
        if (limit) options.limit = parseInt(limit);
        if (filters) options.filters = JSON.parse(filters);

        const result = await dockerService.listContainers(connectionId, options);
        return handleResponse(result);
      }

      case 'container-logs': {
        const containerId = getRequiredParam(request, 'containerId');
        const follow = request.nextUrl.searchParams.get('follow') === 'true';
        const tail = request.nextUrl.searchParams.get('tail');
        const since = request.nextUrl.searchParams.get('since');
        const until = request.nextUrl.searchParams.get('until');
        const timestamps = request.nextUrl.searchParams.get('timestamps') === 'true';

        const options: any = {};
        if (follow) options.follow = follow;
        if (tail) options.tail = parseInt(tail);
        if (since) options.since = since;
        if (until) options.until = until;
        if (timestamps) options.timestamps = timestamps;

        const result = await dockerService.getContainerLogs(connectionId, containerId, options);
        return handleResponse(result);
      }

      case 'health': {
        const result = await dockerService.healthCheck();
        return NextResponse.json({
          success: true,
          data: {
            vmId,
            dockerHealthy: result,
            timestamp: new Date().toISOString(),
          },
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Docker API GET error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}

// DELETE /api/vm/docker - Remove containers, images, etc.
export async function DELETE(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const vmId = getRequiredParam(request, 'vmId');
    const connectionId = getRequiredParam(request, 'connectionId');

    vmLogger.info(`VM Docker API DELETE request`, { action, vmId, connectionId });

    const body = await request.json();
    const validatedConfig = vmConnectionConfigSchema.parse(body.config);
    const dockerService = new VMDockerService(vmId, validatedConfig);

    switch (action) {
      case 'remove-container': {
        const containerId = getRequiredParam(request, 'containerId');
        const force = request.nextUrl.searchParams.get('force') === 'true';
        const removeVolumes = request.nextUrl.searchParams.get('removeVolumes') === 'true';
        const removeLinks = request.nextUrl.searchParams.get('removeLinks') === 'true';

        const options = { force, removeVolumes, removeLinks };
        const result = await dockerService.removeContainer(connectionId, containerId, options);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: 'Invalid action specified',
          },
        }, { status: 400 });
    }
  } catch (error) {
    vmLogger.error('VM Docker API DELETE error', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      },
    }, { status: 500 });
  }
}
