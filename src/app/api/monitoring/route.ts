import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitorService } from '@/services/monitoring/performance-monitor';
import { PythonFramework } from '@/types/python-workspace';

// GET /api/monitoring - Get monitoring data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const action = searchParams.get('action') || 'metrics';
    const limit = parseInt(searchParams.get('limit') || '100');

    if (!workspaceId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'workspaceId is required',
        },
        { status: 400 }
      );
    }

    if (action === 'metrics') {
      // Get performance metrics
      const metrics = performanceMonitorService.getMetrics(workspaceId, limit);
      const latestMetrics = performanceMonitorService.getLatestMetrics(workspaceId);

      // Calculate trends
      const trends = {
        cpu: metrics.map(m => ({ timestamp: m.timestamp, value: m.cpu.usage })),
        memory: metrics.map(m => ({ timestamp: m.timestamp, value: m.memory.usage })),
        disk: metrics.map(m => ({ timestamp: m.timestamp, value: m.disk.usage })),
      };

      // Calculate averages
      const averages = {
        cpu: metrics.length > 0 ? metrics.reduce((sum, m) => sum + m.cpu.usage, 0) / metrics.length : 0,
        memory: metrics.length > 0 ? metrics.reduce((sum, m) => sum + m.memory.usage, 0) / metrics.length : 0,
        disk: metrics.length > 0 ? metrics.reduce((sum, m) => sum + m.disk.usage, 0) / metrics.length : 0,
      };

      return NextResponse.json({
        success: true,
        data: {
          latest: latestMetrics,
          history: metrics,
          trends,
          averages,
          summary: {
            dataPoints: metrics.length,
            timeRange: metrics.length > 0 ? {
              start: metrics[0].timestamp,
              end: metrics[metrics.length - 1].timestamp,
            } : null,
          },
        },
      });

    } else if (action === 'logs') {
      // Get logs
      const level = searchParams.get('level') as any;
      const source = searchParams.get('source');
      
      let logs = performanceMonitorService.getLogs(workspaceId, limit);
      
      // Filter by level if specified
      if (level) {
        logs = logs.filter(log => log.level === level);
      }
      
      // Filter by source if specified
      if (source) {
        logs = logs.filter(log => log.source.includes(source));
      }

      // Group logs by level
      const logsByLevel = logs.reduce((acc, log) => {
        if (!acc[log.level]) {
          acc[log.level] = [];
        }
        acc[log.level].push(log);
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate log statistics
      const stats = {
        total: logs.length,
        byLevel: {
          debug: logsByLevel.debug?.length || 0,
          info: logsByLevel.info?.length || 0,
          warning: logsByLevel.warning?.length || 0,
          error: logsByLevel.error?.length || 0,
          critical: logsByLevel.critical?.length || 0,
        },
        recentErrors: logs
          .filter(log => log.level === 'error' || log.level === 'critical')
          .slice(-10),
      };

      return NextResponse.json({
        success: true,
        data: {
          logs,
          logsByLevel,
          stats,
        },
      });

    } else if (action === 'alerts') {
      // Get alerts
      const includeResolved = searchParams.get('includeResolved') === 'true';
      const severity = searchParams.get('severity') as any;
      
      let alerts = performanceMonitorService.getAlerts(workspaceId, includeResolved);
      
      // Filter by severity if specified
      if (severity) {
        alerts = alerts.filter(alert => alert.severity === severity);
      }

      // Group alerts by severity and type
      const alertsBySeverity = alerts.reduce((acc, alert) => {
        if (!acc[alert.severity]) {
          acc[alert.severity] = [];
        }
        acc[alert.severity].push(alert);
        return acc;
      }, {} as Record<string, any[]>);

      const alertsByType = alerts.reduce((acc, alert) => {
        if (!acc[alert.type]) {
          acc[alert.type] = [];
        }
        acc[alert.type].push(alert);
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate alert statistics
      const stats = {
        total: alerts.length,
        active: alerts.filter(a => !a.resolved).length,
        resolved: alerts.filter(a => a.resolved).length,
        bySeverity: {
          low: alertsBySeverity.low?.length || 0,
          medium: alertsBySeverity.medium?.length || 0,
          high: alertsBySeverity.high?.length || 0,
          critical: alertsBySeverity.critical?.length || 0,
        },
        byType: {
          performance: alertsByType.performance?.length || 0,
          error: alertsByType.error?.length || 0,
          resource: alertsByType.resource?.length || 0,
          security: alertsByType.security?.length || 0,
        },
      };

      return NextResponse.json({
        success: true,
        data: {
          alerts,
          alertsBySeverity,
          alertsByType,
          stats,
        },
      });

    } else if (action === 'report') {
      // Generate performance report
      const startDate = searchParams.get('start');
      const endDate = searchParams.get('end');
      
      const period = {
        start: startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
        end: endDate ? new Date(endDate) : new Date(),
      };

      const report = performanceMonitorService.generateReport(workspaceId, period);

      return NextResponse.json({
        success: true,
        data: report,
      });

    } else if (action === 'status') {
      // Get monitoring status
      const latestMetrics = performanceMonitorService.getLatestMetrics(workspaceId);
      const activeAlerts = performanceMonitorService.getAlerts(workspaceId, false);
      const recentLogs = performanceMonitorService.getLogs(workspaceId, 10);

      const status = {
        isMonitoring: !!latestMetrics,
        lastUpdate: latestMetrics?.timestamp,
        health: {
          overall: activeAlerts.filter(a => a.severity === 'critical').length === 0 ? 'healthy' : 'critical',
          cpu: latestMetrics ? (latestMetrics.cpu.usage < 80 ? 'good' : 'warning') : 'unknown',
          memory: latestMetrics ? (latestMetrics.memory.usage < 85 ? 'good' : 'warning') : 'unknown',
          disk: latestMetrics ? (latestMetrics.disk.usage < 90 ? 'good' : 'warning') : 'unknown',
        },
        alerts: {
          total: activeAlerts.length,
          critical: activeAlerts.filter(a => a.severity === 'critical').length,
          high: activeAlerts.filter(a => a.severity === 'high').length,
        },
        logs: {
          total: recentLogs.length,
          errors: recentLogs.filter(l => l.level === 'error' || l.level === 'critical').length,
        },
      };

      return NextResponse.json({
        success: true,
        data: status,
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: metrics, logs, alerts, report, status',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error handling monitoring request:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Monitoring operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/monitoring - Start/stop monitoring or perform actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workspaceId, action, ...params } = body;

    if (!workspaceId || !action) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId and action are required',
        },
        { status: 400 }
      );
    }

    if (action === 'start') {
      // Start monitoring
      const { interval, retentionPeriod, alertThresholds } = params;
      
      performanceMonitorService.startMonitoring(workspaceId, {
        interval,
        retentionPeriod,
        alertThresholds,
      });

      return NextResponse.json({
        success: true,
        message: `Monitoring started for workspace ${workspaceId}`,
      });

    } else if (action === 'stop') {
      // Stop monitoring
      performanceMonitorService.stopMonitoring(workspaceId);

      return NextResponse.json({
        success: true,
        message: `Monitoring stopped for workspace ${workspaceId}`,
      });

    } else if (action === 'collectMetrics') {
      // Manually collect metrics
      const metrics = await performanceMonitorService.collectMetrics(workspaceId);

      return NextResponse.json({
        success: true,
        data: metrics,
        message: 'Metrics collected successfully',
      });

    } else if (action === 'collectAppMetrics') {
      // Collect application metrics
      const { framework, appName } = params;
      
      if (!framework || !appName) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'framework and appName are required for collectAppMetrics action',
          },
          { status: 400 }
        );
      }

      const metrics = await performanceMonitorService.collectApplicationMetrics(
        workspaceId,
        framework as PythonFramework,
        appName
      );

      return NextResponse.json({
        success: true,
        data: metrics,
        message: 'Application metrics collected successfully',
      });

    } else if (action === 'addLog') {
      // Add log entry
      const { level, source, message, context, stackTrace } = params;
      
      if (!level || !source || !message) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'level, source, and message are required for addLog action',
          },
          { status: 400 }
        );
      }

      const logEntry = performanceMonitorService.addLogEntry(
        workspaceId,
        level,
        source,
        message,
        context,
        stackTrace
      );

      return NextResponse.json({
        success: true,
        data: logEntry,
        message: 'Log entry added successfully',
      });

    } else if (action === 'resolveAlert') {
      // Resolve alert
      const { alertId } = params;
      
      if (!alertId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing alert ID',
            message: 'alertId is required for resolveAlert action',
          },
          { status: 400 }
        );
      }

      const resolved = performanceMonitorService.resolveAlert(workspaceId, alertId);
      
      if (!resolved) {
        return NextResponse.json(
          {
            success: false,
            error: 'Alert not found',
            message: `Alert ${alertId} not found`,
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: `Alert ${alertId} resolved successfully`,
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: start, stop, collectMetrics, collectAppMetrics, addLog, resolveAlert',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error performing monitoring action:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Monitoring action failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
