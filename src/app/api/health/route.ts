import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// GET /api/health - Health check endpoint for Docker containers
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    
    // Check Docker connection
    const dockerHealthy = await dockerService.ping();
    
    // Get system information if Docker is available
    let systemInfo = null;
    if (dockerHealthy) {
      try {
        systemInfo = await dockerService.getSystemInfo();
      } catch (error) {
        console.warn('Could not get Docker system info:', error);
      }
    }
    
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Determine overall health status
    const isHealthy = dockerHealthy;
    const status = isHealthy ? 'healthy' : 'unhealthy';
    const httpStatus = isHealthy ? 200 : 503;
    
    const healthData = {
      status,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        docker: {
          status: dockerHealthy ? 'healthy' : 'unhealthy',
          connected: dockerHealthy,
          ...(systemInfo && {
            containers: systemInfo.containers,
            containersRunning: systemInfo.containersRunning,
            images: systemInfo.images,
            serverVersion: systemInfo.serverVersion,
            architecture: systemInfo.architecture,
            operatingSystem: systemInfo.operatingSystem,
          }),
        },
        database: {
          status: 'not_implemented',
          message: 'Database health check not implemented yet',
        },
        guacamole: {
          status: 'not_implemented',
          message: 'Guacamole health check not implemented yet',
        },
      },
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
    };
    
    return NextResponse.json(healthData, { status: httpStatus });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
