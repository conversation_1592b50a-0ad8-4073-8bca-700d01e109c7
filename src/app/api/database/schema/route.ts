import { NextRequest, NextResponse } from 'next/server';
import { databaseIntegrationService } from '@/services/database/database-integration';
import { PythonFramework } from '@/types/python-workspace';

// GET /api/database/schema - Get database schema
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');
    const includeData = searchParams.get('includeData') === 'true';

    if (!connectionId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'connectionId is required',
        },
        { status: 400 }
      );
    }

    const connection = databaseIntegrationService.getConnection(connectionId);
    if (!connection) {
      return NextResponse.json(
        {
          success: false,
          error: 'Connection not found',
          message: `Database connection '${connectionId}' not found`,
        },
        { status: 404 }
      );
    }

    // Get database schema
    const schema = await databaseIntegrationService.getDatabaseSchema(connectionId);

    // Enhance schema with additional metadata
    const enhancedSchema = {
      ...schema,
      connection: {
        id: connection.id,
        name: connection.name,
        type: connection.type,
        database: connection.database,
      },
      statistics: {
        tableCount: schema.tables.length,
        viewCount: schema.views.length,
        functionCount: schema.functions.length,
        indexCount: schema.indexes.length,
        totalColumns: schema.tables.reduce((sum, table) => sum + table.columns.length, 0),
      },
      tables: schema.tables.map(table => ({
        ...table,
        // Include sample data if requested
        sampleData: includeData ? [] : undefined, // Would fetch actual sample data
        relationships: {
          incoming: schema.tables.filter(t => 
            t.foreignKeys.some(fk => fk.referencedTable === table.name)
          ).map(t => ({
            table: t.name,
            foreignKeys: t.foreignKeys.filter(fk => fk.referencedTable === table.name),
          })),
          outgoing: table.foreignKeys.map(fk => ({
            table: fk.referencedTable,
            foreignKey: fk,
          })),
        },
      })),
    };

    return NextResponse.json({
      success: true,
      data: enhancedSchema,
    });

  } catch (error) {
    console.error('Error getting database schema:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get schema',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/database/schema - Generate ORM models from schema
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      workspaceId,
      connectionId,
      framework,
      options = {},
    } = body;

    // Validate required fields
    if (!workspaceId || !connectionId || !framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId, connectionId, and framework are required',
        },
        { status: 400 }
      );
    }

    // Validate framework
    const supportedFrameworks: PythonFramework[] = ['django', 'flask', 'fastapi', 'streamlit', 'gradio'];
    if (!supportedFrameworks.includes(framework)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid framework',
          message: `Framework must be one of: ${supportedFrameworks.join(', ')}`,
        },
        { status: 400 }
      );
    }

    const connection = databaseIntegrationService.getConnection(connectionId);
    if (!connection) {
      return NextResponse.json(
        {
          success: false,
          error: 'Connection not found',
          message: `Database connection '${connectionId}' not found`,
        },
        { status: 404 }
      );
    }

    // Generate ORM models
    const ormConfig = await databaseIntegrationService.generateORMModels(
      workspaceId,
      connectionId,
      framework,
      {
        tables: options.tables,
        includeRelationships: options.includeRelationships !== false,
        generateMigrations: options.generateMigrations === true,
      }
    );

    // Prepare response data
    const responseData = {
      framework,
      database: {
        id: connection.id,
        name: connection.name,
        type: connection.type,
      },
      models: ormConfig.models.map(model => ({
        ...model,
        filePath: getModelFilePath(framework, model.name),
        preview: model.code.split('\n').slice(0, 20).join('\n') + (model.code.split('\n').length > 20 ? '\n...' : ''),
      })),
      migrations: ormConfig.migrations.map(migration => ({
        ...migration,
        filePath: getMigrationFilePath(framework, migration.name),
        preview: migration.code.split('\n').slice(0, 15).join('\n') + (migration.code.split('\n').length > 15 ? '\n...' : ''),
      })),
      settings: ormConfig.settings,
      statistics: {
        modelsGenerated: ormConfig.models.length,
        migrationsGenerated: ormConfig.migrations.length,
        totalFields: ormConfig.models.reduce((sum, model) => sum + model.fields.length, 0),
        totalRelationships: ormConfig.models.reduce((sum, model) => sum + model.relationships.length, 0),
      },
      nextSteps: getNextSteps(framework, ormConfig),
    };

    return NextResponse.json({
      success: true,
      data: responseData,
      message: `Generated ${ormConfig.models.length} models and ${ormConfig.migrations.length} migrations for ${framework}`,
    });

  } catch (error) {
    console.error('Error generating ORM models:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate ORM models',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getModelFilePath(framework: PythonFramework, modelName: string): string {
  switch (framework) {
    case 'django':
      return `models/${modelName.toLowerCase()}.py`;
    case 'flask':
    case 'fastapi':
      return `models/${modelName.toLowerCase()}.py`;
    default:
      return `models.py`;
  }
}

function getMigrationFilePath(framework: PythonFramework, migrationName: string): string {
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
  switch (framework) {
    case 'django':
      return `migrations/${timestamp}_${migrationName}.py`;
    case 'flask':
    case 'fastapi':
      return `alembic/versions/${timestamp}_${migrationName}.py`;
    default:
      return `migrations/${migrationName}.py`;
  }
}

function getNextSteps(framework: PythonFramework, ormConfig: any): string[] {
  const steps: string[] = [];

  switch (framework) {
    case 'django':
      steps.push(
        'Add the generated models to your Django app',
        'Update DATABASES setting in settings.py',
        'Run "python manage.py makemigrations" to create migrations',
        'Run "python manage.py migrate" to apply migrations',
        'Register models in admin.py if needed'
      );
      break;
    case 'flask':
      steps.push(
        'Install Flask-SQLAlchemy: pip install Flask-SQLAlchemy',
        'Configure database URI in your Flask app',
        'Import and register models with SQLAlchemy',
        'Run db.create_all() to create tables',
        'Consider using Flask-Migrate for migrations'
      );
      break;
    case 'fastapi':
      steps.push(
        'Install SQLAlchemy and database driver',
        'Configure database connection in your FastAPI app',
        'Import models and create database engine',
        'Use Alembic for database migrations',
        'Create Pydantic schemas for API serialization'
      );
      break;
    default:
      steps.push(
        'Review generated models and migrations',
        'Configure database connection',
        'Apply migrations to create database schema',
        'Test database connectivity'
      );
  }

  return steps;
}
