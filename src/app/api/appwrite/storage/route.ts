import { NextRequest, NextResponse } from 'next/server';
import { storageService } from '@/services/appwrite';
import { z } from 'zod';

// Validation schemas
const createBucketSchema = z.object({
  name: z.string().min(1, 'Bucket name is required'),
  bucketId: z.string().optional(),
  permissions: z.array(z.string()).optional(),
  fileSecurity: z.boolean().optional(),
  enabled: z.boolean().optional(),
  maximumFileSize: z.number().optional(),
  allowedFileExtensions: z.array(z.string()).optional(),
  compression: z.enum(['none', 'gzip', 'zstd']).optional(),
  encryption: z.boolean().optional(),
  antivirus: z.boolean().optional(),
});

const updateBucketSchema = z.object({
  bucketId: z.string().min(1, 'Bucket ID is required'),
  name: z.string().optional(),
  permissions: z.array(z.string()).optional(),
  fileSecurity: z.boolean().optional(),
  enabled: z.boolean().optional(),
  maximumFileSize: z.number().optional(),
  allowedFileExtensions: z.array(z.string()).optional(),
  compression: z.enum(['none', 'gzip', 'zstd']).optional(),
  encryption: z.boolean().optional(),
  antivirus: z.boolean().optional(),
});

const imageTransformSchema = z.object({
  width: z.number().optional(),
  height: z.number().optional(),
  gravity: z.enum(['center', 'top-left', 'top', 'top-right', 'left', 'right', 'bottom-left', 'bottom', 'bottom-right']).optional(),
  quality: z.number().min(0).max(100).optional(),
  borderWidth: z.number().optional(),
  borderColor: z.string().optional(),
  borderRadius: z.number().optional(),
  opacity: z.number().min(0).max(1).optional(),
  rotation: z.number().optional(),
  background: z.string().optional(),
  output: z.enum(['jpg', 'jpeg', 'png', 'gif', 'webp']).optional(),
});

// Helper functions
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

function handleResponse<T>(result: { success: boolean; data?: T; error?: any }) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString(),
    });
  } else {
    const statusCode = result.error?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Internal server error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        type: result.error?.type || 'server_error',
      },
      timestamp: new Date().toISOString(),
    }, { status: statusCode });
  }
}

function getPaginationParams(request: NextRequest) {
  const url = new URL(request.url);
  return {
    limit: parseInt(url.searchParams.get('limit') || '25'),
    offset: parseInt(url.searchParams.get('offset') || '0'),
    cursor: url.searchParams.get('cursor') || undefined,
    orderBy: url.searchParams.get('orderBy') || undefined,
    orderDirection: (url.searchParams.get('orderDirection') as 'asc' | 'desc') || 'asc',
  };
}

// POST /api/appwrite/storage - Create bucket or upload file
export async function POST(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);

    switch (action) {
      case 'bucket': {
        const body = await request.json();
        const validatedData = createBucketSchema.parse(body);
        const result = await storageService.createBucket(validatedData);
        return handleResponse(result);
      }

      case 'file': {
        const formData = await request.formData();
        const bucketId = formData.get('bucketId') as string;
        const fileId = formData.get('fileId') as string | null;
        const file = formData.get('file') as File;
        const permissions = formData.get('permissions') as string | null;

        if (!bucketId || !file) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID and file are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const uploadParams = {
          bucketId,
          fileId: fileId || undefined,
          file,
          permissions: permissions ? JSON.parse(permissions) : undefined,
        };

        const result = await storageService.uploadFile(uploadParams, sessionId || undefined);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.issues,
        }
      }, { status: 400 });
    }

    console.error('Storage POST API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// GET /api/appwrite/storage - Get bucket, file, or list items
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);
    const pagination = getPaginationParams(request);

    switch (action) {
      case 'buckets': {
        const result = await storageService.listBuckets(pagination);
        return handleResponse(result);
      }

      case 'bucket': {
        const bucketId = url.searchParams.get('bucketId');
        if (!bucketId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.getBucket(bucketId);
        return handleResponse(result);
      }

      case 'files': {
        const bucketId = url.searchParams.get('bucketId');
        if (!bucketId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.listFiles(bucketId, pagination, sessionId || undefined);
        return handleResponse(result);
      }

      case 'file': {
        const bucketId = url.searchParams.get('bucketId');
        const fileId = url.searchParams.get('fileId');
        
        if (!bucketId || !fileId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID and File ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.getFile(bucketId, fileId, sessionId || undefined);
        return handleResponse(result);
      }

      case 'file-download': {
        const bucketId = url.searchParams.get('bucketId');
        const fileId = url.searchParams.get('fileId');
        
        if (!bucketId || !fileId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID and File ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.getFileDownload(bucketId, fileId);
        return handleResponse(result);
      }

      case 'file-view': {
        const bucketId = url.searchParams.get('bucketId');
        const fileId = url.searchParams.get('fileId');
        
        if (!bucketId || !fileId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID and File ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.getFileView(bucketId, fileId);
        return handleResponse(result);
      }

      case 'file-preview': {
        const bucketId = url.searchParams.get('bucketId');
        const fileId = url.searchParams.get('fileId');
        
        if (!bucketId || !fileId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID and File ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        // Parse transform options
        const transformOptions: any = {};
        for (const [key, value] of url.searchParams.entries()) {
          if (key.startsWith('transform.')) {
            const transformKey = key.replace('transform.', '');
            if (transformKey === 'width' || transformKey === 'height' || transformKey === 'quality' || 
                transformKey === 'borderWidth' || transformKey === 'borderRadius' || transformKey === 'rotation') {
              transformOptions[transformKey] = parseInt(value);
            } else if (transformKey === 'opacity') {
              transformOptions[transformKey] = parseFloat(value);
            } else {
              transformOptions[transformKey] = value;
            }
          }
        }

        const validatedTransforms = Object.keys(transformOptions).length > 0 
          ? imageTransformSchema.parse(transformOptions) 
          : undefined;

        const result = await storageService.getFilePreview(bucketId, fileId, validatedTransforms);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.issues,
        }
      }, { status: 400 });
    }

    console.error('Storage GET API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// PUT /api/appwrite/storage - Update bucket
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const action = request.nextUrl.searchParams.get('action');

    switch (action) {
      case 'bucket': {
        const validatedData = updateBucketSchema.parse(body);
        const result = await storageService.updateBucket(validatedData);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.issues,
        }
      }, { status: 400 });
    }

    console.error('Storage PUT API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// DELETE /api/appwrite/storage - Delete bucket or file
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);

    switch (action) {
      case 'bucket': {
        const bucketId = url.searchParams.get('bucketId');
        if (!bucketId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.deleteBucket(bucketId);
        return handleResponse(result);
      }

      case 'file': {
        const bucketId = url.searchParams.get('bucketId');
        const fileId = url.searchParams.get('fileId');
        
        if (!bucketId || !fileId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Bucket ID and File ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await storageService.deleteFile(bucketId, fileId, sessionId || undefined);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Storage DELETE API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}
