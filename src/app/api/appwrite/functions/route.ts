import { NextRequest, NextResponse } from 'next/server';
import { functionsService } from '@/services/appwrite';
import { z } from 'zod';

// Validation schemas
const createFunctionSchema = z.object({
  name: z.string().min(1, 'Function name is required'),
  runtime: z.string().min(1, 'Runtime is required'),
  functionId: z.string().optional(),
  execute: z.array(z.string()).optional(),
  events: z.array(z.string()).optional(),
  schedule: z.string().optional(),
  timeout: z.number().optional(),
  enabled: z.boolean().optional(),
  logging: z.boolean().optional(),
  entrypoint: z.string().optional(),
  commands: z.string().optional(),
  installationId: z.string().optional(),
  providerRepositoryId: z.string().optional(),
  providerBranch: z.string().optional(),
  providerRootDirectory: z.string().optional(),
  providerSilentMode: z.boolean().optional(),
});

const updateFunctionSchema = z.object({
  functionId: z.string().min(1, 'Function ID is required'),
  name: z.string().optional(),
  runtime: z.string().optional(),
  execute: z.array(z.string()).optional(),
  events: z.array(z.string()).optional(),
  schedule: z.string().optional(),
  timeout: z.number().optional(),
  enabled: z.boolean().optional(),
  logging: z.boolean().optional(),
  entrypoint: z.string().optional(),
  commands: z.string().optional(),
  installationId: z.string().optional(),
  providerRepositoryId: z.string().optional(),
  providerBranch: z.string().optional(),
  providerRootDirectory: z.string().optional(),
  providerSilentMode: z.boolean().optional(),
});

const executeFunctionSchema = z.object({
  functionId: z.string().min(1, 'Function ID is required'),
  body: z.string().optional(),
  async: z.boolean().optional(),
  xpath: z.string().optional(),
  method: z.enum(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']).optional(),
  headers: z.record(z.string(), z.string()).optional(),
});

// Helper functions
function getSessionId(request: NextRequest): string | null {
  return request.headers.get('x-appwrite-session') || 
         request.cookies.get('appwrite-session')?.value || 
         null;
}

function handleResponse<T>(result: { success: boolean; data?: T; error?: any }) {
  if (result.success) {
    return NextResponse.json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString(),
    });
  } else {
    const statusCode = result.error?.statusCode || 500;
    return NextResponse.json({
      success: false,
      error: {
        message: result.error?.message || 'Internal server error',
        code: result.error?.code || 'UNKNOWN_ERROR',
        type: result.error?.type || 'server_error',
      },
      timestamp: new Date().toISOString(),
    }, { status: statusCode });
  }
}

function getPaginationParams(request: NextRequest) {
  const url = new URL(request.url);
  return {
    limit: parseInt(url.searchParams.get('limit') || '25'),
    offset: parseInt(url.searchParams.get('offset') || '0'),
    cursor: url.searchParams.get('cursor') || undefined,
    orderBy: url.searchParams.get('orderBy') || undefined,
    orderDirection: (url.searchParams.get('orderDirection') as 'asc' | 'desc') || 'asc',
  };
}

// POST /api/appwrite/functions - Create function, deployment, or execute function
export async function POST(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');
    const sessionId = getSessionId(request);

    switch (action) {
      case 'function': {
        const body = await request.json();
        const validatedData = createFunctionSchema.parse(body);
        const result = await functionsService.createFunction(validatedData);
        return handleResponse(result);
      }

      case 'deployment': {
        const formData = await request.formData();
        const functionId = formData.get('functionId') as string;
        const code = formData.get('code') as File;
        const activate = formData.get('activate') === 'true';
        const entrypoint = formData.get('entrypoint') as string | null;
        const commands = formData.get('commands') as string | null;

        if (!functionId || !code) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID and code are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const deploymentParams = {
          functionId,
          code,
          activate,
          entrypoint: entrypoint || undefined,
          commands: commands || undefined,
        };

        const result = await functionsService.createDeployment(deploymentParams);
        return handleResponse(result);
      }

      case 'execute': {
        const body = await request.json();
        const validatedData = executeFunctionSchema.parse(body);
        const result = await functionsService.executeFunction(validatedData, sessionId || undefined);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.issues,
        }
      }, { status: 400 });
    }

    console.error('Functions POST API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// GET /api/appwrite/functions - Get function, deployment, execution, or list items
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = request.nextUrl.searchParams.get('action');
    const pagination = getPaginationParams(request);

    switch (action) {
      case 'functions': {
        const result = await functionsService.listFunctions(pagination);
        return handleResponse(result);
      }

      case 'function': {
        const functionId = url.searchParams.get('functionId');
        if (!functionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.getFunction(functionId);
        return handleResponse(result);
      }

      case 'deployments': {
        const functionId = url.searchParams.get('functionId');
        if (!functionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.listDeployments(functionId, pagination);
        return handleResponse(result);
      }

      case 'deployment': {
        const functionId = url.searchParams.get('functionId');
        const deploymentId = url.searchParams.get('deploymentId');
        
        if (!functionId || !deploymentId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID and Deployment ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.getDeployment(functionId, deploymentId);
        return handleResponse(result);
      }

      case 'executions': {
        const functionId = url.searchParams.get('functionId');
        if (!functionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.listExecutions(functionId, pagination);
        return handleResponse(result);
      }

      case 'execution': {
        const functionId = url.searchParams.get('functionId');
        const executionId = url.searchParams.get('executionId');
        
        if (!functionId || !executionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID and Execution ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.getExecution(functionId, executionId);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Functions GET API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// PUT /api/appwrite/functions - Update function or deployment
export async function PUT(request: NextRequest) {
  try {
    const action = request.nextUrl.searchParams.get('action');

    switch (action) {
      case 'function': {
        const body = await request.json();
        const validatedData = updateFunctionSchema.parse(body);
        const result = await functionsService.updateFunction(validatedData);
        return handleResponse(result);
      }

      case 'deployment': {
        const url = new URL(request.url);
        const functionId = url.searchParams.get('functionId');
        const deploymentId = url.searchParams.get('deploymentId');
        
        if (!functionId || !deploymentId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID and Deployment ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.updateDeployment(functionId, deploymentId);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
          details: error.issues,
        }
      }, { status: 400 });
    }

    console.error('Functions PUT API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}

// DELETE /api/appwrite/functions - Delete function or deployment
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = request.nextUrl.searchParams.get('action');

    switch (action) {
      case 'function': {
        const functionId = url.searchParams.get('functionId');
        if (!functionId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID is required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.deleteFunction(functionId);
        return handleResponse(result);
      }

      case 'deployment': {
        const functionId = url.searchParams.get('functionId');
        const deploymentId = url.searchParams.get('deploymentId');
        
        if (!functionId || !deploymentId) {
          return NextResponse.json({
            success: false,
            error: { message: 'Function ID and Deployment ID are required', code: 'MISSING_PARAMETERS' }
          }, { status: 400 });
        }

        const result = await functionsService.deleteDeployment(functionId, deploymentId);
        return handleResponse(result);
      }

      default:
        return NextResponse.json({
          success: false,
          error: { message: 'Invalid action', code: 'INVALID_ACTION' }
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Functions DELETE API error:', error);
    return NextResponse.json({
      success: false,
      error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    }, { status: 500 });
  }
}
