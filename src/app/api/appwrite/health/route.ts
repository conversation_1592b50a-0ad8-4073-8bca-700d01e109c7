import { NextRequest, NextResponse } from 'next/server';
import { AppwriteServiceFactory } from '@/services/appwrite';
import { healthCheck } from '@/lib/appwrite-server';

// GET /api/appwrite/health - Health check for all Appwrite services
export async function GET(request: NextRequest) {
  try {
    const detailed = request.nextUrl.searchParams.get('detailed') === 'true';

    if (detailed) {
      // Detailed health check including individual service checks
      const [serviceHealth, serverHealth] = await Promise.all([
        AppwriteServiceFactory.healthCheckAll(),
        healthCheck()
      ]);

      return NextResponse.json({
        success: true,
        data: {
          overall: serviceHealth.overall && serverHealth.status === 'healthy',
          services: {
            ...serviceHealth.services,
            server: serverHealth.status === 'healthy'
          },
          serverDetails: serverHealth,
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.version,
        }
      });
    } else {
      // Quick health check
      const serviceHealth = await AppwriteServiceFactory.healthCheckAll();
      
      return NextResponse.json({
        success: true,
        data: {
          status: serviceHealth.overall ? 'healthy' : 'unhealthy',
          services: serviceHealth.services,
          timestamp: serviceHealth.timestamp,
        }
      });
    }
  } catch (error) {
    console.error('Health check API error:', error);
    return NextResponse.json({
      success: false,
      error: {
        message: 'Health check failed',
        code: 'HEALTH_CHECK_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}

// POST /api/appwrite/health - Test specific service health
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { service } = body;

    if (!service) {
      return NextResponse.json({
        success: false,
        error: { message: 'Service name is required', code: 'MISSING_PARAMETERS' }
      }, { status: 400 });
    }

    const services = AppwriteServiceFactory.getAllServices();
    
    if (!(service in services)) {
      return NextResponse.json({
        success: false,
        error: { message: 'Invalid service name', code: 'INVALID_SERVICE' }
      }, { status: 400 });
    }

    const serviceInstance = services[service as keyof typeof services];
    const isHealthy = await serviceInstance.healthCheck();

    return NextResponse.json({
      success: true,
      data: {
        service,
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        metrics: serviceInstance.getMetrics(),
      }
    });
  } catch (error) {
    console.error('Service health check API error:', error);
    return NextResponse.json({
      success: false,
      error: {
        message: 'Service health check failed',
        code: 'SERVICE_HEALTH_CHECK_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}
