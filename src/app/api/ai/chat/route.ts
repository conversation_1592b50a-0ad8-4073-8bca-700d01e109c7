import { openai } from '@ai-sdk/openai';
import { streamText, UIMessage, convertToModelMessages, tool, stepCountIs } from 'ai';
import { z } from 'zod';
import { createErrorResponse, withRetry } from '@/lib/ai-error-handler';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { messages }: { messages: UIMessage[] } = await req.json();

    // Validate that messages exist
    if (!messages || !Array.isArray(messages)) {
      return Response.json({ error: 'Invalid messages format', code: 'INVALID_INPUT' }, { status: 400 });
    }

    const result = await withRetry(async () => streamText({
      model: openai('gpt-4o-mini'), // Using gpt-4o-mini for cost efficiency
      messages: convertToModelMessages(messages),
      temperature: 0.7,
      stopWhen: stepCountIs(5), // Allow up to 5 steps for multi-step tool calls
      tools: {
        weather: tool({
          description: 'Get the weather in a location (fahrenheit)',
          inputSchema: z.object({
            location: z.string().describe('The location to get the weather for'),
          }),
          execute: async ({ location }) => {
            // Simulate weather API call
            const temperature = Math.round(Math.random() * (90 - 32) + 32);
            const conditions = ['sunny', 'cloudy', 'rainy', 'partly cloudy'][Math.floor(Math.random() * 4)];
            return {
              location,
              temperature,
              conditions,
              humidity: Math.round(Math.random() * 100),
            };
          },
        }),
        convertFahrenheitToCelsius: tool({
          description: 'Convert a temperature in fahrenheit to celsius',
          inputSchema: z.object({
            temperature: z
              .number()
              .describe('The temperature in fahrenheit to convert'),
          }),
          execute: async ({ temperature }) => {
            const celsius = Math.round((temperature - 32) * (5 / 9));
            return {
              fahrenheit: temperature,
              celsius,
            };
          },
        }),
        calculator: tool({
          description: 'Perform basic mathematical calculations',
          inputSchema: z.object({
            expression: z.string().describe('The mathematical expression to evaluate (e.g., "2 + 2", "10 * 5")'),
          }),
          execute: async ({ expression }) => {
            try {
              // Simple calculator - only allow basic operations for security
              const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
              const result = Function(`"use strict"; return (${sanitized})`)();
              return {
                expression: sanitized,
                result: result,
              };
            } catch (error) {
              return {
                expression,
                error: 'Invalid mathematical expression',
              };
            }
          },
        }),
        terminal: tool({
          description: 'Execute terminal commands in the workspace',
          inputSchema: z.object({
            command: z.string().describe('The terminal command to execute'),
            workingDirectory: z.string().optional().describe('Working directory for the command'),
          }),
          execute: async ({ command, workingDirectory = '/workspace' }) => {
            // Simulate terminal command execution
            // In a real implementation, this would execute commands in the Docker container
            return {
              command,
              workingDirectory,
              output: [`Simulated execution of: ${command}`, 'Command completed successfully'],
              exitCode: 0,
            };
          },
        }),
      },
    }));

    return result.toUIMessageStreamResponse();
  } catch (error) {
    return createErrorResponse(error);
  }
}
