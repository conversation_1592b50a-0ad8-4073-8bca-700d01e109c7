import { NextRequest, NextResponse } from 'next/server';
import { nodeReactAIAssistantService } from '@/services/node-react-ai-assistant';
import { NodeReactAIAssistantContext } from '@/types/node-react-ai-editor';

// POST /api/node-react-ai/debug - Get debugging assistance
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, error, stackTrace } = body;

    // Validate required fields
    if (!context || !error) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'context and error are required',
        },
        { status: 400 }
      );
    }

    // Validate context structure
    if (!context.workspaceId || !context.framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid context',
          message: 'context must include workspaceId and framework',
        },
        { status: 400 }
      );
    }

    // Get debugging help
    const debuggingHelp = await nodeReactAIAssistantService.getDebuggingHelp(
      context as NodeReactAIAssistantContext,
      error,
      stackTrace
    );

    return NextResponse.json({
      success: true,
      explanation: debuggingHelp.explanation,
      suggestions: debuggingHelp.suggestions,
    });
  } catch (error) {
    console.error('Error getting debugging help:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to get debugging help',
      },
      { status: 500 }
    );
  }
}
