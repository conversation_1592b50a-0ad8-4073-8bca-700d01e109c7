import { NextRequest, NextResponse } from 'next/server';
import { nodeReactAIAssistantService } from '@/services/node-react-ai-assistant';
import { NodeReactAIAssistantContext } from '@/types/node-react-ai-editor';

// POST /api/node-react-ai/generate - Generate code based on prompt
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, prompt, type = 'component' } = body;

    // Validate required fields
    if (!context || !prompt) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'context and prompt are required',
        },
        { status: 400 }
      );
    }

    // Validate context structure
    if (!context.workspaceId || !context.framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid context',
          message: 'context must include workspaceId and framework',
        },
        { status: 400 }
      );
    }

    // Validate type
    const validTypes = ['component', 'hook', 'api', 'test', 'config', 'utility'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid type',
          message: `type must be one of: ${validTypes.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Generate code
    const generation = await nodeReactAIAssistantService.generateCode(
      context as NodeReactAIAssistantContext,
      prompt,
      type
    );

    return NextResponse.json({
      success: true,
      generation,
    });
  } catch (error) {
    console.error('Error generating code:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to generate code',
      },
      { status: 500 }
    );
  }
}
