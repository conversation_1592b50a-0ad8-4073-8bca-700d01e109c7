import { NextRequest, NextResponse } from 'next/server';
import { nodeReactAIAssistantService } from '@/services/node-react-ai-assistant';
import { NodeReactAIAssistantContext } from '@/types/node-react-ai-editor';

// POST /api/node-react-ai/explain - Get AI explanation for code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, code } = body;

    // Validate required fields
    if (!context || !code) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'context and code are required',
        },
        { status: 400 }
      );
    }

    // Validate context structure
    if (!context.workspaceId || !context.framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid context',
          message: 'context must include workspaceId and framework',
        },
        { status: 400 }
      );
    }

    // Get explanation
    const explanation = await nodeReactAIAssistantService.explainCode(
      context as NodeReactAIAssistantContext,
      code
    );

    return NextResponse.json({
      success: true,
      explanation,
    });
  } catch (error) {
    console.error('Error explaining code:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to explain code',
      },
      { status: 500 }
    );
  }
}
