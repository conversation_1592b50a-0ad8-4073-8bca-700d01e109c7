import { NextRequest, NextResponse } from 'next/server';
import { nodeReactAIAssistantService } from '@/services/node-react-ai-assistant';
import { NodeReactAIAssistantContext } from '@/types/node-react-ai-editor';

// POST /api/node-react-ai/completions - Get AI code completions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, position, triggerCharacter } = body;

    // Validate required fields
    if (!context || !position) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'context and position are required',
        },
        { status: 400 }
      );
    }

    // Validate context structure
    if (!context.workspaceId || !context.framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid context',
          message: 'context must include workspaceId and framework',
        },
        { status: 400 }
      );
    }

    // Get AI completions
    const completions = await nodeReactAIAssistantService.getCodeCompletions(
      context as NodeReactAIAssistantContext,
      position,
      trigger<PERSON>haracter
    );

    return NextResponse.json({
      success: true,
      completions,
      total: completions.length,
    });
  } catch (error) {
    console.error('Error getting AI completions:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to get completions',
      },
      { status: 500 }
    );
  }
}
