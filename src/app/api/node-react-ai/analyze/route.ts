import { NextRequest, NextResponse } from 'next/server';
import { nodeReactAIAssistantService } from '@/services/node-react-ai-assistant';
import { NodeReactAIAssistantContext } from '@/types/node-react-ai-editor';

// POST /api/node-react-ai/analyze - Analyze code for issues and suggestions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, fileContent } = body;

    // Validate required fields
    if (!context || !fileContent) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'context and fileContent are required',
        },
        { status: 400 }
      );
    }

    // Validate context structure
    if (!context.workspaceId || !context.framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid context',
          message: 'context must include workspaceId and framework',
        },
        { status: 400 }
      );
    }

    // Analyze code
    const analysis = await nodeReactAIAssistantService.analyzeCode(
      context as NodeReactAIAssistantContext,
      fileContent
    );

    return NextResponse.json({
      success: true,
      analysis,
    });
  } catch (error) {
    console.error('Error analyzing code:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to analyze code',
      },
      { status: 500 }
    );
  }
}
