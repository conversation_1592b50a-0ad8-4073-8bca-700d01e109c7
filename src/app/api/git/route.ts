import { NextRequest, NextResponse } from 'next/server';
import { gitIntegrationService } from '@/services/git/git-integration';

// GET /api/git - Get Git repository status and information
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const repoPath = searchParams.get('repoPath') || '/home/<USER>';
    const action = searchParams.get('action') || 'status';

    if (!workspaceId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'workspaceId is required',
        },
        { status: 400 }
      );
    }

    if (action === 'status') {
      // Get repository status
      const status = await gitIntegrationService.getStatus(workspaceId, repoPath);
      
      return NextResponse.json({
        success: true,
        data: {
          status,
          summary: {
            branch: status.branch,
            clean: status.clean,
            totalChanges: status.staged.length + status.unstaged.length + status.untracked.length,
            staged: status.staged.length,
            unstaged: status.unstaged.length,
            untracked: status.untracked.length,
            conflicts: status.conflicted.length,
            ahead: status.ahead,
            behind: status.behind,
          },
        },
      });

    } else if (action === 'log') {
      // Get commit history
      const limit = parseInt(searchParams.get('limit') || '50');
      const skip = parseInt(searchParams.get('skip') || '0');
      const branch = searchParams.get('branch') || undefined;
      const author = searchParams.get('author') || undefined;

      const log = await gitIntegrationService.getLog(workspaceId, repoPath, {
        limit,
        skip,
        branch,
        author,
      });

      return NextResponse.json({
        success: true,
        data: log,
      });

    } else if (action === 'branches') {
      // List branches
      const includeRemote = searchParams.get('includeRemote') === 'true';
      
      const branches = await gitIntegrationService.listBranches(workspaceId, repoPath, {
        includeRemote,
      });

      const currentBranch = branches.find(b => b.current);
      const localBranches = branches.filter(b => !b.remote);
      const remoteBranches = branches.filter(b => b.remote);

      return NextResponse.json({
        success: true,
        data: {
          branches,
          currentBranch,
          localBranches,
          remoteBranches,
          summary: {
            total: branches.length,
            local: localBranches.length,
            remote: remoteBranches.length,
            current: currentBranch?.name || 'unknown',
          },
        },
      });

    } else if (action === 'diff') {
      // Get file diff
      const staged = searchParams.get('staged') === 'true';
      const file = searchParams.get('file') || undefined;
      const commit1 = searchParams.get('commit1') || undefined;
      const commit2 = searchParams.get('commit2') || undefined;

      const diffs = await gitIntegrationService.getDiff(workspaceId, repoPath, {
        staged,
        file,
        commit1,
        commit2,
      });

      return NextResponse.json({
        success: true,
        data: {
          diffs,
          summary: {
            filesChanged: diffs.length,
            additions: diffs.reduce((sum, diff) => 
              sum + diff.hunks.reduce((hunkSum, hunk) => 
                hunkSum + hunk.lines.filter(line => line.type === 'addition').length, 0
              ), 0
            ),
            deletions: diffs.reduce((sum, diff) => 
              sum + diff.hunks.reduce((hunkSum, hunk) => 
                hunkSum + hunk.lines.filter(line => line.type === 'deletion').length, 0
              ), 0
            ),
          },
        },
      });

    } else if (action === 'repositories') {
      // List all repositories
      const repositories = gitIntegrationService.getRepositories();
      
      return NextResponse.json({
        success: true,
        data: {
          repositories,
          summary: {
            total: repositories.length,
            initialized: repositories.filter(r => r.isInitialized).length,
            withRemote: repositories.filter(r => r.hasRemote).length,
          },
        },
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: status, log, branches, diff, repositories',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error handling Git request:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Git operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/git - Perform Git operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workspaceId, action, repoPath = '/home/<USER>', ...params } = body;

    if (!workspaceId || !action) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId and action are required',
        },
        { status: 400 }
      );
    }

    if (action === 'init') {
      // Initialize repository
      const { initialBranch, remoteUrl, userName, userEmail } = params;
      
      const repository = await gitIntegrationService.initRepository(workspaceId, repoPath, {
        initialBranch,
        remoteUrl,
        userName,
        userEmail,
      });

      return NextResponse.json({
        success: true,
        data: repository,
        message: `Git repository initialized at ${repoPath}`,
      });

    } else if (action === 'clone') {
      // Clone repository
      const { remoteUrl, destinationPath, branch, depth, recursive } = params;
      
      if (!remoteUrl || !destinationPath) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'remoteUrl and destinationPath are required for clone action',
          },
          { status: 400 }
        );
      }

      const repository = await gitIntegrationService.cloneRepository(
        workspaceId,
        remoteUrl,
        destinationPath,
        { branch, depth, recursive }
      );

      return NextResponse.json({
        success: true,
        data: repository,
        message: `Repository cloned to ${destinationPath}`,
      });

    } else if (action === 'stage') {
      // Stage files
      const { files } = params;
      
      if (!files || !Array.isArray(files)) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing files',
            message: 'files array is required for stage action',
          },
          { status: 400 }
        );
      }

      await gitIntegrationService.stageFiles(workspaceId, repoPath, files);

      return NextResponse.json({
        success: true,
        message: `${files.length} file(s) staged successfully`,
      });

    } else if (action === 'unstage') {
      // Unstage files
      const { files } = params;
      
      if (!files || !Array.isArray(files)) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing files',
            message: 'files array is required for unstage action',
          },
          { status: 400 }
        );
      }

      await gitIntegrationService.unstageFiles(workspaceId, repoPath, files);

      return NextResponse.json({
        success: true,
        message: `${files.length} file(s) unstaged successfully`,
      });

    } else if (action === 'commit') {
      // Commit changes
      const { message, amend, signOff, author } = params;
      
      if (!message) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing commit message',
            message: 'message is required for commit action',
          },
          { status: 400 }
        );
      }

      const commit = await gitIntegrationService.commit(workspaceId, repoPath, message, {
        amend,
        signOff,
        author,
      });

      return NextResponse.json({
        success: true,
        data: commit,
        message: `Changes committed: ${commit.shortHash}`,
      });

    } else if (action === 'push') {
      // Push changes
      const { remote, branch, force, setUpstream } = params;

      await gitIntegrationService.push(workspaceId, repoPath, {
        remote,
        branch,
        force,
        setUpstream,
      });

      return NextResponse.json({
        success: true,
        message: 'Changes pushed successfully',
      });

    } else if (action === 'pull') {
      // Pull changes
      const { remote, branch, rebase } = params;

      await gitIntegrationService.pull(workspaceId, repoPath, {
        remote,
        branch,
        rebase,
      });

      return NextResponse.json({
        success: true,
        message: 'Changes pulled successfully',
      });

    } else if (action === 'createBranch') {
      // Create branch
      const { branchName, checkout, startPoint } = params;
      
      if (!branchName) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing branch name',
            message: 'branchName is required for createBranch action',
          },
          { status: 400 }
        );
      }

      await gitIntegrationService.createBranch(workspaceId, repoPath, branchName, {
        checkout,
        startPoint,
      });

      return NextResponse.json({
        success: true,
        message: `Branch '${branchName}' created successfully`,
      });

    } else if (action === 'checkout') {
      // Checkout branch
      const { branchName } = params;
      
      if (!branchName) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing branch name',
            message: 'branchName is required for checkout action',
          },
          { status: 400 }
        );
      }

      await gitIntegrationService.checkoutBranch(workspaceId, repoPath, branchName);

      return NextResponse.json({
        success: true,
        message: `Switched to branch '${branchName}'`,
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: init, clone, stage, unstage, commit, push, pull, createBranch, checkout',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error performing Git operation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Git operation failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
