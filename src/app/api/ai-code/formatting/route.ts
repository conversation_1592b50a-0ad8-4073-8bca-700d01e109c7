import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { AICodeServiceFactory } from '@/services/ai-code';
import { handleAIError } from '@/lib/ai-error-handler';

// Request validation schema
const FormattingRequestSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  language: z.string().min(1, 'Language is required'),
  options: z.object({
    indentSize: z.number().min(1).max(8).default(2),
    indentType: z.enum(['spaces', 'tabs']).default('spaces'),
    maxLineLength: z.number().min(40).max(200).default(100),
    insertFinalNewline: z.boolean().default(true),
    trimTrailingWhitespace: z.boolean().default(true),
    semicolons: z.boolean().default(true),
    singleQuotes: z.boolean().default(true),
    trailingCommas: z.boolean().default(true),
    bracketSpacing: z.boolean().default(true),
    arrowParens: z.enum(['avoid', 'always']).default('avoid'),
  }).optional(),
  userId: z.string().optional(),
  workspaceId: z.string().optional(),
});

const FormattingSuggestionsRequestSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  language: z.string().min(1, 'Language is required'),
  userId: z.string().optional(),
  workspaceId: z.string().optional(),
});

// Response helper
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status });
}

// POST /api/ai-code/formatting - Format code
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request
    const validatedData = FormattingRequestSchema.parse(body);
    
    // Get Code Formatting service
    const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
    
    // Create request context
    const context = {
      userId: validatedData.userId,
      workspaceId: validatedData.workspaceId,
      language: validatedData.language,
      timestamp: new Date(),
    };
    
    // Format code
    const result = await codeFormattingService.formatCode(
      validatedData.code,
      validatedData.language,
      validatedData.options || {},
      context
    );
    
    if (result.success) {
      return createResponse({
        success: true,
        data: {
          formattedCode: result.data?.formattedCode,
          edits: result.data?.edits,
          suggestions: result.data?.suggestions,
          metadata: result.metadata,
        },
      });
    } else {
      return createResponse({
        success: false,
        error: result.error,
      }, 500);
    }
  } catch (error) {
    console.error('Code Formatting Error:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return createResponse({
        success: false,
        error: {
          message: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
      }, 400);
    }
    
    // Handle AI errors
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}

// GET /api/ai-code/formatting/suggestions - Get formatting suggestions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const language = searchParams.get('language');
    const userId = searchParams.get('userId');
    const workspaceId = searchParams.get('workspaceId');
    
    if (!code || !language) {
      return createResponse({
        success: false,
        error: {
          message: 'Code and language parameters are required',
          code: 'MISSING_PARAMETERS',
        },
      }, 400);
    }
    
    // Validate with schema
    const validatedData = FormattingSuggestionsRequestSchema.parse({
      code: decodeURIComponent(code),
      language,
      userId: userId || undefined,
      workspaceId: workspaceId || undefined,
    });
    
    // Get Code Formatting service
    const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
    
    // Create request context
    const context = {
      userId: validatedData.userId,
      workspaceId: validatedData.workspaceId,
      language: validatedData.language,
      timestamp: new Date(),
    };
    
    // Get formatting suggestions
    const result = await codeFormattingService.getFormattingSuggestions(
      validatedData.code,
      validatedData.language,
      context
    );
    
    if (result.success) {
      return createResponse({
        success: true,
        data: {
          suggestions: result.data,
          metadata: result.metadata,
        },
      });
    } else {
      return createResponse({
        success: false,
        error: result.error,
      }, 500);
    }
  } catch (error) {
    console.error('Formatting Suggestions Error:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return createResponse({
        success: false,
        error: {
          message: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
      }, 400);
    }
    
    // Handle AI errors
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}

// GET /api/ai-code/formatting/options - Get default formatting options for language
export async function OPTIONS(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const language = searchParams.get('language');
    
    if (!language) {
      return createResponse({
        success: false,
        error: {
          message: 'Language parameter is required',
          code: 'MISSING_PARAMETERS',
        },
      }, 400);
    }
    
    // Get Code Formatting service
    const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
    
    // Get default options
    const defaultOptions = codeFormattingService.getDefaultOptions(language);
    
    if (defaultOptions) {
      return createResponse({
        success: true,
        data: {
          language,
          defaultOptions,
          supportedLanguages: codeFormattingService.getSupportedLanguages(),
        },
      });
    } else {
      return createResponse({
        success: false,
        error: {
          message: `Unsupported language: ${language}`,
          code: 'UNSUPPORTED_LANGUAGE',
        },
      }, 400);
    }
  } catch (error) {
    console.error('Formatting Options Error:', error);
    
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}
