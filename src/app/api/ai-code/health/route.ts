import { NextRequest, NextResponse } from 'next/server';
import { AICodeServiceFactory } from '@/services/ai-code';

// Response helper
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status });
}

// GET /api/ai-code/health - Health check for all AI code services
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    
    // Get health status for all services
    const healthStatus = await AICodeServiceFactory.healthCheck();
    
    if (detailed) {
      // Get detailed information about each service
      const aiCodeService = AICodeServiceFactory.getAICodeService();
      const codeAnalysisService = AICodeServiceFactory.getCodeAnalysisService();
      const aiModelService = AICodeServiceFactory.getAIModelService();
      const codeFormattingService = AICodeServiceFactory.getCodeFormattingService();
      
      return createResponse({
        success: true,
        data: {
          overall: healthStatus.overall,
          services: {
            aiCode: {
              healthy: healthStatus.aiCode,
              config: aiCodeService.getConfig(),
              metrics: aiCodeService.getMetrics(),
            },
            codeAnalysis: {
              healthy: healthStatus.codeAnalysis,
              supportedLanguages: codeAnalysisService.getSupportedLanguages(),
              metrics: codeAnalysisService.getMetrics(),
            },
            aiModel: {
              healthy: healthStatus.aiModel,
              availableModels: aiModelService.getAvailableModels(),
              modelCapabilities: aiModelService.getAvailableModels().reduce((acc, model) => {
                acc[model] = aiModelService.getModelCapabilities(model);
                return acc;
              }, {} as any),
              metrics: aiModelService.getMetrics(),
            },
            codeFormatting: {
              healthy: healthStatus.codeFormatting,
              supportedLanguages: codeFormattingService.getSupportedLanguages(),
              metrics: codeFormattingService.getMetrics(),
            },
          },
          timestamp: new Date().toISOString(),
        },
      });
    } else {
      // Simple health check
      return createResponse({
        success: true,
        data: {
          ...healthStatus,
          timestamp: new Date().toISOString(),
        },
      });
    }
  } catch (error) {
    console.error('AI Code Services Health Check Error:', error);
    
    return createResponse({
      success: false,
      error: {
        message: 'Health check failed',
        code: 'HEALTH_CHECK_ERROR',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
    }, 500);
  }
}
