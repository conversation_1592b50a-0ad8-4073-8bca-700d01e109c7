import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { AICodeServiceFactory } from '@/services/ai-code';
import { handleAIError } from '@/lib/ai-error-handler';

// Request validation schema
const AnalysisRequestSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  language: z.string().min(1, 'Language is required'),
  fileName: z.string().optional(),
  includeStyle: z.boolean().default(true),
  includePerformance: z.boolean().default(true),
  includeSecurity: z.boolean().default(true),
  userId: z.string().optional(),
  workspaceId: z.string().optional(),
});

// Response helper
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status });
}

// POST /api/ai-code/analysis - Analyze code for errors and suggestions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request
    const validatedData = AnalysisRequestSchema.parse(body);
    
    // Get services
    const aiCodeService = AICodeServiceFactory.getAICodeService();
    const codeAnalysisService = AICodeServiceFactory.getCodeAnalysisService();
    
    // Create request context
    const context = {
      userId: validatedData.userId,
      workspaceId: validatedData.workspaceId,
      language: validatedData.language,
      fileName: validatedData.fileName,
      timestamp: new Date(),
    };
    
    // Run both AI analysis and static analysis in parallel
    const [aiAnalysisResult, staticAnalysisResult] = await Promise.all([
      aiCodeService.analyzeCode(
        {
          code: validatedData.code,
          language: validatedData.language,
          fileName: validatedData.fileName,
          includeStyle: validatedData.includeStyle,
          includePerformance: validatedData.includePerformance,
          includeSecurity: validatedData.includeSecurity,
        },
        context
      ),
      codeAnalysisService.analyzeCodeStructure(
        validatedData.code,
        validatedData.language,
        context
      ),
    ]);
    
    // Combine results
    const combinedErrors = [
      ...(aiAnalysisResult.success ? aiAnalysisResult.data?.errors || [] : []),
      ...(staticAnalysisResult.success ? staticAnalysisResult.data?.errors || [] : []),
    ];
    
    const combinedSuggestions = [
      ...(aiAnalysisResult.success ? aiAnalysisResult.data?.suggestions || [] : []),
      ...(staticAnalysisResult.success ? staticAnalysisResult.data?.suggestions || [] : []),
    ];
    
    const metrics = aiAnalysisResult.success 
      ? aiAnalysisResult.data?.metrics 
      : staticAnalysisResult.success 
        ? staticAnalysisResult.data?.metrics 
        : {
            complexity: 0,
            maintainability: 0,
            readability: 0,
            performance: 0,
            security: 0,
          };
    
    return createResponse({
      success: true,
      data: {
        errors: combinedErrors,
        suggestions: combinedSuggestions,
        metrics,
        metadata: {
          aiAnalysis: aiAnalysisResult.metadata,
          staticAnalysis: staticAnalysisResult.metadata,
        },
        aiMetadata: aiAnalysisResult.aiMetadata,
      },
    });
  } catch (error) {
    console.error('AI Code Analysis Error:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return createResponse({
        success: false,
        error: {
          message: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
      }, 400);
    }
    
    // Handle AI errors
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}

// GET /api/ai-code/analysis/symbols - Parse code symbols
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const language = searchParams.get('language');
    const userId = searchParams.get('userId');
    const workspaceId = searchParams.get('workspaceId');
    
    if (!code || !language) {
      return createResponse({
        success: false,
        error: {
          message: 'Code and language parameters are required',
          code: 'MISSING_PARAMETERS',
        },
      }, 400);
    }
    
    // Get code analysis service
    const codeAnalysisService = AICodeServiceFactory.getCodeAnalysisService();
    
    // Create request context
    const context = {
      userId: userId || undefined,
      workspaceId: workspaceId || undefined,
      language,
      timestamp: new Date(),
    };
    
    // Parse code symbols
    const result = await codeAnalysisService.parseCodeSymbols(
      decodeURIComponent(code),
      language,
      context
    );
    
    if (result.success) {
      return createResponse({
        success: true,
        data: result.data,
        metadata: result.metadata,
      });
    } else {
      return createResponse({
        success: false,
        error: result.error,
      }, 500);
    }
  } catch (error) {
    console.error('Code Symbol Parsing Error:', error);
    
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}
