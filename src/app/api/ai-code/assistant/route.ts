import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { AICodeServiceFactory } from '@/services/ai-code';
import { handleAIError } from '@/lib/ai-error-handler';

// Request validation schema
const AssistantRequestSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  codeContext: z.object({
    file: z.string(),
    selection: z.object({
      range: z.object({
        start: z.object({
          line: z.number(),
          column: z.number(),
        }),
        end: z.object({
          line: z.number(),
          column: z.number(),
        }),
      }),
      text: z.string(),
    }).optional(),
    fullCode: z.string().optional(),
  }).optional(),
  conversationHistory: z.array(z.object({
    id: z.string(),
    type: z.enum(['user', 'assistant', 'system']),
    content: z.string(),
    timestamp: z.string(),
    codeContext: z.any().optional(),
  })).optional(),
  userId: z.string().optional(),
  workspaceId: z.string().optional(),
});

// Response helper
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status });
}

// POST /api/ai-code/assistant - Generate AI assistant response
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request
    const validatedData = AssistantRequestSchema.parse(body);
    
    // Get AI Code service
    const aiCodeService = AICodeServiceFactory.getAICodeService();
    
    // Create request context
    const context = {
      userId: validatedData.userId,
      workspaceId: validatedData.workspaceId,
      timestamp: new Date(),
    };
    
    // Convert conversation history
    const conversationHistory = validatedData.conversationHistory?.map(msg => ({
      ...msg,
      timestamp: new Date(msg.timestamp),
    }));
    
    // Generate assistant response
    const result = await aiCodeService.generateAssistantResponse(
      {
        message: validatedData.message,
        codeContext: validatedData.codeContext,
        conversationHistory,
      },
      context
    );
    
    if (result.success) {
      return createResponse({
        success: true,
        data: {
          message: result.data,
          metadata: result.metadata,
          aiMetadata: result.aiMetadata,
        },
      });
    } else {
      return createResponse({
        success: false,
        error: result.error,
      }, 500);
    }
  } catch (error) {
    console.error('AI Assistant Error:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return createResponse({
        success: false,
        error: {
          message: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
      }, 400);
    }
    
    // Handle AI errors
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}
