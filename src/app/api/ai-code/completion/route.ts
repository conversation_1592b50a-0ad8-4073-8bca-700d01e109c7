import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { AICodeServiceFactory } from '@/services/ai-code';
import { handleAIError } from '@/lib/ai-error-handler';

// Request validation schema
const CompletionRequestSchema = z.object({
  code: z.string().min(1, 'Code is required'),
  position: z.object({
    line: z.number().min(1),
    column: z.number().min(0),
  }),
  language: z.string().min(1, 'Language is required'),
  context: z.object({
    fileName: z.string().optional(),
    projectContext: z.string().optional(),
    recentChanges: z.array(z.string()).optional(),
  }).optional(),
  userId: z.string().optional(),
  workspaceId: z.string().optional(),
});

// Response helper
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status });
}

// POST /api/ai-code/completion - Generate code completions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request
    const validatedData = CompletionRequestSchema.parse(body);
    
    // Get AI Code service
    const aiCodeService = AICodeServiceFactory.getAICodeService();
    
    // Create request context
    const context = {
      userId: validatedData.userId,
      workspaceId: validatedData.workspaceId,
      language: validatedData.language,
      timestamp: new Date(),
    };
    
    // Generate completions
    const result = await aiCodeService.generateCompletions(
      {
        code: validatedData.code,
        position: validatedData.position,
        language: validatedData.language,
        context: validatedData.context,
      },
      context
    );
    
    if (result.success) {
      return createResponse({
        success: true,
        data: {
          suggestions: result.data,
          metadata: result.metadata,
          aiMetadata: result.aiMetadata,
        },
      });
    } else {
      return createResponse({
        success: false,
        error: result.error,
      }, 500);
    }
  } catch (error) {
    console.error('AI Code Completion Error:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return createResponse({
        success: false,
        error: {
          message: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors,
        },
      }, 400);
    }
    
    // Handle AI errors
    const aiError = handleAIError(error);
    return createResponse({
      success: false,
      error: aiError,
    }, 500);
  }
}

// GET /api/ai-code/completion/health - Health check
export async function GET() {
  try {
    const aiCodeService = AICodeServiceFactory.getAICodeService();
    const isHealthy = await aiCodeService.healthCheck();
    
    return createResponse({
      success: true,
      data: {
        healthy: isHealthy,
        service: 'ai-code-completion',
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Health check error:', error);
    return createResponse({
      success: false,
      error: {
        message: 'Health check failed',
        code: 'HEALTH_CHECK_ERROR',
      },
    }, 500);
  }
}
