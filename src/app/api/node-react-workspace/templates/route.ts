import { NextRequest, NextResponse } from 'next/server';
import { NODE_REACT_PROJECT_TEMPLATES } from '@/data/node-react-templates';

// GET /api/node-react-workspace/templates - Get all Node/React templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework');
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    let filteredTemplates = [...NODE_REACT_PROJECT_TEMPLATES];

    // Filter by framework
    if (framework && framework !== 'all') {
      filteredTemplates = filteredTemplates.filter(template => template.framework === framework);
    }

    // Filter by category
    if (category && category !== 'all') {
      const categoryFrameworks = {
        frontend: ['react', 'vue', 'angular', 'svelte'],
        fullstack: ['nextjs', 'nuxt', 'remix', 'gatsby'],
        backend: ['express', 'nestjs'],
      };
      
      const frameworks = categoryFrameworks[category as keyof typeof categoryFrameworks] || [];
      filteredTemplates = filteredTemplates.filter(template => frameworks.includes(template.framework));
    }

    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase();
      filteredTemplates = filteredTemplates.filter(template =>
        template.name.toLowerCase().includes(searchLower) ||
        template.description.toLowerCase().includes(searchLower) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
        template.features.some(feature => feature.toLowerCase().includes(searchLower))
      );
    }

    return NextResponse.json({
      success: true,
      data: filteredTemplates,
      total: filteredTemplates.length,
    });
  } catch (error) {
    console.error('Error fetching Node/React templates:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch templates',
      },
      { status: 500 }
    );
  }
}

// POST /api/node-react-workspace/templates - Create custom template (future feature)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, framework, description, files, dependencies, devDependencies } = body;

    // Validate required fields
    if (!name || !framework || !description) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'name, framework, and description are required',
        },
        { status: 400 }
      );
    }

    // TODO: Implement custom template creation
    // This would involve:
    // 1. Validating the template structure
    // 2. Storing the template in a database
    // 3. Making it available for project creation

    return NextResponse.json(
      {
        success: false,
        error: 'Not implemented',
        message: 'Custom template creation is not yet implemented',
      },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error creating custom template:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create template',
      },
      { status: 500 }
    );
  }
}
