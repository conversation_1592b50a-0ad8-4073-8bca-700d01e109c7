import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';
import { nodeReactLivePreviewService } from '@/services/node-react-live-preview';
import { NodeFramework } from '@/types/node-react-workspace';

// POST /api/node-react-workspace/workspaces/[workspaceId]/preview/start - Start live preview
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { 
      projectPath, 
      framework, 
      port, 
      command, 
      environment = {},
      hotReload = true,
      buildMode = 'development'
    } = body;

    // Validate required fields
    if (!projectPath || !framework) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'projectPath and framework are required',
        },
        { status: 400 }
      );
    }

    // Validate framework
    const validFrameworks: NodeFramework[] = [
      'nextjs', 'react', 'express', 'nestjs', 'vue', 'angular', 
      'nuxt', 'svelte', 'remix', 'gatsby'
    ];
    
    if (!validFrameworks.includes(framework)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid framework',
          message: `Framework must be one of: ${validFrameworks.join(', ')}`,
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Check if project path exists
    const pathCheckResult = await dockerService.executeCommand(
      workspaceId,
      `test -d "${projectPath}" && echo "exists" || echo "not found"`
    );
    
    if (!pathCheckResult.stdout.includes('exists')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Project path not found',
          message: `Project path '${projectPath}' does not exist in workspace`,
        },
        { status: 404 }
      );
    }

    // Check if package.json exists
    const packageJsonResult = await dockerService.executeCommand(
      workspaceId,
      `test -f "${projectPath}/package.json" && echo "exists" || echo "not found"`
    );
    
    if (!packageJsonResult.stdout.includes('exists')) {
      return NextResponse.json(
        {
          success: false,
          error: 'No package.json found',
          message: `No package.json found in '${projectPath}'. Make sure this is a Node.js project.`,
        },
        { status: 400 }
      );
    }

    // Start live preview
    const preview = await nodeReactLivePreviewService.startPreview(workspaceId, {
      projectPath,
      framework,
      port,
      command,
      environment,
      hotReload,
      buildMode,
    });

    return NextResponse.json({
      success: true,
      data: preview,
      message: `Live preview started for ${framework} project`,
    });
  } catch (error) {
    console.error('Error starting live preview:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to start live preview',
      },
      { status: 500 }
    );
  }
}

// GET /api/node-react-workspace/workspaces/[workspaceId]/preview/start - Get preview configuration
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const framework = searchParams.get('framework') as NodeFramework;

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get available ports
    const commonPorts = [3000, 3001, 4200, 5000, 5173, 8000, 8080, 8888, 9000];
    const availablePorts: number[] = [];
    
    for (const port of commonPorts) {
      try {
        const result = await dockerService.executeCommand(
          workspaceId, 
          `netstat -tuln | grep :${port} || echo "available"`
        );
        if (result.stdout.includes('available')) {
          availablePorts.push(port);
        }
      } catch (error) {
        availablePorts.push(port);
      }
    }

    // Get framework-specific configuration
    const frameworkConfig = framework ? {
      defaultPort: getDefaultPort(framework),
      defaultCommand: getDefaultCommand(framework),
      supportedFeatures: getSupportedFeatures(framework),
    } : null;

    // Get existing previews
    const existingPreviews = nodeReactLivePreviewService.getWorkspacePreviews(workspaceId);

    const config = {
      workspaceId,
      supportedFrameworks: [
        'nextjs', 'react', 'express', 'nestjs', 'vue', 
        'angular', 'nuxt', 'svelte', 'remix', 'gatsby'
      ],
      availablePorts,
      existingPreviews: existingPreviews.map(p => ({
        framework: p.framework,
        port: p.port,
        status: p.status,
        url: p.url,
      })),
      frameworkConfig,
      defaultSettings: {
        hotReload: true,
        buildMode: 'development',
        environment: {
          NODE_ENV: 'development',
        },
      },
    };

    return NextResponse.json({
      success: true,
      data: config,
    });
  } catch (error) {
    console.error('Error getting preview configuration:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to get preview configuration',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getDefaultPort(framework: NodeFramework): number {
  const ports: Record<NodeFramework, number> = {
    nextjs: 3000,
    react: 5173,
    express: 3001,
    nestjs: 3000,
    vue: 5173,
    angular: 4200,
    nuxt: 3000,
    svelte: 5173,
    remix: 3000,
    gatsby: 8000,
  };
  return ports[framework];
}

function getDefaultCommand(framework: NodeFramework): string {
  const commands: Record<NodeFramework, string> = {
    nextjs: 'npm run dev',
    react: 'npm run dev',
    express: 'npm run dev',
    nestjs: 'npm run dev',
    vue: 'npm run dev',
    angular: 'ng serve --host 0.0.0.0',
    nuxt: 'npm run dev',
    svelte: 'npm run dev',
    remix: 'npm run dev',
    gatsby: 'npm run develop',
  };
  return commands[framework];
}

function getSupportedFeatures(framework: NodeFramework): string[] {
  const features: Record<NodeFramework, string[]> = {
    nextjs: ['hot-reload', 'ssr', 'api-routes', 'image-optimization'],
    react: ['hot-reload', 'fast-refresh', 'jsx'],
    express: ['hot-reload', 'api', 'middleware'],
    nestjs: ['hot-reload', 'decorators', 'dependency-injection'],
    vue: ['hot-reload', 'composition-api', 'single-file-components'],
    angular: ['hot-reload', 'typescript', 'dependency-injection'],
    nuxt: ['hot-reload', 'ssr', 'auto-routing'],
    svelte: ['hot-reload', 'reactive', 'compile-time-optimization'],
    remix: ['hot-reload', 'ssr', 'nested-routing'],
    gatsby: ['hot-reload', 'static-generation', 'graphql'],
  };
  return features[framework] || [];
}
