import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';
import { nodeReactLivePreviewService } from '@/services/node-react-live-preview';

// GET /api/node-react-workspace/workspaces/[workspaceId]/preview - Get live preview status
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get preview status
    const preview = nodeReactLivePreviewService.getPreview(workspaceId);

    if (!preview) {
      return NextResponse.json(
        {
          success: false,
          error: 'No preview found',
          message: 'No live preview is currently running for this workspace',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: preview,
    });
  } catch (error) {
    console.error('Error getting live preview status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to get live preview status',
      },
      { status: 500 }
    );
  }
}
