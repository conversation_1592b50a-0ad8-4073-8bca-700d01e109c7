import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';
import { nodeReactLivePreviewService } from '@/services/node-react-live-preview';

// POST /api/node-react-workspace/workspaces/[workspaceId]/preview/stop - Stop live preview
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Check if preview exists
    const existingPreview = nodeReactLivePreviewService.getPreview(workspaceId);
    if (!existingPreview) {
      return NextResponse.json(
        {
          success: false,
          error: 'No preview running',
          message: 'No live preview is currently running for this workspace',
        },
        { status: 404 }
      );
    }

    // Stop the preview
    const result = await nodeReactLivePreviewService.stopPreview(workspaceId);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to stop preview',
          message: 'An error occurred while stopping the live preview',
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Live preview stopped successfully',
    });
  } catch (error) {
    console.error('Error stopping live preview:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to stop live preview',
      },
      { status: 500 }
    );
  }
}
