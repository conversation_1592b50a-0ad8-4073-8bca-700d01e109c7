import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';
import { nodeReactPackageManagerService } from '@/services/node-react-package-manager';

// POST /api/node-react-workspace/workspaces/[workspaceId]/packages/uninstall - Uninstall package
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { packageName, packageManager = 'npm', projectPath } = body;

    // Validate required fields
    if (!packageName) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'packageName is required',
        },
        { status: 400 }
      );
    }

    // Validate package manager
    if (!['npm', 'yarn', 'pnpm'].includes(packageManager)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid package manager',
          message: 'packageManager must be npm, yarn, or pnpm',
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Uninstall package
    const result = await nodeReactPackageManagerService.uninstallPackage(
      workspaceId,
      packageName,
      packageManager
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Uninstallation failed',
          message: result.error || 'Failed to uninstall package',
          output: result.output,
        },
        { status: 400 }
      );
    }

    // Get updated package list
    let installedPackages: any[] = [];
    try {
      installedPackages = await nodeReactPackageManagerService.getInstalledPackages(
        workspaceId,
        projectPath
      );
    } catch (error) {
      console.warn('Could not get updated package list:', error);
    }

    return NextResponse.json({
      success: true,
      message: `Successfully uninstalled ${packageName}`,
      output: result.output,
      installedPackages,
    });
  } catch (error) {
    console.error('Error uninstalling package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to uninstall package',
      },
      { status: 500 }
    );
  }
}
