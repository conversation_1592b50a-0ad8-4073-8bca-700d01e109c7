import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';
import { nodeReactPackageManagerService } from '@/services/node-react-package-manager';

// POST /api/node-react-workspace/workspaces/[workspaceId]/packages/install - Install package
export async function POST(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { packageName, version, packageManager = 'npm', isDev = false, projectPath } = body;

    // Validate required fields
    if (!packageName) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'packageName is required',
        },
        { status: 400 }
      );
    }

    // Validate package manager
    if (!['npm', 'yarn', 'pnpm'].includes(packageManager)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid package manager',
          message: 'packageManager must be npm, yarn, or pnpm',
        },
        { status: 400 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Install package
    const result = await nodeReactPackageManagerService.installPackage(
      workspaceId,
      packageName,
      packageManager,
      {
        version,
        isDev,
      }
    );

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Installation failed',
          message: result.error || 'Failed to install package',
          output: result.output,
        },
        { status: 400 }
      );
    }

    // Get updated package list
    let installedPackages: any[] = [];
    try {
      installedPackages = await nodeReactPackageManagerService.getInstalledPackages(
        workspaceId,
        projectPath
      );
    } catch (error) {
      console.warn('Could not get updated package list:', error);
    }

    return NextResponse.json({
      success: true,
      message: `Successfully installed ${packageName}${version ? `@${version}` : ''}`,
      output: result.output,
      installedPackages,
    });
  } catch (error) {
    console.error('Error installing package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to install package',
      },
      { status: 500 }
    );
  }
}
