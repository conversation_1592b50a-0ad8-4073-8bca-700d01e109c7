import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/docker';
import { nodeReactPackageManagerService } from '@/services/node-react-package-manager';

// GET /api/node-react-workspace/workspaces/[workspaceId]/packages - Get installed packages
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;
    const { searchParams } = new URL(request.url);
    const projectPath = searchParams.get('projectPath');

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Get installed packages
    const packages = await nodeReactPackageManagerService.getInstalledPackages(
      workspaceId,
      projectPath || undefined
    );

    return NextResponse.json({
      success: true,
      data: packages,
      total: packages.length,
    });
  } catch (error) {
    console.error('Error getting installed packages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to get installed packages',
      },
      { status: 500 }
    );
  }
}
