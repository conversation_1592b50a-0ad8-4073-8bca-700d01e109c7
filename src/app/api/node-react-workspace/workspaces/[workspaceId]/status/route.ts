import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';
import { NodeWorkspaceStatus } from '@/types/node-react-workspace';

// GET /api/node-react-workspace/workspaces/[workspaceId]/status - Get workspace status
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    const { workspaceId } = params;

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist`,
        },
        { status: 404 }
      );
    }

    // Get Node.js version
    let nodeVersion = 'Unknown';
    try {
      const nodeResult = await dockerService.executeCommand(workspaceId, 'node --version');
      if (nodeResult.exitCode === 0) {
        nodeVersion = nodeResult.stdout.trim();
      }
    } catch (error) {
      console.warn('Could not get Node.js version:', error);
    }

    // Get package manager info
    let packageManager = 'npm';
    try {
      // Check for yarn
      const yarnResult = await dockerService.executeCommand(workspaceId, 'yarn --version');
      if (yarnResult.exitCode === 0) {
        packageManager = 'yarn';
      }
      
      // Check for pnpm (higher priority)
      const pnpmResult = await dockerService.executeCommand(workspaceId, 'pnpm --version');
      if (pnpmResult.exitCode === 0) {
        packageManager = 'pnpm';
      }
    } catch (error) {
      console.warn('Could not detect package manager:', error);
    }

    // Get available ports (common Node.js development ports)
    const commonPorts = [3000, 3001, 4200, 5000, 5173, 8000, 8080, 8888, 9000];
    const availablePorts: number[] = [];
    
    for (const port of commonPorts) {
      try {
        const result = await dockerService.executeCommand(
          workspaceId, 
          `netstat -tuln | grep :${port} || echo "available"`
        );
        if (result.stdout.includes('available')) {
          availablePorts.push(port);
        }
      } catch (error) {
        // If netstat fails, assume port is available
        availablePorts.push(port);
      }
    }

    // Scan for Node.js projects
    const activeProjects: NodeWorkspaceStatus['activeProjects'] = [];
    try {
      // Look for package.json files
      const findResult = await dockerService.executeCommand(
        workspaceId,
        'find /home/<USER>"package.json" -type f 2>/dev/null | head -10'
      );
      
      if (findResult.exitCode === 0 && findResult.stdout.trim()) {
        const packageJsonPaths = findResult.stdout.trim().split('\n');
        
        for (const packageJsonPath of packageJsonPaths) {
          try {
            const catResult = await dockerService.executeCommand(
              workspaceId,
              `cat "${packageJsonPath}"`
            );
            
            if (catResult.exitCode === 0) {
              const packageJson = JSON.parse(catResult.stdout);
              const projectDir = packageJsonPath.replace('/package.json', '');
              const projectName = packageJson.name || projectDir.split('/').pop();
              
              // Detect framework
              let framework: any = 'express'; // default
              if (packageJson.dependencies) {
                if (packageJson.dependencies.next) framework = 'nextjs';
                else if (packageJson.dependencies.react) framework = 'react';
                else if (packageJson.dependencies.vue) framework = 'vue';
                else if (packageJson.dependencies['@angular/core']) framework = 'angular';
                else if (packageJson.dependencies.svelte) framework = 'svelte';
                else if (packageJson.dependencies['@nestjs/core']) framework = 'nestjs';
                else if (packageJson.dependencies.nuxt) framework = 'nuxt';
                else if (packageJson.dependencies.gatsby) framework = 'gatsby';
                else if (packageJson.dependencies['@remix-run/react']) framework = 'remix';
              }
              
              // Check if project is running (simple check for common dev servers)
              let status: 'running' | 'stopped' | 'building' | 'error' = 'stopped';
              let port: number | undefined;
              
              // Check for running processes
              const psResult = await dockerService.executeCommand(
                workspaceId,
                `ps aux | grep -E "(npm|yarn|pnpm|node)" | grep -v grep | grep "${projectName}" || echo "not running"`
              );
              
              if (!psResult.stdout.includes('not running')) {
                status = 'running';
                // Try to extract port from common patterns
                const portMatch = psResult.stdout.match(/:(\d{4,5})/);
                if (portMatch) {
                  port = parseInt(portMatch[1]);
                }
              }
              
              activeProjects.push({
                name: projectName,
                framework,
                port,
                status,
              });
            }
          } catch (error) {
            console.warn(`Error processing package.json at ${packageJsonPath}:`, error);
          }
        }
      }
    } catch (error) {
      console.warn('Error scanning for Node.js projects:', error);
    }

    // Get resource usage
    let resources = {
      cpu: 0,
      memory: 0,
      disk: 0,
    };
    
    try {
      // Get CPU usage
      const cpuResult = await dockerService.executeCommand(
        workspaceId,
        `top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1`
      );
      if (cpuResult.exitCode === 0) {
        resources.cpu = parseFloat(cpuResult.stdout.trim()) || 0;
      }
      
      // Get memory usage
      const memResult = await dockerService.executeCommand(
        workspaceId,
        `free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}'`
      );
      if (memResult.exitCode === 0) {
        resources.memory = parseFloat(memResult.stdout.trim()) || 0;
      }
      
      // Get disk usage
      const diskResult = await dockerService.executeCommand(
        workspaceId,
        `df /home/<USER>'{print $5}' | cut -d'%' -f1`
      );
      if (diskResult.exitCode === 0) {
        resources.disk = parseFloat(diskResult.stdout.trim()) || 0;
      }
    } catch (error) {
      console.warn('Error getting resource usage:', error);
    }

    const status: NodeWorkspaceStatus = {
      workspaceId,
      status: workspace.status === 'running' ? 'ready' :
              workspace.status === 'stopped' ? 'stopped' :
              workspace.status === 'starting' ? 'creating' :
              workspace.status === 'stopping' ? 'stopped' :
              workspace.status === 'paused' ? 'stopped' :
              workspace.status === 'restarting' ? 'creating' : 'error',
      activeProjects,
      environment: {
        nodeVersion,
        packageManager,
        availablePorts,
      },
      resources,
    };

    return NextResponse.json({
      success: true,
      data: status,
    });
  } catch (error) {
    console.error('Error getting workspace status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to get workspace status',
      },
      { status: 500 }
    );
  }
}
