import { NextRequest, NextResponse } from 'next/server';
import { nodeReactPackageManagerService } from '@/services/node-react-package-manager';

// GET /api/node-react-workspace/packages/search - Search npm packages
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '20');

    // Validate query
    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing query parameter',
          message: 'Query parameter "q" is required',
        },
        { status: 400 }
      );
    }

    // Validate limit
    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid limit parameter',
          message: 'Limit must be between 1 and 100',
        },
        { status: 400 }
      );
    }

    // Search packages
    const packages = await nodeReactPackageManagerService.searchPackages(query.trim(), limit);

    return NextResponse.json({
      success: true,
      data: packages,
      total: packages.length,
      query: query.trim(),
      limit,
    });
  } catch (error) {
    console.error('Error searching packages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to search packages',
      },
      { status: 500 }
    );
  }
}
