import { NextRequest, NextResponse } from 'next/server';
import { getNodeTemplate } from '@/data/node-react-templates';
import { dockerService } from '@/services/docker';
import { nodeReactPackageManagerService } from '@/services/node-react-package-manager';
import { CreateNodeProjectRequest, CreateNodeProjectResponse } from '@/types/node-react-workspace';

// POST /api/node-react-workspace/projects - Create new Node/React project
export async function POST(request: NextRequest) {
  try {
    const body: CreateNodeProjectRequest = await request.json();
    const { template: templateId, projectName, config, workspaceId, userId } = body;

    // Validate required fields
    if (!templateId || !projectName || !config || !workspaceId || !userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'template, projectName, config, workspaceId, and userId are required',
        },
        { status: 400 }
      );
    }

    // Validate project name
    if (!/^[a-zA-Z0-9-_]+$/.test(projectName)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid project name',
          message: 'Project name can only contain letters, numbers, hyphens, and underscores',
        },
        { status: 400 }
      );
    }

    // Get template
    const template = getNodeTemplate(templateId);
    if (!template) {
      return NextResponse.json(
        {
          success: false,
          error: 'Template not found',
          message: `Template '${templateId}' not found`,
        },
        { status: 404 }
      );
    }

    // Check if workspace exists
    const workspace = await dockerService.getWorkspaceInfo(workspaceId);
    if (!workspace) {
      return NextResponse.json(
        {
          success: false,
          error: 'Workspace not found',
          message: `Workspace '${workspaceId}' does not exist or is not running`,
        },
        { status: 404 }
      );
    }

    // Create project directory
    const projectPath = `/home/<USER>/projects/${projectName}`;
    await dockerService.executeCommand(workspaceId, `mkdir -p ${projectPath}`);

    // Create package.json
    const packageJson = {
      name: projectName,
      version: '0.1.0',
      private: true,
      scripts: {
        dev: template.commands.dev.replace('npm run dev', 'npm run dev'),
        build: template.commands.build,
        start: template.commands.start || 'npm start',
        lint: template.commands.lint || 'eslint .',
        test: template.commands.test || 'jest',
      },
      dependencies: {},
      devDependencies: {},
    };

    // Write package.json
    await dockerService.executeCommand(
      workspaceId,
      `echo '${JSON.stringify(packageJson, null, 2)}' > package.json`,
      projectPath
    );

    // Create template files
    for (const file of template.files) {
      const filePath = `${projectPath}/${file.path}`;
      const fileDir = filePath.substring(0, filePath.lastIndexOf('/'));
      
      // Create directory if it doesn't exist
      if (fileDir !== projectPath) {
        await dockerService.executeCommand(workspaceId, `mkdir -p ${fileDir}`);
      }
      
      // Write file content
      const content = file.content
        .replace(/{{projectName}}/g, projectName)
        .replace(/{{PROJECT_NAME}}/g, projectName.toUpperCase())
        .replace(/{{project-name}}/g, projectName.toLowerCase());
      
      await dockerService.executeCommand(
        workspaceId,
        `cat > ${filePath} << 'EOF'\n${content}\nEOF`
      );
      
      // Make executable if needed
      if (file.executable) {
        await dockerService.executeCommand(workspaceId, `chmod +x ${filePath}`);
      }
    }

    // Install dependencies
    if (template.dependencies.length > 0) {
      for (const dep of template.dependencies) {
        await nodeReactPackageManagerService.installPackage(
          workspaceId,
          dep,
          config.packageManager,
          { isDev: false }
        );
      }
    }

    // Install dev dependencies
    if (template.devDependencies.length > 0) {
      for (const dep of template.devDependencies) {
        await nodeReactPackageManagerService.installPackage(
          workspaceId,
          dep,
          config.packageManager,
          { isDev: true }
        );
      }
    }

    // Run package manager install
    const pm = nodeReactPackageManagerService.getPackageManager(config.packageManager);
    if (pm) {
      await dockerService.executeCommand(workspaceId, pm.installCommand, projectPath);
    }

    // Create TypeScript config if needed
    if (config.typescript && template.framework !== 'angular') {
      const tsConfig = {
        compilerOptions: {
          target: 'ES2020',
          lib: ['DOM', 'DOM.Iterable', 'ES6'],
          allowJs: true,
          skipLibCheck: true,
          esModuleInterop: true,
          allowSyntheticDefaultImports: true,
          strict: true,
          forceConsistentCasingInFileNames: true,
          moduleResolution: 'node',
          resolveJsonModule: true,
          isolatedModules: true,
          noEmit: true,
          jsx: template.framework.includes('react') ? 'react-jsx' : 'preserve',
        },
        include: ['src'],
        exclude: ['node_modules'],
      };

      await dockerService.executeCommand(
        workspaceId,
        `echo '${JSON.stringify(tsConfig, null, 2)}' > tsconfig.json`,
        projectPath
      );
    }

    const response: CreateNodeProjectResponse = {
      success: true,
      project: {
        id: `${workspaceId}-${projectName}`,
        name: projectName,
        path: projectPath,
        framework: template.framework,
        config,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error creating Node/React project:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create project',
      },
      { status: 500 }
    );
  }
}

// GET /api/node-react-workspace/projects - List projects (future feature)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    // const userId = searchParams.get('userId'); // TODO: Use for filtering user projects

    if (!workspaceId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameter',
          message: 'workspaceId is required',
        },
        { status: 400 }
      );
    }

    // TODO: Implement project listing
    // This would involve:
    // 1. Scanning the workspace for Node.js projects
    // 2. Reading package.json files to get project info
    // 3. Returning project metadata

    return NextResponse.json(
      {
        success: false,
        error: 'Not implemented',
        message: 'Project listing is not yet implemented',
      },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error listing projects:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to list projects',
      },
      { status: 500 }
    );
  }
}
