import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// GET /api/images - List Docker images
export async function GET(request: NextRequest) {
  try {
    const images = await dockerService.listImages();
    
    return NextResponse.json({
      success: true,
      data: images,
      count: images.length,
    });
  } catch (error) {
    console.error('Error listing images:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list images',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/images - Pull Docker image
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.image) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing image name',
          message: 'Image name is required',
        },
        { status: 400 }
      );
    }
    
    await dockerService.pullImage(body.image);
    
    return NextResponse.json({
      success: true,
      message: `Image ${body.image} pulled successfully`,
      data: {
        image: body.image,
      },
    });
  } catch (error) {
    console.error('Error pulling image:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to pull image',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
