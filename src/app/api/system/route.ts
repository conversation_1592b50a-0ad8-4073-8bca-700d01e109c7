import { NextRequest, NextResponse } from 'next/server';
import { dockerService } from '@/services/server/docker';

// GET /api/system - Get Docker system information
export async function GET(request: NextRequest) {
  try {
    // Check Docker connection first
    const isConnected = await dockerService.ping();
    
    if (!isConnected) {
      return NextResponse.json(
        {
          success: false,
          error: 'Docker connection failed',
          message: 'Unable to connect to Docker daemon',
          data: {
            connected: false,
          },
        },
        { status: 503 }
      );
    }
    
    const systemInfo = await dockerService.getSystemInfo();
    
    return NextResponse.json({
      success: true,
      data: {
        connected: true,
        ...systemInfo,
      },
    });
  } catch (error) {
    console.error('Error getting system info:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get system information',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          connected: false,
        },
      },
      { status: 500 }
    );
  }
}
