import { Metadata } from 'next';
import { HeroSection } from '@/components/landing/hero-section';
import { FeaturesSection } from '@/components/landing/features-section';
import { ArchitectureSection } from '@/components/landing/architecture-section';
import { UseCasesSection } from '@/components/landing/use-cases-section';
import { PricingSection } from '@/components/landing/pricing-section';
import { TestimonialsSection } from '@/components/landing/testimonials-section';
import { InteractiveDemoSection } from '@/components/landing/interactive-demo-section';
import { CTASection } from '@/components/landing/cta-section';
import { LandingFooter } from '@/components/landing/landing-footer';
import { LandingNavigation } from '@/components/landing/landing-navigation';

export const metadata: Metadata = {
  title: 'Omnispace - AI-Powered Remote Workspace Platform',
  description: 'Transform your development workflow with cloud-based environments powered by Firecracker MicroVMs, seamless VNC access, and intelligent AI assistance.',
};

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-background">
      <LandingNavigation />
      
      <main>
        <HeroSection />
        <FeaturesSection />
        <InteractiveDemoSection />
        <ArchitectureSection />
        <UseCasesSection />
        <TestimonialsSection />
        <PricingSection />
        <CTASection />
      </main>
      
      <LandingFooter />
    </div>
  );
}
