/**
 * Workspaces Dashboard Page
 * Main page for workspace management
 */

'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { WorkspaceList } from '@/components/workspace/WorkspaceList';
import { WorkspaceCreateForm } from '@/components/workspace/WorkspaceCreateForm';
import { Workspace, CreateWorkspaceRequest } from '@/types/workspace';
import { useWorkspace } from '@/hooks/useWorkspace';

export default function WorkspacesPage() {
  const router = useRouter();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [workspaceToDelete, setWorkspaceToDelete] = useState<Workspace | null>(null);
  const [workspaceToShare, setWorkspaceToShare] = useState<Workspace | null>(null);

  const {
    createWorkspace,
    deleteWorkspace,
    isCreating,
    isDeleting,
  } = useWorkspace();

  const handleCreateWorkspace = async (data: CreateWorkspaceRequest) => {
    try {
      const newWorkspace = await createWorkspace(data);
      setShowCreateDialog(false);
      toast.success(`Workspace "${newWorkspace.name}" created successfully!`);
      
      // Optionally navigate to the new workspace
      // router.push(`/workspaces/${newWorkspace.id}`);
    } catch (error) {
      console.error('Failed to create workspace:', error);
      // Error is already handled by the hook with toast
    }
  };

  const handleOpenWorkspace = (workspace: Workspace) => {
    if (workspace.accessUrl) {
      // Open in new tab/window
      window.open(workspace.accessUrl, '_blank');
    } else {
      // Navigate to workspace detail page
      router.push(`/workspaces/${workspace.id}`);
    }
  };

  const handleEditWorkspace = (workspace: Workspace) => {
    router.push(`/workspaces/${workspace.id}/settings`);
  };

  const handleShareWorkspace = (workspace: Workspace) => {
    setWorkspaceToShare(workspace);
  };

  const handleDeleteWorkspace = (workspace: Workspace) => {
    setWorkspaceToDelete(workspace);
  };

  const handleDuplicateWorkspace = async (workspace: Workspace) => {
    try {
      const duplicateData: CreateWorkspaceRequest = {
        name: `${workspace.name} (Copy)`,
        description: workspace.description,
        type: workspace.type,
        configuration: workspace.configuration,
        visibility: 'private', // Always create duplicates as private
        tags: workspace.tags,
      };
      
      const newWorkspace = await createWorkspace(duplicateData);
      toast.success(`Workspace duplicated as "${newWorkspace.name}"`);
    } catch (error) {
      console.error('Failed to duplicate workspace:', error);
    }
  };

  const confirmDeleteWorkspace = async () => {
    if (!workspaceToDelete) return;
    
    try {
      await deleteWorkspace(workspaceToDelete.id);
      setWorkspaceToDelete(null);
      toast.success(`Workspace "${workspaceToDelete.name}" deleted successfully`);
    } catch (error) {
      console.error('Failed to delete workspace:', error);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-8"
      >
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Workspaces</h1>
            <p className="text-muted-foreground mt-2">
              Manage your development environments and collaborate with your team
            </p>
          </div>
          
          <Button 
            onClick={() => setShowCreateDialog(true)}
            size="lg"
            className="shadow-lg"
          >
            Create Workspace
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  Active Workspaces
                </p>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  12
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl">⚡</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">
                  Collaborators
                </p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  24
                </p>
              </div>
              <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl">👥</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                  Total Projects
                </p>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  48
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl">📁</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 rounded-lg p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">
                  Hours This Month
                </p>
                <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                  156
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl">⏱️</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Workspace List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <WorkspaceList
            onCreateWorkspace={() => setShowCreateDialog(true)}
            onOpenWorkspace={handleOpenWorkspace}
            onEditWorkspace={handleEditWorkspace}
            onShareWorkspace={handleShareWorkspace}
            onDeleteWorkspace={handleDeleteWorkspace}
            onDuplicateWorkspace={handleDuplicateWorkspace}
          />
        </motion.div>
      </motion.div>

      {/* Create Workspace Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Workspace</DialogTitle>
            <DialogDescription>
              Set up a new development environment for your project
            </DialogDescription>
          </DialogHeader>
          
          <WorkspaceCreateForm
            onSubmit={handleCreateWorkspace}
            onCancel={() => setShowCreateDialog(false)}
            isLoading={isCreating}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={!!workspaceToDelete} 
        onOpenChange={() => setWorkspaceToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Workspace</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{workspaceToDelete?.name}"? 
              This action cannot be undone and will permanently delete all files and data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteWorkspace}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Workspace'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Share Workspace Dialog */}
      {workspaceToShare && (
        <Dialog open={!!workspaceToShare} onOpenChange={() => setWorkspaceToShare(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Share Workspace</DialogTitle>
              <DialogDescription>
                Invite collaborators to "{workspaceToShare.name}"
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4">
              <p className="text-sm text-muted-foreground">
                Sharing functionality will be implemented here.
              </p>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setWorkspaceToShare(null)}>
                Cancel
              </Button>
              <Button onClick={() => setWorkspaceToShare(null)}>
                Share
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
