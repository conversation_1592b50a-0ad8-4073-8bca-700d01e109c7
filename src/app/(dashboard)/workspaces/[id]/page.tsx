/**
 * Workspace Detail Page
 * Individual workspace management and access
 */

'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play,
  Square,
  RotateCcw,
  ExternalLink,
  Settings,
  Share,
  Users,
  Files,
  Terminal,
  Activity,
  Clock,
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useWorkspaceDetail } from '@/hooks/useWorkspaceDetail';
import { WorkspaceStatus } from '@/types/workspace';
import { formatDistanceToNow } from 'date-fns';

export default function WorkspaceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const workspaceId = params.id as string;
  
  const [activeTab, setActiveTab] = useState('overview');

  const {
    workspace,
    collaborators,
    stats,
    isLoading,
    isStarting,
    isStopping,
    isRestarting,
    error,
    startWorkspace,
    stopWorkspace,
    restartWorkspace,
    isActive,
    isStopped,
    isCreating,
    hasError,
    canEdit,
    canExecute,
    canShare,
  } = useWorkspaceDetail({
    workspaceId,
    autoRefresh: true,
    refreshInterval: 5000,
    onStatusChange: (status: WorkspaceStatus) => {
      switch (status) {
        case 'active':
          toast.success('Workspace is now active');
          break;
        case 'stopped':
          toast.info('Workspace has been stopped');
          break;
        case 'error':
          toast.error('Workspace encountered an error');
          break;
      }
    },
    onError: (error) => {
      toast.error(`Workspace error: ${error.message}`);
    },
  });

  const handleStartWorkspace = async () => {
    try {
      await startWorkspace();
    } catch (error) {
      console.error('Failed to start workspace:', error);
    }
  };

  const handleStopWorkspace = async () => {
    try {
      await stopWorkspace();
    } catch (error) {
      console.error('Failed to stop workspace:', error);
    }
  };

  const handleRestartWorkspace = async () => {
    try {
      await restartWorkspace();
    } catch (error) {
      console.error('Failed to restart workspace:', error);
    }
  };

  const handleOpenWorkspace = () => {
    if (workspace?.accessUrl) {
      window.open(workspace.accessUrl, '_blank');
    }
  };

  const getStatusColor = (status: WorkspaceStatus) => {
    switch (status) {
      case 'creating': return 'bg-blue-500';
      case 'active': return 'bg-green-500';
      case 'stopped': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      case 'archived': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: WorkspaceStatus) => {
    switch (status) {
      case 'creating': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'stopped': return <Square className="h-4 w-4" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      case 'archived': return <Clock className="h-4 w-4" />;
      default: return <Square className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading workspace...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !workspace) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Workspace Not Found</h2>
          <p className="text-muted-foreground mb-4">
            {error?.message || 'The workspace you are looking for does not exist or you do not have access to it.'}
          </p>
          <Button onClick={() => router.push('/workspaces')} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Workspaces
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push('/workspaces')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold">{workspace.name}</h1>
                <div className="flex items-center gap-2">
                  <div className={cn("w-2 h-2 rounded-full", getStatusColor(workspace.status))} />
                  <span className="text-sm text-muted-foreground capitalize">
                    {workspace.status}
                  </span>
                  {getStatusIcon(workspace.status)}
                </div>
              </div>
              
              {workspace.description && (
                <p className="text-muted-foreground">{workspace.description}</p>
              )}
              
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <span>Created {formatDistanceToNow(workspace.createdAt, { addSuffix: true })}</span>
                {workspace.lastAccessedAt && (
                  <span>Last accessed {formatDistanceToNow(workspace.lastAccessedAt, { addSuffix: true })}</span>
                )}
                <Badge variant="outline">{workspace.type}</Badge>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            {isStopped && (
              <Button
                onClick={handleStartWorkspace}
                disabled={isStarting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isStarting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                Start
              </Button>
            )}
            
            {isActive && (
              <>
                <Button
                  onClick={handleOpenWorkspace}
                  disabled={!workspace.accessUrl}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleStopWorkspace}
                  disabled={isStopping}
                >
                  {isStopping ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Square className="h-4 w-4 mr-2" />
                  )}
                  Stop
                </Button>
              </>
            )}
            
            {(isActive || hasError) && (
              <Button
                variant="outline"
                onClick={handleRestartWorkspace}
                disabled={isRestarting}
              >
                {isRestarting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RotateCcw className="h-4 w-4 mr-2" />
                )}
                Restart
              </Button>
            )}
            
            {canShare && (
              <Button variant="outline">
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
            )}
            
            {canEdit && (
              <Button
                variant="outline"
                onClick={() => router.push(`/workspaces/${workspaceId}/settings`)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="files">Files</TabsTrigger>
            <TabsTrigger value="terminal">Terminal</TabsTrigger>
            <TabsTrigger value="collaborators">Collaborators</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Resource Usage */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Resource Usage
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Cpu className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">CPU</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {stats?.cpuUsage || 0}%
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all"
                          style={{ width: `${stats?.cpuUsage || 0}%` }}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <MemoryStick className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">Memory</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {stats?.memoryUsage || 0}%
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all"
                          style={{ width: `${stats?.memoryUsage || 0}%` }}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <HardDrive className="h-4 w-4 text-purple-500" />
                          <span className="text-sm font-medium">Storage</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {stats?.storageUsage || 0}%
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-purple-500 h-2 rounded-full transition-all"
                          style={{ width: `${stats?.storageUsage || 0}%` }}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Network className="h-4 w-4 text-orange-500" />
                          <span className="text-sm font-medium">Network</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {stats?.networkUsage || 0} MB
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-orange-500 h-2 rounded-full transition-all"
                          style={{ width: `${Math.min((stats?.networkUsage || 0) / 100, 100)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Total Runtime</span>
                    <span className="font-medium">
                      {Math.round((stats?.totalRuntime || 0) / 3600)}h
                    </span>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Sessions</span>
                    <span className="font-medium">{stats?.sessionCount || 0}</span>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">File Operations</span>
                    <span className="font-medium">{stats?.fileOperations || 0}</span>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Code Executions</span>
                    <span className="font-medium">{stats?.codeExecutions || 0}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-medium mb-2">Resources</h4>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <div>CPU: {workspace.configuration.resources.cpu} cores</div>
                      <div>Memory: {workspace.configuration.resources.memory} MB</div>
                      <div>Storage: {workspace.configuration.resources.storage} GB</div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Access</h4>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <div>Visibility: {workspace.visibility}</div>
                      <div>Collaborators: {collaborators.length}</div>
                      {workspace.accessUrl && (
                        <div>URL: Available</div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Features</h4>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <div>AI Assistant: {workspace.aiEnabled ? 'Enabled' : 'Disabled'}</div>
                      <div>Collaboration: {workspace.configuration.collaboration?.enabled ? 'Enabled' : 'Disabled'}</div>
                      <div>Auto-save: Enabled</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other tabs would be implemented here */}
          <TabsContent value="files">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Files className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">File explorer will be implemented here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="terminal">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Terminal className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Terminal interface will be implemented here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="collaborators">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Collaborator management will be implemented here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Activity feed will be implemented here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}
