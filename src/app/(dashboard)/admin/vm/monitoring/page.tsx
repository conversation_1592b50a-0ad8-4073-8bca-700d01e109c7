/**
 * VM Monitoring Admin Page
 * Admin interface for system monitoring and metrics
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Activity,
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  AlertTriangle,
  TrendingUp,
  RefreshCw,
  Settings,
  Eye,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { VMMetricsDisplay, VMStatusIndicator } from '@/components/vm';

// Mock monitoring data
const mockSystemMetrics = {
  cpu: {
    usage: 45.2,
    loadAverage: [1.2, 1.5, 1.8],
    processes: 156
  },
  memory: {
    total: 16384,
    used: 8192,
    free: 8192,
    usagePercent: 50.0
  },
  disk: {
    total: 500000,
    used: 350000,
    free: 150000,
    usagePercent: 70.0
  },
  network: {
    bytesIn: 1024000,
    bytesOut: 512000,
    packetsIn: 1500,
    packetsOut: 1200
  }
};

const mockVMMetrics = [
  {
    vmId: 'vm-prod-01',
    name: 'Production Server 1',
    status: 'connected',
    metrics: {
      cpu: { usage: 32.1 },
      memory: { usagePercent: 45.8 },
      disk: { usagePercent: 62.3 },
      uptime: '15d 4h'
    }
  },
  {
    vmId: 'vm-prod-02',
    name: 'Production Server 2',
    status: 'connected',
    metrics: {
      cpu: { usage: 28.7 },
      memory: { usagePercent: 38.2 },
      disk: { usagePercent: 55.1 },
      uptime: '12d 8h'
    }
  },
  {
    vmId: 'vm-dev-03',
    name: 'Development Server',
    status: 'error',
    metrics: {
      cpu: { usage: 89.4 },
      memory: { usagePercent: 92.1 },
      disk: { usagePercent: 78.9 },
      uptime: '2d 1h'
    }
  }
];

const mockAlerts = [
  {
    id: '1',
    type: 'cpu',
    severity: 'high',
    message: 'High CPU usage detected on vm-dev-03',
    value: 89.4,
    threshold: 80,
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    acknowledged: false
  },
  {
    id: '2',
    type: 'memory',
    severity: 'critical',
    message: 'Memory usage critical on vm-dev-03',
    value: 92.1,
    threshold: 85,
    timestamp: new Date(Date.now() - 10 * 60 * 1000),
    acknowledged: false
  },
  {
    id: '3',
    type: 'disk',
    severity: 'medium',
    message: 'Disk space running low on vm-prod-01',
    value: 78.9,
    threshold: 75,
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    acknowledged: true
  }
];

function SystemOverview() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          System Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <VMMetricsDisplay
          metrics={mockSystemMetrics}
          showTrends={true}
          compact={false}
          animate={true}
          variant="glass"
        />
      </CardContent>
    </Card>
  );
}

function VMPerformanceList() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-500';
      case 'disconnected': return 'text-gray-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const getMetricColor = (value: number, threshold: number = 80) => {
    if (value >= threshold) return 'text-red-500';
    if (value >= threshold * 0.8) return 'text-orange-500';
    return 'text-green-500';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          VM Performance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockVMMetrics.map((vm, index) => (
            <motion.div
              key={vm.vmId}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <VMStatusIndicator
                  status={vm.status as any}
                  size="sm"
                  showLabel={false}
                />
                <div>
                  <p className="font-medium">{vm.name}</p>
                  <p className="text-sm text-muted-foreground">
                    Uptime: {vm.metrics.uptime}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-6 text-sm">
                <div className="text-center">
                  <p className="text-muted-foreground mb-1">CPU</p>
                  <p className={cn('font-medium', getMetricColor(vm.metrics.cpu.usage))}>
                    {vm.metrics.cpu.usage.toFixed(1)}%
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-muted-foreground mb-1">Memory</p>
                  <p className={cn('font-medium', getMetricColor(vm.metrics.memory.usagePercent, 85))}>
                    {vm.metrics.memory.usagePercent.toFixed(1)}%
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-muted-foreground mb-1">Disk</p>
                  <p className={cn('font-medium', getMetricColor(vm.metrics.disk.usagePercent, 90))}>
                    {vm.metrics.disk.usagePercent.toFixed(1)}%
                  </p>
                </div>
                <Button variant="ghost" size="sm">
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function AlertsList() {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-500 bg-red-500/10 border-red-500/20';
      case 'high': return 'text-orange-500 bg-orange-500/10 border-orange-500/20';
      case 'medium': return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/20';
      case 'low': return 'text-blue-500 bg-blue-500/10 border-blue-500/20';
      default: return 'text-gray-500 bg-gray-500/10 border-gray-500/20';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    
    if (minutes < 60) return `${minutes}m ago`;
    return `${hours}h ago`;
  };

  const activeAlerts = mockAlerts.filter(alert => !alert.acknowledged);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Active Alerts
          {activeAlerts.length > 0 && (
            <Badge variant="destructive" className="ml-2">
              {activeAlerts.length}
            </Badge>
          )}
        </CardTitle>
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4 mr-2" />
          View All
        </Button>
      </CardHeader>
      <CardContent>
        {activeAlerts.length === 0 ? (
          <div className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No active alerts</p>
          </div>
        ) : (
          <div className="space-y-3">
            {activeAlerts.map((alert, index) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  'p-4 rounded-lg border',
                  getSeverityColor(alert.severity)
                )}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="text-xs">
                        {alert.type.toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {alert.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="font-medium mb-1">{alert.message}</p>
                    <p className="text-sm opacity-80">
                      Current: {alert.value}% | Threshold: {alert.threshold}%
                    </p>
                    <p className="text-xs opacity-60 mt-2">
                      {formatTime(alert.timestamp)}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm">
                    Acknowledge
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function VMMonitoringAdminPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor VM performance and system health in real-time
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <SystemOverview />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VMPerformanceList />
            <AlertsList />
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <VMPerformanceList />
            <SystemOverview />
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <AlertsList />
            <Card>
              <CardHeader>
                <CardTitle>Alert Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Configure alert thresholds and notification settings for your VM infrastructure.
                </p>
                <Button className="mt-4">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Alerts
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
