/**
 * VM Settings Admin Page
 * Admin interface for VM service configuration and preferences
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Settings,
  Save,
  RefreshCw,
  Bell,
  Shield,
  Activity,
  Server,
  Database,
  Clock,
  AlertTriangle,
  Info,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';

// Mock settings data
const mockSettings = {
  general: {
    defaultTimeout: 30000,
    maxRetries: 3,
    retryDelay: 1000,
    autoReconnect: true,
    keepAlive: true,
    logLevel: 'info'
  },
  monitoring: {
    refreshInterval: 30000,
    enableRealTime: true,
    retentionDays: 30,
    alertThresholds: {
      cpu: 80,
      memory: 85,
      disk: 90
    }
  },
  security: {
    sessionTimeout: 3600000,
    maxFailedLogins: 5,
    lockoutDuration: 900000,
    requireMFA: false,
    passwordMinLength: 8
  },
  notifications: {
    emailEnabled: true,
    slackEnabled: false,
    webhookEnabled: false,
    alertLevels: ['critical', 'high']
  }
};

function GeneralSettings() {
  const [settings, setSettings] = React.useState(mockSettings.general);
  const [hasChanges, setHasChanges] = React.useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    // Save settings logic here
    console.log('Saving general settings:', settings);
    setHasChanges(false);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          General Settings
        </CardTitle>
        {hasChanges && (
          <Button onClick={handleSave} size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="timeout">Default Connection Timeout (ms)</Label>
            <Input
              id="timeout"
              type="number"
              value={settings.defaultTimeout}
              onChange={(e) => handleSettingChange('defaultTimeout', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              Default timeout for VM connections
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="retries">Max Retry Attempts</Label>
            <Input
              id="retries"
              type="number"
              value={settings.maxRetries}
              onChange={(e) => handleSettingChange('maxRetries', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              Maximum number of connection retry attempts
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="retryDelay">Retry Delay (ms)</Label>
            <Input
              id="retryDelay"
              type="number"
              value={settings.retryDelay}
              onChange={(e) => handleSettingChange('retryDelay', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              Delay between retry attempts
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="logLevel">Log Level</Label>
            <Select
              value={settings.logLevel}
              onValueChange={(value) => handleSettingChange('logLevel', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="debug">Debug</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="warn">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Minimum log level to record
            </p>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto Reconnect</Label>
              <p className="text-sm text-muted-foreground">
                Automatically reconnect to VMs when connection is lost
              </p>
            </div>
            <Switch
              checked={settings.autoReconnect}
              onCheckedChange={(checked) => handleSettingChange('autoReconnect', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Keep Alive</Label>
              <p className="text-sm text-muted-foreground">
                Send keep-alive packets to maintain connections
              </p>
            </div>
            <Switch
              checked={settings.keepAlive}
              onCheckedChange={(checked) => handleSettingChange('keepAlive', checked)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function MonitoringSettings() {
  const [settings, setSettings] = React.useState(mockSettings.monitoring);
  const [hasChanges, setHasChanges] = React.useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleThresholdChange = (metric: string, value: number[]) => {
    setSettings(prev => ({
      ...prev,
      alertThresholds: {
        ...prev.alertThresholds,
        [metric]: value[0]
      }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    console.log('Saving monitoring settings:', settings);
    setHasChanges(false);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Monitoring Settings
        </CardTitle>
        {hasChanges && (
          <Button onClick={handleSave} size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="refreshInterval">Refresh Interval (ms)</Label>
            <Input
              id="refreshInterval"
              type="number"
              value={settings.refreshInterval}
              onChange={(e) => handleSettingChange('refreshInterval', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              How often to refresh monitoring data
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="retention">Data Retention (days)</Label>
            <Input
              id="retention"
              type="number"
              value={settings.retentionDays}
              onChange={(e) => handleSettingChange('retentionDays', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              How long to keep monitoring data
            </p>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Real-time Monitoring</Label>
              <p className="text-sm text-muted-foreground">
                Enable real-time data updates via WebSocket
              </p>
            </div>
            <Switch
              checked={settings.enableRealTime}
              onCheckedChange={(checked) => handleSettingChange('enableRealTime', checked)}
            />
          </div>
        </div>

        <Separator />

        <div className="space-y-6">
          <h4 className="font-medium">Alert Thresholds</h4>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>CPU Usage (%)</Label>
                <span className="text-sm font-medium">{settings.alertThresholds.cpu}%</span>
              </div>
              <Slider
                value={[settings.alertThresholds.cpu]}
                onValueChange={(value) => handleThresholdChange('cpu', value)}
                max={100}
                step={5}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Memory Usage (%)</Label>
                <span className="text-sm font-medium">{settings.alertThresholds.memory}%</span>
              </div>
              <Slider
                value={[settings.alertThresholds.memory]}
                onValueChange={(value) => handleThresholdChange('memory', value)}
                max={100}
                step={5}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Disk Usage (%)</Label>
                <span className="text-sm font-medium">{settings.alertThresholds.disk}%</span>
              </div>
              <Slider
                value={[settings.alertThresholds.disk]}
                onValueChange={(value) => handleThresholdChange('disk', value)}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function SecuritySettings() {
  const [settings, setSettings] = React.useState(mockSettings.security);
  const [hasChanges, setHasChanges] = React.useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    console.log('Saving security settings:', settings);
    setHasChanges(false);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Security Settings
        </CardTitle>
        {hasChanges && (
          <Button onClick={handleSave} size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="sessionTimeout">Session Timeout (ms)</Label>
            <Input
              id="sessionTimeout"
              type="number"
              value={settings.sessionTimeout}
              onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              How long sessions remain active
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxFailedLogins">Max Failed Logins</Label>
            <Input
              id="maxFailedLogins"
              type="number"
              value={settings.maxFailedLogins}
              onChange={(e) => handleSettingChange('maxFailedLogins', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              Maximum failed login attempts before lockout
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="lockoutDuration">Lockout Duration (ms)</Label>
            <Input
              id="lockoutDuration"
              type="number"
              value={settings.lockoutDuration}
              onChange={(e) => handleSettingChange('lockoutDuration', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              How long accounts are locked after failed attempts
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
            <Input
              id="passwordMinLength"
              type="number"
              value={settings.passwordMinLength}
              onChange={(e) => handleSettingChange('passwordMinLength', parseInt(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">
              Minimum required password length
            </p>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Multi-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Require MFA for all user accounts
              </p>
            </div>
            <Switch
              checked={settings.requireMFA}
              onCheckedChange={(checked) => handleSettingChange('requireMFA', checked)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function NotificationSettings() {
  const [settings, setSettings] = React.useState(mockSettings.notifications);
  const [hasChanges, setHasChanges] = React.useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    console.log('Saving notification settings:', settings);
    setHasChanges(false);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Settings
        </CardTitle>
        {hasChanges && (
          <Button onClick={handleSave} size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Email Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Send alerts and notifications via email
              </p>
            </div>
            <Switch
              checked={settings.emailEnabled}
              onCheckedChange={(checked) => handleSettingChange('emailEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Slack Integration</Label>
              <p className="text-sm text-muted-foreground">
                Send notifications to Slack channels
              </p>
            </div>
            <Switch
              checked={settings.slackEnabled}
              onCheckedChange={(checked) => handleSettingChange('slackEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Webhook Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Send notifications to custom webhook endpoints
              </p>
            </div>
            <Switch
              checked={settings.webhookEnabled}
              onCheckedChange={(checked) => handleSettingChange('webhookEnabled', checked)}
            />
          </div>
        </div>

        <Separator />

        <div className="space-y-2">
          <Label>Alert Levels</Label>
          <p className="text-sm text-muted-foreground mb-4">
            Select which alert levels should trigger notifications
          </p>
          <div className="space-y-2">
            {['critical', 'high', 'medium', 'low'].map((level) => (
              <div key={level} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={level}
                  checked={settings.alertLevels.includes(level)}
                  onChange={(e) => {
                    const newLevels = e.target.checked
                      ? [...settings.alertLevels, level]
                      : settings.alertLevels.filter(l => l !== level);
                    handleSettingChange('alertLevels', newLevels);
                  }}
                  className="rounded border-gray-300"
                />
                <Label htmlFor={level} className="capitalize">
                  {level}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function VMSettingsAdminPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">VM Settings</h1>
          <p className="text-muted-foreground">
            Configure VM service settings and preferences
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <GeneralSettings />
        </TabsContent>

        <TabsContent value="monitoring">
          <MonitoringSettings />
        </TabsContent>

        <TabsContent value="security">
          <SecuritySettings />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}
