/**
 * VM Docker Admin Page
 * Admin interface for Docker container management
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Database,
  Play,
  Square,
  Trash2,
  Plus,
  RefreshCw,
  Search,
  Filter,
  MoreVertical,
  Activity,
  Clock,
  HardDrive
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { VMStatusIndicator, VMLoading } from '@/components/vm';
import { useVMDocker } from '@/hooks/vm';

// Mock container data
const mockContainers = [
  {
    id: 'nginx-proxy-001',
    name: 'nginx-proxy',
    image: 'nginx:latest',
    status: 'running',
    state: 'running',
    vmId: 'vm-prod-01',
    ports: ['80:80', '443:443'],
    created: new Date(Date.now() - 86400000 * 5),
    startedAt: new Date(Date.now() - 3600000 * 2),
    cpu: 12,
    memory: 45
  },
  {
    id: 'redis-cache-002',
    name: 'redis-cache',
    image: 'redis:alpine',
    status: 'running',
    state: 'running',
    vmId: 'vm-prod-02',
    ports: ['6379:6379'],
    created: new Date(Date.now() - 86400000 * 3),
    startedAt: new Date(Date.now() - 3600000 * 1),
    cpu: 8,
    memory: 32
  },
  {
    id: 'postgres-db-003',
    name: 'postgres-db',
    image: 'postgres:13',
    status: 'exited',
    state: 'exited',
    vmId: 'vm-dev-01',
    ports: ['5432:5432'],
    created: new Date(Date.now() - 86400000 * 7),
    finishedAt: new Date(Date.now() - 3600000 * 0.5),
    cpu: 0,
    memory: 0
  }
];

function DockerStatsCards() {
  const totalContainers = mockContainers.length;
  const runningContainers = mockContainers.filter(c => c.status === 'running').length;
  const stoppedContainers = mockContainers.filter(c => c.status === 'exited').length;

  const stats = [
    {
      title: 'Total Containers',
      value: totalContainers,
      icon: Database,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10'
    },
    {
      title: 'Running',
      value: runningContainers,
      icon: Play,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10'
    },
    {
      title: 'Stopped',
      value: stoppedContainers,
      icon: Square,
      color: 'text-gray-500',
      bgColor: 'bg-gray-500/10'
    },
    {
      title: 'Total Images',
      value: 8,
      icon: HardDrive,
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-3xl font-bold">{stat.value}</p>
                </div>
                <div className={cn('p-3 rounded-full', stat.bgColor)}>
                  <stat.icon className={cn('h-6 w-6', stat.color)} />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}

function ContainerCard({ container, index }: { container: any; index: number }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-500';
      case 'exited': return 'text-gray-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const formatUptime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h`;
    return `${hours}h`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <Database className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <h3 className="font-semibold">{container.name}</h3>
                <p className="text-sm text-muted-foreground">{container.image}</p>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {container.status === 'running' ? (
                  <DropdownMenuItem>
                    <Square className="h-4 w-4 mr-2" />
                    Stop Container
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem>
                    <Play className="h-4 w-4 mr-2" />
                    Start Container
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem>
                  <Activity className="h-4 w-4 mr-2" />
                  View Logs
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove Container
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Status</span>
              <Badge variant={container.status === 'running' ? 'default' : 'secondary'}>
                {container.status}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">VM</span>
              <span className="text-sm font-medium">{container.vmId}</span>
            </div>

            {container.ports.length > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Ports</span>
                <span className="text-sm font-mono">{container.ports.join(', ')}</span>
              </div>
            )}

            {container.status === 'running' && (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">CPU</span>
                  <span className="text-sm font-medium">{container.cpu}%</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Memory</span>
                  <span className="text-sm font-medium">{container.memory}%</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Uptime</span>
                  <span className="text-sm font-medium">
                    {formatUptime(container.startedAt!)}
                  </span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default function VMDockerAdminPage() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState<string>('all');

  const filteredContainers = mockContainers.filter(container => {
    const matchesSearch = container.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         container.image.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         container.vmId.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Docker Management</h1>
          <p className="text-muted-foreground">
            Manage Docker containers across your VM infrastructure
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Container
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <DockerStatsCards />

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search containers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  All Containers
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('running')}>
                  Running Only
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('exited')}>
                  Stopped Only
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>

      {/* Containers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredContainers.map((container, index) => (
          <ContainerCard
            key={container.id}
            container={container}
            index={index}
          />
        ))}
      </div>

      {filteredContainers.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No containers found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || statusFilter !== 'all' 
                ? "No containers match your current filters."
                : "No Docker containers are currently available."
              }
            </p>
            {!searchQuery && statusFilter === 'all' && (
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create First Container
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
