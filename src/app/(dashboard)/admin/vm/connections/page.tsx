/**
 * VM Connections Admin Page
 * Admin interface for managing VM connections
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import { VMConnectionDashboard } from '@/components/vm/connection';

export default function VMConnectionsAdminPage() {
  const searchParams = useSearchParams();
  const action = searchParams.get('action');

  const handleConnectionSelect = (vmId: string, connectionId: string) => {
    console.log(`Selected VM ${vmId} with connection ${connectionId}`);
    // Handle connection selection - could navigate to VM details or update state
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" as const }}
    >
      <VMConnectionDashboard
        onConnectionSelect={handleConnectionSelect}
        variant="glass"
        animate={true}
      />
    </motion.div>
  );
}
