/**
 * VM Admin Dashboard Layout
 * Main layout for VM administration pages with navigation and access control
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  Server,
  LayoutDashboard,
  Database,
  Activity,
  Shield,
  Heart,
  Settings,
  Users,
  ChevronRight,
  Menu,
  Key
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet';

interface VMAdminLayoutProps {
  children: React.ReactNode;
}

const navigationItems = [
  {
    title: 'Overview',
    href: '/admin/vm',
    icon: LayoutDashboard,
    description: 'VM infrastructure overview and statistics'
  },
  {
    title: 'Connections',
    href: '/admin/vm/connections',
    icon: Server,
    description: 'Manage VM connections and SSH sessions'
  },
  {
    title: 'SSH Keys',
    href: '/admin/vm/ssh-keys',
    icon: Key,
    description: 'SSH key management and authentication'
  },
  {
    title: 'Docker',
    href: '/admin/vm/docker',
    icon: Database,
    description: 'Container management and Docker operations'
  },
  {
    title: 'Monitoring',
    href: '/admin/vm/monitoring',
    icon: Activity,
    description: 'System monitoring and performance metrics'
  },
  {
    title: 'Security',
    href: '/admin/vm/security',
    icon: Shield,
    description: 'Access control and security management'
  },
  {
    title: 'Health',
    href: '/admin/vm/health',
    icon: Heart,
    description: 'Health checks and service status'
  },
  {
    title: 'Users',
    href: '/admin/vm/users',
    icon: Users,
    description: 'User management and permissions'
  },
  {
    title: 'Settings',
    href: '/admin/vm/settings',
    icon: Settings,
    description: 'VM service configuration and preferences'
  }
];

function VMAdminSidebar({ className }: { className?: string }) {
  const pathname = usePathname();

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-500/10 rounded-lg">
            <Server className="h-6 w-6 text-blue-500" />
          </div>
          <div>
            <h2 className="font-semibold text-lg">VM Admin</h2>
            <p className="text-sm text-muted-foreground">Infrastructure Management</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href || 
            (item.href !== '/admin/vm' && pathname.startsWith(item.href));
          
          return (
            <Link key={item.href} href={item.href}>
              <motion.div
                className={cn(
                  'flex items-center gap-3 p-3 rounded-lg transition-all duration-200',
                  'hover:bg-muted/50 group cursor-pointer',
                  isActive && 'bg-primary/10 text-primary border border-primary/20'
                )}
                whileHover={{ x: 4 }}
                whileTap={{ scale: 0.98 }}
              >
                <item.icon className={cn(
                  'h-5 w-5 transition-colors',
                  isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-foreground'
                )} />
                <div className="flex-1 min-w-0">
                  <p className={cn(
                    'font-medium text-sm',
                    isActive ? 'text-primary' : 'text-foreground'
                  )}>
                    {item.title}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {item.description}
                  </p>
                </div>
                {isActive && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-2 h-2 bg-primary rounded-full"
                  />
                )}
              </motion.div>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <Card className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium">System Status</span>
          </div>
          <p className="text-xs text-muted-foreground">
            All VM services operational
          </p>
        </Card>
      </div>
    </div>
  );
}

function VMAdminBreadcrumb() {
  const pathname = usePathname();
  
  const getBreadcrumbs = () => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [
      { label: 'Admin', href: '/admin' },
      { label: 'VM', href: '/admin/vm' }
    ];

    if (segments.length > 2) {
      const currentPage = segments[segments.length - 1];
      const navItem = navigationItems.find(item => 
        item.href.endsWith(`/${currentPage}`)
      );
      
      if (navItem) {
        breadcrumbs.push({
          label: navItem.title,
          href: pathname
        });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
      {breadcrumbs.map((crumb, index) => (
        <React.Fragment key={crumb.href}>
          {index > 0 && <ChevronRight className="h-4 w-4" />}
          <Link
            href={crumb.href}
            className={cn(
              'hover:text-foreground transition-colors',
              index === breadcrumbs.length - 1 && 'text-foreground font-medium'
            )}
          >
            {crumb.label}
          </Link>
        </React.Fragment>
      ))}
    </nav>
  );
}

function VMAdminHeader() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-6">
        <div className="flex items-center gap-4">
          {/* Mobile menu trigger */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="lg:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 p-0">
              <VMAdminSidebar />
            </SheetContent>
          </Sheet>

          <VMAdminBreadcrumb />
        </div>

        <div className="flex items-center gap-4">
          <Badge variant="outline" className="hidden sm:flex">
            Admin Access
          </Badge>
          
          {/* Quick actions could go here */}
          <div className="flex items-center gap-2">
            {/* Placeholder for user menu, notifications, etc. */}
          </div>
        </div>
      </div>
    </header>
  );
}

export default function VMAdminLayout({ children }: VMAdminLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-screen">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:flex w-80 border-r bg-card">
          <VMAdminSidebar />
        </aside>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <VMAdminHeader />
          
          <main className="flex-1 overflow-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, ease: "easeOut" as const }}
              className="container mx-auto p-6 space-y-6"
            >
              {children}
            </motion.div>
          </main>
        </div>
      </div>
    </div>
  );
}
