/**
 * VM Admin Overview Dashboard Page
 * Main dashboard showing VM infrastructure overview and statistics
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  Server,
  Database,
  Activity,
  Shield,
  Heart,
  Users,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Plus,
  RefreshCw,
  Settings,
  Eye,
  Zap,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  VMStatusIndicator,
  VMMetricsDisplay,
  VMLoading,
  VMError
} from '@/components/vm';
import { useVMConnection, useVMMonitoring, useVMHealth } from '@/hooks/vm';

// Mock data - in real app this would come from API/hooks
const mockVMStats = {
  total: 12,
  connected: 8,
  disconnected: 3,
  error: 1,
  containers: 45,
  runningContainers: 32,
  alerts: 3,
  users: 24
};

const mockRecentActivity = [
  {
    id: '1',
    type: 'connection',
    message: 'VM vm-prod-01 connected successfully',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    status: 'success'
  },
  {
    id: '2',
    type: 'alert',
    message: 'High CPU usage detected on vm-dev-03',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    status: 'warning'
  },
  {
    id: '3',
    type: 'container',
    message: 'Container nginx-proxy started on vm-prod-02',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    status: 'info'
  },
  {
    id: '4',
    type: 'security',
    message: 'New user admin-user added to vm-staging-01',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    status: 'info'
  }
];

const mockTopVMs = [
  {
    id: 'vm-prod-01',
    name: 'Production Server 1',
    status: 'connected',
    cpu: 45,
    memory: 67,
    containers: 8,
    uptime: '15d 4h'
  },
  {
    id: 'vm-prod-02',
    name: 'Production Server 2',
    status: 'connected',
    cpu: 32,
    memory: 54,
    containers: 6,
    uptime: '12d 8h'
  },
  {
    id: 'vm-dev-03',
    name: 'Development Server',
    status: 'error',
    cpu: 89,
    memory: 92,
    containers: 3,
    uptime: '2d 1h'
  }
];

function VMOverviewStats() {
  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      variants={containerVariants}
      initial="initial"
      animate="animate"
    >
      <motion.div variants={itemVariants}>
        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total VMs</p>
                <p className="text-3xl font-bold">{mockVMStats.total}</p>
                <div className="flex items-center gap-1 mt-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-500">+2 this week</span>
                </div>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <Server className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Connected</p>
                <p className="text-3xl font-bold text-green-500">{mockVMStats.connected}</p>
                <div className="flex items-center gap-1 mt-2">
                  <span className="text-sm text-muted-foreground">
                    {Math.round((mockVMStats.connected / mockVMStats.total) * 100)}% uptime
                  </span>
                </div>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <Activity className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Containers</p>
                <p className="text-3xl font-bold">{mockVMStats.containers}</p>
                <div className="flex items-center gap-1 mt-2">
                  <span className="text-sm text-green-500">
                    {mockVMStats.runningContainers} running
                  </span>
                </div>
              </div>
              <div className="p-3 bg-purple-500/10 rounded-full">
                <Database className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Alerts</p>
                <p className="text-3xl font-bold text-orange-500">{mockVMStats.alerts}</p>
                <div className="flex items-center gap-1 mt-2">
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-orange-500">Needs attention</span>
                </div>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <Shield className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}

function VMQuickActions() {
  const actions = [
    {
      title: 'New VM Connection',
      description: 'Add a new virtual machine',
      icon: Plus,
      href: '/admin/vm/connections?action=create',
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10'
    },
    {
      title: 'System Health',
      description: 'Check overall system status',
      icon: Heart,
      href: '/admin/vm/health',
      color: 'text-green-500',
      bgColor: 'bg-green-500/10'
    },
    {
      title: 'Monitor Resources',
      description: 'View performance metrics',
      icon: Activity,
      href: '/admin/vm/monitoring',
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10'
    },
    {
      title: 'Manage Users',
      description: 'Control access permissions',
      icon: Users,
      href: '/admin/vm/users',
      color: 'text-orange-500',
      bgColor: 'bg-orange-500/10'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {actions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Link href={action.href}>
                <div className="flex items-center gap-3 p-4 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer group">
                  <div className={cn('p-2 rounded-lg', action.bgColor)}>
                    <action.icon className={cn('h-5 w-5', action.color)} />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium group-hover:text-primary transition-colors">
                      {action.title}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {action.description}
                    </p>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function VMRecentActivity() {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'connection': return Server;
      case 'alert': return AlertTriangle;
      case 'container': return Database;
      case 'security': return Shield;
      default: return Activity;
    }
  };

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-orange-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    
    if (minutes < 60) return `${minutes}m ago`;
    return `${hours}h ago`;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Recent Activity
        </CardTitle>
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4 mr-2" />
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockRecentActivity.map((activity, index) => {
            const Icon = getActivityIcon(activity.type);
            return (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className={cn('p-2 rounded-full bg-muted', getActivityColor(activity.status))}>
                  <Icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">{activity.message}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatTime(activity.timestamp)}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

function VMTopPerformers() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          VM Performance Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockTopVMs.map((vm, index) => (
            <motion.div
              key={vm.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center justify-between p-4 rounded-lg border"
            >
              <div className="flex items-center gap-3">
                <VMStatusIndicator
                  status={vm.status as any}
                  size="sm"
                  showLabel={false}
                />
                <div>
                  <p className="font-medium">{vm.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {vm.containers} containers • {vm.uptime} uptime
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="text-center">
                  <p className="text-muted-foreground">CPU</p>
                  <p className={cn(
                    'font-medium',
                    vm.cpu > 80 ? 'text-red-500' : vm.cpu > 60 ? 'text-orange-500' : 'text-green-500'
                  )}>
                    {vm.cpu}%
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-muted-foreground">Memory</p>
                  <p className={cn(
                    'font-medium',
                    vm.memory > 80 ? 'text-red-500' : vm.memory > 60 ? 'text-orange-500' : 'text-green-500'
                  )}>
                    {vm.memory}%
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export default function VMAdminOverviewPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">VM Infrastructure Overview</h1>
          <p className="text-muted-foreground">
            Monitor and manage your virtual machine infrastructure
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <VMOverviewStats />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <VMQuickActions />
          <VMRecentActivity />
        </div>
        <div className="space-y-6">
          <VMTopPerformers />
        </div>
      </div>
    </div>
  );
}
