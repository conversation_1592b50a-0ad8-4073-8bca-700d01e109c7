/**
 * VM Health Admin Page
 * Admin interface for health monitoring and diagnostics
 */

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Heart,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Activity,
  Server,
  Database,
  Shield,
  Wifi,
  RefreshCw,
  Settings,
  Eye,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { VMStatusIndicator, VMHealthStatus } from '@/components/vm';

// Mock health data
const mockSystemHealth = {
  overall: 'healthy' as const,
  services: {
    connection: true,
    ssh: true,
    docker: true,
    system: true,
    resources: false,
    monitoring: true,
    security: true
  },
  lastCheck: new Date(),
  responseTime: 145,
  uptime: '15d 4h 32m'
};

const mockVMHealth = [
  {
    vmId: 'vm-prod-01',
    name: 'Production Server 1',
    overall: 'healthy' as const,
    services: {
      connection: true,
      ssh: true,
      docker: true,
      system: true,
      resources: true,
      monitoring: true,
      security: true
    },
    lastCheck: new Date(Date.now() - 2 * 60 * 1000),
    responseTime: 120,
    uptime: '15d 4h'
  },
  {
    vmId: 'vm-prod-02',
    name: 'Production Server 2',
    overall: 'healthy' as const,
    services: {
      connection: true,
      ssh: true,
      docker: true,
      system: true,
      resources: true,
      monitoring: true,
      security: true
    },
    lastCheck: new Date(Date.now() - 1 * 60 * 1000),
    responseTime: 98,
    uptime: '12d 8h'
  },
  {
    vmId: 'vm-dev-03',
    name: 'Development Server',
    overall: 'degraded' as const,
    services: {
      connection: true,
      ssh: true,
      docker: true,
      system: false,
      resources: false,
      monitoring: true,
      security: true
    },
    lastCheck: new Date(Date.now() - 5 * 60 * 1000),
    responseTime: 450,
    uptime: '2d 1h'
  }
];

const mockHealthHistory = [
  { timestamp: new Date(Date.now() - 60 * 60 * 1000), status: 'healthy', responseTime: 120 },
  { timestamp: new Date(Date.now() - 50 * 60 * 1000), status: 'healthy', responseTime: 135 },
  { timestamp: new Date(Date.now() - 40 * 60 * 1000), status: 'degraded', responseTime: 280 },
  { timestamp: new Date(Date.now() - 30 * 60 * 1000), status: 'degraded', responseTime: 320 },
  { timestamp: new Date(Date.now() - 20 * 60 * 1000), status: 'healthy', responseTime: 145 },
  { timestamp: new Date(Date.now() - 10 * 60 * 1000), status: 'healthy', responseTime: 125 }
];

function HealthOverview() {
  const healthyVMs = mockVMHealth.filter(vm => vm.overall === 'healthy').length;
  const degradedVMs = mockVMHealth.filter(vm => vm.overall === 'degraded').length;
  const unhealthyVMs = mockVMHealth.filter(vm => vm.overall === 'unhealthy').length;
  const totalVMs = mockVMHealth.length;

  const healthPercentage = Math.round((healthyVMs / totalVMs) * 100);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overall Health</p>
                <p className="text-3xl font-bold text-green-500">{healthPercentage}%</p>
                <p className="text-sm text-muted-foreground">System operational</p>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <Heart className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Healthy VMs</p>
                <p className="text-3xl font-bold text-green-500">{healthyVMs}</p>
                <p className="text-sm text-muted-foreground">of {totalVMs} total</p>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Degraded</p>
                <p className="text-3xl font-bold text-orange-500">{degradedVMs}</p>
                <p className="text-sm text-muted-foreground">Need attention</p>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <AlertTriangle className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Response Time</p>
                <p className="text-3xl font-bold">{mockSystemHealth.responseTime}ms</p>
                <p className="text-sm text-muted-foreground">Average</p>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <Activity className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

function ServiceHealthGrid() {
  const services = [
    { name: 'Connection', key: 'connection', icon: Wifi },
    { name: 'SSH', key: 'ssh', icon: Server },
    { name: 'Docker', key: 'docker', icon: Database },
    { name: 'System', key: 'system', icon: Activity },
    { name: 'Resources', key: 'resources', icon: Heart },
    { name: 'Monitoring', key: 'monitoring', icon: Eye },
    { name: 'Security', key: 'security', icon: Shield }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Service Health Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {services.map((service, index) => {
            const isHealthy = mockSystemHealth.services[service.key as keyof typeof mockSystemHealth.services];
            return (
              <motion.div
                key={service.key}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  'flex items-center gap-3 p-4 rounded-lg border transition-colors',
                  isHealthy 
                    ? 'bg-green-500/5 border-green-500/20 hover:bg-green-500/10' 
                    : 'bg-red-500/5 border-red-500/20 hover:bg-red-500/10'
                )}
              >
                <div className={cn(
                  'p-2 rounded-lg',
                  isHealthy ? 'bg-green-500/10' : 'bg-red-500/10'
                )}>
                  <service.icon className={cn(
                    'h-4 w-4',
                    isHealthy ? 'text-green-500' : 'text-red-500'
                  )} />
                </div>
                <div>
                  <p className="font-medium text-sm">{service.name}</p>
                  <p className={cn(
                    'text-xs',
                    isHealthy ? 'text-green-600' : 'text-red-600'
                  )}>
                    {isHealthy ? 'Healthy' : 'Issues'}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

function VMHealthList() {
  const formatLastCheck = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    return `${Math.floor(minutes / 60)}h ago`;
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-500';
      case 'degraded': return 'text-orange-500';
      case 'unhealthy': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 200) return 'text-green-500';
    if (time < 500) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          VM Health Status
        </CardTitle>
        <Button variant="ghost" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockVMHealth.map((vm, index) => {
            const healthyServices = Object.values(vm.services).filter(Boolean).length;
            const totalServices = Object.keys(vm.services).length;
            const healthPercentage = Math.round((healthyServices / totalServices) * 100);

            return (
              <motion.div
                key={vm.vmId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <VMHealthStatus
                    health={vm.overall}
                    size="sm"
                    showLabel={false}
                  />
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium">{vm.name}</p>
                      <Badge variant="outline" className={getHealthColor(vm.overall)}>
                        {vm.overall}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {healthyServices}/{totalServices} services healthy • 
                      Uptime: {vm.uptime}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-6 text-sm">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-1">Health</p>
                    <p className="font-medium">{healthPercentage}%</p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground mb-1">Response</p>
                    <p className={cn('font-medium', getResponseTimeColor(vm.responseTime))}>
                      {vm.responseTime}ms
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-muted-foreground mb-1">Last Check</p>
                    <p className="font-medium text-xs">
                      {formatLastCheck(vm.lastCheck)}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

function HealthHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Health History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {mockHealthHistory.map((entry, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center justify-between p-3 rounded-lg border"
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  'w-3 h-3 rounded-full',
                  entry.status === 'healthy' ? 'bg-green-500' :
                  entry.status === 'degraded' ? 'bg-orange-500' : 'bg-red-500'
                )} />
                <div>
                  <p className="text-sm font-medium capitalize">{entry.status}</p>
                  <p className="text-xs text-muted-foreground">
                    {entry.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">{entry.responseTime}ms</p>
                <p className="text-xs text-muted-foreground">Response time</p>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export default function VMHealthAdminPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Health Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor VM health status and service availability
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Run Health Check
          </Button>
          <Button size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Health Overview */}
      <HealthOverview />

      {/* Main Content */}
      <Tabs defaultValue="status" className="space-y-6">
        <TabsList>
          <TabsTrigger value="status">Status</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-6">
          <VMHealthList />
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          <ServiceHealthGrid />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <HealthHistory />
        </TabsContent>
      </Tabs>
    </div>
  );
}
