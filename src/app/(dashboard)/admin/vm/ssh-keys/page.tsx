/**
 * SSH Key Management Admin Page
 * Admin interface for managing SSH keys, assignments, and security
 */

'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Key, 
  Plus, 
  Search, 
  Filter, 
  RefreshCw, 
  Grid, 
  List,
  Upload,
  Download,
  Shield,
  Users,
  Server,
  AlertTriangle,
  TrendingUp,
  Activity,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSSHKeyManagement } from '@/hooks/vm/useSSHKeyManagement';
import { SSHKeyCard, SSHKeyCardCompact } from '@/components/vm/ssh/SSHKeyCard';
import { SSHKeyForm } from '@/components/vm/ssh/SSHKeyForm';
import { VMLoading, VMError, VMEmptyState } from '@/components/vm';
import { SSHKey, SSHKeyGenerationOptions, SSHKeyImportOptions } from '@/types/vm-frontend';

type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'active' | 'expired' | 'encrypted';

export default function SSHKeyManagementPage() {
  const [viewMode, setViewMode] = React.useState<ViewMode>('grid');
  const [filter, setFilter] = React.useState<FilterType>('all');
  const [searchQuery, setSearchQuery] = React.useState('');
  const [showCreateDialog, setShowCreateDialog] = React.useState(false);
  const [showImportDialog, setShowImportDialog] = React.useState(false);
  const [editingKey, setEditingKey] = React.useState<SSHKey | null>(null);

  const sshKeyManagement = useSSHKeyManagement({
    autoRefresh: true,
    refreshInterval: 60000,
    onKeyEvent: (keyId, event, data) => {
      console.log(`SSH Key ${keyId} event:`, event, data);
    }
  });

  const handleCreateKey = async (data: SSHKeyGenerationOptions) => {
    await sshKeyManagement.generateKey(data);
    setShowCreateDialog(false);
  };

  const handleImportKey = async (data: SSHKeyImportOptions) => {
    await sshKeyManagement.importKey(data);
    setShowImportDialog(false);
  };

  const handleEditKey = async (data: SSHKeyGenerationOptions | SSHKeyImportOptions) => {
    if (!editingKey) return;
    await sshKeyManagement.updateKey(editingKey.id, data as Partial<SSHKey>);
    setEditingKey(null);
  };

  const handleDeleteKey = async (keyId: string) => {
    if (confirm('Are you sure you want to delete this SSH key? This action cannot be undone.')) {
      await sshKeyManagement.deleteKey(keyId);
    }
  };

  const handleExportKey = async (keyId: string) => {
    try {
      const keyData = await sshKeyManagement.exportKey(keyId, false);
      const blob = new Blob([keyData], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ssh-key-${keyId}.pub`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const filteredKeys = sshKeyManagement.keys.filter(key => {
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!key.name.toLowerCase().includes(query) && 
          !key.description?.toLowerCase().includes(query) &&
          !key.tags.some(tag => tag.toLowerCase().includes(query))) {
        return false;
      }
    }

    // Apply status filter
    switch (filter) {
      case 'active':
        return key.isActive && (!key.expiresAt || key.expiresAt > new Date());
      case 'expired':
        return key.expiresAt && key.expiresAt < new Date();
      case 'encrypted':
        return key.isEncrypted;
      default:
        return true;
    }
  });

  function SSHKeyStatsCards() {
    const { stats } = sshKeyManagement;
    
    const statsData = [
      {
        title: 'Total Keys',
        value: stats.totalKeys,
        icon: Key,
        color: 'text-blue-500',
        bgColor: 'bg-blue-500/10'
      },
      {
        title: 'Active Keys',
        value: stats.activeKeys,
        icon: Shield,
        color: 'text-green-500',
        bgColor: 'bg-green-500/10'
      },
      {
        title: 'Encrypted Keys',
        value: stats.encryptedKeys,
        icon: Shield,
        color: 'text-purple-500',
        bgColor: 'bg-purple-500/10'
      },
      {
        title: 'Expired Keys',
        value: stats.expiredKeys,
        icon: AlertTriangle,
        color: 'text-red-500',
        bgColor: 'bg-red-500/10'
      }
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                    <p className="text-3xl font-bold">{stat.value}</p>
                  </div>
                  <div className={cn('p-3 rounded-full', stat.bgColor)}>
                    <stat.icon className={cn('h-6 w-6', stat.color)} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    );
  }

  function KeyTypeDistribution() {
    const { stats } = sshKeyManagement;
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Key Type Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(stats.keysByType).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    'w-3 h-3 rounded-full',
                    type === 'ed25519' ? 'bg-green-500' :
                    type === 'rsa' ? 'bg-blue-500' :
                    type === 'ecdsa' ? 'bg-purple-500' : 'bg-gray-500'
                  )} />
                  <span className="font-medium capitalize">{type}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">{count} keys</span>
                  <Badge variant="secondary">
                    {Math.round((count / stats.totalKeys) * 100)}%
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  function RecentUsage() {
    const { stats } = sshKeyManagement;
    
    const formatTime = (date: Date) => {
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      
      if (minutes < 60) return `${minutes}m ago`;
      return `${hours}h ago`;
    };

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Key Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.recentUsage.slice(0, 5).map((usage, index) => (
              <motion.div
                key={usage.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-3 rounded-lg border"
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    'w-2 h-2 rounded-full',
                    usage.success ? 'bg-green-500' : 'bg-red-500'
                  )} />
                  <div>
                    <p className="text-sm font-medium">{usage.action}</p>
                    <p className="text-xs text-muted-foreground">
                      Key: {usage.keyId.substring(0, 8)}... • VM: {usage.vmId}
                    </p>
                  </div>
                </div>
                <span className="text-xs text-muted-foreground">
                  {formatTime(usage.timestamp)}
                </span>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">SSH Key Management</h1>
          <p className="text-muted-foreground">
            Manage SSH keys for secure VM authentication
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => sshKeyManagement.refreshKeys()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Key
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setShowCreateDialog(true)}>
                <Key className="h-4 w-4 mr-2" />
                Generate New Key
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                <Upload className="h-4 w-4 mr-2" />
                Import Existing Key
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Statistics */}
      <SSHKeyStatsCards />

      {/* Main Content */}
      <Tabs defaultValue="keys" className="space-y-6">
        <TabsList>
          <TabsTrigger value="keys">SSH Keys</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="usage">Usage Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="keys" className="space-y-6">
          {/* Controls */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-4 flex-1">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search keys..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                        {filter !== 'all' && (
                          <Badge variant="secondary" className="ml-2">
                            {filter}
                          </Badge>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => setFilter('all')}>
                        All Keys
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilter('active')}>
                        Active Only
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilter('expired')}>
                        Expired Only
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilter('encrypted')}>
                        Encrypted Only
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="flex items-center gap-2">
                  <div className="flex items-center border rounded-lg">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="rounded-r-none"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Keys List */}
          {sshKeyManagement.loading && (
            <VMLoading message="Loading SSH keys..." />
          )}

          {sshKeyManagement.error && (
            <VMError
              message={sshKeyManagement.error}
              onRetry={() => sshKeyManagement.refreshKeys()}
              showRetry
            />
          )}

          {!sshKeyManagement.loading && !sshKeyManagement.error && filteredKeys.length === 0 && (
            <VMEmptyState
              icon={Key}
              title="No SSH keys found"
              message={searchQuery || filter !== 'all' 
                ? "No keys match your current filters."
                : "Get started by creating your first SSH key."
              }
              actionLabel={searchQuery || filter !== 'all' ? undefined : "Generate SSH Key"}
              onAction={searchQuery || filter !== 'all' ? undefined : () => setShowCreateDialog(true)}
            />
          )}

          {!sshKeyManagement.loading && !sshKeyManagement.error && filteredKeys.length > 0 && (
            <AnimatePresence mode="popLayout">
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredKeys.map((key) => (
                    <SSHKeyCard
                      key={key.id}
                      sshKey={key}
                      onEdit={setEditingKey}
                      onDelete={handleDeleteKey}
                      onExport={handleExportKey}
                      variant="glass"
                    />
                  ))}
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredKeys.map((key) => (
                    <SSHKeyCardCompact
                      key={key.id}
                      sshKey={key}
                      onEdit={setEditingKey}
                      onDelete={handleDeleteKey}
                    />
                  ))}
                </div>
              )}
            </AnimatePresence>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <KeyTypeDistribution />
            <RecentUsage />
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Usage Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Detailed usage logs and audit trail for SSH key operations.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Key Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Generate SSH Key</DialogTitle>
          </DialogHeader>
          <SSHKeyForm
            mode="create"
            onSubmit={handleCreateKey}
            onCancel={() => setShowCreateDialog(false)}
            loading={sshKeyManagement.loading}
            animate={false}
          />
        </DialogContent>
      </Dialog>

      {/* Import Key Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import SSH Key</DialogTitle>
          </DialogHeader>
          <SSHKeyForm
            mode="import"
            onSubmit={handleImportKey}
            onCancel={() => setShowImportDialog(false)}
            loading={sshKeyManagement.loading}
            animate={false}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Key Dialog */}
      <Dialog open={!!editingKey} onOpenChange={(open) => !open && setEditingKey(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit SSH Key</DialogTitle>
          </DialogHeader>
          {editingKey && (
            <SSHKeyForm
              mode="edit"
              initialData={editingKey}
              onSubmit={handleEditKey}
              onCancel={() => setEditingKey(null)}
              loading={sshKeyManagement.loading}
              animate={false}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
