import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { Cloud, Rocket, Globe, Cpu, Database, ArrowRight, Star } from 'lucide-react';
import { RegisterForm } from '@/components/auth/register-form';

export const metadata: Metadata = {
  title: 'Sign Up - Omnispace',
  description: 'Create your Omnispace account and start building in the cloud with AI-powered development environments.',
};

export default function RegisterPage() {
  return (
    <div className="min-h-screen flex">
      {/* Left Column - Branding & Information */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-600 via-purple-700 to-blue-800 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse" />
          <div className="absolute top-60 right-20 w-24 h-24 bg-white rounded-full blur-xl animate-pulse delay-1000" />
          <div className="absolute bottom-40 left-20 w-28 h-28 bg-white rounded-full blur-xl animate-pulse delay-2000" />
          <div className="absolute bottom-20 right-10 w-20 h-20 bg-white rounded-full blur-xl animate-pulse delay-3000" />
        </div>

        <div className="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
          {/* Logo */}
          <div className="flex items-center gap-3 mb-12">
            <div className="flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg">
              <Cloud className="h-7 w-7 text-white" />
            </div>
            <span className="text-2xl font-bold">Omnispace</span>
          </div>

          {/* Main Content */}
          <div className="space-y-8">
            <div>
              <h1 className="text-4xl font-bold mb-4 leading-tight">
                Start building in the
                <span className="block text-purple-200">cloud today</span>
              </h1>
              <p className="text-xl text-purple-100 leading-relaxed">
                Join thousands of developers who are already building the future with Omnispace's AI-powered cloud development platform.
              </p>
            </div>

            {/* Features */}
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Rocket className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Launch in Seconds</h3>
                  <p className="text-purple-100 text-sm">
                    Get your development environment ready instantly with pre-configured templates
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Globe className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Global Infrastructure</h3>
                  <p className="text-purple-100 text-sm">
                    Deploy anywhere with our worldwide network of data centers
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Cpu className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Powerful Computing</h3>
                  <p className="text-purple-100 text-sm">
                    Scale from hobby projects to enterprise applications seamlessly
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Database className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Integrated Services</h3>
                  <p className="text-purple-100 text-sm">
                    Databases, APIs, and deployment tools all in one platform
                  </p>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold mb-1">50K+</div>
                  <div className="text-purple-200 text-xs">Developers</div>
                </div>
                <div>
                  <div className="text-2xl font-bold mb-1">1M+</div>
                  <div className="text-purple-200 text-xs">Projects</div>
                </div>
                <div>
                  <div className="text-2xl font-bold mb-1">99.9%</div>
                  <div className="text-purple-200 text-xs">Uptime</div>
                </div>
              </div>
            </div>

            {/* Social Proof */}
            <div className="flex items-center gap-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-purple-100 text-sm">
                Rated 4.9/5 by 10,000+ developers
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Column - Register Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-purple-900">
        {/* Mobile Logo */}
        <div className="lg:hidden flex items-center justify-center mb-8">
          <Link href="/" className="flex items-center gap-3">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-lg">
              <Cloud className="h-7 w-7 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              Omnispace
            </span>
          </Link>
        </div>

        <div className="w-full max-w-md mx-auto">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Create your account
            </h1>
            <p className="text-muted-foreground">
              Join Omnispace and start building in the cloud
            </p>
          </div>

          {/* Register Form */}
          <RegisterForm showHeader={false} />

          {/* Additional Links */}
          <div className="mt-8 text-center space-y-4">
            <div className="text-sm text-muted-foreground">
              Already have an account?{' '}
              <Link href="/auth/login" className="text-primary hover:underline font-medium inline-flex items-center gap-1">
                Sign in
                <ArrowRight className="h-3 w-3" />
              </Link>
            </div>
            
            <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground">
              <Link href="/help" className="hover:text-foreground transition-colors">
                Help
              </Link>
              <Link href="/privacy" className="hover:text-foreground transition-colors">
                Privacy
              </Link>
              <Link href="/terms" className="hover:text-foreground transition-colors">
                Terms
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
