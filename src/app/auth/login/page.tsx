import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { Cloud, Code, Zap, Shield, Users, ArrowRight } from 'lucide-react';
import { LoginForm } from '@/components/auth/login-form';

export const metadata: Metadata = {
  title: 'Sign In - Omnispace',
  description: 'Sign in to your Omnispace account to access your cloud development environments.',
};

export default function LoginPage() {
  return (
    <div className="min-h-screen flex">
      {/* Left Column - Branding & Information */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse" />
          <div className="absolute top-60 right-20 w-24 h-24 bg-white rounded-full blur-xl animate-pulse delay-1000" />
          <div className="absolute bottom-40 left-20 w-28 h-28 bg-white rounded-full blur-xl animate-pulse delay-2000" />
          <div className="absolute bottom-20 right-10 w-20 h-20 bg-white rounded-full blur-xl animate-pulse delay-3000" />
        </div>

        <div className="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
          {/* Logo */}
          <div className="flex items-center gap-3 mb-12">
            <div className="flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg">
              <Cloud className="h-7 w-7 text-white" />
            </div>
            <span className="text-2xl font-bold">Omnispace</span>
          </div>

          {/* Main Content */}
          <div className="space-y-8">
            <div>
              <h1 className="text-4xl font-bold mb-4 leading-tight">
                Welcome back to your
                <span className="block text-blue-200">cloud workspace</span>
              </h1>
              <p className="text-xl text-blue-100 leading-relaxed">
                Continue building amazing projects with AI-powered development environments in the cloud.
              </p>
            </div>

            {/* Features */}
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Code className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Instant Development</h3>
                  <p className="text-blue-100 text-sm">
                    Spin up fully configured development environments in seconds
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">AI-Powered Assistance</h3>
                  <p className="text-blue-100 text-sm">
                    Get intelligent code suggestions and automated workflows
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Secure & Reliable</h3>
                  <p className="text-blue-100 text-sm">
                    Enterprise-grade security with 99.9% uptime guarantee
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex-shrink-0">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1">Team Collaboration</h3>
                  <p className="text-blue-100 text-sm">
                    Real-time collaboration with your team members
                  </p>
                </div>
              </div>
            </div>

            {/* Testimonial */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <p className="text-blue-100 italic mb-3">
                "Omnispace has revolutionized our development workflow. We can now focus on building great products instead of managing infrastructure."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-xs font-semibold">JS</span>
                </div>
                <div>
                  <p className="font-semibold text-sm">Jane Smith</p>
                  <p className="text-blue-200 text-xs">Lead Developer at TechCorp</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Column - Login Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900">
        {/* Mobile Logo */}
        <div className="lg:hidden flex items-center justify-center mb-8">
          <Link href="/" className="flex items-center gap-3">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg">
              <Cloud className="h-7 w-7 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              Omnispace
            </span>
          </Link>
        </div>

        <div className="w-full max-w-md mx-auto">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Welcome back
            </h1>
            <p className="text-muted-foreground">
              Sign in to your Omnispace account
            </p>
          </div>

          {/* Login Form */}
          <LoginForm showHeader={false} showDemoCredentials={true} />

          {/* Additional Links */}
          <div className="mt-8 text-center space-y-4">
            <div className="text-sm text-muted-foreground">
              New to Omnispace?{' '}
              <Link href="/auth/register" className="text-primary hover:underline font-medium inline-flex items-center gap-1">
                Create an account
                <ArrowRight className="h-3 w-3" />
              </Link>
            </div>
            
            <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground">
              <Link href="/help" className="hover:text-foreground transition-colors">
                Help
              </Link>
              <Link href="/privacy" className="hover:text-foreground transition-colors">
                Privacy
              </Link>
              <Link href="/terms" className="hover:text-foreground transition-colors">
                Terms
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
