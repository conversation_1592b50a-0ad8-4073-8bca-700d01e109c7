'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AppwriteDashboard,
  EnhancedAuthForm,
  DocumentManager,
  FileUpload,
  useAuth
} from '@/components/appwrite';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Database, 
  HardDrive, 
  Shield, 
  Zap,
  Code,
  Sparkles,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

export default function AppwriteDemoPage() {
  const { isAuthenticated, user } = useAuth();
  const [activeDemo, setActiveDemo] = useState('overview');

  const features = [
    {
      icon: Shield,
      title: 'Authentication',
      description: 'Secure user authentication with OAuth support',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: Database,
      title: 'Database',
      description: 'Real-time database with document management',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: HardDrive,
      title: 'Storage',
      description: 'File upload and management with transformations',
      color: 'from-purple-500 to-pink-500',
    },
    {
      icon: Zap,
      title: 'Functions',
      description: 'Serverless functions for backend logic',
      color: 'from-orange-500 to-red-500',
    },
  ];

  const codeExamples = [
    {
      title: 'Authentication Hook',
      code: `import { useAppwriteAuth } from '@/hooks/useAppwriteAuth';

const { login, register, user, isLoading } = useAppwriteAuth();

// Login user
const handleLogin = async () => {
  const result = await login({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (result.success) {
    console.log('User logged in:', result.data);
  }
};`,
    },
    {
      title: 'Database Operations',
      code: `import { useAppwriteDatabase } from '@/hooks/useAppwriteDatabase';

const { createDocument, listDocuments } = useAppwriteDatabase();

// Create document
const createUser = async () => {
  const result = await createDocument({
    databaseId: 'my-database',
    collectionId: 'users',
    data: { name: 'John Doe', email: '<EMAIL>' }
  });
};`,
    },
    {
      title: 'File Upload',
      code: `import { FileUpload } from '@/components/storage/FileUpload';

<FileUpload
  bucketId="my-bucket"
  onUploadComplete={(files) => {
    console.log('Uploaded files:', files);
  }}
  maxFiles={5}
  maxSize={10 * 1024 * 1024} // 10MB
/>`,
    },
  ];

  if (isAuthenticated) {
    return <AppwriteDashboard />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            <Sparkles className="h-6 w-6 text-blue-600" />
            <span className="text-sm font-semibold uppercase tracking-wide">
              Appwrite Integration Demo
            </span>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Full-Stack Appwrite
            </span>
            <br />
            <span className="text-gray-900 dark:text-white">
              Integration
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Experience a complete Appwrite integration with authentication, database operations, 
            file storage, and serverless functions - all with beautiful UI components and TypeScript support.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8"
              onClick={() => setActiveDemo('auth')}
            >
              Try Demo
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => setActiveDemo('code')}
            >
              <Code className="mr-2 h-4 w-4" />
              View Code
            </Button>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {features.map((feature, index) => (
            <Card key={feature.title} className="relative overflow-hidden group hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className={`inline-flex p-3 rounded-lg bg-gradient-to-r ${feature.color} text-white mb-4`}>
                  <feature.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Demo Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Tabs value={activeDemo} onValueChange={setActiveDemo} className="space-y-8">
            <TabsList className="grid w-full grid-cols-4 max-w-2xl mx-auto">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="auth">Auth</TabsTrigger>
              <TabsTrigger value="database">Database</TabsTrigger>
              <TabsTrigger value="code">Code</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">Complete Appwrite Integration</CardTitle>
                  <CardDescription className="text-lg">
                    This demo showcases a production-ready Appwrite integration with modern React components
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        Frontend Features
                      </h3>
                      <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                        <li>• React hooks for all Appwrite services</li>
                        <li>• TypeScript support with full type safety</li>
                        <li>• Beautiful UI components with Framer Motion</li>
                        <li>• Form validation with React Hook Form & Zod</li>
                        <li>• File upload with progress tracking</li>
                        <li>• Real-time database operations</li>
                        <li>• OAuth integration (Google, GitHub)</li>
                        <li>• Responsive design with Tailwind CSS</li>
                      </ul>
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        Backend Features
                      </h3>
                      <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                        <li>• Server-side Appwrite SDK integration</li>
                        <li>• RESTful API endpoints</li>
                        <li>• Comprehensive error handling</li>
                        <li>• Rate limiting and security middleware</li>
                        <li>• Health monitoring and metrics</li>
                        <li>• Structured logging system</li>
                        <li>• Connection pooling and retry logic</li>
                        <li>• Production-ready architecture</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Auth Tab */}
            <TabsContent value="auth" className="space-y-6">
              <div className="max-w-md mx-auto">
                <EnhancedAuthForm
                  onSuccess={() => {
                    console.log('Authentication successful!');
                  }}
                  showOAuth={true}
                />
              </div>
            </TabsContent>

            {/* Database Tab */}
            <TabsContent value="database" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Database Demo</CardTitle>
                  <CardDescription>
                    Try the document management interface (requires authentication)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isAuthenticated ? (
                    <DocumentManager
                      databaseId="demo-db"
                      collectionId="demo-collection"
                      title="Demo Documents"
                      description="Create, read, update, and delete documents"
                    />
                  ) : (
                    <div className="text-center py-12">
                      <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">Authentication Required</h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-4">
                        Please sign in to try the database features
                      </p>
                      <Button onClick={() => setActiveDemo('auth')}>
                        Go to Authentication
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Code Tab */}
            <TabsContent value="code" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {codeExamples.map((example, index) => (
                  <Card key={example.title}>
                    <CardHeader>
                      <CardTitle className="text-lg">{example.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto">
                        <code>{example.code}</code>
                      </pre>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="text-center mt-16 pt-8 border-t border-gray-200 dark:border-gray-700"
        >
          <p className="text-gray-600 dark:text-gray-300">
            Built with ❤️ using Appwrite, Next.js, TypeScript, and Tailwind CSS
          </p>
        </motion.div>
      </div>
    </div>
  );
}
