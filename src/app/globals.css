@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.99 0.005 264);
  --foreground: oklch(0.09 0.02 264);
  --card: oklch(0.98 0.008 264);
  --card-foreground: oklch(0.12 0.025 264);
  --popover: oklch(0.98 0.008 264);
  --popover-foreground: oklch(0.12 0.025 264);
  --primary: oklch(0.55 0.18 264);
  --primary-foreground: oklch(0.98 0.008 264);
  --secondary: oklch(0.94 0.015 264);
  --secondary-foreground: oklch(0.25 0.04 264);
  --muted: oklch(0.95 0.012 264);
  --muted-foreground: oklch(0.48 0.06 264);
  --accent: oklch(0.92 0.02 280);
  --accent-foreground: oklch(0.28 0.05 280);
  --destructive: oklch(0.62 0.22 25);
  --border: oklch(0.88 0.02 264);
  --input: oklch(0.9 0.018 264);
  --ring: oklch(0.55 0.18 264);
  --chart-1: oklch(0.65 0.25 35);
  --chart-2: oklch(0.58 0.15 200);
  --chart-3: oklch(0.52 0.18 280);
  --chart-4: oklch(0.72 0.2 120);
  --chart-5: oklch(0.68 0.22 320);
  --sidebar: oklch(0.97 0.01 264);
  --sidebar-foreground: oklch(0.15 0.03 264);
  --sidebar-primary: oklch(0.55 0.18 264);
  --sidebar-primary-foreground: oklch(0.98 0.008 264);
  --sidebar-accent: oklch(0.92 0.02 280);
  --sidebar-accent-foreground: oklch(0.28 0.05 280);
  --sidebar-border: oklch(0.85 0.025 264);
  --sidebar-ring: oklch(0.55 0.18 264);
}

.dark {
  --background: oklch(0.08 0.015 264);
  --foreground: oklch(0.95 0.01 264);
  --card: oklch(0.12 0.02 264);
  --card-foreground: oklch(0.92 0.012 264);
  --popover: oklch(0.12 0.02 264);
  --popover-foreground: oklch(0.92 0.012 264);
  --primary: oklch(0.65 0.2 264);
  --primary-foreground: oklch(0.08 0.015 264);
  --secondary: oklch(0.18 0.025 264);
  --secondary-foreground: oklch(0.88 0.015 264);
  --muted: oklch(0.16 0.022 264);
  --muted-foreground: oklch(0.65 0.05 264);
  --accent: oklch(0.22 0.03 280);
  --accent-foreground: oklch(0.85 0.02 280);
  --destructive: oklch(0.68 0.2 25);
  --border: oklch(0.25 0.03 264);
  --input: oklch(0.2 0.025 264);
  --ring: oklch(0.65 0.2 264);
  --chart-1: oklch(0.7 0.22 35);
  --chart-2: oklch(0.62 0.18 200);
  --chart-3: oklch(0.58 0.2 280);
  --chart-4: oklch(0.75 0.18 120);
  --chart-5: oklch(0.72 0.2 320);
  --sidebar: oklch(0.1 0.018 264);
  --sidebar-foreground: oklch(0.9 0.012 264);
  --sidebar-primary: oklch(0.65 0.2 264);
  --sidebar-primary-foreground: oklch(0.08 0.015 264);
  --sidebar-accent: oklch(0.22 0.03 280);
  --sidebar-accent-foreground: oklch(0.85 0.02 280);
  --sidebar-border: oklch(0.25 0.03 264);
  --sidebar-ring: oklch(0.65 0.2 264);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Smooth scrolling for anchor links */
  html {
    scroll-behavior: smooth;
  }
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px oklch(0.55 0.18 264 / 0.3);
  }
  50% {
    box-shadow: 0 0 30px oklch(0.55 0.18 264 / 0.6);
  }
}

/* Utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Staggered animations */
.animate-stagger-1 {
  animation-delay: 0.1s;
}

.animate-stagger-2 {
  animation-delay: 0.2s;
}

.animate-stagger-3 {
  animation-delay: 0.3s;
}

.animate-stagger-4 {
  animation-delay: 0.4s;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px oklch(0.5 0.1 264 / 0.15);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, oklch(0.55 0.18 264), oklch(0.52 0.18 280));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced gradient variants */
.gradient-text-primary {
  background: linear-gradient(135deg, oklch(0.55 0.18 264), oklch(0.65 0.2 264));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-accent {
  background: linear-gradient(135deg, oklch(0.52 0.18 280), oklch(0.68 0.22 320));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced background gradients */
.bg-gradient-primary {
  background: linear-gradient(135deg, oklch(0.55 0.18 264), oklch(0.65 0.2 264));
}

.bg-gradient-accent {
  background: linear-gradient(135deg, oklch(0.52 0.18 280), oklch(0.68 0.22 320));
}

.bg-gradient-surface {
  background: linear-gradient(135deg, oklch(0.98 0.008 264), oklch(0.95 0.012 264));
}

/* Dark mode gradients */
.dark .bg-gradient-surface {
  background: linear-gradient(135deg, oklch(0.12 0.02 264), oklch(0.16 0.022 264));
}
