#!/usr/bin/env tsx

/**
 * Comprehensive Admin Environment Setup Script
 * 
 * Creates a complete testing environment with:
 * - Default admin user with full privileges
 * - Realistic test data for all system features
 * - Sample organizations, workspaces, and VMs
 * - Test files and configurations
 * 
 * Usage:
 * pnpm setup:admin-env
 * or
 * npx tsx src/scripts/setup-admin-environment.ts
 */

import { Client, Databases, Users, Storage, ID } from 'node-appwrite';
import { config } from 'dotenv';
import crypto from 'crypto';

// Load environment variables
config({ path: '.env.local' });

interface AdminSetupConfig {
  endpoint: string;
  projectId: string;
  apiKey: string;
  databaseId: string;
  collections: {
    users: string;
    organizations: string;
    workspaces: string;
    vms: string;
    sessions: string;
  };
  buckets: {
    avatars: string;
    files: string;
  };
}

interface AdminUser {
  id: string;
  email: string;
  password: string;
  name: string;
  role: 'admin';
}

interface TestOrganization {
  id: string;
  name: string;
  description: string;
  ownerId: string;
  members: string[];
  settings: any;
}

interface TestWorkspace {
  id: string;
  name: string;
  description: string;
  type: string;
  ownerId: string;
  organizationId?: string;
  status: string;
  configuration: any;
  containerId?: string;
}

interface TestVM {
  id: string;
  name: string;
  description: string;
  ownerId: string;
  status: string;
  template: string;
  resources: any;
  network: any;
  createdAt: string;
  lastActivity?: string;
}

class AdminEnvironmentSetup {
  private client: Client;
  private databases: Databases;
  private users: Users;
  private storage: Storage;
  private config: AdminSetupConfig;
  private adminUser: AdminUser;

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        users: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
        organizations: process.env.NEXT_PUBLIC_APPWRITE_ORGANIZATIONS_COLLECTION_ID || 'organizations',
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
        vms: process.env.NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID!,
        sessions: process.env.NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID!,
      },
      buckets: {
        avatars: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID!,
        files: process.env.NEXT_PUBLIC_APPWRITE_FILES_BUCKET_ID || 'files',
      }
    };

    // Initialize Appwrite client
    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
    this.users = new Users(this.client);
    this.storage = new Storage(this.client);

    // Define admin user
    this.adminUser = {
      id: 'admin-user-omnispace',
      email: '<EMAIL>',
      password: 'OmnispaceAdmin2024!',
      name: 'System Administrator',
      role: 'admin'
    };
  }

  async setup(): Promise<void> {
    console.log('🚀 Setting up comprehensive admin environment...\n');

    try {
      // Step 1: Create admin user
      const adminUserId = await this.createAdminUser();

      // Step 2: Create test organizations
      const organizations = await this.createTestOrganizations(adminUserId);

      // Step 3: Create test workspaces
      const workspaces = await this.createTestWorkspaces(adminUserId, organizations);

      // Step 4: Create test VMs
      const vms = await this.createTestVMs(adminUserId);

      // Step 5: Create test sessions
      await this.createTestSessions(adminUserId);

      // Step 6: Create additional test users
      const testUsers = await this.createTestUsers();

      // Step 7: Create collaborative workspaces
      await this.createCollaborativeWorkspaces(adminUserId, testUsers, organizations);

      console.log('\n✅ Admin environment setup completed successfully!');
      console.log('\n📋 Created Resources:');
      console.log(`   👤 Admin User: ${this.adminUser.email} (${this.adminUser.password})`);
      console.log(`   🏢 Organizations: ${organizations.length}`);
      console.log(`   💻 Workspaces: ${workspaces.length}`);
      console.log(`   🖥️  VMs: ${vms.length}`);
      console.log(`   👥 Test Users: ${testUsers.length}`);
      console.log('\n🎯 Admin Dashboard Features Ready:');
      console.log('   ✅ User Management');
      console.log('   ✅ Organization Management');
      console.log('   ✅ Workspace Management');
      console.log('   ✅ VM Lifecycle Operations');
      console.log('   ✅ System Analytics');
      console.log('   ✅ Resource Monitoring');

    } catch (error) {
      console.error('\n❌ Admin environment setup failed:', error);
      throw error;
    }
  }

  private async createAdminUser(): Promise<string> {
    console.log('👤 Creating admin user...');

    try {
      // Check if admin user already exists
      const existingUsers = await this.users.list();
      const existingAdmin = existingUsers.users.find(u => u.email === this.adminUser.email);

      if (existingAdmin) {
        console.log(`ℹ️  Admin user already exists: ${this.adminUser.email}`);
        return existingAdmin.$id;
      }

      // Create admin user account
      const user = await this.users.create(
        this.adminUser.id,
        this.adminUser.email,
        undefined, // phone
        this.adminUser.password,
        this.adminUser.name
      );

      // Create comprehensive admin profile
      const adminProfile = {
        name: this.adminUser.name,
        email: this.adminUser.email,
        bio: 'System Administrator with full access to all Omnispace platform features. Responsible for user management, system monitoring, and platform maintenance.',
        company: 'Omnispace Platform',
        location: 'Global',
        website: 'https://omnispace.platform',
        avatar: '', // Will be set later if avatar upload is implemented
        role: 'admin',
        permissions: JSON.stringify([
          'user.manage',
          'organization.manage',
          'workspace.manage',
          'vm.manage',
          'system.monitor',
          'analytics.view',
          'settings.manage'
        ]),
        preferences: JSON.stringify({
          theme: 'dark',
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            vmUpdates: true,
            securityAlerts: true,
            systemAlerts: true,
            userActivity: true
          },
          dashboard: {
            defaultView: 'admin',
            itemsPerPage: 25,
            autoRefresh: true,
            refreshInterval: 30,
            showAdvancedMetrics: true
          },
          security: {
            twoFactorEnabled: false,
            sessionTimeout: 480, // 8 hours
            requirePasswordChange: false
          }
        }),
        metadata: JSON.stringify({
          createdBy: 'system',
          accountType: 'admin',
          lastLogin: new Date().toISOString(),
          loginCount: 1,
          systemVersion: '1.0.0'
        })
      };

      await this.databases.createDocument(
        this.config.databaseId,
        this.config.collections.users,
        user.$id,
        adminProfile
      );

      console.log(`✅ Admin user created: ${this.adminUser.email}`);
      console.log(`🔑 Password: ${this.adminUser.password}`);

      return user.$id;

    } catch (error: any) {
      if (error.message?.includes('already exists')) {
        console.log(`ℹ️  Admin user already exists: ${this.adminUser.email}`);
        const existingUsers = await this.users.list();
        const existingAdmin = existingUsers.users.find(u => u.email === this.adminUser.email);
        return existingAdmin!.$id;
      }
      throw error;
    }
  }

  private async createTestOrganizations(adminUserId: string): Promise<TestOrganization[]> {
    console.log('\n🏢 Creating test organizations...');

    const organizations: TestOrganization[] = [
      {
        id: 'org-omnispace-main',
        name: 'Omnispace Platform',
        description: 'Main organization for Omnispace platform administration and development.',
        ownerId: adminUserId,
        members: [adminUserId],
        settings: {
          allowPublicWorkspaces: false,
          defaultWorkspaceType: 'development-env',
          maxWorkspacesPerUser: 20,
          resourceLimits: {
            maxCpu: 16,
            maxMemory: 32768,
            maxStorage: 500
          },
          securitySettings: {
            enforceStrongPasswords: true,
            requireTwoFactor: false,
            sessionTimeout: 480
          },
          branding: {
            logo: 'omnispace_logo.png',
            primaryColor: '#3B82F6',
            accentColor: '#10B981'
          }
        }
      },
      {
        id: 'org-development-team',
        name: 'Development Team',
        description: 'Organization for software development teams working on various projects.',
        ownerId: adminUserId,
        members: [adminUserId],
        settings: {
          allowPublicWorkspaces: true,
          defaultWorkspaceType: 'development-env',
          maxWorkspacesPerUser: 10,
          resourceLimits: {
            maxCpu: 8,
            maxMemory: 16384,
            maxStorage: 250
          },
          securitySettings: {
            enforceStrongPasswords: true,
            requireTwoFactor: false,
            sessionTimeout: 360
          }
        }
      },
      {
        id: 'org-data-science',
        name: 'Data Science Lab',
        description: 'Organization for data scientists and machine learning engineers.',
        ownerId: adminUserId,
        members: [adminUserId],
        settings: {
          allowPublicWorkspaces: false,
          defaultWorkspaceType: 'python-dev',
          maxWorkspacesPerUser: 5,
          resourceLimits: {
            maxCpu: 16,
            maxMemory: 65536,
            maxStorage: 1000
          },
          securitySettings: {
            enforceStrongPasswords: true,
            requireTwoFactor: true,
            sessionTimeout: 240
          }
        }
      }
    ];

    const createdOrganizations: TestOrganization[] = [];

    for (const orgData of organizations) {
      try {
        // Check if organization already exists
        const existing = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.organizations
        );

        const existingOrg = existing.documents.find((doc: any) => doc.name === orgData.name);
        if (existingOrg) {
          console.log(`ℹ️  Organization '${orgData.name}' already exists`);
          createdOrganizations.push(orgData);
          continue;
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.organizations,
          orgData.id,
          {
            name: orgData.name,
            description: orgData.description,
            ownerId: orgData.ownerId,
            members: orgData.members,
            settings: JSON.stringify(orgData.settings),
          }
        );

        console.log(`✅ Created organization: ${orgData.name}`);
        createdOrganizations.push(orgData);

      } catch (error: any) {
        console.error(`❌ Failed to create organization ${orgData.name}:`, error.message);
      }
    }

    return createdOrganizations;
  }

  private async createTestWorkspaces(adminUserId: string, organizations: TestOrganization[]): Promise<TestWorkspace[]> {
    console.log('\n💻 Creating test workspaces...');

    const workspaces: TestWorkspace[] = [
      // Admin's personal workspaces
      {
        id: 'ws-admin-python-ml',
        name: 'Admin Python ML Environment',
        description: 'Advanced Python environment for machine learning and data science with pre-installed libraries.',
        type: 'python-dev',
        ownerId: adminUserId,
        organizationId: organizations[0]?.id,
        status: 'running',
        configuration: {
          framework: 'jupyter',
          pythonVersion: '3.11',
          packages: ['tensorflow', 'pytorch', 'scikit-learn', 'pandas', 'numpy', 'matplotlib', 'seaborn', 'jupyter'],
          resources: { cpu: 8, memory: 16384, storage: 100 },
          gpu: { enabled: true, type: 'nvidia-t4' },
          environment: {
            JUPYTER_ENABLE_LAB: 'yes',
            CUDA_VISIBLE_DEVICES: '0'
          },
          ports: { '8888': '8888', '6006': '6006' }
        },
        containerId: 'container-admin-python-ml-001'
      },
      {
        id: 'ws-admin-react-dashboard',
        name: 'Admin Dashboard Development',
        description: 'React TypeScript environment for developing the Omnispace admin dashboard.',
        type: 'node-react',
        ownerId: adminUserId,
        organizationId: organizations[0]?.id,
        status: 'running',
        configuration: {
          framework: 'react',
          nodeVersion: '20',
          packageManager: 'pnpm',
          packages: ['react', 'typescript', 'next', 'tailwindcss', 'framer-motion', '@radix-ui/react-dialog'],
          resources: { cpu: 4, memory: 8192, storage: 50 },
          environment: {
            NODE_ENV: 'development',
            NEXT_TELEMETRY_DISABLED: '1'
          },
          ports: { '3000': '3000', '3001': '3001' }
        },
        containerId: 'container-admin-react-001'
      },
      {
        id: 'ws-admin-ubuntu-desktop',
        name: 'Admin Full Desktop Environment',
        description: 'Complete Ubuntu desktop environment for system administration and testing.',
        type: 'ubuntu-desktop',
        ownerId: adminUserId,
        organizationId: organizations[0]?.id,
        status: 'stopped',
        configuration: {
          desktopEnvironment: 'gnome',
          preinstalledApps: ['firefox', 'vscode', 'git', 'docker', 'kubectl', 'terraform'],
          resources: { cpu: 8, memory: 16384, storage: 200 },
          vnc: { enabled: true, password: 'admin123', port: 5901 },
          ssh: { enabled: true, port: 22 }
        },
        containerId: 'container-admin-ubuntu-001'
      },
      {
        id: 'ws-dev-microservices',
        name: 'Microservices Development',
        description: 'Node.js environment for developing microservices with Docker and Kubernetes.',
        type: 'node-react',
        ownerId: adminUserId,
        organizationId: organizations[1]?.id,
        status: 'running',
        configuration: {
          framework: 'express',
          nodeVersion: '20',
          packageManager: 'npm',
          packages: ['express', 'typescript', 'prisma', 'jest', 'supertest', 'docker'],
          resources: { cpu: 4, memory: 8192, storage: 75 },
          environment: {
            NODE_ENV: 'development',
            DATABASE_URL: 'postgresql://localhost:5432/microservices'
          },
          ports: { '3000': '3000', '5432': '5432', '6379': '6379' }
        },
        containerId: 'container-dev-microservices-001'
      },
      {
        id: 'ws-dev-frontend-testing',
        name: 'Frontend Testing Environment',
        description: 'React environment with comprehensive testing setup including Cypress and Jest.',
        type: 'node-react',
        ownerId: adminUserId,
        organizationId: organizations[1]?.id,
        status: 'paused',
        configuration: {
          framework: 'react',
          nodeVersion: '18',
          packageManager: 'yarn',
          packages: ['react', 'typescript', 'jest', 'cypress', 'testing-library', 'storybook'],
          resources: { cpu: 2, memory: 4096, storage: 40 },
          environment: {
            CI: 'true',
            CYPRESS_CACHE_FOLDER: '/workspace/.cypress'
          },
          ports: { '3000': '3000', '6006': '6006' }
        },
        containerId: 'container-dev-frontend-001'
      }
    ];

    const createdWorkspaces: TestWorkspace[] = [];

    for (const workspaceData of workspaces) {
      try {
        // Check if workspace already exists
        const existing = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.workspaces
        );

        const existingWorkspace = existing.documents.find((doc: any) => doc.name === workspaceData.name);
        if (existingWorkspace) {
          console.log(`ℹ️  Workspace '${workspaceData.name}' already exists`);
          createdWorkspaces.push(workspaceData);
          continue;
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.workspaces,
          workspaceData.id,
          {
            name: workspaceData.name,
            description: workspaceData.description,
            ownerId: workspaceData.ownerId,
            organizationId: workspaceData.organizationId,
            type: workspaceData.type,
            status: workspaceData.status,
            configuration: JSON.stringify(workspaceData.configuration),
            containerId: workspaceData.containerId,
          }
        );

        console.log(`✅ Created workspace: ${workspaceData.name}`);
        createdWorkspaces.push(workspaceData);

      } catch (error: any) {
        console.error(`❌ Failed to create workspace ${workspaceData.name}:`, error.message);
      }
    }

    return createdWorkspaces;
  }

  private async createTestVMs(adminUserId: string): Promise<TestVM[]> {
    console.log('\n🖥️  Creating test VMs...');

    const vms: TestVM[] = [
      {
        id: 'vm-admin-monitoring',
        name: 'System Monitoring VM',
        description: 'Dedicated VM for system monitoring and alerting with Prometheus and Grafana.',
        ownerId: adminUserId,
        status: 'running',
        template: 'ubuntu-server-22.04',
        resources: {
          cpu: 4,
          memory: 8192,
          storage: 100,
          network: 'bridge'
        },
        network: {
          interfaces: [
            { name: 'eth0', type: 'bridge', ip: '***********00', gateway: '***********' }
          ],
          ports: [
            { host: 9090, container: 9090, protocol: 'tcp', description: 'Prometheus' },
            { host: 3000, container: 3000, protocol: 'tcp', description: 'Grafana' }
          ]
        },
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
      },
      {
        id: 'vm-admin-backup',
        name: 'Backup and Recovery VM',
        description: 'VM dedicated to backup operations and disaster recovery procedures.',
        ownerId: adminUserId,
        status: 'stopped',
        template: 'ubuntu-server-22.04',
        resources: {
          cpu: 2,
          memory: 4096,
          storage: 500,
          network: 'bridge'
        },
        network: {
          interfaces: [
            { name: 'eth0', type: 'bridge', ip: '***********01', gateway: '***********' }
          ],
          ports: []
        },
        createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
        lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 24 hours ago
      },
      {
        id: 'vm-dev-database',
        name: 'Development Database VM',
        description: 'PostgreSQL database VM for development and testing purposes.',
        ownerId: adminUserId,
        status: 'running',
        template: 'postgresql-14',
        resources: {
          cpu: 4,
          memory: 16384,
          storage: 200,
          network: 'bridge'
        },
        network: {
          interfaces: [
            { name: 'eth0', type: 'bridge', ip: '***********02', gateway: '***********' }
          ],
          ports: [
            { host: 5432, container: 5432, protocol: 'tcp', description: 'PostgreSQL' }
          ]
        },
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
        lastActivity: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
      },
      {
        id: 'vm-test-load-balancer',
        name: 'Load Balancer Test VM',
        description: 'NGINX load balancer for testing high availability configurations.',
        ownerId: adminUserId,
        status: 'paused',
        template: 'nginx-alpine',
        resources: {
          cpu: 2,
          memory: 2048,
          storage: 20,
          network: 'bridge'
        },
        network: {
          interfaces: [
            { name: 'eth0', type: 'bridge', ip: '***********03', gateway: '***********' }
          ],
          ports: [
            { host: 80, container: 80, protocol: 'tcp', description: 'HTTP' },
            { host: 443, container: 443, protocol: 'tcp', description: 'HTTPS' }
          ]
        },
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
        lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString() // 6 hours ago
      }
    ];

    const createdVMs: TestVM[] = [];

    for (const vmData of vms) {
      try {
        // Check if VM already exists
        const existing = await this.databases.listDocuments(
          this.config.databaseId,
          this.config.collections.vms
        );

        const existingVM = existing.documents.find((doc: any) => doc.name === vmData.name);
        if (existingVM) {
          console.log(`ℹ️  VM '${vmData.name}' already exists`);
          createdVMs.push(vmData);
          continue;
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.vms,
          vmData.id,
          {
            name: vmData.name,
            description: vmData.description,
            ownerId: vmData.ownerId,
            status: vmData.status,
            template: vmData.template,
            resources: JSON.stringify(vmData.resources),
            network: JSON.stringify(vmData.network),
            createdAt: vmData.createdAt,
            lastActivity: vmData.lastActivity,
          }
        );

        console.log(`✅ Created VM: ${vmData.name}`);
        createdVMs.push(vmData);

      } catch (error: any) {
        console.error(`❌ Failed to create VM ${vmData.name}:`, error.message);
      }
    }

    return createdVMs;
  }

  private async createTestSessions(adminUserId: string): Promise<void> {
    console.log('\n🔐 Creating test sessions...');

    const sessions = [
      {
        id: 'session-admin-current',
        userId: adminUserId,
        sessionId: crypto.randomBytes(32).toString('hex'),
        deviceInfo: JSON.stringify({
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          platform: 'Windows',
          browser: 'Chrome',
          version: '120.0.0.0',
          ip: '************',
          location: 'San Francisco, CA, US'
        }),
        activity: JSON.stringify([
          { timestamp: new Date().toISOString(), action: 'login', details: 'Admin dashboard login' },
          { timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), action: 'workspace.create', details: 'Created Python ML workspace' },
          { timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(), action: 'vm.start', details: 'Started monitoring VM' },
          { timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(), action: 'user.view', details: 'Viewed user management page' }
        ]),
        status: 'active',
        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8 hours from now
      },
      {
        id: 'session-admin-mobile',
        userId: adminUserId,
        sessionId: crypto.randomBytes(32).toString('hex'),
        deviceInfo: JSON.stringify({
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
          platform: 'iOS',
          browser: 'Safari',
          version: '17.0',
          ip: '*********',
          location: 'San Francisco, CA, US'
        }),
        activity: JSON.stringify([
          { timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), action: 'login', details: 'Mobile login' },
          { timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), action: 'dashboard.view', details: 'Viewed mobile dashboard' }
        ]),
        status: 'active',
        expiresAt: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString() // 6 hours from now
      }
    ];

    for (const sessionData of sessions) {
      try {
        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.sessions,
          sessionData.id,
          sessionData
        );
        console.log(`✅ Created session: ${sessionData.id}`);
      } catch (error: any) {
        if (error.message?.includes('already exists')) {
          console.log(`ℹ️  Session ${sessionData.id} already exists`);
        } else {
          console.error(`❌ Failed to create session ${sessionData.id}:`, error.message);
        }
      }
    }
  }

  private async createTestUsers(): Promise<string[]> {
    console.log('\n👥 Creating additional test users...');

    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'DevPassword123!',
        name: 'John Developer',
        bio: 'Full-stack developer specializing in React and Node.js applications.',
        company: 'Tech Innovations Inc.',
        location: 'New York, NY',
        role: 'developer'
      },
      {
        email: '<EMAIL>',
        password: 'DesignPassword123!',
        name: 'Sarah Designer',
        bio: 'UI/UX designer passionate about creating intuitive user experiences.',
        company: 'Creative Studio LLC',
        location: 'Los Angeles, CA',
        role: 'designer'
      },
      {
        email: '<EMAIL>',
        password: 'DataPassword123!',
        name: 'Dr. Alex Chen',
        bio: 'Data scientist and machine learning engineer with expertise in deep learning.',
        company: 'AI Research Lab',
        location: 'Seattle, WA',
        role: 'data_scientist'
      }
    ];

    const createdUserIds: string[] = [];

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const existingUsers = await this.users.list();
        const existingUser = existingUsers.users.find(u => u.email === userData.email);

        if (existingUser) {
          console.log(`ℹ️  User ${userData.email} already exists`);
          createdUserIds.push(existingUser.$id);
          continue;
        }

        // Create user account
        const user = await this.users.create(
          ID.unique(),
          userData.email,
          undefined,
          userData.password,
          userData.name
        );

        // Create user profile
        const profileData = {
          name: userData.name,
          email: userData.email,
          bio: userData.bio,
          company: userData.company,
          location: userData.location,
          website: 'https://omnispace.local',
          role: userData.role,
          permissions: JSON.stringify([
            'workspace.create',
            'workspace.manage',
            'vm.create',
            'vm.manage'
          ]),
          preferences: JSON.stringify({
            theme: 'system',
            language: 'en',
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            notifications: {
              email: true,
              push: true,
              vmUpdates: true,
              securityAlerts: true
            },
            dashboard: {
              defaultView: 'grid',
              itemsPerPage: 12,
              autoRefresh: true
            }
          })
        };

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.users,
          user.$id,
          profileData
        );

        console.log(`✅ Created user: ${userData.email}`);
        createdUserIds.push(user.$id);

      } catch (error: any) {
        console.error(`❌ Failed to create user ${userData.email}:`, error.message);
      }
    }

    return createdUserIds;
  }

  private async createCollaborativeWorkspaces(adminUserId: string, testUserIds: string[], organizations: TestOrganization[]): Promise<void> {
    console.log('\n🤝 Creating collaborative workspaces...');

    const collaborativeWorkspaces = [
      {
        id: 'ws-team-frontend-project',
        name: 'Team Frontend Project',
        description: 'Collaborative React project for the entire development team.',
        type: 'node-react',
        ownerId: adminUserId,
        organizationId: organizations[1]?.id,
        status: 'running',
        configuration: {
          framework: 'react',
          nodeVersion: '20',
          packageManager: 'pnpm',
          packages: ['react', 'typescript', 'next', 'tailwindcss'],
          resources: { cpu: 6, memory: 12288, storage: 100 },
          collaboration: {
            enabled: true,
            maxUsers: 5,
            permissions: {
              [adminUserId]: 'owner',
              [testUserIds[0]]: 'editor',
              [testUserIds[1]]: 'viewer'
            }
          },
          environment: {
            NODE_ENV: 'development',
            TEAM_PROJECT: 'true'
          },
          ports: { '3000': '3000', '3001': '3001' }
        },
        containerId: 'container-team-frontend-001'
      },
      {
        id: 'ws-research-ml-project',
        name: 'ML Research Collaboration',
        description: 'Shared workspace for machine learning research and experimentation.',
        type: 'python-dev',
        ownerId: adminUserId,
        organizationId: organizations[2]?.id,
        status: 'running',
        configuration: {
          framework: 'jupyter',
          pythonVersion: '3.11',
          packages: ['tensorflow', 'pytorch', 'scikit-learn', 'jupyter', 'mlflow'],
          resources: { cpu: 12, memory: 24576, storage: 200 },
          collaboration: {
            enabled: true,
            maxUsers: 3,
            permissions: {
              [adminUserId]: 'owner',
              [testUserIds[2]]: 'editor'
            }
          },
          gpu: { enabled: true, type: 'nvidia-t4' },
          environment: {
            JUPYTER_ENABLE_LAB: 'yes',
            MLFLOW_TRACKING_URI: 'http://localhost:5000'
          },
          ports: { '8888': '8888', '5000': '5000' }
        },
        containerId: 'container-research-ml-001'
      }
    ];

    for (const workspaceData of collaborativeWorkspaces) {
      try {
        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.workspaces,
          workspaceData.id,
          {
            name: workspaceData.name,
            description: workspaceData.description,
            ownerId: workspaceData.ownerId,
            organizationId: workspaceData.organizationId,
            type: workspaceData.type,
            status: workspaceData.status,
            configuration: JSON.stringify(workspaceData.configuration),
            containerId: workspaceData.containerId,
          }
        );

        console.log(`✅ Created collaborative workspace: ${workspaceData.name}`);

      } catch (error: any) {
        if (error.message?.includes('already exists')) {
          console.log(`ℹ️  Workspace '${workspaceData.name}' already exists`);
        } else {
          console.error(`❌ Failed to create workspace ${workspaceData.name}:`, error.message);
        }
      }
    }
  }
}

// Main execution
async function main() {
  const setup = new AdminEnvironmentSetup();
  await setup.setup();
}

// Run the setup if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Admin environment setup failed:', error);
    process.exit(1);
  });
}

export { AdminEnvironmentSetup };
