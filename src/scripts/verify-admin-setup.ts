#!/usr/bin/env tsx

/**
 * Verify Admin Setup
 * 
 * This script verifies that the admin environment is properly set up
 * by checking the database directly rather than relying on session authentication.
 * 
 * Usage:
 * pnpm verify:admin-setup
 * or
 * npx tsx src/scripts/verify-admin-setup.ts
 */

import { config } from 'dotenv';
import { Client, Databases, Users } from 'node-appwrite';

// Load environment variables
config({ path: '.env.local' });

interface VerificationConfig {
  endpoint: string;
  projectId: string;
  apiKey: string;
  databaseId: string;
  collections: {
    users: string;
    organizations: string;
    workspaces: string;
    vms: string;
    sessions: string;
  };
}

class AdminSetupVerifier {
  private client: Client;
  private databases: Databases;
  private users: Users;
  private config: VerificationConfig;

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        users: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
        organizations: process.env.NEXT_PUBLIC_APPWRITE_ORGANIZATIONS_COLLECTION_ID || 'organizations',
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
        vms: process.env.NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID!,
        sessions: process.env.NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID!,
      }
    };

    // Initialize Appwrite client with admin privileges
    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
    this.users = new Users(this.client);
  }

  async verify(): Promise<void> {
    console.log('🔍 Verifying Admin Environment Setup...\n');

    try {
      // Step 1: Verify admin user exists
      await this.verifyAdminUser();

      // Step 2: Verify test users exist
      await this.verifyTestUsers();

      // Step 3: Verify organizations
      await this.verifyOrganizations();

      // Step 4: Verify workspaces
      await this.verifyWorkspaces();

      // Step 5: Verify VMs
      await this.verifyVMs();

      // Step 6: Verify sessions
      await this.verifySessions();

      console.log('\n🎉 Admin Environment Verification Complete!');
      console.log('\n✅ All components verified successfully');
      console.log('\n📋 Ready for Testing:');
      console.log('   🔑 Admin login: <EMAIL>');
      console.log('   👥 Test users: developer, designer, data scientist');
      console.log('   🏢 Organizations: 3 created');
      console.log('   💻 Workspaces: Multiple types available');
      console.log('   🖥️  VMs: Various states for testing');

    } catch (error) {
      console.error('\n❌ Verification failed:', error);
      throw error;
    }
  }

  private async verifyAdminUser(): Promise<void> {
    console.log('👤 Verifying Admin User...');

    try {
      // Get all users and find admin
      const usersList = await this.users.list();
      const adminUser = usersList.users.find(u => u.email === '<EMAIL>');

      if (!adminUser) {
        throw new Error('Admin user not found');
      }

      console.log(`✅ Admin user found: ${adminUser.name} (${adminUser.email})`);
      console.log(`   User ID: ${adminUser.$id}`);
      console.log(`   Email Verified: ${adminUser.emailVerification}`);
      console.log(`   Registration: ${adminUser.registration}`);

      // Get admin profile from database
      const adminProfile = await this.databases.getDocument(
        this.config.databaseId,
        this.config.collections.users,
        adminUser.$id
      );

      console.log(`✅ Admin profile found in database`);
      console.log(`   Name: ${adminProfile.name}`);
      console.log(`   Role: ${adminProfile.role || 'Not set'}`);
      console.log(`   Company: ${adminProfile.company || 'Not set'}`);

      // Check admin permissions
      if (adminProfile.permissions) {
        try {
          const permissions = JSON.parse(adminProfile.permissions);
          console.log(`✅ Admin permissions configured: ${permissions.length} permissions`);
          console.log(`   Permissions: ${permissions.join(', ')}`);
        } catch (error) {
          console.log(`⚠️  Permissions exist but couldn't parse`);
        }
      } else {
        console.log(`⚠️  No permissions configured for admin user`);
      }

    } catch (error: any) {
      console.error(`❌ Admin user verification failed:`, error.message);
      throw error;
    }
  }

  private async verifyTestUsers(): Promise<void> {
    console.log('\n👥 Verifying Test Users...');

    const expectedUsers = [
      { email: '<EMAIL>', expectedRole: 'developer' },
      { email: '<EMAIL>', expectedRole: 'designer' },
      { email: '<EMAIL>', expectedRole: 'data_scientist' }
    ];

    try {
      const usersList = await this.users.list();

      for (const expectedUser of expectedUsers) {
        const user = usersList.users.find(u => u.email === expectedUser.email);
        
        if (!user) {
          console.log(`⚠️  Test user not found: ${expectedUser.email}`);
          continue;
        }

        console.log(`✅ Test user found: ${user.name} (${user.email})`);

        // Get user profile
        try {
          const profile = await this.databases.getDocument(
            this.config.databaseId,
            this.config.collections.users,
            user.$id
          );
          
          console.log(`   Role: ${profile.role || 'Not set'}`);
          console.log(`   Company: ${profile.company || 'Not set'}`);
          
          if (profile.role === expectedUser.expectedRole) {
            console.log(`   ✅ Role matches expected: ${expectedUser.expectedRole}`);
          } else {
            console.log(`   ⚠️  Role mismatch: expected ${expectedUser.expectedRole}, got ${profile.role}`);
          }
        } catch (error) {
          console.log(`   ❌ Profile not found for ${user.email}`);
        }
      }

    } catch (error: any) {
      console.error(`❌ Test users verification failed:`, error.message);
      throw error;
    }
  }

  private async verifyOrganizations(): Promise<void> {
    console.log('\n🏢 Verifying Organizations...');

    try {
      const organizations = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.organizations
      );

      console.log(`✅ Found ${organizations.documents.length} organizations`);

      const expectedOrgs = ['Omnispace Platform', 'Development Team', 'Data Science Lab'];
      
      for (const expectedOrg of expectedOrgs) {
        const org = organizations.documents.find((doc: any) => doc.name === expectedOrg);
        if (org) {
          console.log(`   ✅ ${expectedOrg}: ${(org as any).description}`);
        } else {
          console.log(`   ⚠️  Missing organization: ${expectedOrg}`);
        }
      }

    } catch (error: any) {
      console.error(`❌ Organizations verification failed:`, error.message);
      throw error;
    }
  }

  private async verifyWorkspaces(): Promise<void> {
    console.log('\n💻 Verifying Workspaces...');

    try {
      const workspaces = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.workspaces
      );

      console.log(`✅ Found ${workspaces.documents.length} workspaces`);

      const statusCounts = workspaces.documents.reduce((acc: any, ws: any) => {
        acc[ws.status] = (acc[ws.status] || 0) + 1;
        return acc;
      }, {});

      console.log(`   Status distribution:`);
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`     ${status}: ${count}`);
      });

      // Show workspace types
      const typeCounts = workspaces.documents.reduce((acc: any, ws: any) => {
        acc[ws.type] = (acc[ws.type] || 0) + 1;
        return acc;
      }, {});

      console.log(`   Type distribution:`);
      Object.entries(typeCounts).forEach(([type, count]) => {
        console.log(`     ${type}: ${count}`);
      });

    } catch (error: any) {
      console.error(`❌ Workspaces verification failed:`, error.message);
      throw error;
    }
  }

  private async verifyVMs(): Promise<void> {
    console.log('\n🖥️  Verifying VMs...');

    try {
      const vms = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.vms
      );

      console.log(`✅ Found ${vms.documents.length} VMs`);

      const statusCounts = vms.documents.reduce((acc: any, vm: any) => {
        acc[vm.status] = (acc[vm.status] || 0) + 1;
        return acc;
      }, {});

      console.log(`   Status distribution:`);
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`     ${status}: ${count}`);
      });

    } catch (error: any) {
      console.error(`❌ VMs verification failed:`, error.message);
      throw error;
    }
  }

  private async verifySessions(): Promise<void> {
    console.log('\n🔐 Verifying Sessions...');

    try {
      const sessions = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.collections.sessions
      );

      console.log(`✅ Found ${sessions.documents.length} session records`);

      const activeSessions = sessions.documents.filter((session: any) => session.status === 'active');
      console.log(`   Active sessions: ${activeSessions.length}`);

    } catch (error: any) {
      console.error(`❌ Sessions verification failed:`, error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const verifier = new AdminSetupVerifier();
  await verifier.verify();
}

// Run the verification if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Verification failed:', error);
    process.exit(1);
  });
}

export { AdminSetupVerifier };
