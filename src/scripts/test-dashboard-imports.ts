#!/usr/bin/env tsx

/**
 * Test Dashboard Import Issues
 * 
 * This script tests that dashboard components can be imported without
 * causing Docker client-side bundling issues.
 * 
 * Usage:
 * pnpm test:dashboard-imports
 * or
 * npx tsx src/scripts/test-dashboard-imports.ts
 */

import { promises as fs } from 'fs';
import path from 'path';

interface ImportTestResult {
  file: string;
  success: boolean;
  error?: string;
  dockerImports?: string[];
  serverImports?: string[];
}

class DashboardImportTester {
  private dashboardPaths = [
    'src/components/dashboard',
    'src/app/dashboard',
    'src/hooks',
    'src/components/node-react-workspace',
    'src/components/containers',
    'src/components/workspace'
  ];

  private serverSidePatterns = [
    /import.*from.*['"]@\/services\/docker['"]/,
    /import.*from.*['"]@\/services\/node-react-live-preview['"]/,
    /import.*from.*['"]@\/services\/node-react-ai-assistant['"]/,
    /import.*from.*['"]@\/services\/vm\/docker['"]/,
    /import.*from.*['"]@\/services\/vm\/connection['"]/,
    /import.*from.*['"]@\/services\/vm\/monitoring['"]/,
    /import.*from.*['"]@\/services\/vm\/security['"]/,
    /import.*from.*['"]dockerode['"]/,
    /import.*from.*['"]ssh2['"]/,
    /import.*from.*['"]cpu-features['"]/,
    /import.*from.*['"]node-pty['"]/,
    /require\(['"]dockerode['"]\)/,
    /require\(['"]ssh2['"]\)/,
    /require\(['"]cpu-features['"]\)/,
  ];

  private clientSidePatterns = [
    /import.*from.*['"]@\/services\/client\//,
    /import.*from.*['"]@\/hooks\//,
    /import.*from.*['"]@\/types\//,
  ];

  async testDashboardImports(): Promise<void> {
    console.log('🧪 Testing Dashboard Component Imports...\n');

    const results: ImportTestResult[] = [];

    for (const dashboardPath of this.dashboardPaths) {
      const fullPath = path.resolve(process.cwd(), dashboardPath);
      
      try {
        const stat = await fs.stat(fullPath);
        if (stat.isDirectory()) {
          const dirResults = await this.testDirectory(fullPath);
          results.push(...dirResults);
        } else if (stat.isFile() && (fullPath.endsWith('.ts') || fullPath.endsWith('.tsx'))) {
          const fileResult = await this.testFile(fullPath);
          results.push(fileResult);
        }
      } catch (error) {
        console.log(`⚠️  Path not found: ${dashboardPath}`);
      }
    }

    this.printResults(results);
  }

  private async testDirectory(dirPath: string): Promise<ImportTestResult[]> {
    const results: ImportTestResult[] = [];
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory() && !entry.name.startsWith('.')) {
          const subResults = await this.testDirectory(fullPath);
          results.push(...subResults);
        } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
          const fileResult = await this.testFile(fullPath);
          results.push(fileResult);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
    }
    
    return results;
  }

  private async testFile(filePath: string): Promise<ImportTestResult> {
    const relativePath = path.relative(process.cwd(), filePath);
    
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      
      const dockerImports: string[] = [];
      const serverImports: string[] = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // Check for server-side imports
        for (const pattern of this.serverSidePatterns) {
          if (pattern.test(line)) {
            serverImports.push(`Line ${i + 1}: ${line}`);
          }
        }
        
        // Check specifically for Docker-related imports
        if (line.includes('dockerode') || line.includes('ssh2') || line.includes('cpu-features')) {
          dockerImports.push(`Line ${i + 1}: ${line}`);
        }
      }
      
      const hasProblematicImports = dockerImports.length > 0 || serverImports.length > 0;
      
      return {
        file: relativePath,
        success: !hasProblematicImports,
        dockerImports: dockerImports.length > 0 ? dockerImports : undefined,
        serverImports: serverImports.length > 0 ? serverImports : undefined,
      };
      
    } catch (error) {
      return {
        file: relativePath,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private printResults(results: ImportTestResult[]): void {
    const totalFiles = results.length;
    const successfulFiles = results.filter(r => r.success).length;
    const problematicFiles = results.filter(r => !r.success);
    
    console.log(`\n📊 Import Test Results:`);
    console.log(`   Total files tested: ${totalFiles}`);
    console.log(`   ✅ Clean files: ${successfulFiles}`);
    console.log(`   ❌ Problematic files: ${problematicFiles.length}`);
    
    if (problematicFiles.length > 0) {
      console.log(`\n🚨 Files with problematic imports:\n`);
      
      for (const result of problematicFiles) {
        console.log(`📁 ${result.file}`);
        
        if (result.error) {
          console.log(`   ❌ Error: ${result.error}`);
        }
        
        if (result.dockerImports && result.dockerImports.length > 0) {
          console.log(`   🐳 Docker imports:`);
          for (const dockerImport of result.dockerImports) {
            console.log(`      ${dockerImport}`);
          }
        }
        
        if (result.serverImports && result.serverImports.length > 0) {
          console.log(`   🖥️  Server-side imports:`);
          for (const serverImport of result.serverImports) {
            console.log(`      ${serverImport}`);
          }
        }
        
        console.log('');
      }
      
      console.log(`\n💡 Recommendations:`);
      console.log(`   1. Replace server-side service imports with client-safe alternatives`);
      console.log(`   2. Use API routes instead of direct service calls`);
      console.log(`   3. Move server-side logic to API routes`);
      console.log(`   4. Use client-safe services from @/services/client/`);
      
    } else {
      console.log(`\n🎉 All dashboard components are free of problematic imports!`);
    }
    
    console.log(`\n✅ Dashboard import testing completed!`);
  }

  async testWebpackConfiguration(): Promise<void> {
    console.log(`\n🔧 Testing Webpack Configuration...\n`);
    
    try {
      const nextConfigPath = path.resolve(process.cwd(), 'next.config.ts');
      const content = await fs.readFile(nextConfigPath, 'utf-8');
      
      const hasDockerodeExclusion = content.includes("'dockerode'");
      const hasSsh2Exclusion = content.includes("'ssh2'");
      const hasCpuFeaturesExclusion = content.includes("'cpu-features'");
      const hasServiceAliases = content.includes('@/services/docker$');
      
      console.log(`📋 Webpack Configuration Status:`);
      console.log(`   Dockerode exclusion: ${hasDockerodeExclusion ? '✅' : '❌'}`);
      console.log(`   SSH2 exclusion: ${hasSsh2Exclusion ? '✅' : '❌'}`);
      console.log(`   CPU-features exclusion: ${hasCpuFeaturesExclusion ? '✅' : '❌'}`);
      console.log(`   Service aliases: ${hasServiceAliases ? '✅' : '❌'}`);
      
      const allConfigured = hasDockerodeExclusion && hasSsh2Exclusion && hasCpuFeaturesExclusion && hasServiceAliases;
      
      if (allConfigured) {
        console.log(`\n✅ Webpack configuration is properly set up to exclude server-side modules!`);
      } else {
        console.log(`\n⚠️  Webpack configuration may need updates to properly exclude server-side modules.`);
      }
      
    } catch (error) {
      console.error(`❌ Error reading webpack configuration:`, error);
    }
  }
}

// Main execution
async function main() {
  const tester = new DashboardImportTester();
  
  try {
    await tester.testDashboardImports();
    await tester.testWebpackConfiguration();
    
    console.log('\n🎯 Summary:');
    console.log('   Dashboard components tested for problematic imports');
    console.log('   Webpack configuration verified');
    console.log('   Client-side bundling issues should be resolved');
    
  } catch (error) {
    console.error('\n💥 Dashboard import testing failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

export { DashboardImportTester };
