#!/usr/bin/env tsx

/**
 * Appwrite Data Seeder Script
 * 
 * Seeds the database with sample data for development and testing.
 * 
 * Usage:
 * pnpm seed:appwrite
 * or
 * npx tsx src/scripts/seed-appwrite.ts
 */

import { Client, Databases, Users, ID } from 'node-appwrite';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

interface SeedConfig {
  endpoint: string;
  projectId: string;
  apiKey: string;
  databaseId: string;
  collections: {
    users: string;
    organizations: string;
    workspaces: string;
  };
}

interface SeedUser {
  email: string;
  password: string;
  name: string;
  bio?: string;
  company?: string;
  location?: string;
}

class AppwriteSeeder {
  private client: Client;
  private databases: Databases;
  private users: Users;
  private config: SeedConfig;

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        users: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
        organizations: process.env.NEXT_PUBLIC_APPWRITE_ORGANIZATIONS_COLLECTION_ID || 'organizations',
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
      }
    };

    // Initialize Appwrite client
    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
    this.users = new Users(this.client);
  }

  async seed(): Promise<void> {
    console.log('🌱 Starting Appwrite data seeding...\n');

    try {
      // Check if we're in development
      const environment = process.env.NODE_ENV || 'development';
      if (environment === 'production') {
        console.log('⚠️  Seeding is disabled in production environment');
        return;
      }

      // Seed users
      const createdUsers = await this.seedUsers();

      // Seed organizations
      await this.seedOrganizations(createdUsers);

      // Seed workspaces
      await this.seedWorkspaces(createdUsers);

      console.log('\n✅ Data seeding completed successfully!');
      console.log('\n📋 Seeded data:');
      console.log(`   - ${createdUsers.length} users`);
      console.log('   - 2 organizations');
      console.log('   - 4 workspaces');

    } catch (error) {
      console.error('\n❌ Seeding failed:', error);
      process.exit(1);
    }
  }

  private async seedUsers(): Promise<any[]> {
    console.log('👥 Seeding users...');

    const seedUsers: SeedUser[] = [
      {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
        name: 'Admin User',
        bio: 'System administrator',
        company: 'Omnispace',
        location: 'San Francisco, CA',
      },
      {
        email: '<EMAIL>',
        password: 'DevPassword123!',
        name: 'John Developer',
        bio: 'Full-stack developer passionate about cloud computing',
        company: 'Tech Corp',
        location: 'New York, NY',
      },
      {
        email: '<EMAIL>',
        password: 'DesignPassword123!',
        name: 'Jane Designer',
        bio: 'UI/UX designer with a love for clean interfaces',
        company: 'Design Studio',
        location: 'Los Angeles, CA',
      },
    ];

    const createdUsers = [];

    for (const userData of seedUsers) {
      try {
        // Check if user already exists
        try {
          const existingUsers = await this.users.list();
          const existingUser = existingUsers.users.find(u => u.email === userData.email);
          if (existingUser) {
            console.log(`ℹ️  User ${userData.email} already exists`);
            createdUsers.push(existingUser);
            continue;
          }
        } catch (error) {
          // Continue with user creation if list fails
        }

        // Create user account
        const user = await this.users.create(
          ID.unique(),
          userData.email,
          undefined, // phone
          userData.password,
          userData.name
        );

        // Create user profile in database
        const profileData = {
          name: userData.name,
          email: userData.email,
          bio: userData.bio || '',
          company: userData.company || '',
          location: userData.location || '',
          website: 'https://omnispace.dev',
        };

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.users,
          user.$id,
          profileData
        );

        console.log(`✅ Created user: ${userData.email}`);
        createdUsers.push(user);

      } catch (error: any) {
        if (error.message?.includes('already exists')) {
          console.log(`ℹ️  User ${userData.email} already exists`);
        } else {
          console.error(`❌ Failed to create user ${userData.email}:`, error.message);
        }
      }
    }

    return createdUsers;
  }

  private async seedOrganizations(users: any[]): Promise<void> {
    console.log('\n🏢 Seeding organizations...');

    const organizations = [
      {
        name: 'Omnispace Team',
        description: 'The core Omnispace development team',
        ownerId: users[0].$id,
        members: users.map(u => u.$id),
      },
      {
        name: 'Beta Testers',
        description: 'Community of beta testers and early adopters',
        ownerId: users[1].$id,
        members: [users[1].$id, users[2].$id],
      },
    ];

    for (const orgData of organizations) {
      try {
        // Check if organization already exists
        try {
          const existing = await this.databases.listDocuments(
            this.config.databaseId,
            this.config.collections.organizations
          );

          const existingOrg = existing.documents.find((doc: any) => doc.name === orgData.name);
          if (existingOrg) {
            console.log(`ℹ️  Organization '${orgData.name}' already exists`);
            continue;
          }
        } catch (error) {
          // Continue with creation if list fails
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.organizations,
          ID.unique(),
          {
            ...orgData,
            settings: JSON.stringify({
              allowPublicWorkspaces: true,
              defaultWorkspaceType: 'development-env',
              maxWorkspacesPerUser: 10,
            }),
          }
        );

        console.log(`✅ Created organization: ${orgData.name}`);

      } catch (error: any) {
        console.error(`❌ Failed to create organization ${orgData.name}:`, error.message);
      }
    }
  }

  private async seedWorkspaces(users: any[]): Promise<void> {
    console.log('\n💻 Seeding workspaces...');

    const workspaces = [
      {
        name: 'Python Development Environment',
        description: 'A fully configured Python development workspace with Django and FastAPI',
        ownerId: users[1].$id,
        type: 'python-dev',
        status: 'active',
        configuration: JSON.stringify({
          framework: 'django',
          pythonVersion: '3.11',
          packages: ['django', 'fastapi', 'numpy', 'pandas'],
          resources: { cpu: 2, memory: 4096, storage: 20 },
        }),
      },
      {
        name: 'React Development Setup',
        description: 'Modern React development environment with TypeScript and Vite',
        ownerId: users[1].$id,
        type: 'node-react',
        status: 'active',
        configuration: JSON.stringify({
          framework: 'react',
          nodeVersion: '20',
          packageManager: 'pnpm',
          packages: ['react', 'typescript', 'vite', '@types/node'],
          resources: { cpu: 2, memory: 2048, storage: 15 },
        }),
      },
      {
        name: 'Ubuntu Desktop',
        description: 'Full Ubuntu desktop environment for general development',
        ownerId: users[2].$id,
        type: 'ubuntu-desktop',
        status: 'stopped',
        configuration: JSON.stringify({
          desktopEnvironment: 'gnome',
          preinstalledApps: ['firefox', 'vscode', 'git'],
          resources: { cpu: 4, memory: 8192, storage: 40 },
        }),
      },
      {
        name: 'Minimal Development Environment',
        description: 'Lightweight development environment for quick prototyping',
        ownerId: users[2].$id,
        type: 'minimal-desktop',
        status: 'active',
        configuration: JSON.stringify({
          editor: 'nano',
          shell: 'bash',
          tools: ['git', 'curl', 'wget'],
          resources: { cpu: 1, memory: 1024, storage: 10 },
        }),
      },
    ];

    for (const workspaceData of workspaces) {
      try {
        // Check if workspace already exists
        try {
          const existing = await this.databases.listDocuments(
            this.config.databaseId,
            this.config.collections.workspaces
          );

          const existingWorkspace = existing.documents.find((doc: any) => doc.name === workspaceData.name);
          if (existingWorkspace) {
            console.log(`ℹ️  Workspace '${workspaceData.name}' already exists`);
            continue;
          }
        } catch (error) {
          // Continue with creation if list fails
        }

        await this.databases.createDocument(
          this.config.databaseId,
          this.config.collections.workspaces,
          ID.unique(),
          workspaceData
        );

        console.log(`✅ Created workspace: ${workspaceData.name}`);

      } catch (error: any) {
        console.error(`❌ Failed to create workspace ${workspaceData.name}:`, error.message);
      }
    }
  }
}

// Main execution
async function main() {
  const seeder = new AppwriteSeeder();
  await seeder.seed();
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Seeding failed:', error);
    process.exit(1);
  });
}

export { AppwriteSeeder };
