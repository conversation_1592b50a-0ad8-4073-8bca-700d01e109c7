#!/usr/bin/env tsx

/**
 * Debug Admin User
 * 
 * This script debugs the admin user setup to identify and fix issues.
 */

import { config } from 'dotenv';
import { Client, Databases, Users, ID } from 'node-appwrite';

// Load environment variables
config({ path: '.env.local' });

class AdminUserDebugger {
  private client: Client;
  private databases: Databases;
  private users: Users;
  private config: any;

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      usersCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
    };

    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
    this.users = new Users(this.client);
  }

  async debug(): Promise<void> {
    console.log('🔍 Debugging Admin User Setup...\n');

    try {
      // Step 1: List all users
      console.log('1️⃣  Listing all users...');
      const usersList = await this.users.list();
      console.log(`Found ${usersList.users.length} users:`);
      
      usersList.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (${user.email}) - ID: ${user.$id}`);
      });

      // Step 2: Find admin user
      const adminUser = usersList.users.find(u => u.email === '<EMAIL>');
      if (!adminUser) {
        console.log('\n❌ Admin user not found!');
        return;
      }

      console.log(`\n✅ Admin user found: ${adminUser.name} (${adminUser.email})`);
      console.log(`   User ID: ${adminUser.$id}`);

      // Step 3: Check if profile document exists
      console.log('\n2️⃣  Checking admin profile document...');
      try {
        const profile = await this.databases.getDocument(
          this.config.databaseId,
          this.config.usersCollectionId,
          adminUser.$id
        );
        console.log(`✅ Admin profile found in database`);
        console.log(`   Name: ${profile.name}`);
        console.log(`   Role: ${profile.role || 'Not set'}`);
        console.log(`   Email: ${profile.email}`);
      } catch (error: any) {
        if (error.code === 404) {
          console.log(`❌ Admin profile document not found in database`);
          console.log(`   Expected document ID: ${adminUser.$id}`);
          console.log(`   Collection ID: ${this.config.usersCollectionId}`);
          
          // Try to create the missing profile
          await this.createMissingAdminProfile(adminUser);
        } else {
          throw error;
        }
      }

      // Step 4: List all documents in users collection
      console.log('\n3️⃣  Listing all user profile documents...');
      const userProfiles = await this.databases.listDocuments(
        this.config.databaseId,
        this.config.usersCollectionId
      );
      
      console.log(`Found ${userProfiles.documents.length} user profile documents:`);
      userProfiles.documents.forEach((doc: any, index) => {
        console.log(`   ${index + 1}. ${doc.name} (${doc.email}) - ID: ${doc.$id}`);
      });

    } catch (error) {
      console.error('\n❌ Debug failed:', error);
      throw error;
    }
  }

  private async createMissingAdminProfile(adminUser: any): Promise<void> {
    console.log('\n🔧 Creating missing admin profile...');

    try {
      const adminProfile = {
        name: adminUser.name,
        email: adminUser.email,
        bio: 'System Administrator with full access to all Omnispace platform features. Responsible for user management, system monitoring, and platform maintenance.',
        company: 'Omnispace Platform',
        location: 'Global',
        website: 'https://omnispace.platform',
        avatar: '',
        role: 'admin',
        permissions: JSON.stringify([
          'user.manage',
          'organization.manage',
          'workspace.manage',
          'vm.manage',
          'system.monitor',
          'analytics.view',
          'settings.manage'
        ]),
        preferences: JSON.stringify({
          theme: 'dark',
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            vmUpdates: true,
            securityAlerts: true,
            systemAlerts: true,
            userActivity: true
          },
          dashboard: {
            defaultView: 'admin',
            itemsPerPage: 25,
            autoRefresh: true,
            refreshInterval: 30,
            showAdvancedMetrics: true
          },
          security: {
            twoFactorEnabled: false,
            sessionTimeout: 480,
            requirePasswordChange: false
          }
        }),
        metadata: JSON.stringify({
          createdBy: 'system',
          accountType: 'admin',
          lastLogin: new Date().toISOString(),
          loginCount: 1,
          systemVersion: '1.0.0'
        })
      };

      await this.databases.createDocument(
        this.config.databaseId,
        this.config.usersCollectionId,
        adminUser.$id,
        adminProfile
      );

      console.log(`✅ Admin profile created successfully`);
      console.log(`   Document ID: ${adminUser.$id}`);
      console.log(`   Name: ${adminProfile.name}`);
      console.log(`   Role: ${adminProfile.role}`);

    } catch (error: any) {
      console.error(`❌ Failed to create admin profile:`, error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const debugTool = new AdminUserDebugger();
  await debugTool.debug();
}

// Run the debugger if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Debug failed:', error);
    process.exit(1);
  });
}

export { AdminUserDebugger };
