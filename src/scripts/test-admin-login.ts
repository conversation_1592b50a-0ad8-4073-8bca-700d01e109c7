#!/usr/bin/env tsx

/**
 * Test Admin Login Functionality
 *
 * This script tests the admin user login functionality to ensure
 * the admin account works correctly with the authentication system.
 *
 * Usage:
 * npx tsx src/scripts/test-admin-login.ts
 * or
 * pnpm test:admin-login
 */

import { config } from 'dotenv';
import { Client, Account, Databases, Users } from 'node-appwrite';

// Load environment variables
config({ path: '.env.local' });

interface LoginTestConfig {
  endpoint: string;
  projectId: string;
  apiKey: string;
  databaseId: string;
  usersCollectionId: string;
}

class AdminLoginTester {
  private adminClient: Client;
  private sessionClient: Client;
  private adminAccount: Account;
  private adminUsers: Users;
  private sessionAccount: Account;
  private sessionDatabases: Databases;
  private config: LoginTestConfig;

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      usersCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
    };

    // Initialize admin client (server-side SDK with API key)
    this.adminClient = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.adminAccount = new Account(this.adminClient);
    this.adminUsers = new Users(this.adminClient);

    // Initialize session client (for user authentication)
    this.sessionClient = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId);

    this.sessionAccount = new Account(this.sessionClient);
    this.sessionDatabases = new Databases(this.sessionClient);
  }

  async testAdminLogin(): Promise<void> {
    console.log('🧪 Testing Admin Login Functionality...\n');

    const adminEmail = '<EMAIL>';
    const adminPassword = 'OmnispaceAdmin2024!';

    try {
      // Step 1: Verify admin user exists using admin client
      console.log('1️⃣  Verifying admin user exists...');
      const users = await this.adminUsers.list();
      const adminUser = users.users.find(u => u.email === adminEmail);

      if (!adminUser) {
        throw new Error('Admin user not found in database');
      }
      console.log(`✅ Admin user found: ${adminUser.name} (${adminUser.email})`);
      console.log(`   User ID: ${adminUser.$id}`);
      console.log(`   Email Verified: ${adminUser.emailVerification}`);
      console.log(`   Registration: ${adminUser.registration}`);

      // Step 2: Test login with session client
      console.log('2️⃣  Testing admin login...');
      const session = await this.sessionAccount.createEmailPasswordSession(adminEmail, adminPassword);
      console.log(`✅ Login successful! Session ID: ${session.$id}`);
      console.log(`   Provider: ${session.provider}`);
      console.log(`   Expires: ${session.expire}`);

      // Step 3: Get current user account using session
      console.log('3️⃣  Getting admin account details via session...');
      const sessionUser = await this.sessionAccount.get();
      console.log(`✅ Session account retrieved: ${sessionUser.name} (${sessionUser.email})`);
      console.log(`   Session User ID: ${sessionUser.$id}`);
      console.log(`   Matches Admin User: ${sessionUser.$id === adminUser.$id ? '✅' : '❌'}`);

      // Step 4: Get admin profile from database using session
      console.log('4️⃣  Getting admin profile from database...');
      const profile = await this.sessionDatabases.getDocument(
        this.config.databaseId,
        this.config.usersCollectionId,
        sessionUser.$id
      );
      console.log(`✅ Profile retrieved successfully`);
      console.log(`   Name: ${profile.name}`);
      console.log(`   Role: ${profile.role || 'Not set'}`);
      console.log(`   Company: ${profile.company || 'Not set'}`);
      console.log(`   Bio: ${profile.bio ? profile.bio.substring(0, 50) + '...' : 'Not set'}`);

      // Step 5: Test admin permissions (if stored in profile)
      if (profile.permissions) {
        console.log('5️⃣  Checking admin permissions...');
        try {
          const permissions = JSON.parse(profile.permissions);
          console.log(`✅ Admin permissions found: ${permissions.length} permissions`);
          permissions.forEach((permission: string) => {
            console.log(`   - ${permission}`);
          });
        } catch (error) {
          console.log(`⚠️  Permissions data exists but couldn't parse: ${profile.permissions}`);
        }
      } else {
        console.log('5️⃣  No permissions data found in profile');
      }

      // Step 6: Test session management
      console.log('6️⃣  Testing session management...');
      const sessions = await this.sessionAccount.listSessions();
      console.log(`✅ Active sessions: ${sessions.sessions.length}`);
      sessions.sessions.forEach((session, index) => {
        console.log(`   Session ${index + 1}: ${session.provider} (${session.current ? 'current' : 'other'})`);
      });

      // Step 7: Test logout
      console.log('7️⃣  Testing logout...');
      await this.sessionAccount.deleteSession('current');
      console.log(`✅ Logout successful`);

      // Step 8: Verify logout
      console.log('8️⃣  Verifying logout...');
      try {
        await this.sessionAccount.get();
        console.log(`❌ Logout verification failed - user still authenticated`);
      } catch (error: any) {
        if (error.code === 401) {
          console.log(`✅ Logout verified - user no longer authenticated`);
        } else {
          throw error;
        }
      }

      console.log('\n🎉 All admin login tests passed!');
      console.log('\n📋 Test Results Summary:');
      console.log('   ✅ Admin login successful');
      console.log('   ✅ Account details retrieved');
      console.log('   ✅ Profile data accessible');
      console.log('   ✅ Permissions configured');
      console.log('   ✅ Session management working');
      console.log('   ✅ Logout functionality working');

    } catch (error: any) {
      console.error('\n❌ Admin login test failed:', error.message);
      
      // Provide specific error guidance
      if (error.code === 401) {
        console.error('🔍 Authentication failed - check admin credentials');
        console.error(`   Expected Email: ${adminEmail}`);
        console.error(`   Expected Password: ${adminPassword}`);
      } else if (error.code === 404) {
        console.error('🔍 User not found - admin user may not be created');
        console.error('   Run: pnpm setup:admin-env');
      } else {
        console.error('🔍 Unexpected error occurred');
        console.error(`   Error Code: ${error.code}`);
        console.error(`   Error Type: ${error.type}`);
      }
      
      throw error;
    }
  }

  async testUserRoleAccess(): Promise<void> {
    console.log('\n🔐 Testing Role-Based Access...\n');

    const testUsers = [
      { email: '<EMAIL>', password: 'DevPassword123!', expectedRole: 'developer' },
      { email: '<EMAIL>', password: 'DesignPassword123!', expectedRole: 'designer' },
      { email: '<EMAIL>', password: 'DataPassword123!', expectedRole: 'data_scientist' }
    ];

    for (const testUser of testUsers) {
      // Create a new session client for each user test
      const userSessionClient = new Client()
        .setEndpoint(this.config.endpoint)
        .setProject(this.config.projectId);

      const userSessionAccount = new Account(userSessionClient);
      const userSessionDatabases = new Databases(userSessionClient);

      try {
        console.log(`🧪 Testing ${testUser.expectedRole} role access...`);

        // Login as test user
        const session = await userSessionAccount.createEmailPasswordSession(testUser.email, testUser.password);
        console.log(`✅ ${testUser.expectedRole} login successful`);

        // Get user profile
        const user = await userSessionAccount.get();
        const profile = await userSessionDatabases.getDocument(
          this.config.databaseId,
          this.config.usersCollectionId,
          user.$id
        );

        console.log(`   Role: ${profile.role || 'Not set'}`);
        console.log(`   Name: ${profile.name}`);
        console.log(`   Email: ${profile.email}`);

        // Verify role matches expected
        if (profile.role === testUser.expectedRole) {
          console.log(`✅ Role verification passed`);
        } else {
          console.log(`⚠️  Role mismatch: expected ${testUser.expectedRole}, got ${profile.role}`);
        }

        // Logout
        await userSessionAccount.deleteSession('current');
        console.log(`✅ ${testUser.expectedRole} logout successful\n`);

      } catch (error: any) {
        console.error(`❌ ${testUser.expectedRole} test failed:`, error.message);
        if (error.code === 401) {
          console.error(`   Authentication failed - check credentials for ${testUser.email}`);
        } else if (error.code === 404) {
          console.error(`   User not found - ${testUser.email} may not exist`);
        }
        console.log(''); // Add spacing
      }
    }
  }
}

// Main execution
async function main() {
  const tester = new AdminLoginTester();
  
  try {
    await tester.testAdminLogin();
    await tester.testUserRoleAccess();
  } catch (error) {
    console.error('\n💥 Login test suite failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

export { AdminLoginTester };
