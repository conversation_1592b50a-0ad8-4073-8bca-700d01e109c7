#!/usr/bin/env tsx

/**
 * Comprehensive Appwrite Setup Script
 * 
 * This script initializes all required Appwrite services using the Server SDK:
 * - Database and Collections
 * - Storage Buckets
 * - Authentication Settings
 * - Permissions and Security Rules
 * 
 * Usage:
 * pnpm setup:appwrite
 * or
 * npx tsx src/scripts/setup-appwrite.ts
 */

import { Client, Databases, Storage, Users, ID, Permission, Role } from 'node-appwrite';
import { config } from 'dotenv';
import { 
  USERS_COLLECTION_SCHEMA, 
  VMS_COLLECTION_SCHEMA, 
  SESSIONS_COLLECTION_SCHEMA,
  ORGANIZATIONS_COLLECTION_SCHEMA,
  WORKSPACES_COLLECTION_SCHEMA
} from '../lib/appwrite-schema';

// Load environment variables
config({ path: '.env.local' });

interface SetupConfig {
  endpoint: string;
  projectId: string;
  apiKey: string;
  databaseId: string;
  collections: {
    users: string;
    vms: string;
    sessions: string;
    organizations: string;
    workspaces: string;
  };
  buckets: {
    avatars: string;
    files: string;
    backups: string;
  };
}

class AppwriteSetup {
  private client: Client;
  private databases: Databases;
  private storage: Storage;
  private users: Users;
  private config: SetupConfig;

  constructor() {
    // Validate environment variables
    this.validateEnvironment();

    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      collections: {
        users: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
        vms: process.env.NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID!,
        sessions: process.env.NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID!,
        organizations: process.env.NEXT_PUBLIC_APPWRITE_ORGANIZATIONS_COLLECTION_ID || 'organizations',
        workspaces: process.env.NEXT_PUBLIC_APPWRITE_WORKSPACES_COLLECTION_ID || 'workspaces',
      },
      buckets: {
        avatars: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID!,
        files: process.env.NEXT_PUBLIC_APPWRITE_FILES_BUCKET_ID || 'files',
        backups: process.env.NEXT_PUBLIC_APPWRITE_BACKUPS_BUCKET_ID || 'backups',
      }
    };

    // Initialize Appwrite client
    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
    this.storage = new Storage(this.client);
    this.users = new Users(this.client);
  }

  private validateEnvironment(): void {
    const required = [
      'NEXT_PUBLIC_APPWRITE_ENDPOINT',
      'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
      'APPWRITE_API_KEY',
      'NEXT_PUBLIC_APPWRITE_DATABASE_ID',
      'NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID',
      'NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID',
      'NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID',
      'NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID',
    ];

    const missing = required.filter(key => !process.env[key]);
    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:');
      missing.forEach(key => console.error(`   - ${key}`));
      process.exit(1);
    }
  }

  async setup(): Promise<void> {
    console.log('🚀 Starting Appwrite setup...\n');

    try {
      // Test connection
      await this.testConnection();

      // Setup database and collections
      await this.setupDatabase();
      await this.setupCollections();

      // Setup storage buckets
      await this.setupStorage();

      // Setup authentication settings
      await this.setupAuthentication();

      console.log('\n✅ Appwrite setup completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('1. Verify your collections in the Appwrite console');
      console.log('2. Test user registration and login');
      console.log('3. Run the seeder script: pnpm seed:appwrite');

    } catch (error) {
      console.error('\n❌ Setup failed:', error);
      process.exit(1);
    }
  }

  private async testConnection(): Promise<void> {
    console.log('🔗 Testing Appwrite connection...');
    try {
      // Test by trying to get project info (this will fail if connection is bad)
      await this.databases.list();
      console.log('✅ Connection successful');
    } catch (error: any) {
      console.error('❌ Connection failed:', error.message);
      throw error;
    }
  }

  private async setupDatabase(): Promise<void> {
    console.log('\n📊 Setting up database...');
    
    try {
      // Try to get the database first
      await this.databases.get(this.config.databaseId);
      console.log(`✅ Database '${this.config.databaseId}' already exists`);
    } catch (error: any) {
      if (error.code === 404) {
        // Database doesn't exist, create it
        console.log(`📊 Creating database '${this.config.databaseId}'...`);
        await this.databases.create(this.config.databaseId, 'Omnispace Database');
        console.log('✅ Database created successfully');
      } else {
        throw error;
      }
    }
  }

  private async setupCollections(): Promise<void> {
    console.log('\n📋 Setting up collections...');

    const collections = [
      { id: this.config.collections.users, schema: USERS_COLLECTION_SCHEMA },
      { id: this.config.collections.vms, schema: VMS_COLLECTION_SCHEMA },
      { id: this.config.collections.sessions, schema: SESSIONS_COLLECTION_SCHEMA },
      { id: this.config.collections.organizations, schema: ORGANIZATIONS_COLLECTION_SCHEMA },
      { id: this.config.collections.workspaces, schema: WORKSPACES_COLLECTION_SCHEMA },
    ];

    for (const { id, schema } of collections) {
      await this.setupCollection(id, schema);
    }
  }

  private async setupCollection(collectionId: string, schema: any): Promise<void> {
    try {
      // Try to get the collection first
      const existingCollection = await this.databases.getCollection(this.config.databaseId, collectionId);
      console.log(`✅ Collection '${collectionId}' already exists`);
      
      // Update attributes if needed
      await this.updateCollectionAttributes(collectionId, schema, existingCollection);
      
    } catch (error: any) {
      if (error.code === 404) {
        // Collection doesn't exist, create it
        console.log(`📋 Creating collection '${collectionId}'...`);
        
        await this.databases.createCollection(
          this.config.databaseId,
          collectionId,
          schema.name,
          schema.permissions || [],
          schema.documentSecurity || false
        );

        // Add attributes
        for (const attr of schema.attributes) {
          await this.createAttribute(collectionId, attr);
        }

        console.log(`✅ Collection '${collectionId}' created successfully`);
      } else {
        throw error;
      }
    }
  }

  private async updateCollectionAttributes(collectionId: string, schema: any, existingCollection: any): Promise<void> {
    const existingAttributes = existingCollection.attributes || [];
    const existingAttrKeys = existingAttributes.map((attr: any) => attr.key);

    for (const attr of schema.attributes) {
      if (!existingAttrKeys.includes(attr.key)) {
        console.log(`➕ Adding missing attribute '${attr.key}' to collection '${collectionId}'`);
        await this.createAttribute(collectionId, attr);
      }
    }
  }

  private async createAttribute(collectionId: string, attr: any): Promise<void> {
    const { key, type, size, required, array, default: defaultValue } = attr;

    try {
      switch (type) {
        case 'string':
          await this.databases.createStringAttribute(
            this.config.databaseId,
            collectionId,
            key,
            size || 255,
            required || false,
            defaultValue,
            array || false
          );
          break;
        case 'email':
          await this.databases.createEmailAttribute(
            this.config.databaseId,
            collectionId,
            key,
            required || false,
            defaultValue,
            array || false
          );
          break;
        case 'url':
          await this.databases.createUrlAttribute(
            this.config.databaseId,
            collectionId,
            key,
            required || false,
            defaultValue,
            array || false
          );
          break;
        case 'integer':
          await this.databases.createIntegerAttribute(
            this.config.databaseId,
            collectionId,
            key,
            required || false,
            undefined, // min
            undefined, // max
            defaultValue,
            array || false
          );
          break;
        case 'float':
          await this.databases.createFloatAttribute(
            this.config.databaseId,
            collectionId,
            key,
            required || false,
            undefined, // min
            undefined, // max
            defaultValue,
            array || false
          );
          break;
        case 'boolean':
          await this.databases.createBooleanAttribute(
            this.config.databaseId,
            collectionId,
            key,
            required || false,
            defaultValue,
            array || false
          );
          break;
        case 'datetime':
          await this.databases.createDatetimeAttribute(
            this.config.databaseId,
            collectionId,
            key,
            required || false,
            defaultValue,
            array || false
          );
          break;
        default:
          console.warn(`⚠️  Unknown attribute type: ${type}`);
      }

      // Wait a bit for the attribute to be created
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error: any) {
      if (error.message?.includes('Attribute already exists')) {
        console.log(`ℹ️  Attribute '${key}' already exists in collection '${collectionId}'`);
      } else {
        console.error(`❌ Failed to create attribute '${key}':`, error.message);
        throw error;
      }
    }
  }

  private async setupStorage(): Promise<void> {
    console.log('\n🗄️  Setting up storage buckets...');

    // Only create essential buckets to avoid hitting plan limits
    const buckets = [
      { id: this.config.buckets.avatars, name: 'User Avatars', permissions: ['read("any")', 'create("users")', 'update("users")', 'delete("users")'], essential: true },
      { id: this.config.buckets.files, name: 'Workspace Files', permissions: ['read("users")', 'create("users")', 'update("users")', 'delete("users")'], essential: false },
      { id: this.config.buckets.backups, name: 'Workspace Backups', permissions: ['read("users")', 'create("users")', 'update("users")', 'delete("users")'], essential: false },
    ];

    for (const bucket of buckets) {
      try {
        await this.setupBucket(bucket.id, bucket.name, bucket.permissions);
      } catch (error: any) {
        if (error.code === 403 && error.type === 'additional_resource_not_allowed') {
          if (bucket.essential) {
            console.error(`❌ Failed to create essential bucket '${bucket.id}': Plan limit reached`);
            throw error;
          } else {
            console.warn(`⚠️  Skipping optional bucket '${bucket.id}': Plan limit reached`);
            continue;
          }
        }
        throw error;
      }
    }
  }

  private async setupBucket(bucketId: string, name: string, permissions: string[]): Promise<void> {
    try {
      await this.storage.getBucket(bucketId);
      console.log(`✅ Bucket '${bucketId}' already exists`);
    } catch (error: any) {
      if (error.code === 404) {
        console.log(`🗄️  Creating bucket '${bucketId}'...`);
        await this.storage.createBucket(
          bucketId,
          name,
          permissions,
          false, // fileSecurity
          true,  // enabled
          undefined, // maximumFileSize
          ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'txt', 'md', 'json', 'zip'] // allowedFileExtensions
        );
        console.log(`✅ Bucket '${bucketId}' created successfully`);
      } else {
        throw error;
      }
    }
  }

  private async setupAuthentication(): Promise<void> {
    console.log('\n🔐 Setting up authentication...');
    console.log('ℹ️  Authentication settings must be configured manually in the Appwrite console:');
    console.log('   1. Go to Auth > Settings in your Appwrite console');
    console.log('   2. Enable Email/Password authentication');
    console.log('   3. Configure session limits and security settings');
    console.log('   4. Set up OAuth providers if needed');
    console.log('   5. Configure password policies');
  }
}

// Main execution
async function main() {
  const setup = new AppwriteSetup();
  await setup.setup();
}

// Run the setup if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

export { AppwriteSetup };
