#!/usr/bin/env tsx

/**
 * Verify Docker Client-Side Bundling Fixes
 * 
 * This script verifies that the Docker client-side bundling issues have been resolved
 * by testing that dashboard components can be imported without Docker-related errors.
 * 
 * Usage:
 * pnpm verify:docker-fixes
 * or
 * npx tsx src/scripts/verify-docker-fixes.ts
 */

class DockerFixVerifier {
  async verifyFixes(): Promise<void> {
    console.log('🔍 Verifying Docker Client-Side Bundling Fixes...\n');

    const tests = [
      this.testClientServiceImports.bind(this),
      this.testDashboardComponentImports.bind(this),
      this.testWebpackConfiguration.bind(this),
      this.testNoProblematicImports.bind(this),
    ];

    let allPassed = true;

    for (const test of tests) {
      try {
        await test();
      } catch (error) {
        console.error(`❌ Test failed:`, error);
        allPassed = false;
      }
    }

    if (allPassed) {
      console.log('\n🎉 All Docker client-side bundling fixes verified successfully!');
      console.log('\n✅ Summary:');
      console.log('   - Client-safe services are working');
      console.log('   - Dashboard components import correctly');
      console.log('   - Webpack configuration excludes server modules');
      console.log('   - No problematic Docker imports found');
      console.log('\n🚀 Dashboard pages should now load without Docker bundling errors!');
    } else {
      console.log('\n❌ Some verification tests failed. Please check the issues above.');
      process.exit(1);
    }
  }

  private async testClientServiceImports(): Promise<void> {
    console.log('1️⃣  Testing Client Service Imports...');

    try {
      // Test that client services can be imported without errors
      const { nodeReactWorkspaceClientService } = await import('@/services/client/node-react-workspace-client');
      const { nodeReactPackageManagerClientService } = await import('@/services/client/node-react-package-manager-client');

      // Test that services have expected methods
      const workspaceService = nodeReactWorkspaceClientService;
      const packageService = nodeReactPackageManagerClientService;

      const workspaceMethods = [
        'getTemplates',
        'getLivePreview',
        'startLivePreview',
        'stopLivePreview',
        'getWorkspaceStatus'
      ];

      const packageMethods = [
        'getInstalledPackages',
        'searchPackages',
        'installPackage',
        'uninstallPackage'
      ];

      for (const method of workspaceMethods) {
        if (typeof (workspaceService as any)[method] !== 'function') {
          throw new Error(`Workspace service missing method: ${method}`);
        }
      }

      for (const method of packageMethods) {
        if (typeof (packageService as any)[method] !== 'function') {
          throw new Error(`Package service missing method: ${method}`);
        }
      }

      console.log('   ✅ Client services import and have expected methods');
    } catch (error) {
      console.log('   ❌ Client service import failed:', error);
      throw error;
    }
  }

  private async testDashboardComponentImports(): Promise<void> {
    console.log('2️⃣  Testing Dashboard Component Imports...');

    const components = [
      '@/components/node-react-workspace/NodeReactLivePreview',
      '@/components/node-react-workspace/NodeReactTemplateSelector',
      '@/components/node-react-workspace/NodeReactPackageManager',
    ];

    try {
      for (const componentPath of components) {
        try {
          await import(componentPath);
          console.log(`   ✅ ${componentPath.split('/').pop()} imports successfully`);
        } catch (error) {
          console.log(`   ❌ ${componentPath.split('/').pop()} import failed:`, error);
          throw error;
        }
      }
    } catch (error) {
      throw new Error(`Dashboard component import failed: ${error}`);
    }
  }

  private async testWebpackConfiguration(): Promise<void> {
    console.log('3️⃣  Testing Webpack Configuration...');

    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const nextConfigPath = path.resolve(process.cwd(), 'next.config.ts');
      const content = await fs.readFile(nextConfigPath, 'utf-8');

      const requiredExclusions = [
        'dockerode',
        'ssh2',
        'cpu-features',
        'node-pty',
        'ws',
        'bufferutil',
        'utf-8-validate'
      ];

      const requiredAliases = [
        '@/services/docker$',
        '@/services/node-react-live-preview$',
        '@/services/node-react-ai-assistant$',
        '@/services/vm/docker$'
      ];

      let missingExclusions = [];
      let missingAliases = [];

      for (const exclusion of requiredExclusions) {
        if (!content.includes(`'${exclusion}'`)) {
          missingExclusions.push(exclusion);
        }
      }

      for (const alias of requiredAliases) {
        if (!content.includes(alias)) {
          missingAliases.push(alias);
        }
      }

      if (missingExclusions.length > 0) {
        throw new Error(`Missing webpack exclusions: ${missingExclusions.join(', ')}`);
      }

      if (missingAliases.length > 0) {
        throw new Error(`Missing webpack aliases: ${missingAliases.join(', ')}`);
      }

      console.log('   ✅ Webpack configuration properly excludes server-side modules');
    } catch (error) {
      console.log('   ❌ Webpack configuration test failed:', error);
      throw error;
    }
  }

  private async testNoProblematicImports(): Promise<void> {
    console.log('4️⃣  Testing for Problematic Imports...');

    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const problematicPatterns = [
        /import.*from.*['"]dockerode['"]/,
        /import.*from.*['"]ssh2['"]/,
        /import.*from.*['"]cpu-features['"]/,
        /import.*from.*['"]@\/services\/docker['"]/,
        /import.*from.*['"]@\/services\/node-react-live-preview['"]/,
        /import.*from.*['"]@\/services\/node-react-ai-assistant['"]/,
      ];

      const componentsToCheck = [
        'src/components/node-react-workspace/NodeReactLivePreview.tsx',
        'src/components/node-react-workspace/NodeReactTemplateSelector.tsx',
        'src/components/node-react-workspace/NodeReactPackageManager.tsx',
        'src/components/dashboard/DashboardOverview.tsx',
      ];

      let foundProblematicImports = [];

      for (const componentPath of componentsToCheck) {
        try {
          const fullPath = path.resolve(process.cwd(), componentPath);
          const content = await fs.readFile(fullPath, 'utf-8');
          const lines = content.split('\n');

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            for (const pattern of problematicPatterns) {
              if (pattern.test(line)) {
                foundProblematicImports.push(`${componentPath}:${i + 1}: ${line}`);
              }
            }
          }
        } catch (error) {
          // File might not exist, skip
          continue;
        }
      }

      if (foundProblematicImports.length > 0) {
        throw new Error(`Found problematic imports:\n${foundProblematicImports.join('\n')}`);
      }

      console.log('   ✅ No problematic Docker imports found in dashboard components');
    } catch (error) {
      console.log('   ❌ Problematic imports test failed:', error);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const verifier = new DockerFixVerifier();
  
  try {
    await verifier.verifyFixes();
  } catch (error) {
    console.error('\n💥 Docker fix verification failed:', error);
    process.exit(1);
  }
}

// Run the verification if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Verification execution failed:', error);
    process.exit(1);
  });
}

export { DockerFixVerifier };
