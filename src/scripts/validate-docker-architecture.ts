#!/usr/bin/env tsx

/**
 * Docker Architecture Validation Script
 * 
 * This script validates that the Docker service architecture is properly separated:
 * - Server-side code uses @/services/server/docker
 * - Client-side code uses @/services/client/docker-api
 * - No client-side imports of native modules
 * 
 * Usage:
 * pnpm validate:docker-architecture
 * or
 * npx tsx src/scripts/validate-docker-architecture.ts
 */

import { promises as fs } from 'fs';
import path from 'path';

interface ValidationResult {
  file: string;
  issues: string[];
  type: 'client' | 'server' | 'api';
}

class DockerArchitectureValidator {
  private clientPaths = [
    'src/components',
    'src/hooks',
    'src/app/(dashboard)',
    'src/contexts'
  ];

  private serverPaths = [
    'src/app/api',
    'src/services/server',
    'src/lib/server'
  ];

  private problematicPatterns = [
    // Direct Docker service imports
    /import.*from.*['"]@\/services\/docker['"]/,
    /import.*from.*['"]@\/services\/server\/docker['"]/,
    
    // Native module imports
    /import.*from.*['"]dockerode['"]/,
    /import.*from.*['"]ssh2['"]/,
    /import.*from.*['"]cpu-features['"]/,
    /import.*from.*['"]node-pty['"]/,
    
    // Require statements
    /require\(['"]dockerode['"]\)/,
    /require\(['"]ssh2['"]\)/,
    /require\(['"]cpu-features['"]\)/,
  ];

  private serverOnlyPatterns = [
    /import.*from.*['"]@\/services\/server\/docker['"]/,
    /import.*from.*['"]dockerode['"]/,
  ];

  async validateArchitecture(): Promise<void> {
    console.log('🔍 Validating Docker Architecture...\n');

    const results: ValidationResult[] = [];

    // Validate client-side files
    for (const clientPath of this.clientPaths) {
      const clientResults = await this.validateDirectory(clientPath, 'client');
      results.push(...clientResults);
    }

    // Validate server-side files
    for (const serverPath of this.serverPaths) {
      const serverResults = await this.validateDirectory(serverPath, 'server');
      results.push(...serverResults);
    }

    // Report results
    this.reportResults(results);
  }

  private async validateDirectory(dirPath: string, type: 'client' | 'server'): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    
    try {
      const fullPath = path.resolve(process.cwd(), dirPath);
      const files = await this.getAllTsFiles(fullPath);

      for (const file of files) {
        const result = await this.validateFile(file, type);
        if (result.issues.length > 0) {
          results.push(result);
        }
      }
    } catch (error) {
      // Directory might not exist, skip silently
    }

    return results;
  }

  private async validateFile(filePath: string, type: 'client' | 'server'): Promise<ValidationResult> {
    const result: ValidationResult = {
      file: path.relative(process.cwd(), filePath),
      issues: [],
      type
    };

    try {
      const content = await fs.readFile(filePath, 'utf-8');

      if (type === 'client') {
        // Check for problematic imports in client-side code
        for (const pattern of this.problematicPatterns) {
          if (pattern.test(content)) {
            result.issues.push(`Contains server-side import: ${pattern.source}`);
          }
        }

        // Check for direct dockerService usage
        if (content.includes('dockerService') && !content.includes('clientDockerAPIService')) {
          result.issues.push('Uses dockerService instead of clientDockerAPIService');
        }
      } else if (type === 'server') {
        // Check that server-side code uses the correct imports
        if (content.includes('@/services/docker') && !content.includes('@/services/server/docker')) {
          result.issues.push('Uses old Docker service import instead of @/services/server/docker');
        }
      }

    } catch (error) {
      result.issues.push(`Failed to read file: ${error}`);
    }

    return result;
  }

  private async getAllTsFiles(dir: string): Promise<string[]> {
    let results: string[] = [];
    
    try {
      const list = await fs.readdir(dir);
      
      for (const file of list) {
        const filePath = path.resolve(dir, file);
        const stat = await fs.stat(filePath);
        
        if (stat && stat.isDirectory()) {
          // Skip node_modules and other directories we don't want to scan
          if (file.includes('node_modules') || file.includes('.next') || file.includes('.git')) {
            continue;
          }
          results = [...results, ...await this.getAllTsFiles(filePath)];
        } else if (path.extname(file) === '.ts' || path.extname(file) === '.tsx') {
          results.push(filePath);
        }
      }
    } catch (error) {
      // Directory access error, skip
    }
    
    return results;
  }

  private reportResults(results: ValidationResult[]): void {
    const clientIssues = results.filter(r => r.type === 'client' && r.issues.length > 0);
    const serverIssues = results.filter(r => r.type === 'server' && r.issues.length > 0);

    console.log(`📊 Validation Results:\n`);
    console.log(`   Total files scanned: ${results.length}`);
    console.log(`   Client-side issues: ${clientIssues.length}`);
    console.log(`   Server-side issues: ${serverIssues.length}\n`);

    if (clientIssues.length > 0) {
      console.log('❌ Client-side Issues:');
      for (const result of clientIssues) {
        console.log(`   📁 ${result.file}`);
        for (const issue of result.issues) {
          console.log(`      • ${issue}`);
        }
        console.log('');
      }
    }

    if (serverIssues.length > 0) {
      console.log('⚠️  Server-side Issues:');
      for (const result of serverIssues) {
        console.log(`   📁 ${result.file}`);
        for (const issue of result.issues) {
          console.log(`      • ${issue}`);
        }
        console.log('');
      }
    }

    if (clientIssues.length === 0 && serverIssues.length === 0) {
      console.log('🎉 All files pass Docker architecture validation!');
      console.log('✅ Client-side code properly uses client-safe services');
      console.log('✅ Server-side code properly uses server-only services');
      console.log('✅ No native module imports in client-side code');
    } else {
      console.log('💡 Recommendations:');
      console.log('   1. Replace server-side service imports with client-safe alternatives');
      console.log('   2. Use @/services/client/docker-api for client-side Docker operations');
      console.log('   3. Use @/services/server/docker for server-side Docker operations');
      console.log('   4. Remove any direct imports of native modules');
    }

    console.log('\n✅ Docker architecture validation completed!');
  }
}

// Run validation if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new DockerArchitectureValidator();
  validator.validateArchitecture().catch(console.error);
}
