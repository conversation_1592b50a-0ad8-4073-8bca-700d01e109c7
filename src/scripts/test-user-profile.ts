#!/usr/bin/env tsx

/**
 * Test User Profile Creation
 * 
 * This script tests the user profile creation functionality to ensure
 * the "Unknown attribute: name" error is resolved.
 * 
 * Usage:
 * npx tsx src/scripts/test-user-profile.ts
 */

import { config } from 'dotenv';
import { Client, Databases, Users, ID } from 'node-appwrite';

// Load environment variables
config({ path: '.env.local' });

interface TestConfig {
  endpoint: string;
  projectId: string;
  apiKey: string;
  databaseId: string;
  usersCollectionId: string;
}

class UserProfileTester {
  private client: Client;
  private databases: Databases;
  private users: Users;
  private config: TestConfig;

  constructor() {
    this.config = {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!,
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!,
      apiKey: process.env.APPWRITE_API_KEY!,
      databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
      usersCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!,
    };

    // Initialize Appwrite client
    this.client = new Client()
      .setEndpoint(this.config.endpoint)
      .setProject(this.config.projectId)
      .setKey(this.config.apiKey);

    this.databases = new Databases(this.client);
    this.users = new Users(this.client);
  }

  async test(): Promise<void> {
    console.log('🧪 Testing User Profile Creation...\n');

    const testEmail = '<EMAIL>';
    const testUserId = `test-${Date.now()}`;

    try {
      // Step 1: Create user account
      console.log('1️⃣  Creating user account...');
      const user = await this.users.create(
        testUserId,
        testEmail,
        undefined, // phone
        'TestPassword123!',
        'Test User'
      );
      console.log(`✅ User account created: ${user.$id}`);

      // Step 2: Create user profile document
      console.log('2️⃣  Creating user profile document...');
      const profileData = {
        name: user.name,
        email: user.email,
        bio: 'Test user for profile creation',
        company: 'Test Company',
        location: 'Test Location',
        website: 'https://test.example.com',
      };

      const profile = await this.databases.createDocument(
        this.config.databaseId,
        this.config.usersCollectionId,
        user.$id,
        profileData
      );
      console.log(`✅ User profile created: ${profile.$id}`);

      // Step 3: Verify profile can be retrieved
      console.log('3️⃣  Retrieving user profile...');
      const retrievedProfile = await this.databases.getDocument(
        this.config.databaseId,
        this.config.usersCollectionId,
        user.$id
      );
      console.log(`✅ Profile retrieved successfully`);
      console.log(`   Name: ${retrievedProfile.name}`);
      console.log(`   Email: ${retrievedProfile.email}`);
      console.log(`   Bio: ${retrievedProfile.bio}`);

      // Step 4: Update profile
      console.log('4️⃣  Updating user profile...');
      const updatedProfile = await this.databases.updateDocument(
        this.config.databaseId,
        this.config.usersCollectionId,
        user.$id,
        {
          bio: 'Updated test user bio',
          company: 'Updated Test Company',
        }
      );
      console.log(`✅ Profile updated successfully`);
      console.log(`   Updated Bio: ${updatedProfile.bio}`);
      console.log(`   Updated Company: ${updatedProfile.company}`);

      // Step 5: Clean up - Delete test user and profile
      console.log('5️⃣  Cleaning up test data...');
      await this.databases.deleteDocument(
        this.config.databaseId,
        this.config.usersCollectionId,
        user.$id
      );
      await this.users.delete(user.$id);
      console.log(`✅ Test data cleaned up`);

      console.log('\n🎉 All tests passed! User profile creation is working correctly.');

    } catch (error: any) {
      console.error('\n❌ Test failed:', error.message);
      
      // Try to clean up if something went wrong
      try {
        await this.databases.deleteDocument(
          this.config.databaseId,
          this.config.usersCollectionId,
          testUserId
        );
        await this.users.delete(testUserId);
        console.log('🧹 Cleaned up partial test data');
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      throw error;
    }
  }

  async testCollectionSchema(): Promise<void> {
    console.log('\n🔍 Testing Collection Schema...\n');

    try {
      // Get collection details
      const collection = await this.databases.getCollection(
        this.config.databaseId,
        this.config.usersCollectionId
      );

      console.log(`📋 Collection: ${collection.name}`);
      console.log(`🆔 Collection ID: ${collection.$id}`);
      console.log(`📊 Total Documents: ${collection.documentsCount}`);
      console.log(`🔒 Document Security: ${collection.documentSecurity}`);

      console.log('\n📝 Attributes:');
      collection.attributes.forEach((attr: any) => {
        console.log(`   - ${attr.key} (${attr.type}${attr.required ? ', required' : ''}${attr.array ? ', array' : ''})`);
      });

      console.log('\n🔐 Permissions:');
      collection.$permissions.forEach((permission: string) => {
        console.log(`   - ${permission}`);
      });

      // Test required attributes
      const requiredAttributes = ['name', 'email'];
      const existingAttributes = collection.attributes.map((attr: any) => attr.key);
      
      console.log('\n✅ Required Attribute Check:');
      requiredAttributes.forEach(attr => {
        if (existingAttributes.includes(attr)) {
          console.log(`   ✅ ${attr} - exists`);
        } else {
          console.log(`   ❌ ${attr} - missing`);
        }
      });

    } catch (error: any) {
      console.error('❌ Schema test failed:', error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const tester = new UserProfileTester();
  
  try {
    await tester.testCollectionSchema();
    await tester.test();
  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

export { UserProfileTester };
