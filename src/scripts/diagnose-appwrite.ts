#!/usr/bin/env tsx

/**
 * Appwrite Configuration Diagnos<PERSON> Script
 *
 * Run this script to diagnose issues with your Appwrite setup:
 * npx tsx src/scripts/diagnose-appwrite.ts
 */

import { config } from 'dotenv';
import { Client, Databases } from 'node-appwrite';
import { generateAppwriteCLICommands, SETUP_INSTRUCTIONS } from '../lib/appwrite-schema';

// Load environment variables
config({ path: '.env.local' });

async function diagnoseAppwrite() {
  console.log('🔍 Diagnosing Appwrite Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  const requiredEnvVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'NEXT_PUBLIC_APPWRITE_DATABASE_ID',
    'NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID',
    'APPWRITE_API_KEY'
  ];

  let envIssues = 0;
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar}: ${envVar.includes('API_KEY') ? '***' : value}`);
    } else {
      console.log(`❌ ${envVar}: Missing`);
      envIssues++;
    }
  }

  if (envIssues > 0) {
    console.log(`\n⚠️  Found ${envIssues} missing environment variables.`);
    console.log('Please add them to your .env.local file.\n');
  } else {
    console.log('\n✅ All environment variables are configured.\n');
  }

  // Test connection and check collections
  console.log('🗄️  Testing Connection and Collections:');
  try {
    if (envIssues === 0) {
      const client = new Client()
        .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
        .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!)
        .setKey(process.env.APPWRITE_API_KEY!);

      const databases = new Databases(client);

      // Test connection
      await databases.list();
      console.log('✅ Connection successful');

      // Check collections
      const collections = await databases.listCollections(process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!);
      console.log(`✅ Found ${collections.collections.length} collections`);

      const expectedCollections = ['users', 'vms', 'sessions', 'organizations', 'workspaces'];
      const existingCollections = collections.collections.map(c => c.$id);

      expectedCollections.forEach(collectionId => {
        if (existingCollections.includes(collectionId)) {
          console.log(`✅ Collection '${collectionId}' exists`);
        } else {
          console.log(`❌ Collection '${collectionId}' missing`);
        }
      });

    } else {
      console.log('⚠️  Skipping connection test due to missing environment variables');
    }
  } catch (error: any) {
    console.log(`❌ Connection/Schema error: ${error.message}`);
  }

  // Generate CLI commands
  console.log('\n🛠️  Setup Commands:');
  const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || 'YOUR_PROJECT_ID';
  const databaseId = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'YOUR_DATABASE_ID';
  
  console.log('If you need to create the collections, run these Appwrite CLI commands:\n');
  const commands = generateAppwriteCLICommands(projectId, databaseId);
  commands.forEach(command => console.log(command + '\n'));

  // Show setup instructions
  console.log('📖 Setup Instructions:');
  console.log(SETUP_INSTRUCTIONS);
}

// Run the diagnostic
diagnoseAppwrite().catch(error => {
  console.error('❌ Diagnostic failed:', error);
  process.exit(1);
});
