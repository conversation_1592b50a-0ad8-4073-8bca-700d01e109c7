/**
 * VM Server Configuration and Utilities
 * Provides configuration, error handling, logging, and retry mechanisms for VM services
 */

import { logger as appwriteLogger } from '@/lib/appwrite-server';

// VM Server configuration
export const VM_SERVER_CONFIG = {
  retryAttempts: 3,
  retryDelay: 1000, // milliseconds
  timeout: 30000, // 30 seconds
  connectionPoolSize: 5,
  logLevel: process.env.VM_LOG_LEVEL || 'info',
  ssh: {
    timeout: 10000,
    keepAlive: true,
    keepAliveInterval: 5000,
    maxRetries: 3,
    retryDelay: 2000,
  },
  docker: {
    timeout: 30000,
    maxConcurrentOperations: 10,
    healthCheckInterval: 30000,
  },
  monitoring: {
    metricsInterval: 60000, // 1 minute
    healthCheckInterval: 30000, // 30 seconds
    alertThresholds: {
      cpuUsage: 80,
      memoryUsage: 85,
      diskUsage: 90,
    },
  },
  security: {
    maxConcurrentConnections: 10,
    sessionTimeout: 3600000, // 1 hour
    allowedCommands: [
      'docker',
      'ls',
      'cat',
      'echo',
      'pwd',
      'whoami',
      'ps',
      'top',
      'df',
      'free',
      'uname',
    ],
    restrictedPaths: [
      '/etc/passwd',
      '/etc/shadow',
      '/root/.ssh',
      '/home/<USER>/.ssh',
    ],
  },
} as const;

// VM-specific error types
export class VMServerError extends Error {
  constructor(
    message: string,
    public code?: string,
    public type?: string,
    public statusCode?: number,
    public vmId?: string,
    public connectionId?: string
  ) {
    super(message);
    this.name = 'VMServerError';
  }
}

// VM error codes
export const VM_ERROR_CODES = {
  CONNECTION_FAILED: 'VM_CONNECTION_FAILED',
  AUTHENTICATION_FAILED: 'VM_AUTH_FAILED',
  COMMAND_EXECUTION_FAILED: 'VM_COMMAND_FAILED',
  DOCKER_OPERATION_FAILED: 'VM_DOCKER_FAILED',
  RESOURCE_EXHAUSTED: 'VM_RESOURCE_EXHAUSTED',
  TIMEOUT: 'VM_TIMEOUT',
  PERMISSION_DENIED: 'VM_PERMISSION_DENIED',
  INVALID_CONFIGURATION: 'VM_INVALID_CONFIG',
  SERVICE_UNAVAILABLE: 'VM_SERVICE_UNAVAILABLE',
  HEALTH_CHECK_FAILED: 'VM_HEALTH_CHECK_FAILED',
} as const;

// Utility function to handle VM-specific errors
export function handleVMServerError(error: any, vmId?: string, connectionId?: string): VMServerError {
  if (error instanceof VMServerError) {
    return error;
  }

  // Handle SSH errors
  if (error.code === 'ECONNREFUSED') {
    return new VMServerError(
      'Connection refused - VM may be down or SSH service unavailable',
      VM_ERROR_CODES.CONNECTION_FAILED,
      'connection_error',
      503,
      vmId,
      connectionId
    );
  }

  if (error.code === 'ENOTFOUND') {
    return new VMServerError(
      'VM host not found - check VM configuration',
      VM_ERROR_CODES.CONNECTION_FAILED,
      'connection_error',
      404,
      vmId,
      connectionId
    );
  }

  if (error.code === 'ETIMEDOUT') {
    return new VMServerError(
      'Connection timeout - VM may be overloaded or network issues',
      VM_ERROR_CODES.TIMEOUT,
      'timeout_error',
      408,
      vmId,
      connectionId
    );
  }

  // Handle authentication errors
  if (error.message?.includes('Authentication failed') || error.message?.includes('Permission denied')) {
    return new VMServerError(
      'VM authentication failed - check credentials',
      VM_ERROR_CODES.AUTHENTICATION_FAILED,
      'auth_error',
      401,
      vmId,
      connectionId
    );
  }

  // Handle Docker errors
  if (error.message?.includes('docker') || error.message?.includes('Docker')) {
    return new VMServerError(
      `Docker operation failed: ${error.message}`,
      VM_ERROR_CODES.DOCKER_OPERATION_FAILED,
      'docker_error',
      500,
      vmId,
      connectionId
    );
  }

  // Handle command execution errors
  if (error.code && error.signal) {
    return new VMServerError(
      `Command execution failed with code ${error.code}: ${error.message}`,
      VM_ERROR_CODES.COMMAND_EXECUTION_FAILED,
      'execution_error',
      500,
      vmId,
      connectionId
    );
  }

  // Generic error handling
  return new VMServerError(
    error.message || 'An unexpected VM server error occurred',
    'UNKNOWN_VM_ERROR',
    'server_error',
    500,
    vmId,
    connectionId
  );
}

// Retry mechanism for VM operations
export async function withVMRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = VM_SERVER_CONFIG.retryAttempts,
  delay: number = VM_SERVER_CONFIG.retryDelay,
  vmId?: string,
  connectionId?: string
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on authentication errors or client errors (4xx)
      if (error instanceof VMServerError && 
          (error.statusCode && error.statusCode >= 400 && error.statusCode < 500)) {
        throw error;
      }
      
      if (attempt === maxAttempts) {
        break;
      }
      
      // Exponential backoff with jitter
      const jitter = Math.random() * 0.1 * delay;
      const waitTime = (delay * Math.pow(2, attempt - 1)) + jitter;
      
      vmLogger.debug(`VM operation retry ${attempt}/${maxAttempts} after ${waitTime}ms`, {
        vmId,
        connectionId,
        error: error.message
      });
      
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw handleVMServerError(lastError!, vmId, connectionId);
}

// VM-specific logging utility
export const vmLogger = {
  info: (message: string, data?: any) => {
    if (VM_SERVER_CONFIG.logLevel === 'info' || VM_SERVER_CONFIG.logLevel === 'debug') {
      console.log(`[VM-SERVER] ${new Date().toISOString()} INFO: ${message}`, data || '');
    }
  },
  
  error: (message: string, error?: any) => {
    console.error(`[VM-SERVER] ${new Date().toISOString()} ERROR: ${message}`, error || '');
  },
  
  debug: (message: string, data?: any) => {
    if (VM_SERVER_CONFIG.logLevel === 'debug') {
      console.debug(`[VM-SERVER] ${new Date().toISOString()} DEBUG: ${message}`, data || '');
    }
  },
  
  warn: (message: string, data?: any) => {
    console.warn(`[VM-SERVER] ${new Date().toISOString()} WARN: ${message}`, data || '');
  }
};

// VM server constants
export const VM_SERVER_CONSTANTS = {
  // Rate limiting for VM operations
  RATE_LIMITS: {
    CONNECTIONS_PER_MINUTE: 60,
    COMMANDS_PER_MINUTE: 300,
    DOCKER_OPERATIONS_PER_MINUTE: 100,
    MONITORING_REQUESTS_PER_MINUTE: 120,
  },
  
  // Batch operation limits
  BATCH_LIMITS: {
    MAX_CONCURRENT_CONNECTIONS: 5,
    MAX_COMMANDS_PER_BATCH: 10,
    MAX_CONTAINERS_PER_OPERATION: 20,
  },
  
  // Cache settings
  CACHE: {
    VM_STATUS_TTL: 30, // 30 seconds
    SYSTEM_INFO_TTL: 60, // 1 minute
    DOCKER_INFO_TTL: 120, // 2 minutes
    METRICS_TTL: 30, // 30 seconds
  },
  
  // Default ports and paths
  DEFAULTS: {
    SSH_PORT: 22,
    DOCKER_SOCKET: '/var/run/docker.sock',
    WORKSPACE_PATH: '/home/<USER>',
    LOG_PATH: '/var/log/omnispace',
  },
} as const;

// VM health check function
export async function vmHealthCheck(vmId: string): Promise<{
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  checks: {
    connection: boolean;
    ssh: boolean;
    docker: boolean;
    system: boolean;
    resources: boolean;
  };
  details?: Record<string, string>;
}> {
  const timestamp = new Date().toISOString();
  const checks = {
    connection: false,
    ssh: false,
    docker: false,
    system: false,
    resources: false,
  };
  const details: Record<string, string> = {};

  try {
    // This would be implemented by the specific VM services
    // For now, return a basic structure
    return {
      status: 'healthy',
      timestamp,
      checks,
      details,
    };
  } catch (error) {
    vmLogger.error(`VM health check failed for ${vmId}`, error);
    return {
      status: 'unhealthy',
      timestamp,
      checks,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}
