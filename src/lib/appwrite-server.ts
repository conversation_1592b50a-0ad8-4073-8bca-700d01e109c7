import { Client, Account, Databases, Storage, Teams, Functions, Users, ID } from 'node-appwrite';

// Server-side Appwrite configuration
const APPWRITE_ENDPOINT = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!;
const APPWRITE_PROJECT_ID = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!;
const APPWRITE_API_KEY = process.env.APPWRITE_API_KEY!;

// Database and collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!;
export const USERS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!;
export const VMS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID!;
export const SESSIONS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID!;

// Storage bucket ID
export const AVATARS_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID!;

// Server configuration
export const SERVER_CONFIG = {
  retryAttempts: 3,
  retryDelay: 1000, // milliseconds
  timeout: 30000, // 30 seconds
  connectionPoolSize: 10,
  logLevel: process.env.APPWRITE_LOG_LEVEL || 'info',
} as const;

// Create admin client for server-side operations
const createAdminClient = (): Client => {
  return new Client()
    .setEndpoint(APPWRITE_ENDPOINT)
    .setProject(APPWRITE_PROJECT_ID)
    .setKey(APPWRITE_API_KEY);
};

// Create session client for user-specific operations
const createSessionClient = (sessionId?: string): Client => {
  const client = new Client()
    .setEndpoint(APPWRITE_ENDPOINT)
    .setProject(APPWRITE_PROJECT_ID);
  
  if (sessionId) {
    client.setSession(sessionId);
  }
  
  return client;
};

// Admin client instance
const adminClient = createAdminClient();

// Initialize admin services
export const adminAccount = new Account(adminClient);
export const adminDatabases = new Databases(adminClient);
export const adminStorage = new Storage(adminClient);
export const adminTeams = new Teams(adminClient);
export const adminFunctions = new Functions(adminClient);
export const adminUsers = new Users(adminClient);

// Session-based service factory
export const createSessionServices = (sessionId?: string) => {
  const client = createSessionClient(sessionId);
  
  return {
    account: new Account(client),
    databases: new Databases(client),
    storage: new Storage(client),
    teams: new Teams(client),
    functions: new Functions(client),
    client,
  };
};

// Export clients for custom usage
export { adminClient, createAdminClient, createSessionClient, ID };

// Helper function to check if server environment variables are configured
export function checkServerAppwriteConfig(): boolean {
  const requiredVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'APPWRITE_API_KEY',
    'NEXT_PUBLIC_APPWRITE_DATABASE_ID',
    'NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('Missing server-side Appwrite environment variables:', missing);
    return false;
  }

  return true;
}

// Enhanced error types for server-side operations
export class AppwriteServerError extends Error {
  constructor(
    message: string,
    public code?: string,
    public type?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'AppwriteServerError';
  }
}

// Utility function to handle server-side Appwrite errors
export function handleServerAppwriteError(error: any): AppwriteServerError {
  if (error.code && error.message) {
    return new AppwriteServerError(
      error.message, 
      error.code, 
      error.type,
      error.response?.status
    );
  }
  
  return new AppwriteServerError(
    error.message || 'An unexpected server error occurred',
    'UNKNOWN_SERVER_ERROR',
    'server_error',
    500
  );
}

// Retry mechanism for server operations
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = SERVER_CONFIG.retryAttempts,
  delay: number = SERVER_CONFIG.retryDelay
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on authentication errors or client errors (4xx)
      if (error instanceof AppwriteServerError && 
          (error.statusCode && error.statusCode >= 400 && error.statusCode < 500)) {
        throw error;
      }
      
      if (attempt === maxAttempts) {
        break;
      }
      
      // Exponential backoff
      const waitTime = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError!;
}

// Logging utility
export const logger = {
  info: (message: string, data?: any) => {
    if (SERVER_CONFIG.logLevel === 'info' || SERVER_CONFIG.logLevel === 'debug') {
      console.log(`[APPWRITE-SERVER] ${new Date().toISOString()} INFO: ${message}`, data || '');
    }
  },
  
  error: (message: string, error?: any) => {
    console.error(`[APPWRITE-SERVER] ${new Date().toISOString()} ERROR: ${message}`, error || '');
  },
  
  debug: (message: string, data?: any) => {
    if (SERVER_CONFIG.logLevel === 'debug') {
      console.debug(`[APPWRITE-SERVER] ${new Date().toISOString()} DEBUG: ${message}`, data || '');
    }
  },
  
  warn: (message: string, data?: any) => {
    console.warn(`[APPWRITE-SERVER] ${new Date().toISOString()} WARN: ${message}`, data || '');
  }
};

// Server-side constants
export const SERVER_CONSTANTS = {
  // Rate limiting for server operations
  RATE_LIMITS: {
    DATABASE_OPERATIONS_PER_MINUTE: 1000,
    STORAGE_OPERATIONS_PER_MINUTE: 500,
    FUNCTION_EXECUTIONS_PER_MINUTE: 100,
    USER_OPERATIONS_PER_MINUTE: 200,
  },
  
  // Batch operation limits
  BATCH_LIMITS: {
    MAX_DOCUMENTS_PER_BATCH: 100,
    MAX_FILES_PER_BATCH: 50,
    MAX_USERS_PER_BATCH: 25,
  },
  
  // Cache settings
  CACHE: {
    USER_PROFILE_TTL: 300, // 5 minutes
    VM_CONFIG_TTL: 60, // 1 minute
    SESSION_TTL: 1800, // 30 minutes
  },
} as const;

// Health check function
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  services: {
    database: boolean;
    storage: boolean;
    functions: boolean;
    users: boolean;
  };
  error?: string;
}> {
  const timestamp = new Date().toISOString();
  
  try {
    // Test database connection
    const dbHealth = await adminDatabases.list().then(() => true).catch(() => false);
    
    // Test storage connection
    const storageHealth = await adminStorage.listBuckets().then(() => true).catch(() => false);
    
    // Test functions connection
    const functionsHealth = await adminFunctions.list().then(() => true).catch(() => false);
    
    // Test users connection
    const usersHealth = await adminUsers.list().then(() => true).catch(() => false);
    
    const allHealthy = dbHealth && storageHealth && functionsHealth && usersHealth;
    
    return {
      status: allHealthy ? 'healthy' : 'unhealthy',
      timestamp,
      services: {
        database: dbHealth,
        storage: storageHealth,
        functions: functionsHealth,
        users: usersHealth,
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp,
      services: {
        database: false,
        storage: false,
        functions: false,
        users: false,
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
