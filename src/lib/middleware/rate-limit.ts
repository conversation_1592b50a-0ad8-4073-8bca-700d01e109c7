import { NextRequest, NextResponse } from 'next/server';

// Rate limit configuration
interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// In-memory store for rate limiting (in production, use Redis)
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  get(key: string): { count: number; resetTime: number } | undefined {
    const entry = this.store.get(key);
    if (entry && entry.resetTime < Date.now()) {
      this.store.delete(key);
      return undefined;
    }
    return entry;
  }

  set(key: string, value: { count: number; resetTime: number }): void {
    this.store.set(key, value);
  }

  increment(key: string, windowMs: number): { count: number; resetTime: number } {
    const now = Date.now();
    const entry = this.get(key);
    
    if (entry) {
      entry.count++;
      return entry;
    } else {
      const newEntry = { count: 1, resetTime: now + windowMs };
      this.set(key, newEntry);
      return newEntry;
    }
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (entry.resetTime < now) {
        this.store.delete(key);
      }
    }
  }
}

const store = new MemoryStore();

// Cleanup expired entries every 5 minutes
setInterval(() => {
  store.cleanup();
}, 5 * 60 * 1000);

// Rate limiting middleware
export function rateLimitMiddleware(config: RateLimitConfig) {
  return async (
    request: NextRequest,
    handler: (request: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    try {
      // Generate key for rate limiting (IP + user ID if available)
      const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                request.headers.get('x-real-ip') ||
                request.headers.get('cf-connecting-ip') ||
                'unknown';
      
      const userId = request.headers.get('x-user-id');
      const key = userId ? `${ip}:${userId}` : ip;

      // Check current count
      const entry = store.increment(key, config.windowMs);

      // Check if limit exceeded
      if (entry.count > config.maxRequests) {
        const resetTime = Math.ceil((entry.resetTime - Date.now()) / 1000);
        
        return NextResponse.json({
          success: false,
          error: {
            message: config.message || 'Too many requests',
            code: 'RATE_LIMIT_EXCEEDED',
            type: 'rate_limit_error',
            retryAfter: resetTime
          }
        }, { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': entry.resetTime.toString(),
            'Retry-After': resetTime.toString()
          }
        });
      }

      // Execute handler
      const response = await handler(request);

      // Add rate limit headers to response
      const remaining = Math.max(0, config.maxRequests - entry.count);
      response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
      response.headers.set('X-RateLimit-Remaining', remaining.toString());
      response.headers.set('X-RateLimit-Reset', entry.resetTime.toString());

      return response;
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      // Continue without rate limiting on error
      return handler(request);
    }
  };
}

// Predefined rate limit configurations
export const rateLimitConfigs = {
  // Strict rate limiting for authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10,
    message: 'Too many authentication attempts, please try again later'
  },

  // Moderate rate limiting for API endpoints
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    message: 'Too many API requests, please slow down'
  },

  // Lenient rate limiting for read operations
  read: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200,
    message: 'Too many read requests, please slow down'
  },

  // Strict rate limiting for write operations
  write: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50,
    message: 'Too many write requests, please slow down'
  },

  // Very strict rate limiting for file uploads
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    message: 'Too many file uploads, please wait before uploading more files'
  },

  // Moderate rate limiting for function executions
  functions: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    message: 'Too many function executions, please slow down'
  }
} as const;

// Convenience middleware functions
export const authRateLimit = rateLimitMiddleware(rateLimitConfigs.auth);
export const apiRateLimit = rateLimitMiddleware(rateLimitConfigs.api);
export const readRateLimit = rateLimitMiddleware(rateLimitConfigs.read);
export const writeRateLimit = rateLimitMiddleware(rateLimitConfigs.write);
export const uploadRateLimit = rateLimitMiddleware(rateLimitConfigs.upload);
export const functionsRateLimit = rateLimitMiddleware(rateLimitConfigs.functions);

// Adaptive rate limiting based on user tier
export function adaptiveRateLimit(
  freeUserConfig: RateLimitConfig,
  premiumUserConfig: RateLimitConfig,
  enterpriseUserConfig: RateLimitConfig
) {
  return async (
    request: NextRequest,
    handler: (request: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    try {
      // Determine user tier (this would come from your user data)
      const userTier = request.headers.get('x-user-tier') || 'free';
      
      let config: RateLimitConfig;
      switch (userTier) {
        case 'enterprise':
          config = enterpriseUserConfig;
          break;
        case 'premium':
          config = premiumUserConfig;
          break;
        default:
          config = freeUserConfig;
          break;
      }

      return rateLimitMiddleware(config)(request, handler);
    } catch (error) {
      console.error('Adaptive rate limit middleware error:', error);
      // Fall back to free tier limits on error
      return rateLimitMiddleware(freeUserConfig)(request, handler);
    }
  };
}

// IP-based rate limiting for public endpoints
export function ipRateLimit(config: RateLimitConfig) {
  return async (
    request: NextRequest,
    handler: (request: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    try {
      const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                request.headers.get('x-real-ip') ||
                request.headers.get('cf-connecting-ip') ||
                'unknown';

      const entry = store.increment(ip, config.windowMs);

      if (entry.count > config.maxRequests) {
        const resetTime = Math.ceil((entry.resetTime - Date.now()) / 1000);
        
        return NextResponse.json({
          success: false,
          error: {
            message: config.message || 'Too many requests from this IP',
            code: 'IP_RATE_LIMIT_EXCEEDED',
            type: 'rate_limit_error',
            retryAfter: resetTime
          }
        }, { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': entry.resetTime.toString(),
            'Retry-After': resetTime.toString()
          }
        });
      }

      const response = await handler(request);
      
      const remaining = Math.max(0, config.maxRequests - entry.count);
      response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
      response.headers.set('X-RateLimit-Remaining', remaining.toString());
      response.headers.set('X-RateLimit-Reset', entry.resetTime.toString());

      return response;
    } catch (error) {
      console.error('IP rate limit middleware error:', error);
      return handler(request);
    }
  };
}

// Rate limit status endpoint
export async function getRateLimitStatus(request: NextRequest): Promise<{
  ip: string;
  userId?: string;
  limits: Array<{
    type: string;
    limit: number;
    remaining: number;
    resetTime: number;
  }>;
}> {
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
            request.headers.get('x-real-ip') ||
            request.headers.get('cf-connecting-ip') ||
            'unknown';
  
  const userId = request.headers.get('x-user-id');
  const key = userId ? `${ip}:${userId}` : ip;

  const limits = [];
  
  // Check status for different rate limit types
  for (const [type, config] of Object.entries(rateLimitConfigs)) {
    const entry = store.get(`${key}:${type}`);
    limits.push({
      type,
      limit: config.maxRequests,
      remaining: entry ? Math.max(0, config.maxRequests - entry.count) : config.maxRequests,
      resetTime: entry ? entry.resetTime : Date.now() + config.windowMs
    });
  }

  return {
    ip,
    userId: userId || undefined,
    limits
  };
}
