import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/services/appwrite';

// Authentication middleware
export async function authMiddleware(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Extract session ID from headers or cookies
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;

    if (!sessionId) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED',
          type: 'auth_error'
        }
      }, { status: 401 });
    }

    // Verify session by getting current user
    const userResult = await authService.getCurrentUser(sessionId);
    
    if (!userResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Invalid or expired session',
          code: 'INVALID_SESSION',
          type: 'auth_error'
        }
      }, { status: 401 });
    }

    // Add user info to request headers for downstream handlers
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-user-id', userResult.data!.$id);
    requestHeaders.set('x-user-email', userResult.data!.email);
    requestHeaders.set('x-session-verified', 'true');

    // Create new request with updated headers
    const authenticatedRequest = new NextRequest(request.url, {
      method: request.method,
      headers: requestHeaders,
      body: request.body,
    });

    return handler(authenticatedRequest);
  } catch (error) {
    console.error('Auth middleware error:', error);
    return NextResponse.json({
      success: false,
      error: {
        message: 'Authentication error',
        code: 'AUTH_MIDDLEWARE_ERROR',
        type: 'server_error'
      }
    }, { status: 500 });
  }
}

// Optional authentication middleware (doesn't fail if no auth)
export async function optionalAuthMiddleware(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const sessionId = request.headers.get('x-appwrite-session') || 
                     request.cookies.get('appwrite-session')?.value;

    if (sessionId) {
      const userResult = await authService.getCurrentUser(sessionId);
      
      if (userResult.success) {
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set('x-user-id', userResult.data!.$id);
        requestHeaders.set('x-user-email', userResult.data!.email);
        requestHeaders.set('x-session-verified', 'true');

        const authenticatedRequest = new NextRequest(request.url, {
          method: request.method,
          headers: requestHeaders,
          body: request.body,
        });

        return handler(authenticatedRequest);
      }
    }

    // Continue without authentication
    return handler(request);
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    // Continue without authentication on error
    return handler(request);
  }
}

// Admin authentication middleware
export async function adminAuthMiddleware(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Check for admin API key
    const apiKey = request.headers.get('x-appwrite-key') || 
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Admin API key required',
          code: 'ADMIN_KEY_REQUIRED',
          type: 'auth_error'
        }
      }, { status: 401 });
    }

    // Verify API key (in production, you'd validate against your stored keys)
    const expectedApiKey = process.env.APPWRITE_API_KEY;
    
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      return NextResponse.json({
        success: false,
        error: {
          message: 'Invalid admin API key',
          code: 'INVALID_ADMIN_KEY',
          type: 'auth_error'
        }
      }, { status: 401 });
    }

    // Add admin flag to request headers
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-admin-verified', 'true');

    const adminRequest = new NextRequest(request.url, {
      method: request.method,
      headers: requestHeaders,
      body: request.body,
    });

    return handler(adminRequest);
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    return NextResponse.json({
      success: false,
      error: {
        message: 'Admin authentication error',
        code: 'ADMIN_AUTH_ERROR',
        type: 'server_error'
      }
    }, { status: 500 });
  }
}

// Role-based access control middleware
export function roleBasedAuthMiddleware(allowedRoles: string[]) {
  return async (
    request: NextRequest,
    handler: (request: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    try {
      // First, ensure user is authenticated
      const sessionId = request.headers.get('x-appwrite-session') || 
                       request.cookies.get('appwrite-session')?.value;

      if (!sessionId) {
        return NextResponse.json({
          success: false,
          error: {
            message: 'Authentication required',
            code: 'AUTHENTICATION_REQUIRED',
            type: 'auth_error'
          }
        }, { status: 401 });
      }

      const userResult = await authService.getCurrentUser(sessionId);
      
      if (!userResult.success) {
        return NextResponse.json({
          success: false,
          error: {
            message: 'Invalid or expired session',
            code: 'INVALID_SESSION',
            type: 'auth_error'
          }
        }, { status: 401 });
      }

      // Check user roles (this would depend on your user schema)
      const userRoles = (userResult.data as any).roles || ['user']; // Default role
      const hasPermission = allowedRoles.some(role => userRoles.includes(role));

      if (!hasPermission) {
        return NextResponse.json({
          success: false,
          error: {
            message: 'Insufficient permissions',
            code: 'INSUFFICIENT_PERMISSIONS',
            type: 'auth_error'
          }
        }, { status: 403 });
      }

      // Add user info to request headers
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', userResult.data!.$id);
      requestHeaders.set('x-user-email', userResult.data!.email);
      requestHeaders.set('x-user-roles', JSON.stringify(userRoles));
      requestHeaders.set('x-session-verified', 'true');

      const authorizedRequest = new NextRequest(request.url, {
        method: request.method,
        headers: requestHeaders,
        body: request.body,
      });

      return handler(authorizedRequest);
    } catch (error) {
      console.error('Role-based auth middleware error:', error);
      return NextResponse.json({
        success: false,
        error: {
          message: 'Authorization error',
          code: 'AUTHORIZATION_ERROR',
          type: 'server_error'
        }
      }, { status: 500 });
    }
  };
}

// Utility function to extract user info from authenticated request
export function getUserFromRequest(request: NextRequest): {
  userId: string | null;
  userEmail: string | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  roles: string[];
} {
  return {
    userId: request.headers.get('x-user-id'),
    userEmail: request.headers.get('x-user-email'),
    isAuthenticated: request.headers.get('x-session-verified') === 'true',
    isAdmin: request.headers.get('x-admin-verified') === 'true',
    roles: JSON.parse(request.headers.get('x-user-roles') || '[]'),
  };
}
