// Metrics collection and monitoring for Appwrite operations
export interface MetricEntry {
  timestamp: number;
  name: string;
  value: number;
  tags?: Record<string, string>;
  type: 'counter' | 'gauge' | 'histogram' | 'timer';
}

export interface HistogramBucket {
  le: number; // less than or equal to
  count: number;
}

export interface HistogramMetric {
  count: number;
  sum: number;
  buckets: HistogramBucket[];
}

export interface TimerMetric {
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  p50: number;
  p95: number;
  p99: number;
}

class MetricsCollector {
  private metrics = new Map<string, MetricEntry[]>();
  private counters = new Map<string, number>();
  private gauges = new Map<string, number>();
  private histograms = new Map<string, number[]>();
  private timers = new Map<string, number[]>();
  private readonly maxMetricHistory = 1000;

  // Counter metrics (monotonically increasing)
  incrementCounter(name: string, value: number = 1, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    const currentValue = this.counters.get(key) || 0;
    const newValue = currentValue + value;
    
    this.counters.set(key, newValue);
    this.recordMetric(name, newValue, 'counter', tags);
  }

  getCounter(name: string, tags?: Record<string, string>): number {
    const key = this.getMetricKey(name, tags);
    return this.counters.get(key) || 0;
  }

  // Gauge metrics (can go up or down)
  setGauge(name: string, value: number, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    this.gauges.set(key, value);
    this.recordMetric(name, value, 'gauge', tags);
  }

  incrementGauge(name: string, value: number = 1, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    const currentValue = this.gauges.get(key) || 0;
    const newValue = currentValue + value;
    this.setGauge(name, newValue, tags);
  }

  decrementGauge(name: string, value: number = 1, tags?: Record<string, string>): void {
    this.incrementGauge(name, -value, tags);
  }

  getGauge(name: string, tags?: Record<string, string>): number {
    const key = this.getMetricKey(name, tags);
    return this.gauges.get(key) || 0;
  }

  // Histogram metrics (for measuring distributions)
  recordHistogram(name: string, value: number, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    const values = this.histograms.get(key) || [];
    values.push(value);
    
    // Keep only recent values
    if (values.length > this.maxMetricHistory) {
      values.shift();
    }
    
    this.histograms.set(key, values);
    this.recordMetric(name, value, 'histogram', tags);
  }

  getHistogram(name: string, tags?: Record<string, string>): HistogramMetric {
    const key = this.getMetricKey(name, tags);
    const values = this.histograms.get(key) || [];
    
    if (values.length === 0) {
      return { count: 0, sum: 0, buckets: [] };
    }

    const sum = values.reduce((a, b) => a + b, 0);
    const buckets = this.calculateHistogramBuckets(values);

    return {
      count: values.length,
      sum,
      buckets
    };
  }

  // Timer metrics (for measuring durations)
  recordTimer(name: string, duration: number, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    const values = this.timers.get(key) || [];
    values.push(duration);
    
    // Keep only recent values
    if (values.length > this.maxMetricHistory) {
      values.shift();
    }
    
    this.timers.set(key, values);
    this.recordMetric(name, duration, 'timer', tags);
  }

  getTimer(name: string, tags?: Record<string, string>): TimerMetric {
    const key = this.getMetricKey(name, tags);
    const values = this.timers.get(key) || [];
    
    if (values.length === 0) {
      return { count: 0, sum: 0, min: 0, max: 0, avg: 0, p50: 0, p95: 0, p99: 0 };
    }

    const sorted = [...values].sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);
    const count = values.length;

    return {
      count,
      sum,
      min: sorted[0],
      max: sorted[count - 1],
      avg: sum / count,
      p50: this.percentile(sorted, 0.5),
      p95: this.percentile(sorted, 0.95),
      p99: this.percentile(sorted, 0.99)
    };
  }

  // Time a function execution
  async timeFunction<T>(
    name: string,
    fn: () => Promise<T>,
    tags?: Record<string, string>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      this.recordTimer(name, duration, tags);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTimer(name, duration, { ...tags, status: 'error' });
      throw error;
    }
  }

  // Get all metrics
  getAllMetrics(): {
    counters: Record<string, number>;
    gauges: Record<string, number>;
    histograms: Record<string, HistogramMetric>;
    timers: Record<string, TimerMetric>;
  } {
    const counters: Record<string, number> = {};
    const gauges: Record<string, number> = {};
    const histograms: Record<string, HistogramMetric> = {};
    const timers: Record<string, TimerMetric> = {};

    // Convert counters
    for (const [key, value] of this.counters.entries()) {
      counters[key] = value;
    }

    // Convert gauges
    for (const [key, value] of this.gauges.entries()) {
      gauges[key] = value;
    }

    // Convert histograms
    for (const [key] of this.histograms.entries()) {
      const [name, tags] = this.parseMetricKey(key);
      histograms[key] = this.getHistogram(name, tags);
    }

    // Convert timers
    for (const [key] of this.timers.entries()) {
      const [name, tags] = this.parseMetricKey(key);
      timers[key] = this.getTimer(name, tags);
    }

    return { counters, gauges, histograms, timers };
  }

  // Get metrics summary
  getMetricsSummary(): {
    totalMetrics: number;
    activeCounters: number;
    activeGauges: number;
    activeHistograms: number;
    activeTimers: number;
    memoryUsage: number;
  } {
    const totalMetrics = Array.from(this.metrics.values()).reduce((sum, entries) => sum + entries.length, 0);
    
    return {
      totalMetrics,
      activeCounters: this.counters.size,
      activeGauges: this.gauges.size,
      activeHistograms: this.histograms.size,
      activeTimers: this.timers.size,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
    this.counters.clear();
    this.gauges.clear();
    this.histograms.clear();
    this.timers.clear();
  }

  // Export metrics in Prometheus format
  exportPrometheusMetrics(): string {
    const lines: string[] = [];
    const timestamp = Date.now();

    // Export counters
    for (const [key, value] of this.counters.entries()) {
      const [name, tags] = this.parseMetricKey(key);
      const tagsStr = this.formatPrometheusTags(tags);
      lines.push(`# TYPE ${name}_total counter`);
      lines.push(`${name}_total${tagsStr} ${value} ${timestamp}`);
    }

    // Export gauges
    for (const [key, value] of this.gauges.entries()) {
      const [name, tags] = this.parseMetricKey(key);
      const tagsStr = this.formatPrometheusTags(tags);
      lines.push(`# TYPE ${name} gauge`);
      lines.push(`${name}${tagsStr} ${value} ${timestamp}`);
    }

    // Export histograms
    for (const [key] of this.histograms.entries()) {
      const [name, tags] = this.parseMetricKey(key);
      const histogram = this.getHistogram(name, tags);
      const tagsStr = this.formatPrometheusTags(tags);
      
      lines.push(`# TYPE ${name} histogram`);
      
      for (const bucket of histogram.buckets) {
        const bucketTags = this.formatPrometheusTags({ ...tags, le: bucket.le.toString() });
        lines.push(`${name}_bucket${bucketTags} ${bucket.count} ${timestamp}`);
      }
      
      lines.push(`${name}_count${tagsStr} ${histogram.count} ${timestamp}`);
      lines.push(`${name}_sum${tagsStr} ${histogram.sum} ${timestamp}`);
    }

    return lines.join('\n');
  }

  private recordMetric(name: string, value: number, type: MetricEntry['type'], tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    const entries = this.metrics.get(key) || [];
    
    entries.push({
      timestamp: Date.now(),
      name,
      value,
      tags,
      type
    });

    // Keep only recent entries
    if (entries.length > this.maxMetricHistory) {
      entries.shift();
    }

    this.metrics.set(key, entries);
  }

  private getMetricKey(name: string, tags?: Record<string, string>): string {
    if (!tags || Object.keys(tags).length === 0) {
      return name;
    }
    
    const sortedTags = Object.keys(tags).sort().map(key => `${key}=${tags[key]}`).join(',');
    return `${name}{${sortedTags}}`;
  }

  private parseMetricKey(key: string): [string, Record<string, string> | undefined] {
    const match = key.match(/^([^{]+)(?:\{(.+)\})?$/);
    if (!match) {
      return [key, undefined];
    }

    const name = match[1];
    const tagsStr = match[2];
    
    if (!tagsStr) {
      return [name, undefined];
    }

    const tags: Record<string, string> = {};
    for (const pair of tagsStr.split(',')) {
      const [key, value] = pair.split('=');
      tags[key] = value;
    }

    return [name, tags];
  }

  private calculateHistogramBuckets(values: number[]): HistogramBucket[] {
    const buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, Infinity];
    const sorted = [...values].sort((a, b) => a - b);
    
    return buckets.map(le => ({
      le,
      count: sorted.filter(v => v <= le).length
    }));
  }

  private percentile(sortedValues: number[], p: number): number {
    const index = Math.ceil(sortedValues.length * p) - 1;
    return sortedValues[Math.max(0, index)];
  }

  private formatPrometheusTags(tags?: Record<string, string>): string {
    if (!tags || Object.keys(tags).length === 0) {
      return '';
    }
    
    const tagPairs = Object.keys(tags).sort().map(key => `${key}="${tags[key]}"`);
    return `{${tagPairs.join(',')}}`;
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in bytes
    let size = 0;
    
    // Count metric entries
    for (const entries of this.metrics.values()) {
      size += entries.length * 100; // Rough estimate per entry
    }
    
    // Count histogram values
    for (const values of this.histograms.values()) {
      size += values.length * 8; // 8 bytes per number
    }
    
    // Count timer values
    for (const values of this.timers.values()) {
      size += values.length * 8; // 8 bytes per number
    }
    
    return size;
  }
}

// Create singleton metrics collector
export const metrics = new MetricsCollector();

// Predefined metric names for Appwrite operations
export const METRIC_NAMES = {
  // Authentication metrics
  AUTH_LOGIN_ATTEMPTS: 'appwrite_auth_login_attempts',
  AUTH_LOGIN_SUCCESS: 'appwrite_auth_login_success',
  AUTH_LOGIN_FAILURES: 'appwrite_auth_login_failures',
  AUTH_SESSION_DURATION: 'appwrite_auth_session_duration',
  
  // Database metrics
  DB_OPERATIONS: 'appwrite_db_operations',
  DB_QUERY_DURATION: 'appwrite_db_query_duration',
  DB_DOCUMENT_COUNT: 'appwrite_db_document_count',
  DB_COLLECTION_COUNT: 'appwrite_db_collection_count',
  
  // Storage metrics
  STORAGE_UPLOADS: 'appwrite_storage_uploads',
  STORAGE_DOWNLOADS: 'appwrite_storage_downloads',
  STORAGE_FILE_SIZE: 'appwrite_storage_file_size',
  STORAGE_OPERATION_DURATION: 'appwrite_storage_operation_duration',
  
  // Function metrics
  FUNCTION_EXECUTIONS: 'appwrite_function_executions',
  FUNCTION_DURATION: 'appwrite_function_duration',
  FUNCTION_ERRORS: 'appwrite_function_errors',
  
  // API metrics
  API_REQUESTS: 'appwrite_api_requests',
  API_RESPONSE_TIME: 'appwrite_api_response_time',
  API_ERRORS: 'appwrite_api_errors',
  
  // System metrics
  ACTIVE_CONNECTIONS: 'appwrite_active_connections',
  MEMORY_USAGE: 'appwrite_memory_usage',
  CPU_USAGE: 'appwrite_cpu_usage',
} as const;

export default metrics;
