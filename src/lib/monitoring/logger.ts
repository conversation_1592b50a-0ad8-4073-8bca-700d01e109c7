// Enhanced logging system for Appwrite operations
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  service?: string;
  operation?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  duration?: number;
  metadata?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  maxLogSize: number;
  rotateDaily: boolean;
}

class AppwriteLogger {
  private config: LoggerConfig;
  private logs: LogEntry[] = [];
  private readonly maxInMemoryLogs = 1000;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableFile: false,
      enableRemote: false,
      maxLogSize: 10 * 1024 * 1024, // 10MB
      rotateDaily: true,
      ...config
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private formatLogEntry(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;
    const service = entry.service ? `[${entry.service}]` : '';
    const operation = entry.operation ? `[${entry.operation}]` : '';
    const duration = entry.duration ? `(${entry.duration}ms)` : '';
    
    let message = `${timestamp} ${levelName} ${service}${operation} ${entry.message} ${duration}`;
    
    if (entry.userId) {
      message += ` userId=${entry.userId}`;
    }
    
    if (entry.requestId) {
      message += ` requestId=${entry.requestId}`;
    }
    
    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      message += ` metadata=${JSON.stringify(entry.metadata)}`;
    }
    
    if (entry.error) {
      message += `\nError: ${entry.error.name}: ${entry.error.message}`;
      if (entry.error.stack) {
        message += `\nStack: ${entry.error.stack}`;
      }
    }
    
    return message;
  }

  private async writeLog(entry: LogEntry): Promise<void> {
    // Add to in-memory store
    this.logs.push(entry);
    if (this.logs.length > this.maxInMemoryLogs) {
      this.logs.shift(); // Remove oldest log
    }

    // Console logging
    if (this.config.enableConsole) {
      const formattedMessage = this.formatLogEntry(entry);
      
      switch (entry.level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage);
          break;
        case LogLevel.INFO:
          console.info(formattedMessage);
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage);
          break;
        case LogLevel.ERROR:
        case LogLevel.FATAL:
          console.error(formattedMessage);
          break;
      }
    }

    // File logging (in production, you'd implement actual file writing)
    if (this.config.enableFile) {
      // Implementation would depend on your file system setup
      // For serverless environments, consider using external logging services
    }

    // Remote logging
    if (this.config.enableRemote && this.config.remoteEndpoint) {
      try {
        await fetch(this.config.remoteEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(entry)
        });
      } catch (error) {
        console.error('Failed to send log to remote endpoint:', error);
      }
    }
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context: Partial<LogEntry> = {}
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      ...context
    };
  }

  debug(message: string, context: Partial<LogEntry> = {}): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
      this.writeLog(entry);
    }
  }

  info(message: string, context: Partial<LogEntry> = {}): void {
    if (this.shouldLog(LogLevel.INFO)) {
      const entry = this.createLogEntry(LogLevel.INFO, message, context);
      this.writeLog(entry);
    }
  }

  warn(message: string, context: Partial<LogEntry> = {}): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const entry = this.createLogEntry(LogLevel.WARN, message, context);
      this.writeLog(entry);
    }
  }

  error(message: string, error?: Error, context: Partial<LogEntry> = {}): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const entry = this.createLogEntry(LogLevel.ERROR, message, {
        ...context,
        error: error ? {
          name: error.name,
          message: error.message,
          stack: error.stack,
          code: (error as any).code
        } : undefined
      });
      this.writeLog(entry);
    }
  }

  fatal(message: string, error?: Error, context: Partial<LogEntry> = {}): void {
    if (this.shouldLog(LogLevel.FATAL)) {
      const entry = this.createLogEntry(LogLevel.FATAL, message, {
        ...context,
        error: error ? {
          name: error.name,
          message: error.message,
          stack: error.stack,
          code: (error as any).code
        } : undefined
      });
      this.writeLog(entry);
    }
  }

  // Operation logging with timing
  async logOperation<T>(
    operation: string,
    service: string,
    fn: () => Promise<T>,
    context: Partial<LogEntry> = {}
  ): Promise<T> {
    const startTime = Date.now();
    const requestId = context.requestId || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.info(`Starting operation: ${operation}`, {
      service,
      operation,
      requestId,
      ...context
    });

    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      this.info(`Operation completed: ${operation}`, {
        service,
        operation,
        requestId,
        duration,
        ...context
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.error(`Operation failed: ${operation}`, error as Error, {
        service,
        operation,
        requestId,
        duration,
        ...context
      });
      
      throw error;
    }
  }

  // Get recent logs
  getRecentLogs(count: number = 100, level?: LogLevel): LogEntry[] {
    let filteredLogs = this.logs;
    
    if (level !== undefined) {
      filteredLogs = this.logs.filter(log => log.level >= level);
    }
    
    return filteredLogs.slice(-count);
  }

  // Get logs by service
  getLogsByService(service: string, count: number = 100): LogEntry[] {
    return this.logs
      .filter(log => log.service === service)
      .slice(-count);
  }

  // Get logs by user
  getLogsByUser(userId: string, count: number = 100): LogEntry[] {
    return this.logs
      .filter(log => log.userId === userId)
      .slice(-count);
  }

  // Get error logs
  getErrorLogs(count: number = 100): LogEntry[] {
    return this.logs
      .filter(log => log.level >= LogLevel.ERROR)
      .slice(-count);
  }

  // Clear logs
  clearLogs(): void {
    this.logs = [];
  }

  // Get logger statistics
  getStats(): {
    totalLogs: number;
    logsByLevel: Record<string, number>;
    logsByService: Record<string, number>;
    recentErrors: number;
    averageOperationTime: number;
  } {
    const logsByLevel: Record<string, number> = {};
    const logsByService: Record<string, number> = {};
    let totalOperationTime = 0;
    let operationCount = 0;
    let recentErrors = 0;
    const oneHourAgo = Date.now() - 60 * 60 * 1000;

    for (const log of this.logs) {
      // Count by level
      const levelName = LogLevel[log.level];
      logsByLevel[levelName] = (logsByLevel[levelName] || 0) + 1;

      // Count by service
      if (log.service) {
        logsByService[log.service] = (logsByService[log.service] || 0) + 1;
      }

      // Calculate operation times
      if (log.duration) {
        totalOperationTime += log.duration;
        operationCount++;
      }

      // Count recent errors
      if (log.level >= LogLevel.ERROR && new Date(log.timestamp).getTime() > oneHourAgo) {
        recentErrors++;
      }
    }

    return {
      totalLogs: this.logs.length,
      logsByLevel,
      logsByService,
      recentErrors,
      averageOperationTime: operationCount > 0 ? totalOperationTime / operationCount : 0
    };
  }
}

// Create singleton logger instance
const loggerConfig: Partial<LoggerConfig> = {
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableFile: process.env.NODE_ENV === 'production',
  enableRemote: !!process.env.REMOTE_LOGGING_ENDPOINT,
  remoteEndpoint: process.env.REMOTE_LOGGING_ENDPOINT,
};

export const logger = new AppwriteLogger(loggerConfig);

// Export logger for use in services
export default logger;
