import { Client, Account, Databases, Storage, Teams, Functions, ID } from 'appwrite';

// Appwrite configuration
const APPWRITE_ENDPOINT = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!;
const APPWRITE_PROJECT_ID = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!;

// Database and collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!;
export const USERS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID!;
export const VMS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID!;
export const SESSIONS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID!;

// Storage bucket ID
export const AVATARS_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID!;

// Create Appwrite client
const client = new Client()
  .setEndpoint(APPWRITE_ENDPOINT)
  .setProject(APPWRITE_PROJECT_ID);

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const teams = new Teams(client);
export const functions = new Functions(client);

// Export client for custom usage
export { client, ID };

// Helper function to check if environment variables are configured
export function checkAppwriteConfig(): boolean {
  const requiredVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'NEXT_PUBLIC_APPWRITE_DATABASE_ID',
    'NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('Missing Appwrite environment variables:', missing);
    return false;
  }

  return true;
}

// OAuth provider configurations
export const OAUTH_PROVIDERS = {
  google: 'google',
  github: 'github',
  discord: 'discord',
  facebook: 'facebook',
  apple: 'apple',
  microsoft: 'microsoft',
} as const;

export type OAuthProvider = keyof typeof OAUTH_PROVIDERS;

// User preferences and settings
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    vmUpdates: boolean;
    securityAlerts: boolean;
  };
  dashboard: {
    defaultView: 'grid' | 'list';
    itemsPerPage: number;
    autoRefresh: boolean;
  };
}

// Default user preferences
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  notifications: {
    email: true,
    push: true,
    vmUpdates: true,
    securityAlerts: true,
  },
  dashboard: {
    defaultView: 'grid',
    itemsPerPage: 12,
    autoRefresh: true,
  },
};

// User profile interface
export interface UserProfile {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  company?: string;
  location?: string;
  website?: string;
  role?: string;
  permissions?: string;
  metadata?: string;
  preferences: UserPreferences;
  subscription: {
    plan: 'starter' | 'professional' | 'enterprise';
    status: 'active' | 'inactive' | 'cancelled' | 'past_due';
    currentPeriodEnd?: string;
  };
  usage: {
    vmsCreated: number;
    storageUsed: number; // in bytes
    computeHours: number;
    lastActivity: string;
  };
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: string;
    loginSessions: number;
  };
}

// VM configuration interface
export interface VMConfiguration {
  $id?: string;
  $createdAt?: string;
  $updatedAt?: string;
  userId: string;
  name: string;
  description?: string;
  template: 'ubuntu-desktop' | 'development' | 'minimal' | 'custom';
  resources: {
    cpu: number;
    memory: number; // in MB
    storage: number; // in GB
  };
  network: {
    ports: { [key: string]: number };
    publicAccess: boolean;
  };
  status: 'creating' | 'running' | 'stopped' | 'error' | 'deleting';
  vncPort?: number;
  vncPassword?: string;
  tags: string[];
  autoStart: boolean;
  autoStop: boolean;
  backupEnabled: boolean;
  lastAccessed?: string;
  metadata: {
    createdBy: string;
    environment: 'development' | 'staging' | 'production';
    project?: string;
    cost: number; // estimated monthly cost
  };
}

// Session tracking interface
export interface UserSession {
  $id?: string;
  $createdAt?: string;
  $updatedAt?: string;
  userId: string;
  sessionId: string;
  deviceInfo: {
    userAgent: string;
    ip: string;
    location?: string;
    device: string;
    browser: string;
    os: string;
  };
  activity: {
    loginTime: string;
    lastActivity: string;
    actions: number;
    vmAccessed: string[];
  };
  status: 'active' | 'expired' | 'terminated';
  expiresAt: string;
}

// Error types for better error handling
export class AppwriteError extends Error {
  constructor(
    message: string,
    public code?: string,
    public type?: string
  ) {
    super(message);
    this.name = 'AppwriteError';
  }
}

// Utility function to handle Appwrite errors
export function handleAppwriteError(error: any): AppwriteError {
  // Handle network errors
  if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
    return new AppwriteError(
      'Network error. Please check your internet connection and try again.',
      'NETWORK_ERROR',
      'network_error'
    );
  }

  // Handle timeout errors
  if (error.name === 'TimeoutError' || error.message?.includes('timeout')) {
    return new AppwriteError(
      'Request timed out. Please try again.',
      'TIMEOUT_ERROR',
      'timeout_error'
    );
  }

  // Handle Appwrite-specific errors
  if (error.code && error.message) {
    return new AppwriteError(error.message, error.code, error.type);
  }

  // Handle generic errors
  return new AppwriteError(
    error.message || 'An unexpected error occurred',
    'UNKNOWN_ERROR',
    'generic_error'
  );
}

// Constants for common operations
export const APPWRITE_CONSTANTS = {
  // Collection permissions
  PERMISSIONS: {
    READ: 'read',
    WRITE: 'write',
    DELETE: 'delete',
  },
  
  // File upload limits
  FILE_LIMITS: {
    AVATAR_MAX_SIZE: 5 * 1024 * 1024, // 5MB
    AVATAR_ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  },
  
  // Session settings
  SESSION: {
    DURATION: 30 * 24 * 60 * 60, // 30 days in seconds
    REFRESH_THRESHOLD: 7 * 24 * 60 * 60, // 7 days in seconds
  },
  
  // Rate limiting
  RATE_LIMITS: {
    LOGIN_ATTEMPTS: 5,
    PASSWORD_RESET: 3,
    EMAIL_VERIFICATION: 3,
  },
} as const;
