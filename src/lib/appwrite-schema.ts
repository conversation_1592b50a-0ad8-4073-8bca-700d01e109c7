/**
 * Appwrite Database Schema Configuration
 * 
 * This file contains the expected schema for Appwrite collections.
 * Use this as a reference when setting up your Appwrite database.
 */

export interface AppwriteCollectionAttribute {
  key: string;
  type: 'string' | 'integer' | 'float' | 'boolean' | 'datetime' | 'email' | 'ip' | 'url';
  size?: number;
  required: boolean;
  array?: boolean;
  default?: any;
}

export interface AppwriteCollectionSchema {
  name: string;
  attributes: AppwriteCollectionAttribute[];
  permissions: string[];
  documentSecurity: boolean;
}

/**
 * Users Collection Schema
 * Collection ID should match USERS_COLLECTION_ID environment variable
 */
export const USERS_COLLECTION_SCHEMA: AppwriteCollectionSchema = {
  name: 'users',
  documentSecurity: true,
  permissions: [
    'read("any")', // Allow reading user profiles (can be restricted as needed)
    'create("users")', // Allow authenticated users to create profiles
    'update("users")', // Allow authenticated users to update their profiles
    'delete("users")', // Allow authenticated users to delete their profiles
  ],
  attributes: [
    {
      key: 'name',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'email',
      type: 'email',
      size: 255,
      required: true,
    },
    {
      key: 'bio',
      type: 'string',
      size: 1000,
      required: false,
      default: '',
    },
    {
      key: 'company',
      type: 'string',
      size: 255,
      required: false,
      default: '',
    },
    {
      key: 'location',
      type: 'string',
      size: 255,
      required: false,
      default: '',
    },
    {
      key: 'website',
      type: 'url',
      size: 255,
      required: false,
    },
    {
      key: 'avatar',
      type: 'string',
      size: 255,
      required: false,
    },
    {
      key: 'role',
      type: 'string',
      size: 50,
      required: false,
      default: 'user',
    },
    {
      key: 'permissions',
      type: 'string',
      size: 2000,
      required: false,
      default: '[]',
    },
    {
      key: 'preferences',
      type: 'string',
      size: 5000,
      required: false,
      default: '{}',
    },
    {
      key: 'metadata',
      type: 'string',
      size: 2000,
      required: false,
      default: '{}',
    },
  ],
};

/**
 * VMs Collection Schema
 * Collection ID should match VMS_COLLECTION_ID environment variable
 */
export const VMS_COLLECTION_SCHEMA: AppwriteCollectionSchema = {
  name: 'vms',
  documentSecurity: true,
  permissions: [
    'create("users")',
    'read("users")',
    'update("users")',
    'delete("users")',
  ],
  attributes: [
    {
      key: 'name',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'description',
      type: 'string',
      size: 1000,
      required: false,
    },
    {
      key: 'status',
      type: 'string',
      size: 50,
      required: false,
      default: 'stopped',
    },
    {
      key: 'ownerId',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'template',
      type: 'string',
      size: 100,
      required: true,
    },
    {
      key: 'resources',
      type: 'string',
      size: 2000,
      required: true,
    },
    {
      key: 'network',
      type: 'string',
      size: 1000,
      required: false,
    },
    {
      key: 'createdAt',
      type: 'datetime',
      required: true,
    },
    {
      key: 'lastActivity',
      type: 'datetime',
      required: false,
    },
  ],
};

/**
 * Sessions Collection Schema
 * Collection ID should match SESSIONS_COLLECTION_ID environment variable
 */
export const SESSIONS_COLLECTION_SCHEMA: AppwriteCollectionSchema = {
  name: 'sessions',
  documentSecurity: true,
  permissions: [
    'create("users")',
    'read("users")',
    'update("users")',
    'delete("users")',
  ],
  attributes: [
    {
      key: 'userId',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'sessionId',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'deviceInfo',
      type: 'string',
      size: 2000,
      required: true,
    },
    {
      key: 'activity',
      type: 'string',
      size: 2000,
      required: true,
    },
    {
      key: 'status',
      type: 'string',
      size: 50,
      required: false,
      default: 'active',
    },
    {
      key: 'expiresAt',
      type: 'datetime',
      required: true,
    },
  ],
};

/**
 * Organizations Collection Schema
 * Collection ID should match ORGANIZATIONS_COLLECTION_ID environment variable
 */
export const ORGANIZATIONS_COLLECTION_SCHEMA: AppwriteCollectionSchema = {
  name: 'organizations',
  documentSecurity: true,
  permissions: [
    'create("users")',
    'read("users")',
    'update("users")',
    'delete("users")',
  ],
  attributes: [
    {
      key: 'name',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'description',
      type: 'string',
      size: 1000,
      required: false,
      default: '',
    },
    {
      key: 'ownerId',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'members',
      type: 'string',
      size: 5000,
      required: false,
      array: true,
    },
    {
      key: 'settings',
      type: 'string',
      size: 5000,
      required: false,
      default: '{}',
    },
  ],
};

/**
 * Workspaces Collection Schema
 * Collection ID should match WORKSPACES_COLLECTION_ID environment variable
 */
export const WORKSPACES_COLLECTION_SCHEMA: AppwriteCollectionSchema = {
  name: 'workspaces',
  documentSecurity: true,
  permissions: [
    'create("users")',
    'read("users")',
    'update("users")',
    'delete("users")',
  ],
  attributes: [
    {
      key: 'name',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'description',
      type: 'string',
      size: 1000,
      required: false,
      default: '',
    },
    {
      key: 'ownerId',
      type: 'string',
      size: 255,
      required: true,
    },
    {
      key: 'organizationId',
      type: 'string',
      size: 255,
      required: false,
    },
    {
      key: 'type',
      type: 'string',
      size: 100,
      required: true,
    },
    {
      key: 'status',
      type: 'string',
      size: 50,
      required: false,
      default: 'active',
    },
    {
      key: 'configuration',
      type: 'string',
      size: 10000,
      required: false,
      default: '{}',
    },
    {
      key: 'containerId',
      type: 'string',
      size: 255,
      required: false,
    },
  ],
};

/**
 * Helper function to validate if a collection has the required attributes
 */
export function validateCollectionSchema(
  collectionAttributes: any[],
  expectedSchema: AppwriteCollectionSchema
): { isValid: boolean; missingAttributes: string[]; errors: string[] } {
  const missingAttributes: string[] = [];
  const errors: string[] = [];
  
  const attributeKeys = collectionAttributes.map(attr => attr.key);
  
  for (const expectedAttr of expectedSchema.attributes) {
    if (!attributeKeys.includes(expectedAttr.key)) {
      missingAttributes.push(expectedAttr.key);
    }
  }
  
  if (missingAttributes.length > 0) {
    errors.push(`Missing required attributes: ${missingAttributes.join(', ')}`);
  }
  
  return {
    isValid: missingAttributes.length === 0,
    missingAttributes,
    errors,
  };
}

/**
 * Generate Appwrite CLI commands to create the collections
 */
export function generateAppwriteCLICommands(
  projectId: string,
  databaseId: string
): string[] {
  const commands: string[] = [];
  
  const schemas = [
    { id: 'users', schema: USERS_COLLECTION_SCHEMA },
    { id: 'vms', schema: VMS_COLLECTION_SCHEMA },
    { id: 'sessions', schema: SESSIONS_COLLECTION_SCHEMA },
  ];
  
  for (const { id, schema } of schemas) {
    // Create collection command
    commands.push(
      `appwrite databases createCollection \\
  --databaseId ${databaseId} \\
  --collectionId ${id} \\
  --name "${schema.name}" \\
  --permissions '${JSON.stringify(schema.permissions)}' \\
  --documentSecurity ${schema.documentSecurity}`
    );
    
    // Create attributes commands
    for (const attr of schema.attributes) {
      let command = `appwrite databases createStringAttribute \\
  --databaseId ${databaseId} \\
  --collectionId ${id} \\
  --key ${attr.key} \\
  --size ${attr.size || 255} \\
  --required ${attr.required}`;
      
      if (attr.default !== undefined) {
        command += ` \\
  --default "${attr.default}"`;
      }
      
      commands.push(command);
    }
  }
  
  return commands;
}

/**
 * Instructions for manual setup
 */
export const SETUP_INSTRUCTIONS = `
# Appwrite Database Setup Instructions

## Prerequisites
1. Create an Appwrite project
2. Create a database in your project
3. Note down your project ID, database ID, and API key

## Environment Variables
Add these to your .env.local file:
- NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
- NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
- NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id
- NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID=users
- NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID=vms
- NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID=sessions
- NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=avatars
- APPWRITE_API_KEY=your_api_key

## Manual Collection Setup
1. Go to your Appwrite console
2. Navigate to Databases > Your Database
3. Create collections with the schemas defined in this file
4. Set appropriate permissions for each collection

## Using Appwrite CLI
Run the generated CLI commands to automatically create the collections and attributes.

## Troubleshooting
- If you get "Unknown attribute" errors, ensure all required attributes are created
- If you get permission errors, check collection and document-level permissions
- For development, you can use more permissive settings and restrict them in production
`;
