// AI Code Editor Accessibility and Theme Configuration

export interface AccessibilityConfig {
  enableScreenReader: boolean;
  enableKeyboardNavigation: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  announceChanges: boolean;
  focusIndicators: boolean;
}

export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  accentColor: string;
  errorColor: string;
  warningColor: string;
  successColor: string;
  glassmorphism: {
    enabled: boolean;
    opacity: number;
    blur: number;
  };
}

// Default accessibility configuration
export const DEFAULT_ACCESSIBILITY_CONFIG: AccessibilityConfig = {
  enableScreenReader: true,
  enableKeyboardNavigation: true,
  enableHighContrast: false,
  enableReducedMotion: false,
  fontSize: 'medium',
  announceChanges: true,
  focusIndicators: true,
};

// Default theme configurations
export const DEFAULT_THEMES: Record<string, ThemeConfig> = {
  light: {
    mode: 'light',
    primaryColor: 'hsl(221.2 83.2% 53.3%)',
    backgroundColor: 'hsl(0 0% 100%)',
    textColor: 'hsl(222.2 84% 4.9%)',
    borderColor: 'hsl(214.3 31.8% 91.4%)',
    accentColor: 'hsl(210 40% 98%)',
    errorColor: 'hsl(0 84.2% 60.2%)',
    warningColor: 'hsl(38 92% 50%)',
    successColor: 'hsl(142.1 76.2% 36.3%)',
    glassmorphism: {
      enabled: true,
      opacity: 0.8,
      blur: 12,
    },
  },
  dark: {
    mode: 'dark',
    primaryColor: 'hsl(217.2 91.2% 59.8%)',
    backgroundColor: 'hsl(222.2 84% 4.9%)',
    textColor: 'hsl(210 40% 98%)',
    borderColor: 'hsl(217.2 32.6% 17.5%)',
    accentColor: 'hsl(222.2 84% 4.9%)',
    errorColor: 'hsl(0 62.8% 30.6%)',
    warningColor: 'hsl(38 92% 50%)',
    successColor: 'hsl(142.1 70.6% 45.3%)',
    glassmorphism: {
      enabled: true,
      opacity: 0.9,
      blur: 16,
    },
  },
  highContrast: {
    mode: 'dark',
    primaryColor: 'hsl(0 0% 100%)',
    backgroundColor: 'hsl(0 0% 0%)',
    textColor: 'hsl(0 0% 100%)',
    borderColor: 'hsl(0 0% 100%)',
    accentColor: 'hsl(0 0% 10%)',
    errorColor: 'hsl(0 100% 50%)',
    warningColor: 'hsl(60 100% 50%)',
    successColor: 'hsl(120 100% 50%)',
    glassmorphism: {
      enabled: false,
      opacity: 1,
      blur: 0,
    },
  },
};

// ARIA labels and descriptions
export const ARIA_LABELS = {
  // Main components
  aiCodeEditor: 'AI-powered code editor',
  aiAssistant: 'AI coding assistant',
  codeCompletion: 'Code completion suggestions',
  errorAnalyzer: 'Code error analysis',
  refactoringSuggestions: 'Code refactoring suggestions',

  // Actions
  applyCompletion: 'Apply code completion',
  dismissCompletion: 'Dismiss completion suggestions',
  applyFix: 'Apply error fix',
  dismissError: 'Dismiss error',
  applyRefactoring: 'Apply refactoring suggestion',
  previewRefactoring: 'Preview refactoring changes',
  sendMessage: 'Send message to AI assistant',
  clearConversation: 'Clear conversation history',

  // Status indicators
  aiProcessing: 'AI is processing your request',
  codeAnalyzing: 'Analyzing code for issues',
  completionLoading: 'Loading code completions',
  errorCount: (count: number) => `${count} code ${count === 1 ? 'issue' : 'issues'} found`,
  suggestionCount: (count: number) => `${count} refactoring ${count === 1 ? 'suggestion' : 'suggestions'} available`,

  // Navigation
  nextSuggestion: 'Next suggestion',
  previousSuggestion: 'Previous suggestion',
  selectSuggestion: 'Select suggestion',
  navigateUp: 'Navigate up',
  navigateDown: 'Navigate down',

  // File operations
  saveFile: 'Save current file',
  openFile: 'Open file',
  closeFile: 'Close file',
  newFile: 'Create new file',
};

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  // Code completion
  triggerCompletion: 'Ctrl+Space',
  acceptCompletion: 'Tab',
  dismissCompletion: 'Escape',
  nextCompletion: 'ArrowDown',
  previousCompletion: 'ArrowUp',

  // AI assistant
  toggleAssistant: 'Ctrl+Shift+A',
  sendMessage: 'Ctrl+Enter',
  clearConversation: 'Ctrl+Shift+C',

  // File operations
  saveFile: 'Ctrl+S',
  newFile: 'Ctrl+N',
  closeFile: 'Ctrl+W',

  // Navigation
  focusEditor: 'Ctrl+1',
  focusAssistant: 'Ctrl+2',
  focusTerminal: 'Ctrl+3',

  // Analysis
  analyzeCode: 'Ctrl+Shift+E',
  nextError: 'F8',
  previousError: 'Shift+F8',
};

// Screen reader announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  if (typeof window === 'undefined') return;

  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

// Focus management utilities
export class FocusManager {
  private focusStack: HTMLElement[] = [];

  pushFocus(element: HTMLElement) {
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus) {
      this.focusStack.push(currentFocus);
    }
    element.focus();
  }

  popFocus() {
    const previousFocus = this.focusStack.pop();
    if (previousFocus) {
      previousFocus.focus();
    }
  }

  trapFocus(container: HTMLElement) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
}

// Theme utilities
export function applyTheme(theme: ThemeConfig) {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;
  
  root.style.setProperty('--primary', theme.primaryColor);
  root.style.setProperty('--background', theme.backgroundColor);
  root.style.setProperty('--foreground', theme.textColor);
  root.style.setProperty('--border', theme.borderColor);
  root.style.setProperty('--accent', theme.accentColor);
  root.style.setProperty('--destructive', theme.errorColor);
  root.style.setProperty('--warning', theme.warningColor);
  root.style.setProperty('--success', theme.successColor);

  // Glassmorphism properties
  if (theme.glassmorphism.enabled) {
    root.style.setProperty('--glass-opacity', theme.glassmorphism.opacity.toString());
    root.style.setProperty('--glass-blur', `${theme.glassmorphism.blur}px`);
  }

  // Update theme mode
  root.setAttribute('data-theme', theme.mode);
}

// Accessibility utilities
export function applyAccessibilityConfig(config: AccessibilityConfig) {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;

  // Font size
  const fontSizeMap = {
    small: '14px',
    medium: '16px',
    large: '18px',
    'extra-large': '20px',
  };
  root.style.setProperty('--base-font-size', fontSizeMap[config.fontSize]);

  // Reduced motion
  if (config.enableReducedMotion) {
    root.style.setProperty('--animation-duration', '0.01ms');
    root.style.setProperty('--transition-duration', '0.01ms');
  }

  // High contrast
  if (config.enableHighContrast) {
    applyTheme(DEFAULT_THEMES.highContrast);
  }

  // Focus indicators
  if (config.focusIndicators) {
    root.classList.add('focus-visible');
  } else {
    root.classList.remove('focus-visible');
  }
}

// Color contrast utilities
export function getContrastRatio(color1: string, color2: string): number {
  // Simplified contrast ratio calculation
  // In a real implementation, you'd use a proper color library
  return 4.5; // Placeholder
}

export function ensureAccessibleContrast(foreground: string, background: string): string {
  const ratio = getContrastRatio(foreground, background);
  if (ratio < 4.5) {
    // Return a more contrasted version
    return foreground; // Placeholder
  }
  return foreground;
}

// Export focus manager instance
export const focusManager = new FocusManager();
