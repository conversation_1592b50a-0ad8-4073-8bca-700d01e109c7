import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  devIndicators: false,

  // Enable standalone output for Docker deployment
  output: 'standalone',

  // Experimental features
  experimental: {
    // Add experimental features here when needed
  },

  // Environment variables to expose to the client
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Webpack configuration to exclude server-side modules from client bundles
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Exclude server-side modules from client bundle
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
        child_process: false,
        dns: false,
        module: false,
        readline: false,
        repl: false,
        constants: false,
      };

      // Exclude specific server-side packages and services
      config.externals = config.externals || [];
      config.externals.push({
        'dockerode': 'commonjs dockerode',
        'ssh2': 'commonjs ssh2',
        'cpu-features': 'commonjs cpu-features',
        'node-pty': 'commonjs node-pty',
        'ws': 'commonjs ws',
        'bufferutil': 'commonjs bufferutil',
        'utf-8-validate': 'commonjs utf-8-validate',
      });

      // Exclude server-side service modules from client bundle
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/services/docker$': false,
        '@/services/node-react-live-preview$': false,
        '@/services/node-react-ai-assistant$': false,
        '@/services/vm/docker$': false,
        '@/services/vm/connection$': false,
        '@/services/vm/monitoring$': false,
        '@/services/vm/security$': false,
      };
    }

    return config;
  },
};

export default nextConfig;
