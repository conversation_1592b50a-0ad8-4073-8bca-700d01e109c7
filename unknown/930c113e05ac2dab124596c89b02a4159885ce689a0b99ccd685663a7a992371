import { NextRequest, NextResponse } from 'next/server';
import { fileSyncService } from '@/services/collaboration/file-sync-service';
import { workspaceAuthService } from '@/services/collaboration/auth-service';

// POST /api/collaboration/sync - Sync workspace files
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const token = request.cookies.get('workspace-token')?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const session = await workspaceAuthService.verifyToken(token);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

    // Check permissions
    if (!workspaceAuthService.hasPermission(session.user, 'files', 'write')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action = 'sync', filePath, content } = body;

    if (action === 'sync') {
      // Full workspace sync
      const result = await fileSyncService.syncWorkspace(session.workspaceId);
      
      return NextResponse.json({
        success: true,
        data: result,
        message: `Sync completed: ${result.uploaded} uploaded, ${result.downloaded} downloaded, ${result.conflicts} conflicts`,
      });

    } else if (action === 'upload') {
      // Upload specific file
      if (!filePath || content === undefined) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'filePath and content are required for upload',
          },
          { status: 400 }
        );
      }

      const file = await fileSyncService.uploadFile(session.workspaceId, {
        path: filePath,
        content,
      });

      return NextResponse.json({
        success: true,
        data: file,
        message: `File ${filePath} uploaded successfully`,
      });

    } else if (action === 'download') {
      // Download specific file
      if (!filePath) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'filePath is required for download',
          },
          { status: 400 }
        );
      }

      // Find file in cloud storage
      const files = fileSyncService.getRemoteFiles(session.workspaceId);
      const file = files.find(f => f.path === filePath);
      
      if (!file) {
        return NextResponse.json(
          {
            success: false,
            error: 'File not found',
            message: `File ${filePath} not found in cloud storage`,
          },
          { status: 404 }
        );
      }

      await fileSyncService.downloadFile(session.workspaceId, file);

      return NextResponse.json({
        success: true,
        data: file,
        message: `File ${filePath} downloaded successfully`,
      });

    } else if (action === 'delete') {
      // Delete file
      if (!filePath) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'filePath is required for delete',
          },
          { status: 400 }
        );
      }

      await fileSyncService.deleteFile(session.workspaceId, filePath);

      return NextResponse.json({
        success: true,
        message: `File ${filePath} deleted successfully`,
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be one of: sync, upload, download, delete',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Sync error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Sync failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/collaboration/sync - Get sync status and operations
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const token = request.cookies.get('workspace-token')?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const session = await workspaceAuthService.verifyToken(token);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

    // Check permissions
    if (!workspaceAuthService.hasPermission(session.user, 'files', 'read')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeContent = searchParams.get('includeContent') === 'true';

    // Get sync information
    const operations = fileSyncService.getWorkspaceOperations(session.workspaceId);
    const conflicts = fileSyncService.getWorkspaceConflicts(session.workspaceId);
    const files = fileSyncService.getRemoteFiles(session.workspaceId);

    // Filter out content if not requested
    const filesData = files.map(file => ({
      ...file,
      content: includeContent ? file.content : undefined,
    }));

    // Calculate sync statistics
    const stats = {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      lastSyncTime: files.length > 0 
        ? Math.max(...files.map(f => f.lastSyncedAt.getTime()))
        : null,
      pendingOperations: operations.filter(op => op.status === 'pending').length,
      activeConflicts: conflicts.length,
      filesByType: files.reduce((acc, file) => {
        const ext = file.path.split('.').pop()?.toLowerCase() || 'unknown';
        acc[ext] = (acc[ext] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json({
      success: true,
      data: {
        files: filesData,
        operations: operations.slice(-20), // Last 20 operations
        conflicts,
        stats,
        workspace: {
          id: session.workspaceId,
          lastActivity: new Date(),
        },
      },
    });

  } catch (error) {
    console.error('Error getting sync status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get sync status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
