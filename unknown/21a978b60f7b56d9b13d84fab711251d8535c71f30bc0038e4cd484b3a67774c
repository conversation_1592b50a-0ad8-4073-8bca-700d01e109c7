import { NextRequest, NextResponse } from 'next/server';
import { databaseIntegrationService } from '@/services/database/database-integration';

// GET /api/database/connections - List database connections
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const type = searchParams.get('type');

    if (!workspaceId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'workspaceId is required',
        },
        { status: 400 }
      );
    }

    let connections = databaseIntegrationService.getConnections();

    // Filter by database type if specified
    if (type) {
      connections = connections.filter(conn => conn.type === type);
    }

    // Group connections by type
    const connectionsByType = connections.reduce((acc, conn) => {
      if (!acc[conn.type]) {
        acc[conn.type] = [];
      }
      acc[conn.type].push({
        ...conn,
        password: '***', // Hide password in response
      });
      return acc;
    }, {} as Record<string, any[]>);

    // Get connection statistics
    const stats = {
      total: connections.length,
      active: connections.filter(c => c.isActive).length,
      byType: Object.keys(connectionsByType).reduce((acc, type) => {
        acc[type] = connectionsByType[type].length;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json({
      success: true,
      data: {
        connections: connections.map(conn => ({
          ...conn,
          password: '***', // Hide password
        })),
        connectionsByType,
        stats,
        supportedTypes: ['postgresql', 'mysql', 'sqlite', 'redis', 'mongodb'],
      },
    });

  } catch (error) {
    console.error('Error listing database connections:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to list connections',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/database/connections - Create database connection
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      workspaceId,
      name,
      type,
      host,
      port,
      database,
      username,
      password,
      ssl = false,
      options = {},
      testConnection = true,
    } = body;

    // Validate required fields
    if (!workspaceId || !name || !type || !host || !port || !database || !username) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'workspaceId, name, type, host, port, database, and username are required',
        },
        { status: 400 }
      );
    }

    // Validate database type
    const supportedTypes = ['postgresql', 'mysql', 'sqlite', 'redis', 'mongodb'];
    if (!supportedTypes.includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid database type',
          message: `Database type must be one of: ${supportedTypes.join(', ')}`,
        },
        { status: 400 }
      );
    }

    const connectionData = {
      name,
      type,
      host,
      port: parseInt(port),
      database,
      username,
      password: password || '',
      ssl,
      options,
    };

    // Test connection if requested
    if (testConnection) {
      const testResult = await databaseIntegrationService.testConnection(connectionData);
      if (!testResult.success) {
        return NextResponse.json(
          {
            success: false,
            error: 'Connection test failed',
            message: testResult.message,
            testResult,
          },
          { status: 400 }
        );
      }
    }

    // Create connection
    const connection = await databaseIntegrationService.createConnection(workspaceId, connectionData);

    return NextResponse.json({
      success: true,
      data: {
        ...connection,
        password: '***', // Hide password in response
      },
      message: `Database connection '${name}' created successfully`,
    });

  } catch (error) {
    console.error('Error creating database connection:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create connection',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT /api/database/connections - Update database connection
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { connectionId, ...updateData } = body;

    if (!connectionId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields',
          message: 'connectionId is required',
        },
        { status: 400 }
      );
    }

    const connection = databaseIntegrationService.getConnection(connectionId);
    if (!connection) {
      return NextResponse.json(
        {
          success: false,
          error: 'Connection not found',
          message: `Database connection '${connectionId}' not found`,
        },
        { status: 404 }
      );
    }

    // Update connection (simplified - in real implementation, would update in database)
    Object.assign(connection, updateData);

    return NextResponse.json({
      success: true,
      data: {
        ...connection,
        password: '***', // Hide password in response
      },
      message: `Database connection '${connection.name}' updated successfully`,
    });

  } catch (error) {
    console.error('Error updating database connection:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update connection',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/database/connections - Delete database connection
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');

    if (!connectionId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          message: 'connectionId is required',
        },
        { status: 400 }
      );
    }

    const connection = databaseIntegrationService.getConnection(connectionId);
    if (!connection) {
      return NextResponse.json(
        {
          success: false,
          error: 'Connection not found',
          message: `Database connection '${connectionId}' not found`,
        },
        { status: 404 }
      );
    }

    // Delete connection (simplified - in real implementation, would delete from database)
    // For now, just mark as inactive
    connection.isActive = false;

    return NextResponse.json({
      success: true,
      message: `Database connection '${connection.name}' deleted successfully`,
    });

  } catch (error) {
    console.error('Error deleting database connection:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete connection',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
