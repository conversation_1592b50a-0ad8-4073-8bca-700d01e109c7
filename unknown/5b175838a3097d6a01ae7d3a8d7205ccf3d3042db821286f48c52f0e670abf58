import { NextRequest, NextResponse } from 'next/server';
import { workspaceAuthService } from '@/services/collaboration/auth-service';

// POST /api/collaboration/auth - Authenticate user for workspace access
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, workspaceId, action = 'login' } = body;

    if (action === 'login') {
      // Validate required fields
      if (!email || !password || !workspaceId) {
        return NextResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            message: 'email, password, and workspaceId are required',
          },
          { status: 400 }
        );
      }

      // Authenticate user
      const result = await workspaceAuthService.authenticateUser(email, password, workspaceId);
      
      if (!result) {
        return NextResponse.json(
          {
            success: false,
            error: 'Authentication failed',
            message: 'Invalid credentials or access denied',
          },
          { status: 401 }
        );
      }

      // Set HTTP-only cookie for token
      const response = NextResponse.json({
        success: true,
        data: {
          user: {
            id: result.user.id,
            name: result.user.name,
            email: result.user.email,
            avatar: result.user.avatar,
            role: result.user.role,
            permissions: result.user.permissions,
          },
          workspaceId,
        },
        message: 'Authentication successful',
      });

      // Set secure cookie
      response.cookies.set('workspace-token', result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60, // 24 hours
        path: '/',
      });

      return response;

    } else if (action === 'verify') {
      // Verify existing token
      const token = request.cookies.get('workspace-token')?.value;
      
      if (!token) {
        return NextResponse.json(
          {
            success: false,
            error: 'No token provided',
            message: 'Authentication token not found',
          },
          { status: 401 }
        );
      }

      const session = await workspaceAuthService.verifyToken(token);
      
      if (!session) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid token',
            message: 'Authentication token is invalid or expired',
          },
          { status: 401 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          user: {
            id: session.user.id,
            name: session.user.name,
            email: session.user.email,
            avatar: session.user.avatar,
            role: session.user.role,
            permissions: session.user.permissions,
          },
          workspaceId: session.workspaceId,
          expiresAt: session.expiresAt,
        },
        message: 'Token verified successfully',
      });

    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Action must be "login" or "verify"',
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Authentication failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/collaboration/auth - Logout user
export async function DELETE(request: NextRequest) {
  try {
    const token = request.cookies.get('workspace-token')?.value;
    
    if (token) {
      // Revoke session
      workspaceAuthService.revokeSession(token);
    }

    // Clear cookie
    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully',
    });

    response.cookies.delete('workspace-token');
    
    return response;

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Logout failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET /api/collaboration/auth - Get current user info
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('workspace-token')?.value;
    
    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not authenticated',
          message: 'No authentication token found',
        },
        { status: 401 }
      );
    }

    const session = await workspaceAuthService.verifyToken(token);
    
    if (!session) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid session',
          message: 'Authentication session is invalid or expired',
        },
        { status: 401 }
      );
    }

    // Get workspace users for collaboration info
    const workspaceUsers = workspaceAuthService.getWorkspaceUsers(session.workspaceId);
    const activeSessions = workspaceAuthService.getActiveSessions(session.workspaceId);

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: session.user.id,
          name: session.user.name,
          email: session.user.email,
          avatar: session.user.avatar,
          role: session.user.role,
          permissions: session.user.permissions,
          lastLogin: session.user.lastLogin,
        },
        workspace: {
          id: session.workspaceId,
          users: workspaceUsers.map(user => ({
            id: user.id,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
            role: user.role,
            isActive: user.isActive,
            lastLogin: user.lastLogin,
          })),
          activeSessions: activeSessions.length,
        },
        session: {
          expiresAt: session.expiresAt,
        },
      },
    });

  } catch (error) {
    console.error('Error getting user info:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get user info',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
