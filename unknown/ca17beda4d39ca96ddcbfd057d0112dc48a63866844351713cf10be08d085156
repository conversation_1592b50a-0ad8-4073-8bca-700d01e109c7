'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Database, 
  Plus, 
  Settings, 
  Play,
  Table,
  Key,
  Link,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Code,
  FileText,
  Zap
} from 'lucide-react';
import { DatabaseConnection, DatabaseType } from '@/services/database/database-integration';
import { PythonFramework } from '@/types/python-workspace';

interface DatabaseManagerProps {
  workspaceId: string;
  framework?: PythonFramework;
  className?: string;
  onConnectionChange?: (connections: DatabaseConnection[]) => void;
}

const databaseIcons: Record<DatabaseType, React.ReactNode> = {
  postgresql: <Database className="h-4 w-4 text-blue-600" />,
  mysql: <Database className="h-4 w-4 text-orange-600" />,
  sqlite: <Database className="h-4 w-4 text-green-600" />,
  redis: <Database className="h-4 w-4 text-red-600" />,
  mongodb: <Database className="h-4 w-4 text-green-700" />,
};

const databaseColors: Record<DatabaseType, string> = {
  postgresql: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  mysql: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  sqlite: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  redis: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  mongodb: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300',
};

export function DatabaseManager({ 
  workspaceId, 
  framework,
  className = '',
  onConnectionChange 
}: DatabaseManagerProps) {
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<DatabaseConnection | null>(null);
  const [schema, setSchema] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showNewConnection, setShowNewConnection] = useState(false);
  const [newConnection, setNewConnection] = useState({
    name: '',
    type: 'postgresql' as DatabaseType,
    host: 'localhost',
    port: '5432',
    database: '',
    username: '',
    password: '',
  });

  // Load connections on mount
  useEffect(() => {
    loadConnections();
  }, [workspaceId]);

  const loadConnections = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/database/connections?workspaceId=${workspaceId}`);
      if (!response.ok) {
        throw new Error('Failed to load connections');
      }
      
      const data = await response.json();
      if (data.success) {
        setConnections(data.data.connections);
        onConnectionChange?.(data.data.connections);
      } else {
        throw new Error(data.message || 'Failed to load connections');
      }
    } catch (err) {
      console.error('Error loading connections:', err);
      setError(err instanceof Error ? err.message : 'Failed to load connections');
    } finally {
      setLoading(false);
    }
  };

  const createConnection = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/database/connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          ...newConnection,
          port: parseInt(newConnection.port),
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create connection');
      }
      
      const data = await response.json();
      if (data.success) {
        await loadConnections();
        setShowNewConnection(false);
        setNewConnection({
          name: '',
          type: 'postgresql',
          host: 'localhost',
          port: '5432',
          database: '',
          username: '',
          password: '',
        });
      } else {
        throw new Error(data.message || 'Failed to create connection');
      }
    } catch (err) {
      console.error('Error creating connection:', err);
      setError(err instanceof Error ? err.message : 'Failed to create connection');
    } finally {
      setLoading(false);
    }
  };

  const loadSchema = async (connection: DatabaseConnection) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/database/schema?connectionId=${connection.id}`);
      if (!response.ok) {
        throw new Error('Failed to load schema');
      }
      
      const data = await response.json();
      if (data.success) {
        setSchema(data.data);
        setSelectedConnection(connection);
      } else {
        throw new Error(data.message || 'Failed to load schema');
      }
    } catch (err) {
      console.error('Error loading schema:', err);
      setError(err instanceof Error ? err.message : 'Failed to load schema');
    } finally {
      setLoading(false);
    }
  };

  const generateORMModels = async () => {
    if (!selectedConnection || !framework) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/database/schema', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workspaceId,
          connectionId: selectedConnection.id,
          framework,
          options: {
            includeRelationships: true,
            generateMigrations: true,
          },
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate ORM models');
      }
      
      const data = await response.json();
      if (data.success) {
        // Show success message or navigate to generated files
        console.log('ORM models generated:', data.data);
      } else {
        throw new Error(data.message || 'Failed to generate ORM models');
      }
    } catch (err) {
      console.error('Error generating ORM models:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate ORM models');
    } finally {
      setLoading(false);
    }
  };

  const getDefaultPort = (type: DatabaseType): string => {
    const ports: Record<DatabaseType, string> = {
      postgresql: '5432',
      mysql: '3306',
      sqlite: '',
      redis: '6379',
      mongodb: '27017',
    };
    return ports[type];
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Database Manager</h2>
          <p className="text-muted-foreground">
            Manage database connections and generate ORM models
          </p>
        </div>
        
        <Button
          onClick={() => setShowNewConnection(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Connection
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md"
        >
          <div className="flex items-center gap-2 text-red-800 dark:text-red-300">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Error</span>
          </div>
          <p className="text-sm text-red-700 dark:text-red-400 mt-1">{error}</p>
        </motion.div>
      )}

      {/* New Connection Form */}
      <AnimatePresence>
        {showNewConnection && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">New Database Connection</CardTitle>
                <CardDescription>
                  Add a new database connection to your workspace
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Connection Name</label>
                    <Input
                      placeholder="My Database"
                      value={newConnection.name}
                      onChange={(e) => setNewConnection(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Database Type</label>
                    <Select
                      value={newConnection.type}
                      onValueChange={(value: DatabaseType) => {
                        setNewConnection(prev => ({ 
                          ...prev, 
                          type: value,
                          port: getDefaultPort(value)
                        }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="postgresql">PostgreSQL</SelectItem>
                        <SelectItem value="mysql">MySQL</SelectItem>
                        <SelectItem value="sqlite">SQLite</SelectItem>
                        <SelectItem value="redis">Redis</SelectItem>
                        <SelectItem value="mongodb">MongoDB</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Host</label>
                    <Input
                      placeholder="localhost"
                      value={newConnection.host}
                      onChange={(e) => setNewConnection(prev => ({ ...prev, host: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Port</label>
                    <Input
                      placeholder="5432"
                      value={newConnection.port}
                      onChange={(e) => setNewConnection(prev => ({ ...prev, port: e.target.value }))}
                      disabled={newConnection.type === 'sqlite'}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Database</label>
                    <Input
                      placeholder="database_name"
                      value={newConnection.database}
                      onChange={(e) => setNewConnection(prev => ({ ...prev, database: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Username</label>
                    <Input
                      placeholder="username"
                      value={newConnection.username}
                      onChange={(e) => setNewConnection(prev => ({ ...prev, username: e.target.value }))}
                      disabled={newConnection.type === 'sqlite'}
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Password</label>
                  <Input
                    type="password"
                    placeholder="password"
                    value={newConnection.password}
                    onChange={(e) => setNewConnection(prev => ({ ...prev, password: e.target.value }))}
                    disabled={newConnection.type === 'sqlite'}
                  />
                </div>

                <div className="flex items-center gap-2 pt-4">
                  <Button
                    onClick={createConnection}
                    disabled={loading || !newConnection.name || !newConnection.database}
                  >
                    {loading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4" />
                    )}
                    Create Connection
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowNewConnection(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Connections List */}
      <div className="grid gap-4">
        <AnimatePresence>
          {connections.map((connection) => (
            <motion.div
              key={connection.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <Card className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedConnection?.id === connection.id ? 'ring-2 ring-primary' : ''
              }`}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {databaseIcons[connection.type]}
                      <div>
                        <h3 className="font-medium">{connection.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={`text-xs ${databaseColors[connection.type]}`}>
                            {connection.type}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {connection.host}:{connection.port}/{connection.database}
                          </span>
                          {connection.isActive && (
                            <Badge variant="secondary" className="text-xs">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Active
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadSchema(connection)}
                        disabled={loading}
                      >
                        <Table className="h-3 w-3" />
                        Schema
                      </Button>
                      
                      {framework && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={generateORMModels}
                          disabled={loading || selectedConnection?.id !== connection.id}
                        >
                          <Code className="h-3 w-3" />
                          Generate ORM
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {connections.length === 0 && !loading && (
          <div className="text-center py-12">
            <Database className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <p className="text-lg font-medium mb-2">No database connections</p>
            <p className="text-sm text-muted-foreground">
              Add your first database connection to get started
            </p>
          </div>
        )}
      </div>

      {/* Schema Display */}
      {schema && selectedConnection && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Table className="h-5 w-5" />
              Database Schema - {selectedConnection.name}
            </CardTitle>
            <CardDescription>
              {schema.statistics.tableCount} tables, {schema.statistics.totalColumns} columns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {schema.tables.map((table: any) => (
                <div key={table.name} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <Table className="h-4 w-4" />
                      {table.name}
                    </h4>
                    <Badge variant="outline" className="text-xs">
                      {table.columns.length} columns
                    </Badge>
                  </div>
                  
                  <div className="grid gap-2">
                    {table.columns.slice(0, 5).map((column: any) => (
                      <div key={column.name} className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <span className="font-mono">{column.name}</span>
                          {column.isPrimaryKey && (
                            <Key className="h-3 w-3 text-yellow-600" />
                          )}
                          {column.isForeignKey && (
                            <Link className="h-3 w-3 text-blue-600" />
                          )}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {column.type}
                        </Badge>
                      </div>
                    ))}
                    {table.columns.length > 5 && (
                      <div className="text-xs text-muted-foreground">
                        +{table.columns.length - 5} more columns
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
