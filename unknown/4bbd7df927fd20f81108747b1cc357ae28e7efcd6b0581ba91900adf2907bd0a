'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  MessageSquare, 
  FileSync, 
  Activity,
  UserPlus,
  Settings,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  Edit,
  Terminal,
  Package
} from 'lucide-react';
import { CollaborationUser } from '@/services/collaboration/websocket-service';
import { WorkspaceUser } from '@/services/collaboration/auth-service';

interface CollaborationPanelProps {
  workspaceId: string;
  currentUser: WorkspaceUser;
  className?: string;
}

interface ActiveUser extends CollaborationUser {
  permissions: string[];
  isTyping?: boolean;
  currentFile?: string;
}

interface SyncStatus {
  isOnline: boolean;
  lastSync: Date;
  pendingChanges: number;
  conflicts: number;
}

export function CollaborationPanel({ 
  workspaceId, 
  currentUser, 
  className = '' 
}: CollaborationPanelProps) {
  const [activeUsers, setActiveUsers] = useState<ActiveUser[]>([]);
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: true,
    lastSync: new Date(),
    pendingChanges: 0,
    conflicts: 0,
  });
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);

  // Initialize WebSocket connection
  useEffect(() => {
    // In a real implementation, this would connect to the WebSocket service
    // For now, we'll simulate the connection
    setIsConnected(true);
    
    // Simulate some active users
    const mockUsers: ActiveUser[] = [
      {
        id: currentUser.id,
        name: currentUser.name,
        email: currentUser.email,
        avatar: currentUser.avatar,
        isActive: true,
        lastSeen: new Date(),
        permissions: ['read', 'write', 'execute'],
        currentFile: 'main.py',
      },
      {
        id: 'user-2',
        name: 'Alice Johnson',
        email: '<EMAIL>',
        avatar: undefined,
        isActive: true,
        lastSeen: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        permissions: ['read', 'write'],
        isTyping: true,
        currentFile: 'models.py',
      },
      {
        id: 'user-3',
        name: 'Bob Smith',
        email: '<EMAIL>',
        avatar: undefined,
        isActive: false,
        lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        permissions: ['read'],
      },
    ];
    
    setActiveUsers(mockUsers);

    return () => {
      // Cleanup WebSocket connection
      setIsConnected(false);
    };
  }, [workspaceId, currentUser]);

  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim()) return;

    const message = {
      id: Date.now().toString(),
      userId: currentUser.id,
      userName: currentUser.name,
      content: newMessage,
      timestamp: new Date(),
      type: 'text',
    };

    setChatMessages(prev => [...prev, message]);
    setNewMessage('');

    // In a real implementation, this would send via WebSocket
  }, [newMessage, currentUser]);

  const handleSyncWorkspace = useCallback(async () => {
    try {
      // Simulate sync operation
      setSyncStatus(prev => ({ ...prev, lastSync: new Date() }));
      
      // In a real implementation, this would call the sync API
      const response = await fetch('/api/collaboration/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'sync' }),
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Sync completed:', data);
      }
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }, []);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return '👑';
      case 'admin': return '⚡';
      case 'editor': return '✏️';
      case 'viewer': return '👁️';
      default: return '👤';
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'read': return <Eye className="h-3 w-3" />;
      case 'write': return <Edit className="h-3 w-3" />;
      case 'execute': return <Terminal className="h-3 w-3" />;
      case 'admin': return <Settings className="h-3 w-3" />;
      default: return null;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Connection Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Collaboration
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge 
                variant={isConnected ? "default" : "destructive"}
                className="flex items-center gap-1"
              >
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                {isConnected ? 'Connected' : 'Disconnected'}
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Collaboration Tabs */}
      <Tabs defaultValue="users" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users" className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            Users ({activeUsers.filter(u => u.isActive).length})
          </TabsTrigger>
          <TabsTrigger value="sync" className="flex items-center gap-1">
            <FileSync className="h-3 w-3" />
            Sync
            {syncStatus.conflicts > 0 && (
              <Badge variant="destructive" className="ml-1 text-xs">
                {syncStatus.conflicts}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-1">
            <MessageSquare className="h-3 w-3" />
            Chat ({chatMessages.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Active Users</CardTitle>
              <CardDescription>
                Users currently working in this workspace
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <AnimatePresence>
                {activeUsers.map((user) => (
                  <motion.div
                    key={user.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      user.isActive ? 'bg-green-50 dark:bg-green-900/20' : 'bg-gray-50 dark:bg-gray-900/20'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={user.avatar} />
                          <AvatarFallback>
                            {user.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        {user.isActive && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900" />
                        )}
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{user.name}</span>
                          <span className="text-xs">{getRoleIcon(currentUser.role)}</span>
                          {user.isTyping && (
                            <Badge variant="secondary" className="text-xs">
                              Typing...
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {user.currentFile && (
                            <span>📄 {user.currentFile}</span>
                          )}
                          <span>
                            {user.isActive ? 'Active now' : `Last seen ${user.lastSeen.toLocaleTimeString()}`}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-1">
                      {user.permissions.map((permission, index) => (
                        <div key={index} className="text-muted-foreground">
                          {getPermissionIcon(permission)}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              <Button variant="outline" className="w-full flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Invite Users
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm">File Synchronization</CardTitle>
                  <CardDescription>
                    Keep your files in sync across all devices
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSyncWorkspace}
                  className="flex items-center gap-1"
                >
                  <RefreshCw className="h-3 w-3" />
                  Sync Now
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Sync Status */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="text-sm font-medium">Last Sync</div>
                    <div className="text-xs text-muted-foreground">
                      {syncStatus.lastSync.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="text-sm font-medium">Pending</div>
                    <div className="text-xs text-muted-foreground">
                      {syncStatus.pendingChanges} changes
                    </div>
                  </div>
                </div>
              </div>

              {/* Conflicts */}
              {syncStatus.conflicts > 0 && (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                  <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-300">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {syncStatus.conflicts} file conflict{syncStatus.conflicts > 1 ? 's' : ''} detected
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700 dark:text-yellow-400 mt-1">
                    Review and resolve conflicts to continue syncing
                  </p>
                  <Button variant="outline" size="sm" className="mt-2">
                    Resolve Conflicts
                  </Button>
                </div>
              )}

              {/* Sync Options */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Sync Settings</h4>
                <div className="space-y-2 text-sm">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    Auto-sync on file changes
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    Sync Python files (.py)
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    Sync configuration files
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    Sync virtual environments
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chat" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Team Chat</CardTitle>
              <CardDescription>
                Communicate with your team members
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Chat Messages */}
              <div className="h-64 overflow-y-auto space-y-2 p-2 bg-gray-50 dark:bg-gray-900/20 rounded-md">
                <AnimatePresence>
                  {chatMessages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex gap-2 ${
                        message.userId === currentUser.id ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div className={`max-w-xs p-2 rounded-lg text-sm ${
                        message.userId === currentUser.id
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-white dark:bg-gray-800 border'
                      }`}>
                        {message.userId !== currentUser.id && (
                          <div className="font-medium text-xs mb-1">
                            {message.userName}
                          </div>
                        )}
                        <div>{message.content}</div>
                        <div className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                
                {chatMessages.length === 0 && (
                  <div className="text-center text-muted-foreground text-sm py-8">
                    No messages yet. Start a conversation!
                  </div>
                )}
              </div>

              {/* Message Input */}
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  className="flex-1 px-3 py-2 border rounded-md text-sm"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  size="sm"
                >
                  Send
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
