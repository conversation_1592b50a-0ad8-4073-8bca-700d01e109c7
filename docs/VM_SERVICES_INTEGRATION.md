# VM Services Integration Documentation

## Overview

This document provides comprehensive documentation for the VM (Virtual Machine) services integration implemented in the Omnispace platform. The integration includes complete services for VM connection management, Docker operations, infrastructure monitoring, and security controls.

## Architecture

### Service Layer Structure

```
src/services/vm/
├── base.ts              # Base VM service class with common functionality
├── connection.ts        # VM connection and SSH management service
├── docker.ts           # Docker operations through VM connections
├── monitoring.ts       # Infrastructure monitoring and metrics collection
├── security.ts         # Authentication, authorization, and access control
├── index.ts            # Service factory and exports
└── __tests__/          # Comprehensive unit tests
    ├── base.test.ts
    ├── connection.test.ts
    ├── security.test.ts
    └── route.test.ts
```

### API Routes Structure

```
src/app/api/vm/
├── route.ts            # Main VM management endpoints
├── docker/route.ts     # Docker operations endpoints
├── monitoring/route.ts # Infrastructure monitoring endpoints
├── health/route.ts     # Health check endpoints
└── __tests__/          # API route tests
    └── route.test.ts
```

### Configuration and Utilities

```
src/lib/vm-server.ts    # VM-specific configuration, error handling, and logging
src/types/vm.ts         # Comprehensive TypeScript type definitions
```

## Services

### 1. Base VM Service (`BaseVMService`)

**Features:**
- Common functionality for all VM services
- Error handling and logging
- Retry mechanisms with exponential backoff
- Parameter validation and security checks
- Connection pool management
- Caching and rate limiting support

**Key Methods:**
```typescript
// Execute operations with error handling and logging
protected async executeVMOperation<T>(
  operationName: string,
  operation: () => Promise<T>,
  useRetry?: boolean,
  connectionId?: string
): Promise<VMServiceResult<T>>

// Validate required parameters
protected validateRequired(params: Record<string, any>, requiredFields: string[]): void

// Validate VM connection configuration
protected validateConnectionConfig(config: VMConnectionConfig): void

// Validate commands for security
protected validateCommand(command: string): void
```

### 2. VM Connection Service (`VMConnectionService`)

**Features:**
- Secure SSH connection establishment and management
- Connection pooling and lifecycle management
- Command execution with security validation
- Session management and tracking
- Authentication with multiple credential types
- Connection health monitoring

**Key Methods:**
```typescript
// Establish SSH connection
async connect(credentials?: VMAuthCredentials): Promise<VMServiceResult<VMConnectionStatus>>

// Execute command on VM
async executeCommand(
  connectionId: string,
  command: string,
  options?: {
    timeout?: number;
    cwd?: string;
    env?: Record<string, string>;
  }
): Promise<VMServiceResult<{ stdout: string; stderr: string; exitCode: number }>>

// Create user session
async createSession(
  connectionId: string,
  userId: string,
  metadata?: Record<string, any>
): Promise<VMServiceResult<VMSession>>

// Disconnect from VM
async disconnect(connectionId: string): Promise<VMServiceResult<boolean>>
```

**Usage Example:**
```typescript
import { VMConnectionService } from '@/services/vm';

const config: VMConnectionConfig = {
  host: 'vm.example.com',
  port: 22,
  username: 'admin',
  privateKey: process.env.VM_PRIVATE_KEY,
  timeout: 30000,
  keepAlive: true,
  maxRetries: 3
};

const service = new VMConnectionService('vm-001', config);

// Connect to VM
const connectResult = await service.connect();
if (connectResult.success) {
  const connectionId = connectResult.data.connectionId;
  
  // Execute command
  const cmdResult = await service.executeCommand(connectionId, 'docker ps');
  console.log('Command output:', cmdResult.data?.stdout);
  
  // Disconnect
  await service.disconnect(connectionId);
}
```

### 3. VM Docker Service (`VMDockerService`)

**Features:**
- Complete Docker operations through VM connections
- Container lifecycle management (create, start, stop, remove)
- Container execution and log retrieval
- Docker system information and health checks
- Image and network management
- Comprehensive error handling

**Key Methods:**
```typescript
// Get Docker system information
async getDockerInfo(connectionId: string): Promise<VMServiceResult<VMDockerInfo>>

// List containers
async listContainers(
  connectionId: string,
  options?: { all?: boolean; filters?: Record<string, string>; limit?: number }
): Promise<VMServiceResult<DockerContainer[]>>

// Create container
async createContainer(
  connectionId: string,
  options: ContainerCreateOptions
): Promise<VMServiceResult<{ containerId: string; warnings: string[] }>>

// Execute command in container
async execInContainer(
  connectionId: string,
  containerId: string,
  command: string[],
  options?: ExecuteOptions
): Promise<VMServiceResult<{ stdout: string; stderr: string; exitCode: number }>>
```

**Usage Example:**
```typescript
import { VMDockerService } from '@/services/vm';

const dockerService = new VMDockerService('vm-001', config);

// Get Docker info
const infoResult = await dockerService.getDockerInfo(connectionId);

// Create and start container
const createResult = await dockerService.createContainer(connectionId, {
  name: 'test-container',
  image: 'nginx:latest',
  ports: { '80': '8080' },
  environment: { NODE_ENV: 'production' }
});

if (createResult.success) {
  await dockerService.startContainer(connectionId, createResult.data.containerId);
}
```

### 4. VM Monitoring Service (`VMMonitoringService`)

**Features:**
- Real-time system metrics collection
- Resource usage monitoring (CPU, memory, disk, network)
- Process and service monitoring
- Health checks and alerting
- Metrics history and trend analysis
- Configurable alert thresholds

**Key Methods:**
```typescript
// Get system information
async getSystemInfo(connectionId: string): Promise<VMServiceResult<VMSystemInfo>>

// Get current resource metrics
async getResourceMetrics(connectionId: string): Promise<VMServiceResult<VMResourceMetrics>>

// Get running processes
async getProcesses(
  connectionId: string,
  options?: { sortBy?: 'cpu' | 'memory' | 'name'; limit?: number; filter?: string }
): Promise<VMServiceResult<SystemProcess[]>>

// Perform comprehensive health check
async performHealthCheck(connectionId: string): Promise<VMServiceResult<VMHealthCheck>>

// Start continuous monitoring
async startMonitoring(connectionId: string): Promise<VMServiceResult<boolean>>
```

**Usage Example:**
```typescript
import { VMMonitoringService } from '@/services/vm';

const monitoringService = new VMMonitoringService('vm-001', config);

// Get system metrics
const metricsResult = await monitoringService.getResourceMetrics(connectionId);
if (metricsResult.success) {
  const metrics = metricsResult.data;
  console.log(`CPU Usage: ${metrics.cpu.usage}%`);
  console.log(`Memory Usage: ${metrics.memory.usagePercent}%`);
}

// Start continuous monitoring
await monitoringService.startMonitoring(connectionId);

// Get active alerts
const alertsResult = await monitoringService.getActiveAlerts();
```

### 5. VM Security Service (`VMSecurityService`)

**Features:**
- User authentication and authorization
- Role-based access control (RBAC)
- Session management and token validation
- Audit logging and security event tracking
- IP whitelisting and access restrictions
- Failed attempt tracking and lockout protection

**Key Methods:**
```typescript
// Create access control entry
async createAccessControl(
  userId: string,
  username: string,
  permissions: VMPermissionLevel[],
  options?: AccessControlOptions
): Promise<VMServiceResult<VMAccessControlEntry>>

// Authenticate user
async authenticateUser(
  userId: string,
  credentials: VMAuthCredentials,
  clientInfo?: { ipAddress?: string; userAgent?: string }
): Promise<VMServiceResult<VMAuthToken>>

// Check permissions
async checkPermission(
  token: string,
  action: string,
  resource?: string
): Promise<VMServiceResult<boolean>>

// Get audit log
async getAuditLog(
  options?: AuditLogOptions
): Promise<VMServiceResult<VMAuditLogEntry[]>>
```

**Usage Example:**
```typescript
import { VMSecurityService } from '@/services/vm';

const securityService = new VMSecurityService('vm-001', config);

// Create access control
await securityService.createAccessControl('user-123', 'john.doe', ['read', 'write'], {
  allowedCommands: ['docker', 'ls', 'cat'],
  ipWhitelist: ['***********/24'],
  sessionTimeout: 3600000 // 1 hour
});

// Authenticate user
const authResult = await securityService.authenticateUser('user-123', {
  type: 'password',
  username: 'john.doe',
  password: 'secure-password'
}, { ipAddress: '*************' });

if (authResult.success) {
  const token = authResult.data.token;
  
  // Check permission
  const hasPermission = await securityService.checkPermission(token, 'write');
}
```

## API Routes

### Main VM API (`/api/vm`)

**Endpoints:**

- `POST /api/vm?action=connect&vmId={vmId}` - Establish VM connection
- `POST /api/vm?action=execute&vmId={vmId}&connectionId={connectionId}` - Execute command
- `POST /api/vm?action=create-session&vmId={vmId}&connectionId={connectionId}` - Create session
- `GET /api/vm?action=health&vmId={vmId}` - Health check
- `GET /api/vm?action=connections&vmId={vmId}` - List connections
- `DELETE /api/vm?action=disconnect&vmId={vmId}&connectionId={connectionId}` - Disconnect
- `PUT /api/vm?action=update-config&vmId={vmId}` - Update configuration

**Example Usage:**
```bash
# Connect to VM
curl -X POST "http://localhost:3000/api/vm?action=connect&vmId=vm-001" \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "host": "vm.example.com",
      "port": 22,
      "username": "admin",
      "password": "secure-password"
    }
  }'

# Execute command
curl -X POST "http://localhost:3000/api/vm?action=execute&vmId=vm-001&connectionId=conn-123" \
  -H "Content-Type: application/json" \
  -d '{
    "config": {...},
    "command": "docker ps",
    "options": { "timeout": 5000 }
  }'
```

### Docker API (`/api/vm/docker`)

**Endpoints:**

- `POST /api/vm/docker?action=create-container&vmId={vmId}&connectionId={connectionId}` - Create container
- `POST /api/vm/docker?action=start-container&vmId={vmId}&connectionId={connectionId}&containerId={containerId}` - Start container
- `POST /api/vm/docker?action=exec-container&vmId={vmId}&connectionId={connectionId}&containerId={containerId}` - Execute in container
- `GET /api/vm/docker?action=containers&vmId={vmId}&connectionId={connectionId}` - List containers
- `GET /api/vm/docker?action=info&vmId={vmId}&connectionId={connectionId}` - Docker info
- `DELETE /api/vm/docker?action=remove-container&vmId={vmId}&connectionId={connectionId}&containerId={containerId}` - Remove container

### Monitoring API (`/api/vm/monitoring`)

**Endpoints:**

- `GET /api/vm/monitoring?action=system-info&vmId={vmId}&connectionId={connectionId}` - System information
- `GET /api/vm/monitoring?action=metrics&vmId={vmId}&connectionId={connectionId}` - Resource metrics
- `GET /api/vm/monitoring?action=processes&vmId={vmId}&connectionId={connectionId}` - Process list
- `GET /api/vm/monitoring?action=health&vmId={vmId}&connectionId={connectionId}` - Health check
- `POST /api/vm/monitoring?action=start-monitoring&vmId={vmId}&connectionId={connectionId}` - Start monitoring
- `GET /api/vm/monitoring?action=alerts&vmId={vmId}&connectionId={connectionId}` - Active alerts

### Health Check API (`/api/vm/health`)

**Endpoints:**

- `GET /api/vm/health` - API health status
- `GET /api/vm/health?vmId={vmId}` - Basic VM health
- `GET /api/vm/health?vmId={vmId}&detailed=true&config={config}` - Detailed health check
- `POST /api/vm/health` - Health check with configuration

## Service Factory

The VM Service Factory provides a centralized way to create and manage VM service instances:

```typescript
import { vmServiceFactory, VMServiceManager } from '@/services/vm';

// Create services
const connectionService = vmServiceFactory.createConnectionService(vmId, config);
const dockerService = vmServiceFactory.createDockerService(vmId, config);
const monitoringService = vmServiceFactory.createMonitoringService(vmId, config);
const securityService = vmServiceFactory.createSecurityService(vmId, config);

// Get all services
const allServices = vmServiceFactory.getAllServices(vmId, config);

// Health check all services
const healthStatus = await vmServiceFactory.healthCheckAll(vmId, config);

// Use service manager for high-level operations
const serviceManager = new VMServiceManager();
const setupResult = await serviceManager.setupVM(vmId, config);
```

## Configuration

### VM Connection Configuration

```typescript
interface VMConnectionConfig {
  host: string;                    // VM hostname or IP address
  port: number;                    // SSH port (default: 22)
  username: string;                // SSH username
  password?: string;               // SSH password (if not using key)
  privateKey?: string;             // SSH private key
  passphrase?: string;             // Private key passphrase
  timeout?: number;                // Connection timeout (default: 30000ms)
  keepAlive?: boolean;             // Enable keep-alive (default: true)
  maxRetries?: number;             // Max retry attempts (default: 3)
  retryDelay?: number;             // Retry delay (default: 1000ms)
}
```

### Environment Variables

```bash
# VM Service Configuration
VM_LOG_LEVEL=info                # Logging level (debug, info, warn, error)
VM_CONNECTION_POOL_SIZE=5        # Max connections per VM
VM_SESSION_TIMEOUT=3600000       # Session timeout in milliseconds
VM_MAX_CONCURRENT_CONNECTIONS=10 # Max concurrent connections

# Security Configuration
VM_ALLOWED_COMMANDS=docker,ls,cat,echo,pwd,whoami,ps,top,df,free,uname
VM_RESTRICTED_PATHS=/etc/passwd,/etc/shadow,/root/.ssh,/home/<USER>/.ssh

# Monitoring Configuration
VM_METRICS_INTERVAL=60000        # Metrics collection interval
VM_HEALTH_CHECK_INTERVAL=30000   # Health check interval
VM_CPU_THRESHOLD=80              # CPU usage alert threshold
VM_MEMORY_THRESHOLD=85           # Memory usage alert threshold
VM_DISK_THRESHOLD=90             # Disk usage alert threshold
```

## Security Considerations

1. **SSH Key Management**: Use SSH keys instead of passwords for authentication
2. **Command Validation**: All commands are validated against allowed lists and security patterns
3. **Access Control**: Implement role-based access control with proper permissions
4. **Audit Logging**: All operations are logged for security auditing
5. **Rate Limiting**: Protection against abuse with configurable limits
6. **IP Whitelisting**: Restrict access to specific IP ranges
7. **Session Management**: Secure session handling with timeouts and cleanup

## Error Handling

The VM services use comprehensive error handling with specific error codes:

```typescript
// VM Error Codes
VM_CONNECTION_FAILED     // Connection to VM failed
VM_AUTH_FAILED          // Authentication failed
VM_COMMAND_FAILED       // Command execution failed
VM_DOCKER_FAILED        // Docker operation failed
VM_RESOURCE_EXHAUSTED   // VM resources exhausted
VM_TIMEOUT              // Operation timeout
VM_PERMISSION_DENIED    // Permission denied
VM_INVALID_CONFIG       // Invalid configuration
VM_SERVICE_UNAVAILABLE  // Service unavailable
VM_HEALTH_CHECK_FAILED  // Health check failed
```

## Testing

Unit tests are provided for all services in `src/services/vm/__tests__/`. Run tests with:

```bash
pnpm test src/services/vm/__tests__/
```

## Performance Considerations

1. **Connection Pooling**: Reuse SSH connections to reduce overhead
2. **Caching**: Cache frequently accessed data with appropriate TTL
3. **Batch Operations**: Group multiple operations when possible
4. **Resource Monitoring**: Monitor VM resource usage to prevent overload
5. **Cleanup**: Proper cleanup of connections and resources

## Deployment

### Prerequisites

1. SSH access to target VMs
2. Docker installed on VMs (for Docker operations)
3. Proper network connectivity
4. SSH keys or credentials configured

### Installation

1. Install dependencies:
```bash
pnpm install ssh2
```

2. Configure environment variables
3. Set up SSH keys or credentials
4. Test connectivity to target VMs

### Production Deployment

1. Use SSH keys instead of passwords
2. Configure proper firewall rules
3. Set up monitoring and alerting
4. Implement log rotation and cleanup
5. Configure backup and disaster recovery

## Troubleshooting

### Common Issues

1. **Connection Timeout**: Check network connectivity and firewall rules
2. **Authentication Failed**: Verify SSH credentials and key permissions
3. **Command Not Allowed**: Check security configuration and allowed commands
4. **Docker Not Available**: Ensure Docker is installed and running on VM
5. **Resource Exhausted**: Monitor VM resources and scale as needed

### Debug Mode

Enable debug logging:
```bash
VM_LOG_LEVEL=debug
```

### Health Checks

Use the health check endpoints to diagnose issues:
```bash
curl "http://localhost:3000/api/vm/health?vmId=vm-001&detailed=true"
```

## Migration Guide

If migrating from existing VM management solutions:

1. **Assess Current Setup**: Document existing VM configurations and access patterns
2. **Plan Migration**: Create migration plan with rollback procedures
3. **Test Integration**: Test VM services with non-production VMs first
4. **Gradual Rollout**: Migrate VMs in phases to minimize risk
5. **Monitor Performance**: Monitor system performance during and after migration
6. **Update Documentation**: Update operational procedures and documentation

## Quick Start Guide

### 1. Basic VM Connection

```typescript
import { createVMConnectionService } from '@/services/vm';

// Configure VM connection
const config = {
  host: 'your-vm.example.com',
  port: 22,
  username: 'admin',
  privateKey: process.env.VM_PRIVATE_KEY
};

// Create service and connect
const service = createVMConnectionService('vm-001', config);
const result = await service.connect();

if (result.success) {
  console.log('Connected to VM:', result.data.connectionId);
}
```

### 2. Docker Container Management

```typescript
import { createVMDockerService } from '@/services/vm';

const dockerService = createVMDockerService('vm-001', config);

// Create and start a container
const createResult = await dockerService.createContainer(connectionId, {
  name: 'my-app',
  image: 'node:18-alpine',
  ports: { '3000': '3000' },
  environment: { NODE_ENV: 'production' }
});

if (createResult.success) {
  await dockerService.startContainer(connectionId, createResult.data.containerId);
}
```

### 3. System Monitoring

```typescript
import { createVMMonitoringService } from '@/services/vm';

const monitoring = createVMMonitoringService('vm-001', config);

// Get system metrics
const metrics = await monitoring.getResourceMetrics(connectionId);
console.log(`CPU: ${metrics.data.cpu.usage}%, Memory: ${metrics.data.memory.usagePercent}%`);

// Start continuous monitoring
await monitoring.startMonitoring(connectionId);
```

### 4. Security and Access Control

```typescript
import { createVMSecurityService } from '@/services/vm';

const security = createVMSecurityService('vm-001', config);

// Create user access
await security.createAccessControl('user-123', 'john.doe', ['read', 'write']);

// Authenticate user
const auth = await security.authenticateUser('user-123', {
  type: 'password',
  username: 'john.doe',
  password: 'secure-password'
});

// Use token for operations
const hasPermission = await security.checkPermission(auth.data.token, 'write');
```

## Best Practices

### 1. Connection Management
- Always disconnect when operations are complete
- Use connection pooling for multiple operations
- Implement proper error handling and cleanup
- Monitor connection health regularly

### 2. Security
- Use SSH keys instead of passwords
- Implement proper access control
- Validate all user inputs
- Log security events for auditing
- Regularly rotate credentials

### 3. Monitoring
- Set up continuous monitoring for critical VMs
- Configure appropriate alert thresholds
- Monitor resource usage trends
- Implement automated responses to alerts

### 4. Error Handling
- Always check operation results
- Implement retry logic for transient failures
- Log errors with sufficient context
- Provide meaningful error messages to users

### 5. Performance
- Use connection pooling to reduce overhead
- Cache frequently accessed data
- Batch operations when possible
- Monitor and optimize resource usage

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review error logs with debug mode enabled
3. Test with health check endpoints
4. Consult the API documentation
5. Review unit tests for usage examples
