# Appwrite Setup Guide

This guide will help you set up Appwrite for the Omnispace platform and resolve common authentication issues.

## Quick Fix for "Unknown attribute" Error

If you're seeing an error like "Invalid document structure: Unknown attribute: 'userId'", it means your Appwrite database collections are not properly configured.

### Option 1: Use Account-Based Authentication (Recommended for Quick Start)

The application has been updated to work without a custom user profile collection. It will automatically fall back to using Appwrite's built-in user account data when the database collection is not properly configured.

**No additional setup required** - the authentication will work with basic functionality.

### Option 2: Full Database Setup (Recommended for Production)

For full functionality including user profiles, preferences, and usage tracking, set up the database collections:

#### Step 1: Environment Variables

Add these to your `.env.local` file:

```env
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id
NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID=users
NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID=vms
NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID=sessions
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=avatars
APPWRITE_API_KEY=your_api_key
```

#### Step 2: Create Collections

##### Users Collection
1. Go to your Appwrite Console
2. Navigate to Databases → Your Database
3. Create a new collection with ID: `users`
4. Add these attributes:
   - `name` (String, 255 chars, required)
   - `email` (Email, 255 chars, required)
   - `bio` (String, 1000 chars, optional)
   - `company` (String, 255 chars, optional)
   - `location` (String, 255 chars, optional)
   - `website` (URL, 255 chars, optional)
   - `avatar` (String, 255 chars, optional)

5. Set permissions:
   - Read: `any` (or restrict as needed)
   - Create: `users`
   - Update: `users`
   - Delete: `users`

6. Enable document security

##### VMs Collection
Create a collection with ID: `vms` and these attributes:
- `name` (String, 255 chars, required)
- `description` (String, 1000 chars, optional)
- `status` (String, 50 chars, required, default: "stopped")
- `ownerId` (String, 255 chars, required)
- `template` (String, 100 chars, required)
- `resources` (String, 2000 chars, required)
- `network` (String, 1000 chars, optional)
- `createdAt` (DateTime, required)
- `lastActivity` (DateTime, optional)

##### Sessions Collection
Create a collection with ID: `sessions` and these attributes:
- `userId` (String, 255 chars, required)
- `sessionId` (String, 255 chars, required)
- `deviceInfo` (String, 2000 chars, required)
- `activity` (String, 2000 chars, required)
- `status` (String, 50 chars, required, default: "active")
- `expiresAt` (DateTime, required)

#### Step 3: Create Storage Bucket

1. Go to Storage in your Appwrite Console
2. Create a bucket with ID: `avatars`
3. Set appropriate permissions for file uploads

## Diagnostic Tool

Run the diagnostic script to check your configuration:

```bash
npx tsx src/scripts/diagnose-appwrite.ts
```

This will:
- Check all environment variables
- Validate database schema
- Provide setup recommendations
- Generate CLI commands for collection creation

## Using Appwrite CLI (Advanced)

If you have the Appwrite CLI installed, you can use the generated commands from the diagnostic tool to automatically create collections.

## Troubleshooting

### Common Issues

1. **"Unknown attribute" errors**
   - Your collection is missing required attributes
   - Add the missing attributes to your collection schema

2. **"Collection not found" errors**
   - The collection doesn't exist in your database
   - Create the collection with the correct ID

3. **Permission errors**
   - Check collection-level permissions
   - Ensure document security is properly configured
   - Verify user authentication status

4. **Environment variable issues**
   - Double-check all environment variables are set
   - Ensure no typos in variable names
   - Restart your development server after changes

### Fallback Behavior

The application is designed to gracefully handle missing database collections:

- **Missing users collection**: Uses Appwrite account data only
- **Missing VMs collection**: VM functionality will be limited
- **Missing sessions collection**: Session tracking will be disabled

This allows you to get started quickly and add full database functionality later.

## Production Considerations

For production deployments:

1. **Security**: Review and restrict collection permissions
2. **Backup**: Set up regular database backups
3. **Monitoring**: Enable Appwrite logging and monitoring
4. **Performance**: Consider indexing frequently queried attributes
5. **Scaling**: Plan for database scaling as your user base grows

## Support

If you continue to have issues:

1. Check the browser console for detailed error messages
2. Run the diagnostic tool for configuration validation
3. Review the Appwrite documentation
4. Check the application logs for additional context

The authentication system is now more resilient and will work even with incomplete database setup, allowing you to focus on development while gradually implementing full database functionality.
