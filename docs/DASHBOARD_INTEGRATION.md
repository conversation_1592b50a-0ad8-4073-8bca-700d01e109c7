# 🎛️ Omnispace Dashboard Integration Guide

This guide shows how to use the new dashboard pages and workspace components in your Omnispace application.

## 📁 Dashboard Structure

```
src/app/dashboard/
├── layout.tsx              # Main dashboard layout with sidebar
├── page.tsx                # Dashboard overview (home page)
├── workspaces/
│   └── page.tsx            # Workspace management page
├── monitoring/
│   └── page.tsx            # System monitoring dashboard
├── settings/
│   └── page.tsx            # Settings and configuration
├── containers/
│   └── page.tsx            # Container management (legacy)
└── images/
    └── page.tsx            # Image management (legacy)
```

## 🎨 Components Overview

### Dashboard Components
- **`DashboardOverview`** - Main dashboard with stats and quick actions
- **`StatsCard`** - Reusable statistics card widget
- **`QuickActions`** - Quick action buttons widget

### Workspace Components
- **`WorkspaceManager`** - Complete workspace CRUD interface
- **`WorkspaceCreationWizard`** - Step-by-step workspace creation
- **`GuacamoleClient`** - Embedded remote desktop client
- **`WorkspaceMonitor`** - Real-time resource monitoring
- **`WorkspaceDashboard`** - All-in-one workspace interface

## 🚀 Usage Examples

### 1. Basic Dashboard Page

```tsx
"use client";

import { DashboardOverview } from "@/components/dashboard/DashboardOverview";
import { ProtectedRoute } from "@/components/auth/protected-route";

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardOverview />
    </ProtectedRoute>
  );
}
```

### 2. Workspace Management Page

```tsx
"use client";

import React, { useState } from 'react';
import { WorkspaceManager, GuacamoleClient } from '@/components/workspace';
import { WorkspaceInfo } from '@/services/docker';

export default function WorkspacesPage() {
  const [selectedWorkspace, setSelectedWorkspace] = useState<WorkspaceInfo | null>(null);
  const userId = 'demo-user'; // Get from auth context

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      {selectedWorkspace ? (
        <GuacamoleClient
          workspace={selectedWorkspace}
          onDisconnect={() => setSelectedWorkspace(null)}
        />
      ) : (
        <WorkspaceManager
          onSelectWorkspace={setSelectedWorkspace}
          userId={userId}
        />
      )}
    </div>
  );
}
```

### 3. Custom Stats Cards

```tsx
import { StatsCard } from '@/components/dashboard/widgets';
import { Monitor, Activity, Cpu } from 'lucide-react';

export function CustomDashboard() {
  return (
    <div className="grid gap-4 md:grid-cols-3">
      <StatsCard
        title="Active Workspaces"
        value={5}
        description="Currently running"
        icon={Monitor}
        badge={{ text: 'LIVE', variant: 'default' }}
        trend={{
          value: 25,
          label: 'vs yesterday',
          direction: 'up'
        }}
      />
      
      <StatsCard
        title="CPU Usage"
        value="67%"
        description="8 of 12 cores"
        icon={Cpu}
        trend={{
          value: 12,
          label: 'vs last hour',
          direction: 'down'
        }}
      />
      
      <StatsCard
        title="System Health"
        value="Healthy"
        description="All systems operational"
        icon={Activity}
        badge={{ text: 'OK', variant: 'default' }}
      />
    </div>
  );
}
```

### 4. Quick Actions Widget

```tsx
import { QuickActions } from '@/components/dashboard/widgets';
import { Plus, Monitor, Settings, BarChart3 } from 'lucide-react';

export function DashboardActions() {
  return (
    <QuickActions
      title="Quick Actions"
      actions={[
        {
          title: 'Create Workspace',
          description: 'Launch a new desktop environment',
          href: '/dashboard/workspaces',
          icon: Plus,
          variant: 'default'
        },
        {
          title: 'View Monitoring',
          description: 'Check system metrics',
          href: '/dashboard/monitoring',
          icon: BarChart3
        },
        {
          title: 'Open Settings',
          description: 'Configure preferences',
          href: '/dashboard/settings',
          icon: Settings
        }
      ]}
    />
  );
}
```

## 🔧 API Integration

### Workspace API Endpoints

```typescript
// List workspaces
GET /api/workspaces?userId=demo-user

// Create workspace
POST /api/workspaces
{
  "workspaceType": "development-env",
  "userId": "demo-user",
  "displayWidth": 1920,
  "displayHeight": 1080,
  "resources": { "cpu": 4, "memory": 4096 }
}

// Get workspace details
GET /api/workspaces/[id]

// Delete workspace
DELETE /api/workspaces/[id]

// Workspace actions
POST /api/workspaces/[id]?action=start
POST /api/workspaces/[id]?action=stop
POST /api/workspaces/[id]?action=restart
```

### Template API Endpoints

```typescript
// List templates
GET /api/workspace-templates

// Get template details
GET /api/workspace-templates/[id]

// Get recommendations
POST /api/workspace-templates
{
  "useCase": "development",
  "resources": { "maxCpu": 8, "maxMemory": 8192 }
}
```

## 🎯 Navigation Integration

The dashboard uses the existing sidebar navigation. Update `src/components/app-sidebar.tsx`:

```typescript
const data = {
  navMain: [
    {
      title: "Overview",
      url: "/dashboard",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Workspaces",
      url: "/dashboard/workspaces",
      icon: Monitor,
      isActive: false,
    },
    {
      title: "Monitoring",
      url: "/dashboard/monitoring",
      icon: BarChart3,
      isActive: false,
    },
    {
      title: "Settings",
      url: "/dashboard/settings",
      icon: Settings,
      isActive: false,
    },
  ],
};
```

## 🔒 Authentication Integration

All dashboard pages use the `ProtectedRoute` component:

```tsx
import { ProtectedRoute } from '@/components/auth/protected-route';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      {/* Your dashboard content */}
    </ProtectedRoute>
  );
}
```

## 📊 Real-time Updates

Components automatically refresh data:

```typescript
// WorkspaceManager refreshes every 30 seconds
useEffect(() => {
  fetchWorkspaces();
  const interval = setInterval(fetchWorkspaces, 30000);
  return () => clearInterval(interval);
}, [userId]);

// WorkspaceMonitor refreshes every 10 seconds
useEffect(() => {
  fetchData();
  const interval = setInterval(fetchData, 10000);
  return () => clearInterval(interval);
}, [userId]);
```

## 🎨 Styling and Theming

All components use Tailwind CSS and shadcn/ui components:

- **Cards**: `Card`, `CardHeader`, `CardTitle`, `CardContent`
- **Buttons**: `Button` with variants (`default`, `outline`, `secondary`)
- **Badges**: `Badge` with variants (`default`, `secondary`, `destructive`)
- **Icons**: Lucide React icons
- **Layout**: Flexbox and CSS Grid

## 🔧 Customization

### Custom Workspace Types

Add new workspace types in the template API:

```typescript
// src/app/api/workspace-templates/route.ts
const WORKSPACE_TEMPLATES: WorkspaceTemplate[] = [
  // ... existing templates
  {
    id: 'custom-workspace',
    name: 'Custom Workspace',
    description: 'Your custom workspace description',
    image: 'your-registry/custom-workspace:latest',
    category: 'custom',
    // ... other properties
  }
];
```

### Custom Dashboard Widgets

Create new widgets following the existing pattern:

```tsx
// src/components/dashboard/widgets/CustomWidget.tsx
export const CustomWidget: React.FC<CustomWidgetProps> = ({ ... }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Custom Widget</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Your custom content */}
      </CardContent>
    </Card>
  );
};
```

## 🚀 Deployment

The dashboard pages are ready for production deployment:

1. **Build the application**: `npm run build`
2. **Start the server**: `npm start`
3. **Access the dashboard**: `http://localhost:3000/dashboard`

## 📱 Mobile Responsiveness

All dashboard components are responsive:

- **Grid layouts**: Automatically adjust columns on smaller screens
- **Sidebar**: Collapsible on mobile devices
- **Cards**: Stack vertically on mobile
- **Tables**: Horizontal scroll on overflow

This comprehensive dashboard integration provides a complete workspace management solution with modern UI/UX and real-time monitoring capabilities! 🎯✨
