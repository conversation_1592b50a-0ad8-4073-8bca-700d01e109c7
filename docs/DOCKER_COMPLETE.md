# 🐳 Omnispace Complete Docker Setup

This is the complete Docker setup for Omnispace, including the main application and workspace containers with Apache Guacamole integration.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Omnispace Platform                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Omnispace     │    │  Apache          │                   │
│  │   Frontend      │───▶│  Guacamole       │                   │
│  │   (Next.js)     │    │  (Web Gateway)   │                   │
│  └─────────────────┘    └──────────────────┘                   │
│           │                       │                            │
│           │              ┌──────────────────┐                  │
│           └─────────────▶│  PostgreSQL      │                  │
│                          │  Database        │                  │
│                          └──────────────────┘                  │
├─────────────────────────────────────────────────────────────────┤
│                    Workspace Containers                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │ Ubuntu Desktop  │ │ Development Env │ │ Minimal Desktop │   │
│  │ (GNOME + VNC)   │ │ (VS Code + VNC) │ │ (XFCE + VNC)    │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### 1. Initialize Omnispace
```bash
# First-time setup
./scripts/deploy.sh init

# This will:
# - Create necessary directories and networks
# - Generate Guacamole database initialization
# - Create secure passwords
# - Set up environment configuration
```

### 2. Build and Deploy
```bash
# Build all images and start platform
./scripts/deploy.sh start --build

# Or just start with existing images
./scripts/deploy.sh start
```

### 3. Access the Platform
- **Omnispace Web UI**: http://localhost:3000
- **Guacamole Interface**: http://localhost:8080 (admin: guacadmin/guacadmin)
- **Health Check**: http://localhost:3000/api/health

## 📦 What's Included

### Main Application
- **Omnispace Frontend** - Next.js application with AI integration
- **Health Check API** - `/api/health` endpoint for monitoring
- **Docker Integration** - Container management through Docker API
- **Guacamole Integration** - Automatic workspace registration

### Apache Guacamole Stack
- **Guacamole Web App** - Clientless remote desktop gateway
- **Guacd Daemon** - Protocol support (VNC, RDP, SSH)
- **PostgreSQL Database** - User and connection management
- **Session Recording** - Built-in audit capabilities

### Workspace Images
- **Ubuntu Desktop** - Full GNOME desktop environment
- **Development Environment** - VS Code, Docker, Node.js, Python, Git
- **Minimal Desktop** - Lightweight XFCE desktop

### Management Scripts
- **`scripts/build.sh`** - Build application and workspace images
- **`scripts/deploy.sh`** - Deploy and manage the platform
- **`scripts/manage-workspaces.sh`** - Create and manage workspace containers

## 🛠️ Management Commands

### Platform Management
```bash
# Initialize (first time only)
./scripts/deploy.sh init

# Start platform
./scripts/deploy.sh start [--build] [--pull]

# Stop platform
./scripts/deploy.sh stop

# Restart platform
./scripts/deploy.sh restart

# Show status
./scripts/deploy.sh status

# View logs
./scripts/deploy.sh logs [--follow] [--service <name>]
```

### Building Images
```bash
# Build everything
./scripts/build.sh all

# Build only main app
./scripts/build.sh app

# Build only workspace images
./scripts/build.sh workspaces

# Build without cache
./scripts/build.sh all --no-cache

# Build and push to registry
./scripts/build.sh all --push
```

### Workspace Management
```bash
# Create Ubuntu Desktop workspace
./scripts/manage-workspaces.sh create --type ubuntu --user john

# Create Development Environment
./scripts/manage-workspaces.sh create --type development --user jane --cpu 4 --memory 4096

# List all workspaces
./scripts/manage-workspaces.sh list

# Stop specific workspace
./scripts/manage-workspaces.sh stop --container workspace-john-ubuntu

# Remove all workspaces
./scripts/manage-workspaces.sh remove --all

# View workspace logs
./scripts/manage-workspaces.sh logs --container workspace-john-ubuntu

# Cleanup stopped containers
./scripts/manage-workspaces.sh cleanup
```

## ⚙️ Configuration

### Environment Variables
Key configuration options in `.env`:

```env
# Application
OMNISPACE_PORT=3000
NODE_ENV=production

# AI Integration
OPENAI_API_KEY=your_key_here

# Guacamole
GUACAMOLE_PORT=8080
GUACAMOLE_DB_PASSWORD=secure_password

# Security
JWT_SECRET=secure_jwt_secret

# Resources
DEFAULT_WORKSPACE_CPU=2
DEFAULT_WORKSPACE_MEMORY=2048
MAX_WORKSPACES_PER_USER=5
```

### Docker Compose Profiles
Enable optional services:

```env
# Enable Redis for session management
COMPOSE_PROFILES=redis

# Enable multiple services
COMPOSE_PROFILES=redis,monitoring
```

## 🔒 Security Features

### Built-in Security
- **Random password generation** for VNC and database access
- **JWT-based authentication** for API access
- **Container isolation** with dedicated networks
- **Resource limits** for workspace containers
- **Session recording** through Guacamole

### Production Hardening
```bash
# Generate secure passwords
GUACAMOLE_DB_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)

# Set resource limits
echo "DEFAULT_WORKSPACE_CPU=2" >> .env
echo "DEFAULT_WORKSPACE_MEMORY=2048" >> .env
echo "MAX_WORKSPACES_PER_USER=3" >> .env

# Enable monitoring
echo "ENABLE_METRICS=true" >> .env
```

## 📊 Monitoring and Health Checks

### Health Check Endpoint
```bash
# Check platform health
curl http://localhost:3000/api/health

# Response includes:
# - Docker connection status
# - System information
# - Memory usage
# - Service status
```

### Container Monitoring
```bash
# Monitor resource usage
docker stats

# Check service logs
./scripts/deploy.sh logs --follow --service omnispace

# View workspace containers
./scripts/manage-workspaces.sh list
```

## 🔧 Troubleshooting

### Common Issues

1. **Platform won't start**
   ```bash
   # Check Docker status
   docker info
   
   # Verify environment
   ./scripts/deploy.sh status
   
   # Check logs
   ./scripts/deploy.sh logs
   ```

2. **Workspace creation fails**
   ```bash
   # Check workspace images
   docker images | grep omnispace
   
   # Verify network
   docker network ls | grep workspace
   
   # Check available resources
   docker system df
   ```

3. **Guacamole connection issues**
   ```bash
   # Check Guacamole logs
   ./scripts/deploy.sh logs --service guacamole
   
   # Verify database
   ./scripts/deploy.sh logs --service guacamole-db
   
   # Test connectivity
   docker exec omnispace-guacamole ping guacd
   ```

### Performance Optimization
```bash
# Clean up unused resources
docker system prune -a

# Optimize database
docker exec omnispace-guacamole-db psql -U guacamole_user -d guacamole_db -c "VACUUM ANALYZE;"

# Monitor workspace resources
docker stats $(docker ps --filter "label=omnispace.workspace=true" -q)
```

## 📚 Additional Resources

- **[DOCKER.md](DOCKER.md)** - Detailed Docker deployment guide
- **[workspace-images/README.md](workspace-images/README.md)** - Workspace images documentation
- **[workspace-images/DEPLOYMENT.md](workspace-images/DEPLOYMENT.md)** - Production deployment guide

## 🎯 Next Steps

1. **Customize workspace images** - Add your own tools and configurations
2. **Set up SSL/TLS** - Configure HTTPS for production
3. **Integrate authentication** - Connect with your identity provider
4. **Scale horizontally** - Deploy across multiple hosts
5. **Monitor and alert** - Set up comprehensive monitoring

This complete Docker setup provides a robust, scalable foundation for your Omnispace platform! 🚀
