# Admin Environment Setup Guide

This guide covers the comprehensive admin environment setup for the Omnispace platform, including the creation of a default admin user with full system privileges and realistic test data for thorough feature testing.

## Overview

The admin environment setup creates:
- **Default Admin User**: Full system privileges and access to all features
- **Test Organizations**: Sample organizations with different configurations
- **Test Workspaces**: Various workspace types demonstrating different use cases
- **Test VMs**: Virtual machines in different states for testing VM lifecycle operations
- **Test Sessions**: Sample user sessions and activity logs
- **Additional Test Users**: Users with different roles for collaboration testing

## Quick Setup

### Prerequisites
1. Ensure Appwrite is properly configured (run `pnpm setup:appwrite` first)
2. Environment variables are set in `.env.local`
3. Database collections are created and accessible

### Run Admin Environment Setup
```bash
pnpm setup:admin-env
```

This single command will create the complete testing environment.

## Default Admin User

### Credentials
- **Email**: `<EMAIL>`
- **Password**: `OmnispaceAdmin2024!`
- **Role**: System Administrator

### Admin Privileges
The admin user has full access to:
- User Management (create, edit, delete users)
- Organization Management (create, manage organizations)
- Workspace Management (create, manage all workspaces)
- VM Lifecycle Operations (start, stop, restart, remove VMs)
- System Monitoring (view analytics, performance metrics)
- Settings Management (configure system settings)

### Admin Profile Features
- **Enhanced Dashboard**: Admin-specific views with advanced metrics
- **Extended Permissions**: Full CRUD operations on all resources
- **System Notifications**: Alerts for system events and user activities
- **Advanced Analytics**: Access to detailed usage statistics and performance data

## Created Test Data

### Organizations (3)

#### 1. Omnispace Platform
- **Owner**: Admin User
- **Type**: Main administrative organization
- **Features**: 
  - High resource limits (16 CPU, 32GB RAM, 500GB storage)
  - Enhanced security settings
  - Custom branding configuration
  - Private workspaces only

#### 2. Development Team
- **Owner**: Admin User
- **Type**: Software development organization
- **Features**:
  - Moderate resource limits (8 CPU, 16GB RAM, 250GB storage)
  - Public workspace sharing enabled
  - Collaboration features enabled
  - Development-focused templates

#### 3. Data Science Lab
- **Owner**: Admin User
- **Type**: Research and ML organization
- **Features**:
  - High-performance resources (16 CPU, 64GB RAM, 1TB storage)
  - GPU access enabled
  - Enhanced security (2FA required)
  - Python/ML focused environment

### Workspaces (7)

#### Admin Personal Workspaces

1. **Admin Python ML Environment**
   - **Type**: Python Development
   - **Status**: Running
   - **Features**: TensorFlow, PyTorch, Jupyter Lab, GPU support
   - **Resources**: 8 CPU, 16GB RAM, 100GB storage

2. **Admin Dashboard Development**
   - **Type**: React/Node.js
   - **Status**: Running
   - **Features**: Next.js, TypeScript, Tailwind CSS, Framer Motion
   - **Resources**: 4 CPU, 8GB RAM, 50GB storage

3. **Admin Full Desktop Environment**
   - **Type**: Ubuntu Desktop
   - **Status**: Stopped
   - **Features**: GNOME desktop, VS Code, Docker, Kubernetes tools
   - **Resources**: 8 CPU, 16GB RAM, 200GB storage

#### Development Team Workspaces

4. **Microservices Development**
   - **Type**: Node.js/Express
   - **Status**: Running
   - **Features**: Express, TypeScript, Prisma, Jest, Docker
   - **Resources**: 4 CPU, 8GB RAM, 75GB storage

5. **Frontend Testing Environment**
   - **Type**: React Testing
   - **Status**: Paused
   - **Features**: React, Jest, Cypress, Testing Library, Storybook
   - **Resources**: 2 CPU, 4GB RAM, 40GB storage

#### Collaborative Workspaces

6. **Team Frontend Project**
   - **Type**: React Collaboration
   - **Status**: Running
   - **Features**: Multi-user editing, shared development environment
   - **Collaborators**: Admin (owner), Developer (editor), Designer (viewer)

7. **ML Research Collaboration**
   - **Type**: Python ML Research
   - **Status**: Running
   - **Features**: Jupyter Lab, MLflow, GPU access, shared notebooks
   - **Collaborators**: Admin (owner), Data Scientist (editor)

### Virtual Machines (4)

1. **System Monitoring VM**
   - **Status**: Running
   - **Purpose**: Prometheus and Grafana monitoring
   - **Resources**: 4 CPU, 8GB RAM, 100GB storage
   - **Network**: Bridge mode with exposed ports (9090, 3000)

2. **Backup and Recovery VM**
   - **Status**: Stopped
   - **Purpose**: Backup operations and disaster recovery
   - **Resources**: 2 CPU, 4GB RAM, 500GB storage
   - **Network**: Bridge mode, no exposed ports

3. **Development Database VM**
   - **Status**: Running
   - **Purpose**: PostgreSQL database for development
   - **Resources**: 4 CPU, 16GB RAM, 200GB storage
   - **Network**: Bridge mode with PostgreSQL port (5432)

4. **Load Balancer Test VM**
   - **Status**: Paused
   - **Purpose**: NGINX load balancer testing
   - **Resources**: 2 CPU, 2GB RAM, 20GB storage
   - **Network**: Bridge mode with HTTP/HTTPS ports (80, 443)

### Test Users (3)

1. **John Developer** (`<EMAIL>`)
   - **Role**: Developer
   - **Password**: `DevPassword123!`
   - **Specialization**: Full-stack React/Node.js development

2. **Sarah Designer** (`<EMAIL>`)
   - **Role**: Designer
   - **Password**: `DesignPassword123!`
   - **Specialization**: UI/UX design and user experience

3. **Dr. Alex Chen** (`<EMAIL>`)
   - **Role**: Data Scientist
   - **Password**: `DataPassword123!`
   - **Specialization**: Machine learning and deep learning research

### Active Sessions (2)

1. **Admin Desktop Session**
   - **Device**: Windows Chrome browser
   - **Location**: San Francisco, CA
   - **Activity**: Recent workspace creation, VM management
   - **Status**: Active (expires in 8 hours)

2. **Admin Mobile Session**
   - **Device**: iPhone Safari browser
   - **Location**: San Francisco, CA
   - **Activity**: Mobile dashboard access
   - **Status**: Active (expires in 6 hours)

## Testing Scenarios Enabled

### User Management Testing
- ✅ Create, edit, delete users
- ✅ Assign roles and permissions
- ✅ Manage user sessions
- ✅ View user activity logs

### Organization Management Testing
- ✅ Create and configure organizations
- ✅ Manage organization members
- ✅ Set resource limits and policies
- ✅ Configure organization settings

### Workspace Management Testing
- ✅ Create workspaces of different types
- ✅ Configure workspace resources
- ✅ Manage workspace lifecycle
- ✅ Test collaboration features
- ✅ Monitor workspace usage

### VM Lifecycle Testing
- ✅ Start, stop, restart VMs
- ✅ Monitor VM resources
- ✅ Configure VM networking
- ✅ Test different VM states
- ✅ Manage VM templates

### System Analytics Testing
- ✅ View resource usage statistics
- ✅ Monitor system performance
- ✅ Track user activity
- ✅ Generate usage reports

### Authentication & Authorization Testing
- ✅ Test admin login flows
- ✅ Verify role-based access control
- ✅ Test session management
- ✅ Validate permission enforcement

## Dashboard Features Ready for Testing

### Admin Dashboard Views
1. **System Overview**: Resource usage, active users, system health
2. **User Management**: User list, roles, permissions, activity
3. **Organization Management**: Organization settings, members, resources
4. **Workspace Management**: All workspaces, usage statistics, lifecycle operations
5. **VM Management**: VM list, states, resources, networking
6. **Analytics**: Usage trends, performance metrics, cost analysis
7. **System Settings**: Platform configuration, security settings

### User Dashboard Views
1. **Personal Dashboard**: User's workspaces, recent activity, quick actions
2. **Workspace Management**: Create, manage personal workspaces
3. **Collaboration**: Shared workspaces, team projects
4. **Profile Settings**: User preferences, security settings

## Edge Cases and Error Scenarios

The test data includes scenarios to test:
- **Resource Limits**: Workspaces with different resource allocations
- **State Transitions**: VMs and workspaces in various states
- **Permission Boundaries**: Different user roles and access levels
- **Network Configurations**: Various networking setups
- **Collaboration Conflicts**: Multi-user workspace scenarios
- **Session Management**: Multiple active sessions, expiration handling

## Maintenance and Updates

### Resetting Test Environment
To reset the test environment:
```bash
# This will recreate all test data
pnpm setup:admin-env
```

### Adding More Test Data
The script is designed to be idempotent - running it multiple times will not create duplicates. To add more test data, modify the arrays in the script and run it again.

### Customizing Test Data
Edit `src/scripts/setup-admin-environment.ts` to:
- Add more organizations, workspaces, or VMs
- Modify resource allocations
- Change user roles and permissions
- Add different workspace types
- Include additional test scenarios

## Security Considerations

### Default Passwords
- All default passwords are strong and unique
- Change default passwords in production environments
- Consider implementing password rotation policies

### Admin Privileges
- The admin user has full system access
- Monitor admin activities in production
- Implement audit logging for admin actions
- Consider implementing admin approval workflows

### Test Data
- Test data includes realistic but fictional information
- No real user data or sensitive information is included
- Test data is clearly marked and identifiable

## Next Steps

After running the admin environment setup:

1. **Login as Admin**: Use the provided credentials to access the admin dashboard
2. **Explore Features**: Navigate through all admin dashboard sections
3. **Test Operations**: Try creating, modifying, and deleting resources
4. **Verify Permissions**: Test different user roles and access levels
5. **Monitor Performance**: Check system analytics and monitoring views
6. **Test Collaboration**: Use multiple user accounts to test collaborative features

The admin environment provides a comprehensive testing platform that enables validation of all implemented Omnispace platform features without requiring manual data creation.
