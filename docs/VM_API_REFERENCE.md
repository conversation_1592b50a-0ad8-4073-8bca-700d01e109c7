# VM Services API Reference

## Overview

This document provides detailed API reference for all VM service endpoints in the Omnispace platform.

## Base URL

All VM API endpoints are prefixed with `/api/vm`

## Authentication

VM API endpoints require proper authentication and authorization. Include the authentication token in the request headers:

```
Authorization: Bearer <token>
```

## Common Response Format

All API endpoints return responses in the following format:

```typescript
{
  success: boolean;
  data?: any;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: Date;
    operation: string;
    duration: number;
    vmId: string;
    connectionId?: string;
    serviceName: string;
  };
}
```

## Main VM API

### POST /api/vm

#### Connect to VM
Establishes a secure SSH connection to the specified VM.

**Parameters:**
- `action=connect` (query)
- `vmId` (query) - VM identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
  credentials?: VMAuthCredentials;
}
```

**Response:**
```typescript
{
  success: true;
  data: {
    isConnected: boolean;
    connectionId: string;
    establishedAt: Date;
    lastActivity: Date;
    latency?: number;
  };
}
```

**Example:**
```bash
curl -X POST "/api/vm?action=connect&vmId=vm-001" \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "host": "vm.example.com",
      "port": 22,
      "username": "admin",
      "privateKey": "-----BEGIN PRIVATE KEY-----\n..."
    }
  }'
```

#### Execute Command
Executes a command on the connected VM.

**Parameters:**
- `action=execute` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
  command: string;
  options?: {
    timeout?: number;
    cwd?: string;
    env?: Record<string, string>;
  };
}
```

**Response:**
```typescript
{
  success: true;
  data: {
    stdout: string;
    stderr: string;
    exitCode: number;
  };
}
```

#### Create Session
Creates a user session for tracking VM operations.

**Parameters:**
- `action=create-session` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
  userId: string;
  metadata?: Record<string, any>;
}
```

**Response:**
```typescript
{
  success: true;
  data: {
    id: string;
    vmId: string;
    userId: string;
    connectionId: string;
    startTime: Date;
    isActive: boolean;
    metadata: Record<string, any>;
  };
}
```

### GET /api/vm

#### Health Check
Returns the health status of the VM API or specific VM.

**Parameters:**
- `action=health` (query)
- `vmId` (query, optional) - VM identifier

**Response:**
```typescript
{
  success: true;
  data: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: string;
    vmId?: string;
    message: string;
  };
}
```

#### Connection Status
Gets the status of a specific VM connection.

**Parameters:**
- `action=connection-status` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

**Response:**
```typescript
{
  success: true;
  data: {
    isConnected: boolean;
    connectionId: string;
    establishedAt?: Date;
    lastActivity?: Date;
    latency?: number;
  };
}
```

#### List Connections
Lists all active connections for a VM.

**Parameters:**
- `action=connections` (query)
- `vmId` (query) - VM identifier

**Response:**
```typescript
{
  success: true;
  data: VMConnectionStatus[];
}
```

### DELETE /api/vm

#### Disconnect
Closes a VM connection.

**Parameters:**
- `action=disconnect` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

**Response:**
```typescript
{
  success: true;
  data: boolean;
}
```

#### Close Session
Closes a user session.

**Parameters:**
- `action=close-session` (query)
- `vmId` (query) - VM identifier
- `sessionId` (query) - Session identifier

**Response:**
```typescript
{
  success: true;
  data: boolean;
}
```

### PUT /api/vm

#### Update Configuration
Updates VM connection configuration.

**Parameters:**
- `action=update-config` (query)
- `vmId` (query) - VM identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
}
```

#### Acknowledge Alert
Acknowledges a monitoring alert.

**Parameters:**
- `action=acknowledge-alert` (query)
- `vmId` (query) - VM identifier
- `alertId` (query) - Alert identifier

**Response:**
```typescript
{
  success: true;
  data: boolean;
}
```

## Docker API

### POST /api/vm/docker

#### Create Container
Creates a new Docker container on the VM.

**Parameters:**
- `action=create-container` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
  options: {
    name?: string;
    image: string;
    command?: string[];
    environment?: Record<string, string>;
    ports?: Record<string, string>;
    volumes?: Record<string, string>;
    // ... other container options
  };
}
```

**Response:**
```typescript
{
  success: true;
  data: {
    containerId: string;
    warnings: string[];
  };
}
```

#### Start Container
Starts a Docker container.

**Parameters:**
- `action=start-container` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `containerId` (query) - Container identifier

#### Stop Container
Stops a Docker container.

**Parameters:**
- `action=stop-container` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `containerId` (query) - Container identifier
- `timeout` (query, optional) - Stop timeout in seconds

#### Execute in Container
Executes a command inside a Docker container.

**Parameters:**
- `action=exec-container` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `containerId` (query) - Container identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
  command: string[];
  options?: {
    interactive?: boolean;
    tty?: boolean;
    user?: string;
    workingDir?: string;
    environment?: Record<string, string>;
  };
}
```

### GET /api/vm/docker

#### Docker Info
Gets Docker system information.

**Parameters:**
- `action=info` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `config` (query) - Base64 encoded configuration

**Response:**
```typescript
{
  success: true;
  data: {
    version: string;
    apiVersion: string;
    containers: number;
    containersRunning: number;
    images: number;
    // ... other Docker info
  };
}
```

#### List Containers
Lists Docker containers.

**Parameters:**
- `action=containers` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `all` (query, optional) - Include stopped containers
- `limit` (query, optional) - Limit number of results
- `filters` (query, optional) - JSON encoded filters

**Response:**
```typescript
{
  success: true;
  data: DockerContainer[];
}
```

#### Container Logs
Gets logs from a Docker container.

**Parameters:**
- `action=container-logs` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `containerId` (query) - Container identifier
- `follow` (query, optional) - Follow log output
- `tail` (query, optional) - Number of lines to tail
- `since` (query, optional) - Show logs since timestamp
- `until` (query, optional) - Show logs until timestamp
- `timestamps` (query, optional) - Include timestamps

### DELETE /api/vm/docker

#### Remove Container
Removes a Docker container.

**Parameters:**
- `action=remove-container` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `containerId` (query) - Container identifier
- `force` (query, optional) - Force removal
- `removeVolumes` (query, optional) - Remove associated volumes
- `removeLinks` (query, optional) - Remove associated links

## Monitoring API

### GET /api/vm/monitoring

#### System Information
Gets comprehensive system information.

**Parameters:**
- `action=system-info` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `config` (query) - Base64 encoded configuration

**Response:**
```typescript
{
  success: true;
  data: {
    hostname: string;
    platform: string;
    arch: string;
    release: string;
    uptime: number;
    loadAverage: number[];
    totalMemory: number;
    freeMemory: number;
    cpuCount: number;
    diskUsage: DiskUsage[];
    networkInterfaces: NetworkInterfaceInfo[];
  };
}
```

#### Resource Metrics
Gets current resource usage metrics.

**Parameters:**
- `action=metrics` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `config` (query) - Base64 encoded configuration

**Response:**
```typescript
{
  success: true;
  data: {
    timestamp: Date;
    cpu: {
      usage: number;
      loadAverage: number[];
      processes: number;
    };
    memory: {
      total: number;
      used: number;
      free: number;
      usagePercent: number;
    };
    disk: {
      total: number;
      used: number;
      free: number;
      usagePercent: number;
    };
    network: {
      bytesIn: number;
      bytesOut: number;
      packetsIn: number;
      packetsOut: number;
    };
  };
}
```

#### Process List
Gets list of running processes.

**Parameters:**
- `action=processes` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `sortBy` (query, optional) - Sort by 'cpu', 'memory', or 'name'
- `limit` (query, optional) - Limit number of results
- `filter` (query, optional) - Filter processes by name/command

#### Health Check
Performs comprehensive health check.

**Parameters:**
- `action=health` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `config` (query) - Base64 encoded configuration

**Response:**
```typescript
{
  success: true;
  data: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: {
      connection: boolean;
      ssh: boolean;
      docker: boolean;
      system: boolean;
      resources: boolean;
    };
    lastCheck: Date;
    details?: Record<string, string>;
  };
}
```

#### Active Alerts
Gets list of active monitoring alerts.

**Parameters:**
- `action=alerts` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `config` (query) - Base64 encoded configuration

### POST /api/vm/monitoring

#### Start Monitoring
Starts continuous monitoring for a VM.

**Parameters:**
- `action=start-monitoring` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

**Request Body:**
```typescript
{
  config: VMConnectionConfig;
}
```

#### Stop Monitoring
Stops continuous monitoring.

**Parameters:**
- `action=stop-monitoring` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier

#### Acknowledge Alert
Acknowledges a monitoring alert.

**Parameters:**
- `action=acknowledge-alert` (query)
- `vmId` (query) - VM identifier
- `connectionId` (query) - Connection identifier
- `alertId` (query) - Alert identifier

## Health Check API

### GET /api/vm/health

#### API Health
Gets overall API health status.

**Response:**
```typescript
{
  success: true;
  data: {
    status: 'healthy';
    message: 'VM Health API is operational';
    timestamp: string;
    services: {
      api: boolean;
      logging: boolean;
      configuration: boolean;
    };
  };
}
```

#### VM Health
Gets health status for a specific VM.

**Parameters:**
- `vmId` (query) - VM identifier
- `detailed` (query, optional) - Include detailed health check
- `config` (query, optional) - Base64 encoded configuration

### POST /api/vm/health

#### Health Check with Configuration
Performs health check with provided configuration.

**Request Body:**
```typescript
{
  vmId: string;
  config: VMConnectionConfig;
  detailed?: boolean;
}
```

**Response:**
```typescript
{
  success: true;
  data: {
    overall: 'healthy' | 'degraded' | 'unhealthy';
    services: {
      connection: boolean;
      docker: boolean;
      monitoring: boolean;
    };
    details?: Record<string, string>;
    timestamp: string;
    vmId: string;
    responseTime: number;
  };
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VM_CONNECTION_FAILED` | Failed to establish VM connection |
| `VM_AUTH_FAILED` | Authentication failed |
| `VM_COMMAND_FAILED` | Command execution failed |
| `VM_DOCKER_FAILED` | Docker operation failed |
| `VM_RESOURCE_EXHAUSTED` | VM resources exhausted |
| `VM_TIMEOUT` | Operation timeout |
| `VM_PERMISSION_DENIED` | Permission denied |
| `VM_INVALID_CONFIG` | Invalid configuration |
| `VM_SERVICE_UNAVAILABLE` | Service unavailable |
| `VM_HEALTH_CHECK_FAILED` | Health check failed |
| `VALIDATION_ERROR` | Request validation failed |
| `MISSING_PARAMETER` | Required parameter missing |
| `INVALID_ACTION` | Invalid action specified |
| `INTERNAL_ERROR` | Internal server error |

## Rate Limits

| Endpoint | Limit |
|----------|-------|
| Connection operations | 60 per minute |
| Command executions | 300 per minute |
| Docker operations | 100 per minute |
| Monitoring requests | 120 per minute |
| Health checks | 60 per minute |

## SDK Usage Examples

### JavaScript/TypeScript

```typescript
import { VMApiClient } from '@/lib/vm-api-client';

const client = new VMApiClient('http://localhost:3000');

// Connect to VM
const connection = await client.connect('vm-001', {
  host: 'vm.example.com',
  port: 22,
  username: 'admin',
  privateKey: process.env.VM_PRIVATE_KEY
});

// Execute command
const result = await client.executeCommand(
  'vm-001',
  connection.connectionId,
  'docker ps'
);

// Create container
const container = await client.createContainer('vm-001', connection.connectionId, {
  name: 'my-app',
  image: 'nginx:latest',
  ports: { '80': '8080' }
});
```

### Python

```python
import requests
import json

class VMApiClient:
    def __init__(self, base_url):
        self.base_url = base_url
    
    def connect(self, vm_id, config):
        response = requests.post(
            f"{self.base_url}/api/vm",
            params={"action": "connect", "vmId": vm_id},
            json={"config": config}
        )
        return response.json()
    
    def execute_command(self, vm_id, connection_id, command):
        response = requests.post(
            f"{self.base_url}/api/vm",
            params={
                "action": "execute",
                "vmId": vm_id,
                "connectionId": connection_id
            },
            json={"command": command}
        )
        return response.json()

# Usage
client = VMApiClient("http://localhost:3000")
result = client.connect("vm-001", {
    "host": "vm.example.com",
    "port": 22,
    "username": "admin",
    "privateKey": "..."
})
```
