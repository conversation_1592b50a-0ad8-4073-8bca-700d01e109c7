# Frontend Components and Hooks Documentation

## Overview

This document provides comprehensive documentation for the frontend components and hooks that integrate with the Appwrite server-side services. All components are built with TypeScript, React, and modern UI patterns using shadcn/ui components and Framer Motion animations.

## Hooks

### `useAppwriteAuth`

A comprehensive authentication hook that provides both client-side and server-side authentication capabilities.

```typescript
import { useAppwriteAuth } from '@/hooks/useAppwriteAuth';

const {
  user,
  isLoading,
  isAuthenticated,
  error,
  login,
  register,
  logout,
  updateProfile,
  updatePassword,
  sendPasswordReset,
  clearError
} = useAppwriteAuth();
```

**Features:**
- User registration and login
- Session management
- Profile updates
- Password management
- Error handling
- Loading states

**Methods:**
- `login(params)` - Authenticate user
- `register(params)` - Create new user account
- `logout()` - End user session
- `updateProfile(params)` - Update user profile
- `updatePassword(params)` - Change password
- `sendPasswordReset(email)` - Send password reset email

### `useAppwriteDatabase`

Hook for database operations including document CRUD and collection management.

```typescript
import { useAppwriteDatabase } from '@/hooks/useAppwriteDatabase';

const {
  isLoading,
  error,
  createDocument,
  getDocument,
  listDocuments,
  updateDocument,
  deleteDocument,
  listCollections,
  clearError
} = useAppwriteDatabase();
```

**Features:**
- Document CRUD operations
- Collection listing
- Query building
- Pagination support
- Error handling

### `useAppwriteStorage`

Hook for file storage operations with upload progress tracking.

```typescript
import { useAppwriteStorage } from '@/hooks/useAppwriteStorage';

const {
  isLoading,
  uploadProgress,
  error,
  uploadFile,
  getFile,
  listFiles,
  getFilePreview,
  deleteFile,
  clearError
} = useAppwriteStorage();
```

**Features:**
- File upload with progress tracking
- File management
- Image transformations
- Preview URL generation
- Error handling

## Components

### `EnhancedAuthForm`

A comprehensive authentication form that supports both login and registration with OAuth integration.

```typescript
import { EnhancedAuthForm } from '@/components/auth/EnhancedAuthForm';

<EnhancedAuthForm
  onSuccess={() => console.log('Auth successful')}
  defaultTab="login"
  showOAuth={true}
  className="max-w-md mx-auto"
/>
```

**Props:**
- `onSuccess?: () => void` - Callback on successful authentication
- `defaultTab?: 'login' | 'register'` - Default active tab
- `showOAuth?: boolean` - Show OAuth buttons
- `className?: string` - Additional CSS classes

**Features:**
- Tabbed interface (Login/Register)
- Form validation with Zod
- Password strength indicator
- OAuth integration (Google, GitHub)
- Animated transitions
- Error handling
- Loading states

### `FileUpload`

A drag-and-drop file upload component with progress tracking and file management.

```typescript
import { FileUpload } from '@/components/storage/FileUpload';

<FileUpload
  bucketId="my-bucket"
  onUploadComplete={(files) => console.log('Uploaded:', files)}
  onUploadError={(error) => console.error('Error:', error)}
  maxFiles={10}
  maxSize={10 * 1024 * 1024} // 10MB
  acceptedFileTypes={['image/*', 'video/*', 'application/pdf']}
/>
```

**Props:**
- `bucketId: string` - Appwrite storage bucket ID
- `onUploadComplete?: (files) => void` - Upload success callback
- `onUploadError?: (error) => void` - Upload error callback
- `maxFiles?: number` - Maximum number of files (default: 10)
- `maxSize?: number` - Maximum file size in bytes (default: 10MB)
- `acceptedFileTypes?: string[]` - Accepted MIME types
- `className?: string` - Additional CSS classes

**Features:**
- Drag and drop interface
- Multiple file upload
- Progress tracking
- File type validation
- Size validation
- Preview thumbnails
- Error handling
- Animated file list

### `DocumentManager`

A comprehensive document management interface for database collections.

```typescript
import { DocumentManager } from '@/components/database/DocumentManager';

<DocumentManager
  databaseId="my-database"
  collectionId="my-collection"
  title="My Documents"
  description="Manage your documents"
/>
```

**Props:**
- `databaseId: string` - Appwrite database ID
- `collectionId: string` - Appwrite collection ID
- `title?: string` - Component title
- `description?: string` - Component description
- `className?: string` - Additional CSS classes

**Features:**
- Document listing with pagination
- Search functionality
- Create new documents
- Edit existing documents
- Delete documents
- Real-time updates
- Field-level editing
- Error handling

### `AppwriteDashboard`

A complete dashboard showcasing all Appwrite services with health monitoring.

```typescript
import { AppwriteDashboard } from '@/components/dashboard/AppwriteDashboard';

<AppwriteDashboard className="min-h-screen" />
```

**Features:**
- Service health monitoring
- User profile display
- Tabbed interface for different services
- Quick actions
- Real-time status updates
- Responsive design
- Integration with all hooks

## Usage Examples

### Basic Authentication Flow

```typescript
import { EnhancedAuthForm, useAppwriteAuth } from '@/components/appwrite';

function AuthPage() {
  const { isAuthenticated, user } = useAppwriteAuth();

  if (isAuthenticated) {
    return <div>Welcome, {user?.name}!</div>;
  }

  return (
    <EnhancedAuthForm
      onSuccess={() => {
        // Redirect to dashboard
        window.location.href = '/dashboard';
      }}
    />
  );
}
```

### File Upload with Callback

```typescript
import { FileUpload } from '@/components/appwrite';

function UploadPage() {
  return (
    <FileUpload
      bucketId="user-uploads"
      onUploadComplete={(files) => {
        files.forEach(file => {
          console.log(`Uploaded: ${file.name} - ${file.url}`);
        });
      }}
      onUploadError={(error) => {
        alert(`Upload failed: ${error}`);
      }}
      maxFiles={5}
      acceptedFileTypes={['image/*']}
    />
  );
}
```

### Database Operations

```typescript
import { DocumentManager, useAppwriteDatabase } from '@/components/appwrite';

function DatabasePage() {
  const { createDocument } = useAppwriteDatabase();

  const handleCreateUser = async () => {
    const result = await createDocument({
      databaseId: 'main',
      collectionId: 'users',
      data: {
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user'
      }
    });

    if (result.success) {
      console.log('User created:', result.data);
    }
  };

  return (
    <div>
      <button onClick={handleCreateUser}>Create User</button>
      <DocumentManager
        databaseId="main"
        collectionId="users"
        title="User Management"
      />
    </div>
  );
}
```

### Complete Dashboard

```typescript
import { AppwriteDashboard, useAuth } from '@/components/appwrite';

function DashboardPage() {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }

  return <AppwriteDashboard />;
}
```

## Styling and Theming

All components use:
- **Tailwind CSS** for styling
- **shadcn/ui** components for consistency
- **Framer Motion** for animations
- **Dark mode** support
- **Responsive design**
- **Glassmorphism** effects

## Error Handling

All components include comprehensive error handling:
- Form validation errors
- Network errors
- Appwrite service errors
- User-friendly error messages
- Error recovery mechanisms

## Accessibility

Components follow accessibility best practices:
- ARIA labels and roles
- Keyboard navigation
- Focus management
- Screen reader support
- High contrast support

## Performance

Optimizations included:
- Lazy loading
- Memoized callbacks
- Debounced search
- Pagination
- Progress tracking
- Connection pooling

## Browser Support

Components support all modern browsers:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

Key dependencies:
- React 18+
- TypeScript 5+
- Framer Motion
- React Hook Form
- Zod validation
- shadcn/ui components
- Tailwind CSS
- Lucide React icons
