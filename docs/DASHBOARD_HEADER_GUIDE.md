# 🎛️ Dashboard Header System - Complete Guide

This guide covers the powerful, feature-rich header system for the Omnispace dashboard, including all components and their usage.

## 📋 Overview

The dashboard header system provides a comprehensive navigation and control interface with:

- **Global Search** - Powerful search across workspaces, containers, and system resources
- **Real-time Notifications** - Categorized notifications with priority levels
- **System Status Monitoring** - Live system health and resource usage
- **User Profile Management** - Comprehensive user account and preferences
- **Breadcrumb Navigation** - Dynamic navigation with dropdown menus
- **Quick Actions Toolbar** - Context-aware action buttons
- **Responsive Design** - Adapts to different screen sizes and usage contexts

## 🏗️ Architecture

```
DashboardHeader (Main Component)
├── SidebarTrigger (Mobile menu)
├── BreadcrumbNavigation (Path navigation)
├── GlobalSearch (Search functionality)
├── QuickActionsToolbar (Action buttons)
├── SystemStatus (Resource monitoring)
├── NotificationSystem (Alerts & messages)
└── UserProfileDropdown (User account)
```

## 🎨 Components

### 1. DashboardHeader (Main Component)

The main header component that orchestrates all sub-components.

```tsx
import { DashboardHeader } from '@/components/dashboard';

<DashboardHeader
  title="Custom Page Title"
  subtitle="Page description"
  showBreadcrumbs={true}
  showQuickActions={true}
  showSystemStatus={true}
  variant="full" // 'full' | 'compact' | 'minimal'
/>
```

**Props:**
- `title?: string` - Custom page title
- `subtitle?: string` - Page description
- `showBreadcrumbs?: boolean` - Show/hide breadcrumb navigation
- `showQuickActions?: boolean` - Show/hide quick actions toolbar
- `showSystemStatus?: boolean` - Show/hide system status indicators
- `variant?: 'full' | 'compact' | 'minimal'` - Header layout variant
- `className?: string` - Additional CSS classes

### 2. GlobalSearch

Powerful search component with keyboard shortcuts and categorized results.

```tsx
import { GlobalSearch } from '@/components/dashboard';

<GlobalSearch
  placeholder="Search workspaces, containers..."
  onSelect={(result) => console.log('Selected:', result)}
/>
```

**Features:**
- **Keyboard Shortcut**: `⌘+K` or `Ctrl+K`
- **Categorized Results**: Workspaces, containers, settings, actions
- **Recent Searches**: Remembers previous searches
- **Fuzzy Matching**: Intelligent search across titles, descriptions, and tags
- **Quick Actions**: Direct navigation to create/manage resources

### 3. NotificationSystem

Advanced notification system with real-time updates and categorization.

```tsx
import { NotificationSystem } from '@/components/dashboard';

<NotificationSystem
  maxNotifications={50}
  autoMarkAsRead={true}
  showCategories={true}
/>
```

**Features:**
- **Real-time Updates**: Live notification feed
- **Priority Levels**: Critical, high, medium, low
- **Categories**: System, workspace, security, updates
- **Persistent Notifications**: Important alerts that can't be dismissed
- **Batch Actions**: Mark all as read, clear all
- **Action Links**: Direct navigation to relevant pages

**Notification Types:**
- `success` - Successful operations (green)
- `warning` - Warnings and alerts (yellow)
- `error` - Errors and failures (red)
- `info` - General information (blue)

### 4. SystemStatus

Real-time system monitoring with detailed metrics.

```tsx
import { SystemStatus } from '@/components/dashboard';

<SystemStatus
  compact={false}
  showDetails={true}
  refreshInterval={30000}
/>
```

**Metrics Displayed:**
- **CPU Usage**: Current usage percentage and trends
- **Memory Usage**: RAM utilization and available memory
- **Disk Usage**: Storage consumption and free space
- **Network Status**: Connectivity and throughput
- **Service Health**: Docker, Guacamole, database status
- **Workspace Status**: Running/total workspaces and health

### 5. UserProfileDropdown

Comprehensive user account management dropdown.

```tsx
import { UserProfileDropdown } from '@/components/dashboard';

<UserProfileDropdown
  user={{
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'admin',
    plan: 'pro'
  }}
  onLogout={() => console.log('Logging out')}
  onThemeChange={(theme) => console.log('Theme:', theme)}
/>
```

**Features:**
- **User Information**: Name, email, role, organization
- **Quick Stats**: Workspace count, storage usage
- **Theme Switching**: Light, dark, system themes
- **Preferences**: Notifications, auto-save, compact mode
- **Account Management**: Settings, billing, security
- **Support Links**: Documentation, help, data export

### 6. BreadcrumbNavigation

Dynamic breadcrumb navigation with dropdown menus.

```tsx
import { BreadcrumbNavigation } from '@/components/dashboard';

<BreadcrumbNavigation
  showIcons={true}
  maxItems={4}
  customItems={[
    { label: 'Dashboard', href: '/dashboard', icon: Home },
    { label: 'Workspaces', href: '/dashboard/workspaces', icon: Monitor }
  ]}
/>
```

**Features:**
- **Auto-generation**: Creates breadcrumbs from current path
- **Dropdown Menus**: Shows related pages for each level
- **Icon Support**: Optional icons for each breadcrumb
- **Overflow Handling**: Ellipsis for long paths
- **Custom Items**: Override auto-generated breadcrumbs

### 7. QuickActionsToolbar

Context-aware action buttons with tooltips and shortcuts.

```tsx
import { QuickActionsToolbar } from '@/components/dashboard';

<QuickActionsToolbar
  variant="full" // 'full' | 'compact' | 'minimal'
  showLabels={true}
  maxPrimaryActions={4}
  customActions={[
    {
      id: 'create',
      label: 'Create Workspace',
      icon: Plus,
      href: '/dashboard/workspaces',
      tooltip: 'Create a new workspace',
      shortcut: '⌘+N'
    }
  ]}
/>
```

**Action Categories:**
- `primary` - Most important actions (always visible)
- `secondary` - Secondary actions (in dropdown)
- `workspace` - Workspace-specific actions
- `system` - System management actions
- `tools` - Utility actions

## 🎯 Usage Examples

### Basic Implementation

```tsx
// app/dashboard/layout.tsx
import { DashboardHeader } from '@/components/dashboard';

export default function DashboardLayout({ children }) {
  return (
    <div className="min-h-screen">
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset className="flex flex-col">
          <DashboardHeader />
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
```

### Custom Page Header

```tsx
// Custom page with specific header configuration
export default function CustomPage() {
  return (
    <div className="p-6">
      <DashboardHeader
        title="Custom Analytics"
        subtitle="Advanced workspace analytics and insights"
        variant="compact"
        showSystemStatus={false}
      />
      {/* Page content */}
    </div>
  );
}
```

### Standalone Components

```tsx
// Using individual components
import { 
  GlobalSearch, 
  NotificationSystem, 
  SystemStatus 
} from '@/components/dashboard';

export function CustomHeader() {
  return (
    <header className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center gap-4">
        <h1>My App</h1>
        <GlobalSearch />
      </div>
      <div className="flex items-center gap-2">
        <SystemStatus compact />
        <NotificationSystem />
      </div>
    </header>
  );
}
```

## 📱 Responsive Behavior

### Desktop (≥1024px)
- Full header with all components visible
- System status shows detailed metrics
- Breadcrumbs with icons and dropdowns
- Full quick actions toolbar

### Tablet (768px - 1023px)
- Compact system status
- Simplified breadcrumbs
- Reduced quick actions
- Maintained search functionality

### Mobile (≤767px)
- Minimal header variant
- Hamburger menu for sidebar
- Search icon only (opens dialog)
- Essential actions only

## 🎨 Customization

### Theme Integration

```tsx
// Theme-aware header
<DashboardHeader
  className="dark:bg-gray-900 dark:border-gray-700"
  variant="full"
/>
```

### Custom Styling

```css
/* Custom header styles */
.dashboard-header {
  @apply bg-gradient-to-r from-blue-600 to-purple-600;
}

.dashboard-header .search-input {
  @apply bg-white/10 backdrop-blur-sm;
}
```

### Custom Actions

```tsx
const customActions = [
  {
    id: 'deploy',
    label: 'Deploy',
    icon: Rocket,
    onClick: () => deployWorkspace(),
    variant: 'default',
    tooltip: 'Deploy to production',
    shortcut: '⌘+D',
    category: 'primary'
  },
  {
    id: 'backup',
    label: 'Backup',
    icon: Download,
    href: '/backup',
    category: 'tools'
  }
];

<QuickActionsToolbar customActions={customActions} />
```

## 🔧 Advanced Features

### Real-time Updates

All components support real-time updates:

```tsx
// System status updates every 30 seconds
<SystemStatus refreshInterval={30000} />

// Notifications update in real-time via WebSocket
<NotificationSystem />
```

### Keyboard Shortcuts

Built-in keyboard shortcuts:
- `⌘+K` / `Ctrl+K` - Open global search
- `⌘+N` / `Ctrl+N` - Create new workspace
- `⌘+M` / `Ctrl+M` - Open monitoring
- `⌘+,` / `Ctrl+,` - Open settings
- `⌘+R` / `Ctrl+R` - Refresh current page

### Accessibility

All components include:
- ARIA labels and descriptions
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

## 🚀 Performance

### Optimization Features

- **Lazy Loading**: Components load on demand
- **Memoization**: Prevents unnecessary re-renders
- **Virtual Scrolling**: For large notification lists
- **Debounced Search**: Optimized search performance
- **Cached Results**: Search results cached locally

### Bundle Size

- **Core Header**: ~15KB gzipped
- **All Components**: ~45KB gzipped
- **Tree Shaking**: Import only what you need

This comprehensive header system provides a professional, feature-rich navigation experience that scales from simple dashboards to complex enterprise applications! 🎯✨
