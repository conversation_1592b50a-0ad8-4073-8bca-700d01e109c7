# Appwrite Server Integration Documentation

## Overview

This document provides comprehensive documentation for the Appwrite server-side integration implemented in the Omnispace platform. The integration includes complete services for authentication, database operations, storage management, and functions execution.

## Architecture

### Service Layer Structure

```
src/services/appwrite/
├── base.ts              # Base service class with common functionality
├── auth.ts              # Authentication service
├── database.ts          # Database operations service
├── storage.ts           # Storage management service
├── functions.ts         # Functions execution service
└── index.ts             # Service factory and exports
```

### API Routes Structure

```
src/app/api/appwrite/
├── auth/route.ts        # Authentication endpoints
├── database/route.ts    # Database operation endpoints
├── storage/route.ts     # Storage management endpoints
├── functions/route.ts   # Functions execution endpoints
└── health/route.ts      # Health check endpoints
```

## Services

### 1. Authentication Service (`AuthService`)

**Features:**
- User registration and login
- Session management
- Password reset and email verification
- User profile updates
- Multi-session support

**Key Methods:**
- `createUser(params)` - Register new user
- `loginUser(params)` - Authenticate user
- `logoutUser(sessionId)` - End user session
- `getCurrentUser(sessionId)` - Get current user info
- `updateUser(sessionId, params)` - Update user profile
- `updatePassword(sessionId, params)` - Change password
- `sendPasswordReset(params)` - Send password reset email

### 2. Database Service (`DatabaseService`)

**Features:**
- Database and collection management
- Document CRUD operations
- Advanced query building
- Schema management (attributes and indexes)
- Pagination support

**Key Methods:**
- `createDatabase(params)` - Create new database
- `createCollection(params)` - Create new collection
- `createAttribute(params)` - Add collection attribute
- `createIndex(params)` - Create collection index
- `createDocument(params, sessionId?)` - Create document
- `listDocuments(databaseId, collectionId, queryParams?, sessionId?)` - Query documents
- `updateDocument(params, sessionId?)` - Update document
- `deleteDocument(databaseId, collectionId, documentId, sessionId?)` - Delete document

### 3. Storage Service (`StorageService`)

**Features:**
- Bucket management
- File upload/download operations
- Image transformations and optimization
- File permissions and access control
- Preview URL generation

**Key Methods:**
- `createBucket(params)` - Create storage bucket
- `uploadFile(params, sessionId?)` - Upload file
- `getFile(bucketId, fileId, sessionId?)` - Get file info
- `listFiles(bucketId, pagination?, sessionId?)` - List bucket files
- `getFilePreview(bucketId, fileId, transformOptions?)` - Get preview URL
- `deleteFile(bucketId, fileId, sessionId?)` - Delete file

### 4. Functions Service (`FunctionsService`)

**Features:**
- Function creation and management
- Code deployment
- Function execution
- Execution monitoring and logs

**Key Methods:**
- `createFunction(params)` - Create new function
- `createDeployment(params)` - Deploy function code
- `executeFunction(params, sessionId?)` - Execute function
- `listExecutions(functionId, pagination?)` - Get execution history

## API Endpoints

### Authentication Endpoints

```
POST /api/appwrite/auth?action=register    # Register user
POST /api/appwrite/auth?action=login       # Login user
POST /api/appwrite/auth?action=logout      # Logout user
GET  /api/appwrite/auth                    # Get current user
PUT  /api/appwrite/auth?action=profile     # Update profile
PUT  /api/appwrite/auth?action=password    # Change password
```

### Database Endpoints

```
POST /api/appwrite/database?action=database    # Create database
POST /api/appwrite/database?action=collection  # Create collection
POST /api/appwrite/database?action=document    # Create document
GET  /api/appwrite/database?action=databases   # List databases
GET  /api/appwrite/database?action=documents   # List documents
PUT  /api/appwrite/database?action=document    # Update document
DELETE /api/appwrite/database?action=document  # Delete document
```

### Storage Endpoints

```
POST /api/appwrite/storage?action=bucket       # Create bucket
POST /api/appwrite/storage?action=file         # Upload file
GET  /api/appwrite/storage?action=buckets      # List buckets
GET  /api/appwrite/storage?action=files        # List files
GET  /api/appwrite/storage?action=file-preview # Get preview URL
DELETE /api/appwrite/storage?action=file       # Delete file
```

### Functions Endpoints

```
POST /api/appwrite/functions?action=function    # Create function
POST /api/appwrite/functions?action=deployment  # Deploy code
POST /api/appwrite/functions?action=execute     # Execute function
GET  /api/appwrite/functions?action=functions   # List functions
GET  /api/appwrite/functions?action=executions  # List executions
```

## Configuration

### Environment Variables

```bash
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://fra.cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
APPWRITE_API_KEY=your_server_api_key

# Database Configuration
NEXT_PUBLIC_APPWRITE_DATABASE_ID=omnispace_db
NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID=users
NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID=vms
NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID=sessions

# Storage Configuration
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=avatars
```

### Server Configuration

The server configuration is managed in `src/lib/appwrite-server.ts`:

```typescript
export const SERVER_CONFIG = {
  retryAttempts: 3,
  retryDelay: 1000,
  timeout: 30000,
  connectionPoolSize: 10,
  logLevel: process.env.APPWRITE_LOG_LEVEL || 'info',
};
```

## Middleware

### Authentication Middleware

- `authMiddleware` - Requires valid session
- `optionalAuthMiddleware` - Optional authentication
- `adminAuthMiddleware` - Requires admin API key
- `roleBasedAuthMiddleware` - Role-based access control

### Rate Limiting Middleware

- `authRateLimit` - Strict limits for auth endpoints
- `apiRateLimit` - General API rate limiting
- `uploadRateLimit` - File upload rate limiting
- `functionsRateLimit` - Function execution limits

## Monitoring and Logging

### Logger Features

- Structured logging with metadata
- Operation timing and performance tracking
- Error tracking with stack traces
- Service-specific log filtering
- Remote logging support

### Metrics Collection

- Counter metrics for operations
- Gauge metrics for system state
- Histogram metrics for distributions
- Timer metrics for performance
- Prometheus-compatible export

## Error Handling

### Error Types

- `AppwriteServerError` - Server-side Appwrite errors
- Validation errors with detailed field information
- Rate limiting errors with retry information
- Authentication and authorization errors

### Error Response Format

```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "type": "error_type",
    "details": {}
  },
  "timestamp": "2025-01-XX..."
}
```

## Usage Examples

### Creating a User

```typescript
import { authService } from '@/services/appwrite';

const result = await authService.createUser({
  email: '<EMAIL>',
  password: 'securepassword',
  name: 'John Doe'
});

if (result.success) {
  console.log('User created:', result.data);
} else {
  console.error('Error:', result.error);
}
```

### Uploading a File

```typescript
import { storageService } from '@/services/appwrite';

const result = await storageService.uploadFile({
  bucketId: 'my-bucket',
  file: fileObject,
  permissions: ['read("any")']
}, sessionId);
```

### Querying Documents

```typescript
import { databaseService } from '@/services/appwrite';

const result = await databaseService.listDocuments(
  'database-id',
  'collection-id',
  {
    filters: { status: 'active' },
    pagination: { limit: 10, offset: 0 }
  },
  sessionId
);
```

## Health Monitoring

Access health status at `/api/appwrite/health`:

```bash
# Quick health check
GET /api/appwrite/health

# Detailed health check
GET /api/appwrite/health?detailed=true
```

## Security Considerations

1. **API Key Security**: Server API keys are never exposed to client-side code
2. **Session Management**: Secure session handling with HTTP-only cookies
3. **Input Validation**: Comprehensive validation using Zod schemas
4. **Rate Limiting**: Protection against abuse with configurable limits
5. **Error Sanitization**: Sensitive information is not leaked in error responses

## Testing

Unit tests are provided for all services in `src/services/appwrite/__tests__/`. Run tests with:

```bash
pnpm test
```

## Deployment

The integration is production-ready with:
- Connection pooling and retry logic
- Comprehensive error handling
- Performance monitoring
- Health checks
- Graceful degradation

For deployment, ensure all environment variables are properly configured and the Appwrite project is set up with the correct permissions and collections.
