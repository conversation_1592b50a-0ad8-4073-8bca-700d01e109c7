# AI Code Editor Integration Guide

This guide explains how to integrate the AI Code Editor system into your Omnispace workspace or external application.

## Quick Start

### 1. Basic Setup

```typescript
import { AICodeEditor } from '@/components/ai-code-editor';
import { CodeFile } from '@/types/ai-code-editor';

function MyEditor() {
  const [file, setFile] = useState<CodeFile>({
    id: 'demo-file',
    name: 'demo.ts',
    path: '/demo.ts',
    content: 'console.log("Hello, AI!");',
    language: 'typescript',
    isDirty: false,
    lastModified: new Date(),
    size: 25,
  });

  return (
    <AICodeEditor
      file={file}
      onFileChange={setFile}
      userId="your-user-id"
      workspaceId="your-workspace-id"
    />
  );
}
```

### 2. Workspace Integration

```typescript
// Add to your workspace layout
import { AIEditorPanel } from '@/components/workspace/components/panels/AIEditorPanel';

// In your workspace configuration
const workspaceConfig = {
  panels: [
    {
      id: 'ai-editor',
      title: 'AI Code Editor',
      type: 'ai-editor' as const,
      closable: false,
      minSize: 60,
      maxSize: 90,
      defaultSize: 75,
    },
  ],
};
```

### 3. Route Setup

Add the AI editor routes to your application:

```typescript
// app/workspace/[id]/ai-editor/page.tsx
import AIEditorPage from '@/app/workspace/[id]/ai-editor/page';

// app/workspace/[id]/ai-assistant/page.tsx  
import AIAssistantPage from '@/app/workspace/[id]/ai-assistant/page';
```

## Advanced Integration

### Custom AI Service Configuration

```typescript
import { AICodeServiceFactory } from '@/services/ai-code';

// Configure AI models
const aiModelService = AICodeServiceFactory.getAIModelService();

aiModelService.updateModelConfig('gpt-4', {
  temperature: 0.1,
  maxTokens: 4096,
  topP: 0.9,
});

// Use different models for different tasks
const completionModel = aiModelService.getModelRecommendation('completion');
const analysisModel = aiModelService.getModelRecommendation('analysis');
```

### Custom Hooks Integration

```typescript
import { 
  useAICodeCompletion,
  useCodeAnalysis,
  useAIAssistant 
} from '@/hooks/ai-code-editor';

function CustomCodeEditor() {
  // Configure completion behavior
  const completion = useAICodeCompletion({
    debounceMs: 500,
    maxSuggestions: 8,
    autoTrigger: true,
    triggerCharacters: ['.', '(', ' ', '\n', ':'],
  });

  // Configure analysis behavior
  const analysis = useCodeAnalysis({
    debounceMs: 1000,
    autoAnalyze: true,
    includeStyle: true,
    includePerformance: true,
    includeSecurity: false, // Disable for faster analysis
  });

  // Configure assistant behavior
  const assistant = useAIAssistant({
    maxMessages: 100,
    persistConversation: true,
  });

  // Your custom editor implementation
  return (
    <div>
      {/* Custom editor UI using the hooks */}
    </div>
  );
}
```

### Theme and Accessibility Integration

```typescript
import { 
  applyTheme, 
  applyAccessibilityConfig,
  DEFAULT_THEMES,
  DEFAULT_ACCESSIBILITY_CONFIG 
} from '@/lib/ai-code-editor-accessibility';

function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState('dark');
  const [accessibility, setAccessibility] = useState(DEFAULT_ACCESSIBILITY_CONFIG);

  useEffect(() => {
    applyTheme(DEFAULT_THEMES[theme]);
    applyAccessibilityConfig(accessibility);
  }, [theme, accessibility]);

  return (
    <div data-theme={theme}>
      {children}
    </div>
  );
}
```

## Environment Configuration

### Required Environment Variables

```env
# AI Service API Keys
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key

# Optional: Custom AI service endpoints
AI_SERVICE_BASE_URL=https://your-ai-service.com/api
AI_SERVICE_TIMEOUT=30000

# Optional: Rate limiting configuration
AI_RATE_LIMIT_RPM=60
AI_RATE_LIMIT_RPH=1000

# Optional: Caching configuration
AI_CACHE_TTL=3600
AI_CACHE_ENABLED=true
```

### Next.js Configuration

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['monaco-editor'],
  },
  webpack: (config) => {
    // Monaco Editor configuration
    config.module.rules.push({
      test: /\.worker\.js$/,
      use: { loader: 'worker-loader' },
    });
    
    return config;
  },
};

module.exports = nextConfig;
```

## Custom Components

### Creating Custom AI Panels

```typescript
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCodeAnalysis } from '@/hooks/ai-code-editor';

function CustomAnalysisPanel({ code, language }: { code: string; language: string }) {
  const analysis = useCodeAnalysis();

  useEffect(() => {
    if (code) {
      analysis.analyzeCode({ code, language });
    }
  }, [code, language]);

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Code Quality Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Metrics Display */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analysis.metrics.maintainability}/10
              </div>
              <div className="text-sm text-muted-foreground">Maintainability</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analysis.metrics.readability}/10
              </div>
              <div className="text-sm text-muted-foreground">Readability</div>
            </div>
          </div>

          {/* Errors List */}
          <div className="space-y-2">
            {analysis.errors.map(error => (
              <motion.div
                key={error.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-2 border rounded-md"
              >
                <div className="font-medium">{error.message}</div>
                <div className="text-sm text-muted-foreground">
                  Line {error.range.start.line}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

### Custom Completion Provider

```typescript
import { AICompletionSuggestion } from '@/types/ai-code-editor';

class CustomCompletionProvider {
  async getCompletions(
    code: string,
    position: { line: number; column: number },
    language: string
  ): Promise<AICompletionSuggestion[]> {
    // Your custom completion logic
    const response = await fetch('/api/your-custom-completion', {
      method: 'POST',
      body: JSON.stringify({ code, position, language }),
    });

    const data = await response.json();
    
    return data.suggestions.map((suggestion: any, index: number) => ({
      id: `custom-${Date.now()}-${index}`,
      text: suggestion.text,
      insertText: suggestion.insertText,
      confidence: suggestion.confidence,
      type: suggestion.type,
      priority: suggestion.priority,
      range: {
        start: position,
        end: position,
      },
    }));
  }
}

// Use in your component
function EditorWithCustomCompletions() {
  const completionProvider = new CustomCompletionProvider();
  
  // Integrate with existing hooks or create custom logic
  return <YourCustomEditor />;
}
```

## Testing Integration

### Component Testing

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AICodeEditor } from '@/components/ai-code-editor';

// Mock the AI services
jest.mock('@/services/ai-code', () => ({
  AICodeServiceFactory: {
    getAICodeService: () => ({
      generateCompletions: jest.fn().mockResolvedValue({
        success: true,
        data: [
          {
            id: 'test-completion',
            text: 'console.log',
            insertText: 'console.log()',
            confidence: 0.9,
            type: 'function',
            priority: 8,
          },
        ],
      }),
    }),
  },
}));

describe('AI Code Editor Integration', () => {
  it('should render and handle file changes', async () => {
    const mockFile = {
      id: 'test-file',
      name: 'test.ts',
      path: '/test.ts',
      content: 'const test = "hello";',
      language: 'typescript',
      isDirty: false,
      lastModified: new Date(),
      size: 20,
    };

    const onFileChange = jest.fn();

    render(
      <AICodeEditor
        file={mockFile}
        onFileChange={onFileChange}
        userId="test-user"
        workspaceId="test-workspace"
      />
    );

    expect(screen.getByText('test.ts')).toBeInTheDocument();
  });
});
```

### API Testing

```typescript
import { testApiHandler } from 'next-test-api-route-handler';
import handler from '@/app/api/ai-code/completion/route';

describe('/api/ai-code/completion', () => {
  it('should return completions', async () => {
    await testApiHandler({
      handler,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'POST',
          body: JSON.stringify({
            code: 'const test = "hello";\ntest.',
            position: { line: 2, column: 5 },
            language: 'typescript',
          }),
        });

        expect(response.status).toBe(200);
        
        const data = await response.json();
        expect(data.success).toBe(true);
        expect(data.data.suggestions).toBeDefined();
      },
    });
  });
});
```

## Performance Optimization

### Code Splitting

```typescript
import { lazy, Suspense } from 'react';

// Lazy load AI components
const AICodeEditor = lazy(() => import('@/components/ai-code-editor/AICodeEditor'));
const AIAssistantPanel = lazy(() => import('@/components/ai-code-editor/AIAssistantPanel'));

function OptimizedEditor() {
  return (
    <Suspense fallback={<div>Loading AI Editor...</div>}>
      <AICodeEditor {...props} />
    </Suspense>
  );
}
```

### Debouncing and Throttling

```typescript
import { useDebouncedCallback } from 'use-debounce';

function OptimizedCodeAnalysis() {
  const analysis = useCodeAnalysis({ autoAnalyze: false });
  
  const debouncedAnalyze = useDebouncedCallback(
    (code: string, language: string) => {
      analysis.analyzeCode({ code, language });
    },
    1000
  );

  return {
    analyzeCode: debouncedAnalyze,
    ...analysis,
  };
}
```

## Troubleshooting

### Common Issues

1. **Monaco Editor not loading**
   ```typescript
   // Ensure proper dynamic import
   const Editor = dynamic(() => import('@monaco-editor/react'), {
     ssr: false,
   });
   ```

2. **AI services timing out**
   ```typescript
   // Increase timeout in service configuration
   const aiService = new AICodeService({
     timeout: 60000, // 60 seconds
     retryAttempts: 3,
   });
   ```

3. **Rate limiting issues**
   ```typescript
   // Check rate limit status
   const rateLimitStatus = aiService.getRateLimitStatus('user-id');
   if (rateLimitStatus.minuteRequests >= rateLimitStatus.minuteLimit) {
     // Handle rate limit
   }
   ```

### Debug Mode

```typescript
// Enable debug logging
localStorage.setItem('ai-code-editor-debug', 'true');

// Check service health
const healthStatus = await AICodeServiceFactory.healthCheck();
console.log('Service Health:', healthStatus);
```

## Migration Guide

### From Basic Editor to AI Editor

```typescript
// Before: Basic Monaco Editor
import Editor from '@monaco-editor/react';

function BasicEditor() {
  return (
    <Editor
      height="400px"
      defaultLanguage="typescript"
      defaultValue="// Basic editor"
    />
  );
}

// After: AI-Powered Editor
import { AICodeEditor } from '@/components/ai-code-editor';

function EnhancedEditor() {
  return (
    <AICodeEditor
      file={file}
      config={{
        autoCompletion: true,
        aiAssistance: true,
        errorAnalysis: true,
      }}
      onFileChange={handleFileChange}
    />
  );
}
```

This integration guide provides everything needed to successfully integrate the AI Code Editor system into your application. For additional support, refer to the main README and API documentation.
