# AI Code Editor System

A comprehensive AI-powered code editor system for the Omnispace platform that provides intelligent code completion, real-time error analysis, AI-powered refactoring suggestions, and an interactive AI assistant.

## Features

### 🤖 AI-Powered Code Completion
- Real-time intelligent code suggestions
- Context-aware completions based on project structure
- Support for multiple programming languages
- Confidence scoring and priority ranking

### 🔍 Real-Time Error Analysis
- Static code analysis with AI enhancement
- Intelligent error detection and categorization
- Automated fix suggestions with confidence ratings
- Performance, security, and style recommendations

### 🛠️ AI Refactoring Suggestions
- Extract method, rename, inline, and move refactoring
- Code optimization recommendations
- Style and maintainability improvements
- Preview changes before applying

### 💬 Interactive AI Assistant
- Natural language code explanations
- Bug detection and debugging assistance
- Test generation and optimization advice
- Context-aware conversations with code selection

### ♿ Accessibility & Themes
- Full keyboard navigation support
- Screen reader compatibility
- High contrast and reduced motion options
- Dark/light theme support with glassmorphism effects

## Architecture

### Services Layer
```
src/services/ai-code/
├── base.ts                    # Base AI service with caching and rate limiting
├── ai-code-service.ts         # Main AI code operations
├── code-analysis-service.ts   # Static and AI-powered code analysis
├── ai-model-service.ts        # AI model management and selection
├── code-formatting-service.ts # Code formatting and style suggestions
└── index.ts                   # Service factory and exports
```

### Components Layer
```
src/components/ai-code-editor/
├── AICodeEditor.tsx           # Main editor component with Monaco integration
├── AIAssistantPanel.tsx       # Interactive AI chat interface
├── CodeCompletionPopup.tsx    # Inline completion suggestions
├── AIErrorAnalyzer.tsx        # Error display and fix suggestions
├── CodeRefactoringSuggestions.tsx # Refactoring recommendations
└── index.ts                   # Component exports
```

### Hooks Layer
```
src/hooks/
├── use-ai-code-completion.ts  # Code completion logic
├── use-code-analysis.ts       # Error analysis and metrics
├── use-ai-assistant.ts        # AI chat functionality
├── use-code-refactoring.ts    # Refactoring suggestions
├── use-ai-editor-state.ts     # Editor state management
└── ai-code-editor.ts          # Hook exports
```

### API Layer
```
src/app/api/ai-code/
├── completion/route.ts        # Code completion endpoint
├── analysis/route.ts          # Code analysis endpoint
├── assistant/route.ts         # AI assistant endpoint
├── formatting/route.ts        # Code formatting endpoint
└── health/route.ts            # Service health check
```

## Installation

The AI Code Editor system is integrated into the Omnispace platform. To use it:

1. **Install Dependencies** (already included in the project):
   ```bash
   pnpm add @monaco-editor/react monaco-editor ai @ai-sdk/openai @ai-sdk/anthropic
   ```

2. **Environment Variables**:
   ```env
   OPENAI_API_KEY=your_openai_api_key
   ANTHROPIC_API_KEY=your_anthropic_api_key
   ```

3. **Import Components**:
   ```typescript
   import { AICodeEditor, AIAssistantPanel } from '@/components/ai-code-editor';
   import { useAICodeCompletion, useCodeAnalysis } from '@/hooks/ai-code-editor';
   ```

## Usage Examples

### Basic AI Code Editor

```typescript
import { AICodeEditor } from '@/components/ai-code-editor';
import { CodeFile } from '@/types/ai-code-editor';

function MyCodeEditor() {
  const [file, setFile] = useState<CodeFile>({
    id: 'example-file',
    name: 'example.ts',
    path: '/example.ts',
    content: 'const greeting = "Hello, World!";',
    language: 'typescript',
    isDirty: false,
    lastModified: new Date(),
    size: 32,
  });

  return (
    <AICodeEditor
      file={file}
      onFileChange={setFile}
      onSave={async (file) => {
        console.log('Saving file:', file.name);
      }}
      userId="user-123"
      workspaceId="workspace-456"
    />
  );
}
```

### Using AI Code Completion Hook

```typescript
import { useAICodeCompletion } from '@/hooks/ai-code-editor';

function CodeCompletionExample() {
  const completion = useAICodeCompletion({
    autoTrigger: true,
    maxSuggestions: 5,
    userId: 'user-123',
    workspaceId: 'workspace-456',
  });

  const handleRequestCompletion = async () => {
    await completion.requestCompletion({
      code: 'const user = { name: "John", age: 30 };\nuser.',
      position: { line: 2, column: 5 },
      language: 'typescript',
      context: {
        fileName: 'user.ts',
        projectContext: 'User management system',
      },
    });
  };

  return (
    <div>
      <button onClick={handleRequestCompletion}>
        Get Completions
      </button>
      {completion.suggestions.map(suggestion => (
        <div key={suggestion.id}>
          {suggestion.text} (confidence: {suggestion.confidence})
        </div>
      ))}
    </div>
  );
}
```

### AI Assistant Integration

```typescript
import { AIAssistantPanel } from '@/components/ai-code-editor';
import { useAIAssistant } from '@/hooks/ai-code-editor';

function AIAssistantExample() {
  const [isOpen, setIsOpen] = useState(false);
  const assistant = useAIAssistant({
    userId: 'user-123',
    workspaceId: 'workspace-456',
  });

  return (
    <div className="flex h-screen">
      <div className="flex-1">
        {/* Your main content */}
      </div>
      {isOpen && (
        <div className="w-80">
          <AIAssistantPanel
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
            currentFile={{
              name: 'example.ts',
              content: 'const example = "code";',
              language: 'typescript',
            }}
            userId="user-123"
            workspaceId="workspace-456"
          />
        </div>
      )}
    </div>
  );
}
```

### Code Analysis Hook

```typescript
import { useCodeAnalysis } from '@/hooks/ai-code-editor';

function CodeAnalysisExample() {
  const analysis = useCodeAnalysis({
    autoAnalyze: true,
    includeStyle: true,
    includePerformance: true,
    includeSecurity: true,
  });

  const analyzeCode = async () => {
    await analysis.analyzeCode({
      code: `
        function calculateTotal(items) {
          var total = 0;
          for (var i = 0; i < items.length; i++) {
            if (items[i].price != null) {
              total += items[i].price;
            }
          }
          return total;
        }
      `,
      language: 'javascript',
      fileName: 'calculator.js',
    });
  };

  return (
    <div>
      <button onClick={analyzeCode}>Analyze Code</button>
      
      {analysis.errors.map(error => (
        <div key={error.id} className="error">
          <strong>{error.severity}:</strong> {error.message}
          <span>Line {error.range.start.line}</span>
          {error.fixes?.map(fix => (
            <button
              key={fix.id}
              onClick={() => analysis.applyFix(fix)}
            >
              {fix.title}
            </button>
          ))}
        </div>
      ))}
      
      <div className="metrics">
        <p>Complexity: {analysis.metrics.complexity}/10</p>
        <p>Maintainability: {analysis.metrics.maintainability}/10</p>
        <p>Readability: {analysis.metrics.readability}/10</p>
      </div>
    </div>
  );
}
```

## API Reference

### REST Endpoints

#### POST /api/ai-code/completion
Generate code completions for a given context.

**Request Body:**
```typescript
{
  code: string;
  position: { line: number; column: number };
  language: string;
  context?: {
    fileName?: string;
    projectContext?: string;
    recentChanges?: string[];
  };
  userId?: string;
  workspaceId?: string;
}
```

**Response:**
```typescript
{
  success: boolean;
  data: {
    suggestions: AICompletionSuggestion[];
    metadata: ResponseMetadata;
    aiMetadata: AIResponseMetadata;
  };
}
```

#### POST /api/ai-code/analysis
Analyze code for errors, suggestions, and metrics.

**Request Body:**
```typescript
{
  code: string;
  language: string;
  fileName?: string;
  includeStyle?: boolean;
  includePerformance?: boolean;
  includeSecurity?: boolean;
  userId?: string;
  workspaceId?: string;
}
```

#### POST /api/ai-code/assistant
Send a message to the AI assistant.

**Request Body:**
```typescript
{
  message: string;
  codeContext?: {
    file: string;
    selection?: CodeSelection;
    fullCode?: string;
  };
  conversationHistory?: AIAssistantMessage[];
  userId?: string;
  workspaceId?: string;
}
```

#### GET /api/ai-code/health
Check the health status of all AI services.

**Response:**
```typescript
{
  success: boolean;
  data: {
    overall: boolean;
    aiCode: boolean;
    codeAnalysis: boolean;
    aiModel: boolean;
    codeFormatting: boolean;
    timestamp: string;
  };
}
```

## Configuration

### AI Model Configuration

```typescript
import { AIModelService } from '@/services/ai-code';

const modelService = new AIModelService();

// Update model configuration
modelService.updateModelConfig('gpt-4', {
  temperature: 0.1,
  maxTokens: 2048,
  topP: 1,
});

// Get model recommendations
const recommendedModel = modelService.getModelRecommendation('completion');
```

### Accessibility Configuration

```typescript
import { 
  applyAccessibilityConfig, 
  DEFAULT_ACCESSIBILITY_CONFIG 
} from '@/lib/ai-code-editor-accessibility';

// Apply accessibility settings
applyAccessibilityConfig({
  ...DEFAULT_ACCESSIBILITY_CONFIG,
  enableHighContrast: true,
  fontSize: 'large',
  enableReducedMotion: true,
});
```

### Theme Configuration

```typescript
import { applyTheme, DEFAULT_THEMES } from '@/lib/ai-code-editor-accessibility';

// Apply dark theme
applyTheme(DEFAULT_THEMES.dark);

// Apply custom theme
applyTheme({
  mode: 'dark',
  primaryColor: 'hsl(280 100% 70%)',
  backgroundColor: 'hsl(240 10% 3.9%)',
  // ... other theme properties
});
```

## Keyboard Shortcuts

| Action | Shortcut | Description |
|--------|----------|-------------|
| Trigger Completion | `Ctrl+Space` | Show AI code completions |
| Accept Completion | `Tab` | Apply selected completion |
| Dismiss Completion | `Escape` | Hide completion popup |
| Toggle AI Assistant | `Ctrl+Shift+A` | Open/close AI assistant |
| Send Message | `Ctrl+Enter` | Send message to AI assistant |
| Save File | `Ctrl+S` | Save current file |
| Analyze Code | `Ctrl+Shift+E` | Run code analysis |
| Next Error | `F8` | Navigate to next error |
| Previous Error | `Shift+F8` | Navigate to previous error |

## Testing

Run the test suite:

```bash
# Run all AI code editor tests
pnpm test src/__tests__/ai-code-services.test.ts
pnpm test src/__tests__/ai-code-hooks.test.tsx

# Run with coverage
pnpm test --coverage
```

## Performance Considerations

- **Debouncing**: Code completion and analysis requests are debounced to prevent excessive API calls
- **Caching**: AI responses are cached for 1 hour to improve performance
- **Rate Limiting**: Built-in rate limiting prevents API quota exhaustion
- **Lazy Loading**: Components are loaded on-demand to reduce initial bundle size

## Security

- **Input Validation**: All API inputs are validated using Zod schemas
- **Rate Limiting**: Per-user rate limiting prevents abuse
- **Error Handling**: Comprehensive error handling prevents information leakage
- **API Key Management**: Secure handling of AI service API keys

## Contributing

1. Follow the existing TypeScript patterns and project structure
2. Add comprehensive tests for new features
3. Ensure accessibility compliance
4. Update documentation for API changes
5. Test with both light and dark themes

## License

This AI Code Editor system is part of the Omnispace platform and follows the project's licensing terms.
